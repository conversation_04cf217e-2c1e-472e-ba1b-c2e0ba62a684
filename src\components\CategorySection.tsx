"use client";

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';

interface Category {
  id: string;
  title: string;
  image: string;
  link: string;
}

interface CategorySectionProps {
  title: string;
  categories: Category[];
}

export default function CategorySection({ title, categories }: CategorySectionProps) {
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);

  return (
    <section className="py-8">
      <div className="container px-4 md:px-6 mx-auto">
        <h2 className="text-xl md:text-2xl font-semibold text-vista-light tracking-tight mb-5">{title}</h2>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4">
          {categories.map((category, index) => (
            <motion.div
              key={category.id}
              initial={{ opacity: 0, y: 15 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: index * 0.1 }}
              onMouseEnter={() => setHoveredCategory(category.id)}
              onMouseLeave={() => setHoveredCategory(null)}
              className="group"
            >
              <Link href={category.link} className="block relative">
                {/* Category Card */}
                <div className="aspect-[16/9] relative overflow-hidden rounded-lg shadow-vista hover-card-effect">
                  {/* Background Image */}
                  <Image
                    src={category.image}
                    alt={category.title}
                    fill
                    className="object-cover content-image-hover"
                  />

                  {/* Gradient Overlay */}
                  <div
                    className={`absolute inset-0 transition-opacity duration-300 bg-card-gradient opacity-90`}
                  />

                  {/* Title */}
                  <div className="absolute inset-0 flex items-center justify-center p-3">
                    <h3 className={`text-lg md:text-xl font-medium text-center transition-transform duration-300 text-vista-light ${
                      hoveredCategory === category.id ? 'scale-105' : ''
                    }`}>
                      {category.title}
                    </h3>
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}

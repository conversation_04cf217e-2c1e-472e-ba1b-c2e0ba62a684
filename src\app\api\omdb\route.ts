import { NextRequest, NextResponse } from 'next/server';
import { getContentByImdbId, searchOMDB } from '@/lib/omdb-api';
import { IContent } from '@/data/content';

/**
 * API route for OMDB data
 * Provides endpoints for searching and retrieving content from OMDB
 */
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const imdbId = searchParams.get('imdbId');
  const query = searchParams.get('query');
  const type = searchParams.get('type') as 'movie' | 'series' | undefined;
  const page = searchParams.get('page') ? parseInt(searchParams.get('page') as string) : 1;
  
  try {
    // If IMDb ID is provided, fetch specific content
    if (imdbId) {
      console.log(`OMDB API: Fetching content with IMDb ID: ${imdbId}`);
      const content = await getContentByImdbId(imdbId);
      return NextResponse.json(content);
    }
    
    // If query is provided, search for content
    if (query) {
      console.log(`OMDB API: Searching for "${query}" (type: ${type || 'all'}, page: ${page})`);
      const results = await searchOMDB(query, page, type);
      return NextResponse.json(results);
    }
    
    // If neither IMDb ID nor query is provided, return error
    return NextResponse.json({ error: 'Either imdbId or query parameter is required' }, { status: 400 });
  } catch (error) {
    console.error('Error in OMDB API route:', error);
    return NextResponse.json({ error: 'Failed to fetch data from OMDB' }, { status: 500 });
  }
}

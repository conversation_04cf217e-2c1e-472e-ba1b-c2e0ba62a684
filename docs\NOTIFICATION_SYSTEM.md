# Notification System

## Overview

StreamVista's Notification System provides real-time notifications to users across the platform. The system is designed to deliver immediate updates about new content, recommendations, system updates, and other important events to enhance user engagement and experience.

## Core Components

### Notification Model

```typescript
interface NotificationDocument extends Document {
  userId: mongoose.Types.ObjectId;
  type: NotificationType;  // 'new_content' | 'recommendation' | 'update' | 'system'
  title: string;
  message: string;
  contentId?: string;
  contentType?: 'movie' | 'show';
  image?: string;
  read: boolean;
  createdAt: Date;
  expiresAt?: Date;
  deletedBy?: mongoose.Types.ObjectId[];
  isGlobal?: boolean;
}
```

### Real-time Delivery

The notification system uses <PERSON><PERSON><PERSON> for real-time delivery, with two types of channels:
- `global-notifications` - System-wide events and broad announcements
- `user-${userId}` - User-specific notifications for individual users

### Event Types

- `new-notification` - Triggered when a new notification is created
- `notification-deleted` - Triggered when a specific notification is deleted
- `notification-deleted-all` - Triggered when all notifications are cleared (admin action)

## Client-Side Implementation

### Notification Context

The `NotificationContext` provides access to notifications throughout the application:

```typescript
interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  loading: boolean;
  error: string | null;
  fetchNotifications: () => Promise<void>;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (id: string) => Promise<void>;
}
```

### Usage Example

```tsx
import { useNotifications } from '@/contexts/NotificationContext';

function MyComponent() {
  const { notifications, unreadCount, markAsRead } = useNotifications();
  
  return (
    <div>
      <span>You have {unreadCount} unread notifications</span>
      <ul>
        {notifications.map(notification => (
          <li key={notification._id} onClick={() => markAsRead(notification._id)}>
            {notification.title}
          </li>
        ))}
      </ul>
    </div>
  );
}
```

## API Endpoints

### User Endpoints

#### GET /api/notifications
Retrieves notifications for the current user with filtering and pagination.

**Query Parameters:**
- `userId` (required): The ID of the user
- `limit` (optional): Number of notifications to return (default: 10)
- `page` (optional): Page number for pagination (default: 1)
- `read` (optional): Filter by read status ('true', 'false', or omit for all)

**Response:**
```json
{
  "notifications": [
    {
      "_id": "string",
      "userId": "string",
      "type": "new_content",
      "title": "string",
      "message": "string",
      "contentId": "string",
      "contentType": "movie",
      "image": "string",
      "read": false,
      "createdAt": "string",
      "expiresAt": "string"
    }
  ],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 10,
    "pages": 10
  },
  "unreadCount": 5
}
```

#### PATCH /api/notifications/[id]
Marks a notification as read.

**Request Body:**
```json
{
  "userId": "string",
  "read": true
}
```

**Response:**
```json
{
  "notification": {
    "_id": "string",
    "userId": "string",
    "type": "new_content",
    "title": "string",
    "message": "string",
    "read": true,
    "createdAt": "string"
  }
}
```

#### DELETE /api/notifications/[id]
Deletes a notification for the current user.

**Query Parameters:**
- `userId` (required): The ID of the user

**Response:**
```json
{
  "success": true,
  "message": "Notification deleted"
}
```

### Admin Endpoints

#### POST /api/admin/notifications/broadcast
Creates and broadcasts a notification to all users.

**Request Body:**
```json
{
  "type": "system",
  "title": "System Maintenance",
  "message": "StreamVista will be undergoing maintenance...",
  "contentId": "string",
  "contentType": "movie",
  "image": "string",
  "expiresAt": "2023-12-31T23:59:59Z"
}
```

**Response:**
```json
{
  "message": "Notification broadcast successful",
  "sentCount": 150
}
```

#### DELETE /api/admin/notifications
Deletes all notifications in the system.

**Response:**
```json
{
  "message": "All notifications deleted",
  "deletedCount": 350
}
```

#### DELETE /api/admin/notifications/[id]
Deletes a specific notification or all instances of a global notification.

**Response:**
```json
{
  "message": "Notification deleted",
  "deletedCount": 1
}
```

## Notification Types

| Type | Purpose | Icon | Background |
|------|---------|------|------------|
| `new_content` | New movies and shows | Film icon | Blue |
| `recommendation` | Personalized content recommendations | TV icon | Green |
| `update` | Platform updates and features | Info icon | Amber |
| `system` | System announcements | Bell icon | Purple |

## UI Components

### NotificationsClient
A full-page view for managing all notifications with filtering capabilities.

**Location:** `src/app/notifications/NotificationsClient.tsx`

### Admin Components

#### NotificationCreationForm
Admin form for creating and broadcasting notifications to all users.

**Location:** `src/components/admin/NotificationCreationForm.tsx`

#### AdminNotificationsClient
Admin dashboard for managing all notifications in the system.

**Location:** `src/app/admin/notifications/AdminNotificationsClient.tsx`

#### NotificationCard
Displays an individual notification with actions for managing it.

**Location:** `src/components/admin/NotificationCard.tsx`

## Best Practices

### Creating Notifications

1. **Be concise**: Keep notification titles under 50 characters and messages under 150 characters
2. **Be specific**: Clearly state what the notification is about
3. **Add context**: Include relevant content links when applicable
4. **Set expiration**: Add expiration dates for time-sensitive notifications
5. **Use appropriate type**: Choose the correct notification type for proper styling

### Implementation Guidelines

1. **Error handling**: Always handle errors in notification operations
2. **Optimize fetching**: Implement caching and pagination for better performance
3. **Cleanup subscriptions**: Unsubscribe from Pusher channels when components unmount
4. **Batch operations**: Group notifications when possible to reduce API calls
5. **Use debouncing**: Implement debouncing for actions like marking as read

## Integration with Other Services

### Content Service
Notifications are automatically generated when new content is added to the platform.

### Recommendation Service
The recommendation engine triggers notifications for personalized content suggestions.

### Watch Party Service
Watch party invitations and updates are delivered through the notification system.

## Future Enhancements

1. **Notification preferences**: Allow users to customize which notifications they receive
2. **Mobile push notifications**: Extend the system to support mobile push delivery
3. **Email notifications**: Add email delivery for important notifications
4. **Rich notifications**: Support for rich media content in notifications
5. **Notification analytics**: Track engagement metrics for notifications
6. **Priority levels**: Add support for urgent/important notification highlighting
7. **Notification grouping**: Group similar notifications to reduce clutter
8. **Interactive notifications**: Add action buttons directly in notifications

## Troubleshooting

### Common Issues

1. **Notifications not appearing in real-time**:
   - Check Pusher configuration
   - Verify channel subscription in client
   - Ensure WebSocket connection is active

2. **Missing notifications in database**:
   - Verify notification creation request has all required fields
   - Check user ID is valid
   - Ensure MongoDB connection is active

3. **Admin notifications not broadcasting**:
   - Verify admin role permissions
   - Check for rate limiting issues with Pusher
   - Look for errors in admin broadcasting endpoint

## Monitoring and Maintenance

1. **Pusher Dashboard**: Monitor real-time connection and message metrics
2. **Database Queries**: Regularly review notification collection performance
3. **Client Performance**: Watch for memory leaks from notification subscriptions
4. **Cleanup**: Implement automated cleanup of expired notifications 
import { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold } from "@google/generative-ai"
import { getPopularMovies, getPopularTVShows, getTrendingDaily, getTrendingWeekly, getUpcomingMovies, getNowPlayingMovies } from '@/lib/tmdb-api' // Import dynamic content fetchers
import { NextResponse } from "next/server"
import fs from "fs" // Import Node.js fs module
import path from "path" // Import Node.js path module

const MODEL_NAME = "gemini-1.5-flash-latest"
// Use the environment variable that's available on Netlify
const API_KEY = process.env.GOOGLE_API_KEY || process.env.NEXT_PUBLIC_GOOGLE_API_KEY || ""

// Function to read context from file
function getWebsiteContext(): string {
  try {
    // Construct the absolute path to the file
    // process.cwd() gives the root of your Next.js project
    const filePath = path.join(process.cwd(), "src", "data", "website-info.md")
    return fs.readFileSync(filePath, "utf-8")
  } catch (error) {
    console.error("Error reading website info context file:", error)
    return "[Error: Could not load website information]" // Provide fallback
  }
}

// Run chat with full conversation history (user+assistant messages)
async function runChat(conversation: Array<{ sender: string; text: string }>) {
  const genAI = new GoogleGenerativeAI(API_KEY)
  const model = genAI.getGenerativeModel({ model: MODEL_NAME })

  // Load context
  const websiteContext = getWebsiteContext()

  // Fetch real-time popular movies, TV shows, trending, upcoming, now playing
  let popularContext = ''
  try {
    const popularMovies = await getPopularMovies()           // MappedContent[]
    const popularShows = await getPopularTVShows()
    const trendingDaily = await getTrendingDaily('all')      // Trending daily (all media)
    const trendingWeekly = await getTrendingWeekly('all')    // Trending weekly
    const upcomingMovies = await getUpcomingMovies()         // Upcoming movies
    const nowPlayingMovies = await getNowPlayingMovies()     // Now playing movies

    // Helper to join top 5 titles
    const listTitles = (items: { title: string }[]) => items.slice(0,5).map(i => i.title).join(', ')

    const topMovies = listTitles(popularMovies)
    const topShows = listTitles(popularShows)
    const trendingToday = listTitles(trendingDaily)
    const trendingWeek = listTitles(trendingWeekly)
    const upcoming = listTitles(upcomingMovies)
    const nowPlaying = listTitles(nowPlayingMovies)

    popularContext = `Top 5 movies: ${topMovies}. Top 5 TV shows: ${topShows}. Trending today: ${trendingToday}. Trending this week: ${trendingWeek}. Upcoming movies: ${upcoming}. Now playing: ${nowPlaying}.`
  } catch (err) {
    console.error('Failed to fetch real-time popular content:', err)
  }

  // Build conversation history text and get last user message
  const historyText = conversation.slice(0, -1)
    .map(msg => msg.sender === 'user' ? `User: ${msg.text}` : `Assistant: ${msg.text}`)
    .join("\n");
  const userMessage = conversation[conversation.length - 1]?.text || '';

  // --- Build full prompt including dynamic context and conversation history ---
  const fullPrompt = `You are a highly knowledgeable, detailed, and engaging assistant for the StreamVista website. You can answer any user's question perfectly in a friendly, conversational style.
Use the context and dynamic real-time content provided to inform your responses. KEEP ALL RESPONSES CONCISE (max 3-4 sentences for general questions).

When listing movie or TV show recommendations, use this CLEAR format:

• **Title** (year)
  Genre: [primary genre]
  Description: [one brief sentence]

Leave a blank line between each movie or show for better readability.

If listing more than 3 items, make descriptions even shorter or omit them. 
Never include "Availability status" unless specifically asked.

Keep your total response under 150 words regardless of the question.

For all other questions, provide concise, accurate answers without excessive details. Use 1-2 sentences when possible.

End your responses with a short, relevant follow-up question that moves the conversation forward.

Always prioritize information from the StreamVista FAQ context. For missing information, clearly but briefly state you don't have those details rather than guessing.

--- START STREAMVISTA INFO ---
${websiteContext}
--- END STREAMVISTA INFO ---

--- START REAL-TIME POPULAR CONTENT ---
${popularContext}
--- END REAL-TIME POPULAR CONTENT ---

--- START CONVERSATION HISTORY ---
${historyText}
--- END CONVERSATION HISTORY ---

Last user message: ${userMessage}
Assistant:`

  const result = await model.generateContent(fullPrompt)
  const response = result.response
  return response.text()
  // --- End Simple Text Generation ---
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    let conversation: Array<{ sender: string; text: string }>;
    if (Array.isArray(body.messages) && body.messages.length) {
      conversation = body.messages;
    } else if (typeof body.message === 'string' && body.message.trim()) {
      conversation = [{ sender: 'user', text: body.message.trim() }];
    } else {
      return NextResponse.json(
        { error: 'Must provide a non-empty messages array or a single message string' },
        { status: 400 }
      );
    }

    if (!API_KEY) {
      console.error("GOOGLE_API_KEY is not set in environment variables.")
      return NextResponse.json(
        { error: "API key not configured" },
        { status: 500 }
      )
    }

    const aiResponse = await runChat(conversation)

    return NextResponse.json({ reply: aiResponse })
  } catch (error) {
    console.error("Error in chat API:", error)
    // Check if the error is from the Generative AI API specifically
    let errorMessage = "Failed to get response from AI.";
    if (error instanceof Error) {
        errorMessage = `Failed to get response from AI: ${error.message}`;
        // You might want to log error.stack here as well for more details
    }
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    )
  }
} 
# Watch Party Service

## Overview

StreamVista's Watch Party Service enables real-time synchronized video watching experiences for multiple users. The service provides functionality for creating and managing watch parties, synchronizing playback across participants, and facilitating real-time communication through chat and reactions.

## Core Architecture

The Watch Party system is built using <PERSON><PERSON><PERSON> as its real-time communication layer:

**Pusher Integration**
- Client-side: Initializes safely with fallbacks in `pusher-client.ts`
- Server-side: Handles broadcasting events to connected clients via `pusher-server.ts`
- Channel Structure: 
  - Main channel (`watch-parties`) for global updates
  - Party-specific channels (`watch-party-${partyId}`) for targeted updates

**Storage Strategy**
- Server-side In-memory Map: Primary storage for active parties
- Global Variable Persistence: Maintains state between API route executions
- Local Storage: Caches user identity and preferences
- Session Storage: Tracks notification state for the current session
- Custom Cleanup Logic: Implements automatic deletion of stale parties

## Core Components

### Watch Party State

```typescript
interface WatchPartyState {
  id: string;
  contentId: string;
  content?: any;
  hostId: string;
  title?: string;           // Title of the party
  contentType?: string;     // Type of content (video, movie, show)
  hostName?: string;        // Name of the host
  members: WatchPartyMember[];
  messages: ChatMessage[];
  currentTime: number;
  isPlaying: boolean;
  createdAt: string;
  startedAt?: string;
  currentSeason?: number;   // For TV shows
  currentEpisode?: number;  // For TV shows
}

interface WatchPartyMember {
  id: string;
  name: string;
  avatar?: string;
  isHost: boolean;
  joinedAt: string;
  isReady: boolean;
  lastActiveAt?: string;    // Track member activity
}

interface ChatMessage {
  id: string;
  memberId: string;
  memberName: string;
  avatar?: string;          // Member avatar for messages
  content: string;
  timestamp: string;
  type: 'chat' | 'system' | 'reaction';
}
```

## Communication Events

```typescript
const WATCH_PARTY_EVENTS = {
  // Party management events
  CREATE_PARTY: 'create-watch-party',
  JOIN_PARTY: 'join-watch-party',
  LEAVE_PARTY: 'leave-watch-party',
  GET_PARTIES: 'get-watch-parties',
  
  // Update events
  PARTY_CREATED: 'party-created',
  PARTY_UPDATE: 'party-update',
  PARTY_DELETED: 'party-deleted',
  MEMBER_UPDATE: 'member-update',
  PLAYBACK_UPDATE: 'playback-update',
  NEW_MESSAGE: 'new-message',
  AVAILABLE_PARTIES: 'available-parties'
};
```

## Features

### 1. Party Management

- **Create Watch Parties**: Create private or public watch parties for specific content
- **Join Existing Parties**: Join parties via invitations or listings
- **Party Listings**: Browse available public parties
- **Member Management**: Track participants, assign host privileges
- **Host Controls**: Special privileges for party creators
- **Private Parties**: Invite-only access with shareable links
- **Party Persistence**: Automatic recovery of disconnected parties

### 2. Synchronized Playback

#### Time Synchronization
```typescript
// Threshold for significant time differences (2 seconds)
const TIME_DIFF_THRESHOLD = 2;

// Only send updates for significant changes
if (Math.abs(prevTime - currentTime) > TIME_DIFF_THRESHOLD) {
  updatePlayback(currentTime, isPlaying);
}
```

#### Update Throttling
```typescript
// Throttle playback updates to reduce network traffic
const now = Date.now();
if (now - lastUpdateTime < MIN_UPDATE_INTERVAL) {
  return; // Skip update if too soon
}
lastUpdateTime = now;
```

#### Conflict Resolution
```typescript
// Host has authority in conflicts
if (isHost || !otherIsHost) {
  // Allow update if we're host or the updater isn't host
  applyPlaybackUpdate(update);
} else {
  // Defer to host's state
  console.log("Deferring to host's playback state");
}
```

#### Key Features
- **Playback Controls**: Play, pause, seek synchronized across all members
- **Host Authority**: Host has final control over playback
- **TV Show Navigation**: Season and episode selection with synchronization
- **Buffering Handling**: Smart detection of buffering states
- **Race Condition Prevention**: Update sequencing to prevent out-of-order updates
- **Automatic Resync**: Automatic recovery from desynchronized states

### 3. Real-time Communication

- **Text Chat**: Real-time messaging between party members
- **System Messages**: Automated messages for joins, leaves, and state changes
- **Emoji Support**: Full emoji support in chat messages
- **Message History**: Persistent message history within party sessions
- **Member Presence**: Real-time member status and activity tracking
- **Notifications**: Toast notifications for important events

### 4. Content Type Handling

The system properly handles different content types with specific logic:

```typescript
// Different parameters needed for different content types
if (contentToUse.type === 'show') {
  // Get season and episode from the party data, with strong fallbacks
  const season = (party?.currentSeason !== undefined)
      ? party.currentSeason
      : (searchParams.get('season') ? parseInt(searchParams.get('season')!) : 1);

  const episode = (party?.currentEpisode !== undefined)
      ? party.currentEpisode
      : (searchParams.get('episode') ? parseInt(searchParams.get('episode')!) : 1);

  // Add the parameters to the URL
  watchUrl += `&season=${season}&episode=${episode}`;
}
```

### 5. Error Handling & Recovery

Advanced error handling includes:

- **Retry Mechanisms**: Automatic retry for failed operations with backoff
- **Debounce Protection**: Prevents rapid-fire API calls during errors
- **Party Verification**: Validates party existence before joining
- **Graceful Degradation**: Falls back to alternative paths when primary fails
- **User-friendly Notifications**: Translates errors into actionable user interfaces

## State Management

### Core Pattern

The watch party feature uses a custom hook-based state management approach:

```typescript
// Primary state management hook
export function useWatchPartyWithPusher() {
  // User identity management with localStorage persistence
  const [userId] = useState<string>(() => {
    const storedId = typeof window !== 'undefined' ? localStorage.getItem('watchPartyUserId') : null;
    const id = storedId || uuidv4().substring(0, 8);
    if (typeof window !== 'undefined' && !storedId) {
      localStorage.setItem('watchPartyUserId', id);
    }
    return id;
  });

  // Party state
  const [currentParty, setCurrentParty] = useState<WatchPartyState | null>(null);
  const [availableParties, setAvailableParties] = useState<WatchPartyState[]>([]);
  
  // Action functions
  const createParty = useCallback(async (content: IContent, season?: number, episode?: number, isPrivate: boolean = false): Promise<string> => {
    // Implementation
  }, [userId, userName, fetchAvailableParties]);

  const joinParty = useCallback(async (partyId: string): Promise<void> => {
    // Implementation with retry logic
  }, [userId, userName, currentParty?.id]);

  // Other action functions...

  // Return comprehensive API
  return {
    currentParty,
    availableParties,
    isLoading,
    error,
    sendMessage,
    joinParty,
    leaveParty,
    updatePlayback,
    userId,
    userName,
    setUserName,
    isHost,
    createParty,
    deleteParty
  };
}
```

### Optimistic Updates

The implementation uses optimistic updates to provide a responsive user experience:

```typescript
// Example of optimistic update for messages
const sendMessage = useCallback(async (content: string): Promise<void> => {
  if (!currentParty?.id || content.trim() === '') return;

  // Create message object
  const messageId = uuidv4();
  const message: ChatMessage = {
    id: messageId,
    memberId: userId,
    memberName: userName,
    content,
    timestamp: new Date().toISOString(),
    type: 'chat'
  };

  // Optimistically add to local state
  setCurrentParty(prev => {
    if (!prev) return null;
    return {
      ...prev,
      messages: [...prev.messages, message]
    };
  });

  // Then send to server
  try {
    await fetch('/api/watch-party', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        event: 'new-message',
        data: {
          partyId: currentParty.id,
          message
        }
      })
    });
  } catch (error) {
    console.error('Failed to send message:', error);
  }
}, [currentParty?.id, userId, userName]);
```

## Performance Optimizations

### 1. Smart Rendering

```typescript
// Memoize expensive computations
const membersSortedByTime = useMemo(() => {
  if (!party?.members?.length) return [];
  
  return [...party.members].sort((a, b) => {
    // Host always first
    if (a.isHost) return -1;
    if (b.isHost) return 1;
    
    // Then sort by join time
    return new Date(a.joinedAt).getTime() - new Date(b.joinedAt).getTime();
  });
}, [party?.members]);
```

### 2. Background Loading

```typescript
// Preload content details in background
useEffect(() => {
  if (party?.contentId) {
    // Set immediate loading state
    setIsContentLoading(true);
    
    // Fetch in background
    getContentById(party.contentId, party.contentType || 'movie')
      .then(content => {
        setContentDetails(content);
      })
      .catch(err => console.error('Error loading content details:', err))
      .finally(() => setIsContentLoading(false));
  }
}, [party?.contentId, party?.contentType]);
```

### 3. Event Throttling

```typescript
// Prevent too frequent playback updates
const lastPlaybackUpdate = useRef<number>(0);
const MIN_UPDATE_INTERVAL = 1000; // 1 second minimum between updates

const sendPlaybackUpdate = (time: number, playing: boolean) => {
  const now = Date.now();
  if (now - lastPlaybackUpdate.current < MIN_UPDATE_INTERVAL) {
    return; // Skip this update to reduce network traffic
  }
  
  lastPlaybackUpdate.current = now;
  updatePlayback(time, playing);
};
```

## Security & Data Integrity

The implementation includes several important security measures:

1. **Host Authority**: Only hosts can make certain changes like starting parties
2. **Member Validation**: Checks for valid member status before allowing access
3. **Input Sanitization**: Validates and sanitizes inputs at multiple levels
4. **Private Party Access**: Requires valid access codes for private parties
5. **Data Validation**: Implements schema validation for incoming data
6. **Cross-Session Protection**: Prevents session mix-ups with user ID tracking

## Development Guidelines

1. **Adding Features**
   - Define event types in `WATCH_PARTY_EVENTS`
   - Update state interfaces
   - Implement handlers in API route
   - Add client methods to `useWatchPartyWithPusher` hook

2. **Testing Watch Parties**
   - Test party operations with multiple clients
   - Verify synchronization across devices
   - Check error handling and recovery
   - Test TV show vs. movie specific behavior
   - Test disconnection and reconnection flows

3. **Debugging Tips**
   - Examine browser console logs (prefixed with `[WatchParty]`)
   - Check server-side logs for event processing
   - Use browser storage inspection for user identity
   - Monitor network requests for API calls
   - Use multiple browsers to simulate multiple users 
'use client';

import React, { useState } from 'react';
import { RefreshCw, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { TooltipElement } from '@/components/ui/tooltip';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

interface AutoRefreshControlProps {
  isEnabled?: boolean;
  enabled?: boolean;
  onToggle?: (enabled: boolean) => void;
  toggleAutoRefresh?: (enabled: boolean) => void;
  currentInterval?: number;
  refreshInterval?: number;
  onIntervalChange?: (interval: number) => void;
  setRefreshInterval?: (interval: number) => void;
  lastUpdated?: string;
  isLoading?: boolean;
}

const REFRESH_INTERVALS = [
  { value: 10000, label: '10 seconds' },
  { value: 30000, label: '30 seconds' },
  { value: 60000, label: '1 minute' },
  { value: 300000, label: '5 minutes' }
];

export function AutoRefreshControl({
  isEnabled,
  enabled = isEnabled,
  onToggle,
  toggleAutoRefresh = onToggle,
  currentInterval,
  refreshInterval = currentInterval,
  onIntervalChange,
  setRefreshInterval = onIntervalChange,
  lastUpdated,
  isLoading
}: AutoRefreshControlProps) {
  const [isOpen, setIsOpen] = useState(false);
  
  // Use the new or old prop names
  const isAutoRefreshEnabled = enabled ?? isEnabled ?? false;
  const interval = refreshInterval ?? currentInterval ?? 30000;
  const handleToggle = toggleAutoRefresh ?? onToggle ?? (() => {});
  const handleIntervalChange = setRefreshInterval ?? onIntervalChange ?? (() => {});

  // Format the last updated timestamp
  const formatTimestamp = (timestamp?: string) => {
    if (!timestamp) return 'Never';
    
    try {
      const date = new Date(timestamp);
      return date.toLocaleTimeString();
    } catch (e) {
      return 'Invalid time';
    }
  };

  return (
    <div className="flex items-center space-x-2">
      {lastUpdated && (
        <TooltipElement content="Last data refresh time">
          <Button variant="outline" size="sm" className="text-vista-light/70 hidden md:flex">
            <Clock className="mr-2 h-4 w-4" />
            <span className="text-xs">Updated: {formatTimestamp(lastUpdated)}</span>
          </Button>
        </TooltipElement>
      )}
      
      <div className="relative">
        <Button
          variant={isAutoRefreshEnabled ? "default" : "outline"}
          size="sm"
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center gap-1"
        >
          <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : isAutoRefreshEnabled ? 'animate-pulse' : ''}`} />
          <span className="hidden sm:inline">Auto-refresh</span>
          {isAutoRefreshEnabled && <span className="bg-white bg-opacity-20 text-xs px-1 rounded hidden sm:inline">ON</span>}
        </Button>
        
        {isOpen && (
          <div className="absolute right-0 top-10 mt-1 w-64 p-4 bg-vista-dark-lighter rounded-md shadow-lg z-10 border border-vista-light/10">
            <div className="flex items-center justify-between mb-4">
              <Label htmlFor="auto-refresh-toggle" className="text-sm font-medium text-vista-light">
                Auto-refresh data
              </Label>
              <Switch
                id="auto-refresh-toggle"
                checked={isAutoRefreshEnabled}
                onCheckedChange={(checked) => {
                  handleToggle(!!checked);
                  if (!checked) setIsOpen(false);
                }}
              />
            </div>
            
            {isAutoRefreshEnabled && (
              <div className="space-y-3">
                <div>
                  <Label htmlFor="refresh-interval" className="text-xs text-vista-light/70 block mb-1">
                    Refresh interval
                  </Label>
                  <Select
                    value={interval.toString()}
                    onValueChange={(value) => handleIntervalChange(parseInt(value))}
                  >
                    <SelectTrigger id="refresh-interval" className="w-full bg-vista-dark">
                      <SelectValue placeholder="Select interval" />
                    </SelectTrigger>
                    <SelectContent>
                      {REFRESH_INTERVALS.map((interval) => (
                        <SelectItem key={interval.value} value={interval.value.toString()}>
                          {interval.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <p className="text-xs text-vista-light/60 italic">
                  Data will automatically refresh every {interval / 1000} seconds.
                </p>
              </div>
            )}
            
            <Button
              variant="outline"
              size="sm"
              className="w-full mt-4"
              onClick={() => setIsOpen(false)}
            >
              Close
            </Button>
          </div>
        )}
      </div>
    </div>
  );
} 
'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  Users,
  Tv,
  Laptop,
  Smartphone,
  Bookmark,
  ThumbsUp,
  MessageCircle,
  Flame,
  Play,
  ArrowRight,
  MonitorPlay,
  Sparkles
} from 'lucide-react';

export default function StreamingFeatures() {
  const [activeFeature, setActiveFeature] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const autoPlayRef = useRef<NodeJS.Timeout | null>(null);
  const [isMobile, setIsMobile] = useState(false);

  const features = [
    {
      id: 'watch-party',
      title: 'Watch Party',
      description: 'Watch movies and shows together with friends in perfect sync, no matter where you are.',
      details: [
        'Chat in real-time while watching',
        'Share reactions with custom emojis',
        'Invite up to 20 friends to join',
        'Works with all StreamVista content'
      ],
      icon: <Users className="h-6 w-6" />,
      image: 'https://images.unsplash.com/photo-1529156069898-49953e39b3ac?q=80&w=1632',
      color: 'from-blue-600/30 to-indigo-600/30',
      link: '/watch-party',
      linkText: 'Try Watch Party',
      secondaryLink: '/features/watch-party',
      secondaryLinkText: 'Learn More'
    },
    {
      id: 'multi-device',
      title: 'Multi-device Streaming',
      description: 'Start watching on your phone and continue seamlessly on your laptop or TV.',
      details: [
        'Automatic progress syncing across devices',
        'Optimized streaming quality for each device',
        'Download content for offline viewing',
        'Smart recommendations based on your device'
      ],
      icon: <MonitorPlay className="h-6 w-6" />,
      image: 'https://images.unsplash.com/photo-1507297230445-ff678f10b524?q=80&w=1471',
      color: 'from-emerald-600/30 to-teal-600/30',
      link: '/features/multi-device',
      linkText: 'Try It Now',
      secondaryLink: '/features',
      secondaryLinkText: 'View All Features'
    },
    {
      id: 'personalized',
      title: 'Personalized Recommendations',
      description: 'Discover new content tailored to your viewing history and preferences.',
      details: [
        'AI-powered content discovery',
        'Weekly personalized watchlists',
        'Similar content suggestions',
        'Trending among users with similar taste'
      ],
      icon: <Sparkles className="h-6 w-6" />,
      image: 'https://images.unsplash.com/photo-1542204165-65bf26472b9b?q=80&w=1374',
      color: 'from-purple-600/30 to-pink-600/30',
      link: '/recommendations',
      linkText: 'View Recommendations',
      secondaryLink: '/features/recommendations',
      secondaryLinkText: 'How It Works'
    },
    {
      id: 'watchlist',
      title: 'My List',
      description: 'Save movies and shows to watch later with our convenient watchlist feature.',
      details: [
        'Create multiple custom lists',
        'Sort and filter by genre, length, or mood',
        'Get notified when new content is added',
        'Share lists with friends and family'
      ],
      icon: <Bookmark className="h-6 w-6" />,
      image: 'https://images.unsplash.com/photo-1553775282-20af80779df7?q=80&w=1470',
      color: 'from-amber-600/30 to-orange-600/30',
      link: '/my-list',
      linkText: 'Go to My List',
      secondaryLink: '/features/my-list',
      secondaryLinkText: 'Manage Lists'
    }
  ];
  
  // Check for mobile on mount
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(
        window.innerWidth < 768 || 
        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
      );
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Auto-rotate features - disabled on mobile
  useEffect(() => {
    // Only auto-rotate on desktop
    if (isAutoPlaying && !isMobile) {
      autoPlayRef.current = setInterval(() => {
        setActiveFeature(prev => (prev + 1) % features.length);
      }, 10000);
    }

    return () => {
      if (autoPlayRef.current) {
        clearInterval(autoPlayRef.current);
      }
    };
  }, [isAutoPlaying, features.length, isMobile]);

  // Pause auto-rotation when user interacts with the component
  const handleFeatureClick = (index: number) => {
    // Clear existing interval
    if (autoPlayRef.current) {
      clearInterval(autoPlayRef.current);
      setIsAutoPlaying(false);
    }

    // Set the active feature
    setActiveFeature(index);

    // Restart auto-rotation after 15 seconds of inactivity
    setTimeout(() => {
      setIsAutoPlaying(true);
    }, 15000);
  };

  const currentFeature = features[activeFeature];

  return (
    <section className="py-16 relative overflow-hidden bg-gradient-to-b from-vista-dark to-vista-dark-lighter">
      {/* Animated background elements - disabled on mobile */}
      {!isMobile && (
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-0 left-0 w-full h-full opacity-30">
            <div className="absolute top-10 left-10 w-72 h-72 bg-vista-blue/10 rounded-full filter blur-3xl animate-blob"></div>
            <div className="absolute top-40 right-10 w-72 h-72 bg-purple-700/10 rounded-full filter blur-3xl animate-blob animation-delay-2000"></div>
            <div className="absolute bottom-10 left-1/3 w-72 h-72 bg-emerald-700/10 rounded-full filter blur-3xl animate-blob animation-delay-4000"></div>
          </div>
        </div>
      )}

      <div className="container mx-auto px-4 relative z-10">
        {/* Title section */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-vista-light mb-4">
            StreamVista Features
          </h2>
          <p className="text-vista-light/70 max-w-2xl mx-auto">
            Discover what makes StreamVista the perfect platform for your entertainment needs
          </p>
        </div>

        {/* Feature Navigation */}
        <div className="flex flex-wrap justify-center gap-3 md:gap-4 mb-12">
          {features.map((feature, index) => (
            <button
              key={feature.id}
              onClick={() => handleFeatureClick(index)}
              className={`px-5 py-3 rounded-full flex items-center gap-2 transition-all duration-300 ${
                activeFeature === index
                  ? 'bg-vista-blue text-white shadow-lg shadow-vista-blue/20'
                  : 'bg-vista-dark-lighter/80 text-vista-light/70 hover:text-vista-light hover:bg-vista-dark-lighter'
              }`}
            >
              <span className="relative">
                {feature.icon}
                {activeFeature === index && (
                  <span className="absolute -top-1 -right-1 w-2 h-2 bg-white rounded-full" />
                )}
              </span>
              <span className="hidden md:inline font-medium">{feature.title}</span>
            </button>
          ))}
        </div>

        {/* Feature Showcase - simplified for mobile */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          {/* Feature Content */}
          <div className="order-2 lg:order-1">
            <div className="bg-vista-dark-lighter/80 backdrop-blur-sm p-6 md:p-8 rounded-2xl shadow-xl border border-vista-blue/10 overflow-hidden relative">
              {/* Feature header */}
              <div className="flex items-center gap-4 mb-6">
                <div className={`p-4 ${currentFeature.color.split(' ')[0].replace('from-', 'bg-')} rounded-full shadow-inner`}>
                  {currentFeature.icon}
                </div>
                <h3 className="text-2xl md:text-3xl font-bold text-vista-light">
                  {currentFeature.title}
                </h3>
              </div>

              {/* Feature description */}
              <p className="text-vista-light/90 text-lg mb-6 leading-relaxed">
                {currentFeature.description}
              </p>

              {/* Key features */}
              <div className="bg-black/20 backdrop-blur-sm rounded-xl p-4 mb-6 border border-white/5">
                <h4 className="text-vista-light/80 text-sm uppercase tracking-wider mb-3 font-medium">Key Features</h4>
                <ul className="space-y-2">
                  {currentFeature.details.map((detail, idx) => (
                    <li key={idx} className="flex items-start gap-2 text-vista-light/90">
                      <div className="mt-1 text-vista-blue">
                        <div className="h-2 w-2 rounded-full bg-vista-blue"></div>
                      </div>
                      <span className="text-sm">{detail}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Action buttons */}
              <div className="flex flex-wrap gap-4">
                <Link href={currentFeature.link}>
                  <Button className="bg-vista-blue hover:bg-vista-blue/90 gap-2 shadow-lg shadow-vista-blue/20">
                    {currentFeature.linkText}
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                </Link>

                <Link href={currentFeature.secondaryLink}>
                  <Button variant="outline" className="border-vista-light/20 text-vista-light hover:bg-white/10 gap-2">
                    {currentFeature.secondaryLinkText}
                  </Button>
                </Link>
              </div>
            </div>
          </div>

          {/* Feature Image */}
          <div className="order-1 lg:order-2">
            <div className="relative h-[300px] md:h-[400px] rounded-2xl overflow-hidden shadow-2xl border border-vista-blue/10">
              <Image
                src={currentFeature.image}
                alt={currentFeature.title}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, 50vw"
                priority
              />
              <div className="absolute inset-0 bg-gradient-to-t from-vista-dark/90 via-vista-dark/30 to-transparent" />

              {/* Content overlay with visual elements */}
              <div className="absolute inset-0 flex flex-col justify-between p-6">
                {/* Top section with badge */}
                <div className="flex justify-end">
                  <div className="bg-vista-blue/80 text-white px-3 py-1.5 rounded-full text-xs font-medium shadow-lg backdrop-blur-sm">
                    {currentFeature.id === 'watch-party' && 'Live Now'}
                    {currentFeature.id === 'multi-device' && 'All Devices'}
                    {currentFeature.id === 'personalized' && 'AI Powered'}
                    {currentFeature.id === 'watchlist' && 'New Feature'}
                  </div>
                </div>

                {/* Bottom section with feature info */}
                <div className="space-y-4">
                  {/* Feature badge */}
                  <div className="bg-vista-blue/90 text-white px-5 py-2.5 rounded-full text-sm font-medium shadow-lg backdrop-blur-sm flex items-center gap-2 w-fit">
                    {currentFeature.icon}
                    <span>{currentFeature.title}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Feature dots navigation */}
        <div className="flex justify-center mt-10 gap-3">
          {features.map((feature, index) => (
            <button
              key={`dot-${index}`}
              onClick={() => handleFeatureClick(index)}
              className="group relative py-4 px-2"
              aria-label={`View feature ${index + 1}`}
            >
              <div className="relative">
                <div 
                  className={`h-3 rounded-full transition-all duration-500 ${activeFeature === index ? 'bg-vista-blue shadow-glow-blue w-10' : 'bg-vista-light/30 group-hover:bg-vista-light/50 w-4'}`}
                />
              </div>
            </button>
          ))}
        </div>
      </div>
    </section>
  );
}

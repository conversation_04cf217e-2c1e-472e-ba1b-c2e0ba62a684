import { NextRequest, NextResponse } from 'next/server';

// Simple in-memory cache to reduce API calls to external services
interface CacheEntry {
  imageUrl: string | null;
  timestamp: number;
}

const imageCache = new Map<string, CacheEntry>();
const CACHE_TTL = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

/**
 * API endpoint to search for actor/director images from WikiData
 * This serves as a fallback when TMDb doesn't have profile images
 */
export async function GET(request: NextRequest) {
  try {
    // Get parameters from the query
    const searchParams = request.nextUrl.searchParams;
    const name = searchParams.get('name');
    const path = searchParams.get('path');
    const size = searchParams.get('size') || 'w342';
    const forceRefresh = searchParams.get('refresh') === 'true';

    // Handle TMDb path parameter directly (for cast/crew images)
    if (path) {
      console.log(`[API] wiki-image called with path parameter: ${path}, size: ${size}`);

      // Construct TMDb image URL
      let imageUrl = '';
      if (path.startsWith('http')) {
        // Already a full URL
        imageUrl = path;
      } else {
        // TMDb path format
        imageUrl = `https://image.tmdb.org/t/p/${size}${path.startsWith('/') ? path : `/${path}`}`;
      }

      // Set CORS headers
      const response = NextResponse.json({
        path,
        imageUrl,
        source: 'tmdb'
      });

      response.headers.set('Access-Control-Allow-Origin', '*');
      response.headers.set('Access-Control-Allow-Methods', 'GET');
      response.headers.set('Access-Control-Allow-Headers', 'Content-Type');
      response.headers.set('Cache-Control', 'public, max-age=86400');

      return response;
    }

    // Handle name parameter for wiki searches
    if (!name) {
      console.warn('[API] wiki-image called without name or path parameter');
      return NextResponse.json({ error: 'Either name or path parameter is required' }, { status: 400 });
    }

    console.log(`[API] wiki-image called for name: "${name}", force refresh: ${forceRefresh}`);

    // Check cache first (unless force refresh is requested)
    const cacheKey = `wiki-image-${name.toLowerCase()}`;
    const now = Date.now();
    const cachedEntry = imageCache.get(cacheKey);

    if (!forceRefresh && cachedEntry && (now - cachedEntry.timestamp < CACHE_TTL)) {
      console.log(`[API] Using cached wiki image for "${name}": ${cachedEntry.imageUrl ? 'Found' : 'Not found'}`);

      // Set CORS headers
      const response = NextResponse.json({
        name,
        imageUrl: cachedEntry.imageUrl,
        source: cachedEntry.imageUrl ? (cachedEntry.imageUrl.includes('wikipedia.org') ? 'wikipedia' : 'wikidata') : null,
        fromCache: true
      });

      response.headers.set('Access-Control-Allow-Origin', '*');
      response.headers.set('Access-Control-Allow-Methods', 'GET');
      response.headers.set('Access-Control-Allow-Headers', 'Content-Type');
      response.headers.set('Cache-Control', 'public, max-age=86400');

      return response;
    }

    // First try a direct search on Wikipedia REST API
    console.log(`[API] Searching Wikipedia for "${name}"`);
    const wikiImageUrl = await findWikipediaImage(name);

    if (wikiImageUrl) {
      console.log(`[API] Found Wikipedia image for "${name}"`);

      // Cache the result
      imageCache.set(cacheKey, { imageUrl: wikiImageUrl, timestamp: now });

      // Set CORS headers
      const response = NextResponse.json({
        name,
        imageUrl: wikiImageUrl,
        source: 'wikipedia'
      });

      response.headers.set('Access-Control-Allow-Origin', '*');
      response.headers.set('Access-Control-Allow-Methods', 'GET');
      response.headers.set('Access-Control-Allow-Headers', 'Content-Type');
      response.headers.set('Cache-Control', 'public, max-age=86400');

      return response;
    }

    // Fall back to WikiData if Wikipedia direct search failed
    console.log(`[API] No Wikipedia image found for "${name}", trying WikiData`);
    const wikiDataImageUrl = await findWikiDataImage(name);

    // Cache the result (even if null)
    imageCache.set(cacheKey, { imageUrl: wikiDataImageUrl, timestamp: now });

    console.log(`[API] WikiData search result for "${name}": ${wikiDataImageUrl ? 'Found image' : 'No image found'}`);

    // Set CORS headers
    const response = NextResponse.json({
      name,
      imageUrl: wikiDataImageUrl,
      source: wikiDataImageUrl ? 'wikidata' : null
    });

    response.headers.set('Access-Control-Allow-Origin', '*');
    response.headers.set('Access-Control-Allow-Methods', 'GET');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type');
    response.headers.set('Cache-Control', 'public, max-age=86400');

    return response;
  } catch (error) {
    console.error('[API] Error in wiki-image API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch wiki image', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

/**
 * Function to search for a person's image on Wikipedia directly
 * This is more reliable than WikiData for well-known actors
 */
async function findWikipediaImage(name: string): Promise<string | null> {
  try {
    // Use Wikipedia's REST API to get summary info including image
    const formattedName = name.replace(/\s+/g, '_');
    const url = `https://en.wikipedia.org/api/rest_v1/page/summary/${encodeURIComponent(formattedName)}`;

    console.log(`[API] Fetching from Wikipedia: ${url}`);

    const response = await fetch(url, {
      headers: {
        'User-Agent': 'StreamVista/1.0 (Movie App for educational purposes)',
        'Accept': 'application/json'
      },
      // Add a timeout to prevent hanging requests
      signal: AbortSignal.timeout(5000)
    });

    if (!response.ok) {
      console.warn(`[API] Wikipedia API returned ${response.status} for "${name}"`);
      return null;
    }

    const data = await response.json();
    console.log(`[API] Wikipedia data for "${name}": has thumbnail: ${!!data.thumbnail}, has original: ${!!data.originalimage}`);

    // Check if we have an image
    if (data.thumbnail && data.thumbnail.source) {
      // Get higher resolution by modifying URL (400px is a good balance of quality and size)
      const highResImage = data.thumbnail.source.replace(/\d+px/, '400px');
      return highResImage;
    }

    // If original image exists, use that (usually higher quality)
    if (data.originalimage && data.originalimage.source) {
      return data.originalimage.source;
    }

    return null;
  } catch (error) {
    console.error(`[API] Error searching Wikipedia for "${name}":`, error);
    return null;
  }
}

/**
 * Function to search for a person's image on WikiData
 */
async function findWikiDataImage(name: string): Promise<string | null> {
  try {
    // First, search for the entity using the WikiData API with proper fetch settings
    const searchUrl = `https://www.wikidata.org/w/api.php?action=wbsearchentities&search=${encodeURIComponent(name)}&language=en&format=json&origin=*`;

    console.log(`[API] Searching WikiData for "${name}"`);

    const searchResponse = await fetch(searchUrl, {
      headers: {
        'User-Agent': 'StreamVista/1.0 (Movie App for educational purposes)',
        'Accept': 'application/json'
      },
      // Add a timeout to prevent hanging requests
      signal: AbortSignal.timeout(5000)
    });

    if (!searchResponse.ok) {
      console.warn(`[API] WikiData search API returned ${searchResponse.status} for "${name}"`);
      return null;
    }

    const searchData = await searchResponse.json();

    // No results found
    if (!searchData.search || searchData.search.length === 0) {
      console.log(`[API] No WikiData entities found for "${name}"`);
      return null;
    }

    // Get the first result - most likely match
    const entityId = searchData.search[0].id;
    console.log(`[API] Found WikiData entity for "${name}": ${entityId}`);

    // Fetch the entity data to get the image
    const entityUrl = `https://www.wikidata.org/w/api.php?action=wbgetentities&ids=${entityId}&props=claims&format=json&origin=*`;

    const entityResponse = await fetch(entityUrl, {
      headers: {
        'User-Agent': 'StreamVista/1.0 (Movie App for educational purposes)',
        'Accept': 'application/json'
      },
      // Add a timeout to prevent hanging requests
      signal: AbortSignal.timeout(5000)
    });

    if (!entityResponse.ok) {
      console.warn(`[API] WikiData entity API returned ${entityResponse.status} for "${name}" (${entityId})`);
      return null;
    }

    const entityData = await entityResponse.json();

    // Check if the entity has an image property (P18)
    const entity = entityData.entities[entityId];
    if (!entity || !entity.claims || !entity.claims.P18) {
      console.log(`[API] No image property found for "${name}" (${entityId})`);
      return null;
    }

    // Get the image filename
    const imageFilename = entity.claims.P18[0].mainsnak.datavalue.value;
    console.log(`[API] Found image filename for "${name}": ${imageFilename}`);

    // Convert directly to Wikimedia Commons URL using MD5 hash
    // Note: This is a simplified way to directly access Commons images
    const filename = imageFilename.replace(/ /g, '_');
    const md5Hash = await stringToMD5(filename);
    const md5prefix = md5Hash.substring(0, 2);

    const directUrl = `https://upload.wikimedia.org/wikipedia/commons/${md5prefix.charAt(0)}/${md5prefix}/${encodeURIComponent(filename)}`;
    console.log(`[API] Generated Wikimedia Commons URL for "${name}": ${directUrl}`);

    return directUrl;
  } catch (error) {
    console.error(`[API] Error searching WikiData for "${name}":`, error);
    return null;
  }
}

/**
 * Calculate MD5 hash of a string using Web Crypto API
 * This is used to construct direct Wikimedia Commons URLs
 */
async function stringToMD5(input: string): Promise<string> {
  try {
    // Use TextEncoder to convert string to Uint8Array
    const textEncoder = new TextEncoder();
    const data = textEncoder.encode(input);

    // Use Web Crypto API to calculate MD5 hash
    const hashBuffer = await crypto.subtle.digest('MD5', data);

    // Convert buffer to hex string
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');

    return hashHex;
  } catch (error) {
    console.error(`[API] Error calculating MD5 hash:`, error);
    // Fallback - just use first letters as prefix if crypto fails
    // Not accurate but better than nothing
    return input.substring(0, 4);
  }
}
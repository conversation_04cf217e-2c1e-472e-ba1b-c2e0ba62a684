import { NextRequest, NextResponse } from 'next/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { profileId: string } }
) {
  try {
    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // --- Custom Authentication using userId cookie ---
    const userId = request.cookies.get('userId')?.value;

    console.log('userId cookie in API:', userId);

    if (!userId || !mongoose.default.isValidObjectId(userId)) {
      console.log('Unauthorized: No valid userId cookie found');
      return NextResponse.json({ error: 'Unauthorized - Invalid or missing user identifier' }, { status: 401 });
    }
    // --- End Custom Authentication ---

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const UserSchema = new mongoose.default.Schema({
      role: String
    });

    const User = mongoose.default.models.User ||
                mongoose.default.model('User', UserSchema);

    // Define the Profile schema directly
    const ProfileSchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      name: String,
      avatar: String,
      isKids: Boolean,
      isPrimary: Boolean,
      preferences: mongoose.default.Schema.Types.Mixed,
      favoriteGenres: [String],
      watchHistory: [mongoose.default.Schema.Types.Mixed],
      myList: [mongoose.default.Schema.Types.Mixed]
    }, {
      timestamps: true
    });

    // Get the Profile model
    const Profile = mongoose.default.models.Profile ||
                   mongoose.default.model('Profile', ProfileSchema);

    // Await params before destructuring
    const resolvedParams = await params;
    const { profileId } = resolvedParams;

    // Validate profile ID
    if (!profileId || !mongoose.default.isValidObjectId(profileId)) {
      return NextResponse.json({ error: 'Invalid profile ID' }, { status: 400 });
    }

    // --- Optional: Verify userId exists in DB ---
    const requestingUser = await User.findById(userId).select('role').lean(); // Fetch only role
    if (!requestingUser) {
      console.log('Unauthorized: User ID from cookie not found in DB');
      // Clear the invalid cookie? Could be done here or on client-side upon 401.
      return NextResponse.json({ error: 'Unauthorized - User not found' }, { status: 401 });
    }
    console.log('Requesting user found:', { userId: userId, role: (requestingUser as any).role });
    const isAdmin = (requestingUser as any).role === 'admin' || (requestingUser as any).role === 'superadmin';
    // --- End Optional Verification ---

    // Fetch the profile using profileId
    const profile = await Profile.findById(profileId);

    if (!profile) {
      console.log('Profile not found with ID:', profileId);
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 });
    }

    // --- Authorization Check (Example: Ensure user owns the profile or is admin) ---
    // Uncomment and adapt if needed:
    // if (profile.userId.toString() !== userId && !isAdmin) {
    //   console.log(`Authorization failed: User ${userId} tried to access profile ${profileId} owned by ${profile.userId}`);
    //   return NextResponse.json({ error: 'Forbidden - You do not own this profile' }, { status: 403 });
    // }
    // --- End Authorization Check ---

    console.log('Profile found:', {
      id: profile._id.toString(),
      createdAt: profile.createdAt,
      userId: profile.userId?.toString()
    });

    // Format dates to ensure they're properly serialized
    const createdDate = profile.createdAt ? new Date(profile.createdAt).toISOString() : null;
    const updatedDate = profile.updatedAt ? new Date(profile.updatedAt).toISOString() : null;

    // Return profile details
    return NextResponse.json({
      id: profile._id.toString(),
      name: profile.name,
      avatar: profile.avatar,
      isKids: profile.isKids,
      isPrimary: profile.isPrimary,
      createdAt: createdDate,
      updatedAt: updatedDate,
      preferences: profile.preferences,
      favoriteGenres: profile.favoriteGenres,
      watchHistoryCount: profile.watchHistory?.length || 0,
      myListCount: profile.myList?.length || 0,
      isAdmin: isAdmin // Use isAdmin derived from the verified user
    });
  } catch (error) {
    console.error('Error fetching profile details:', error);
    return NextResponse.json(
      { error: 'Failed to fetch profile details', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

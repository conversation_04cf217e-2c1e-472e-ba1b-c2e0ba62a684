'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
  Download, Pause, Play, X, ChevronDown, ChevronUp, Trash2,
  CheckCircle, RefreshCw, Clock, AlertCircle, Filter,
  Grid2X2, List as ListIcon, ArrowDownToLine, HardDrive,
  Smartphone, SortDesc, Wifi
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import SkeletonLoader from '@/components/SkeletonLoader';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import {
  DownloadItem, DownloadStats,
  getDownloads, saveDownloads, updateDownloadProgress,
  updateDownloadStatus, deleteDownload, generateSampleDownloads,
  getDownloadStats, QUALITY_OPTIONS, getEstimatedSize
} from '@/lib/download-utils';
import { useToastHelpers } from '@/lib/ToastContext';

export default function DownloadManager() {
  const [loading, setLoading] = useState(true);
  const [downloads, setDownloads] = useState<DownloadItem[]>([]);
  const [stats, setStats] = useState<DownloadStats>({
    totalDownloads: 0,
    completedDownloads: 0,
    activeDownloads: 0,
    pausedDownloads: 0,
    totalSpaceUsed: '0 GB',
    availableSpace: '64 GB'
  });
  const [viewType, setViewType] = useState<'grid' | 'list'>('list');
  const [sortBy, setSortBy] = useState<'date' | 'name' | 'size'>('date');
  const [filterStatus, setFilterStatus] = useState<'all' | 'completed' | 'active' | 'paused'>('all');
  const [selectedDownload, setSelectedDownload] = useState<DownloadItem | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const toast = useToastHelpers();

  // Load downloads from localStorage (or generate sample data if empty)
  useEffect(() => {
    setTimeout(() => {
      let loadedDownloads = getDownloads();

      // If no downloads exist, generate sample data for demonstration
      if (loadedDownloads.length === 0) {
        loadedDownloads = generateSampleDownloads();
        saveDownloads(loadedDownloads);
      }

      setDownloads(loadedDownloads);
      setStats(getDownloadStats(loadedDownloads));
      setLoading(false);
    }, 500);
  }, []);

  // Simulate download progress for active downloads
  useEffect(() => {
    if (loading) return;

    const intervalId = setInterval(() => {
      setDownloads((prevDownloads) => {
        const updatedDownloads = prevDownloads.map((download) => {
          if (download.status === 'downloading') {
            const newProgress = Math.min(download.progress + 5, 100);
            if (newProgress === 100) {
              toast.success(
                'Download Complete',
                `"${download.title}" is ready to watch offline`
              );
              return { ...download, status: 'completed' as const, progress: 100 };
            }
            return { ...download, progress: newProgress };
          }
          return download;
        });
        return updatedDownloads;
      });
    }, 1000);

    return () => clearInterval(intervalId);
  }, [downloads, loading, toast]);

  // Handle download status change
  const handleStatusChange = (id: string, status: DownloadItem['status']) => {
    setDownloads((prevDownloads) => {
      const updatedDownloads = prevDownloads.map((download) =>
        download.id === id ? { ...download, status } : download
      );
      return updatedDownloads;
    });

    const download = downloads.find((d) => d.id === id);
    if (download) {
      if (status === 'downloading') {
        toast.info(
          'Download Resumed',
          `"${download.title}" is downloading again`
        );
      } else if (status === 'paused') {
        toast.info(
          'Download Paused',
          `"${download.title}" has been paused`
        );
      }
    }
  };

  // Handle download deletion
  const handleDelete = () => {
    if (selectedDownload) {
      setDownloads((prevDownloads) =>
        prevDownloads.filter((download) => download.id !== selectedDownload.id)
      );

      // Show toast notification
      toast.success(
        'Download Deleted',
        `"${selectedDownload.title}" has been removed`
      );

      setSelectedDownload(null);
      setIsDeleteDialogOpen(false);
    }
  };

  // Filter downloads based on the selected status
  const filteredDownloads = downloads.filter(download => {
    if (filterStatus === 'all') return true;
    if (filterStatus === 'completed') return download.status === 'completed';
    if (filterStatus === 'active') return download.status === 'downloading';
    if (filterStatus === 'paused') return download.status === 'paused';
    return true;
  });

  // Sort filtered downloads
  const sortedDownloads = [...filteredDownloads].sort((a, b) => {
    if (sortBy === 'name') {
      return a.title.localeCompare(b.title);
    } else if (sortBy === 'size') {
      // Parse the size to bytes for accurate comparison
      const sizeA = parseFloat(a.size.split(' ')[0]);
      const sizeB = parseFloat(b.size.split(' ')[0]);
      // Check if units are different (MB vs GB)
      const unitA = a.size.split(' ')[1];
      const unitB = b.size.split(' ')[1];
      if (unitA === unitB) {
        return sizeB - sizeA; // Larger size first
      }
      // Different units - convert
      if (unitA === 'GB' && unitB === 'MB') return -1;
      if (unitA === 'MB' && unitB === 'GB') return 1;
      return 0;
    } else {
      // Sort by date (most recent first)
      return new Date(b.addedDate).getTime() - new Date(a.addedDate).getTime();
    }
  });

  // Get status icon based on download status
  const getStatusIcon = (status: DownloadItem['status']) => {
    switch (status) {
      case 'downloading':
        return <Download className="w-4 h-4 text-vista-blue animate-pulse" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'paused':
        return <Pause className="w-4 h-4 text-yellow-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return null;
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Display download size with appropriate suffix
  const displaySize = (size: string) => {
    return size;
  };

  // Empty state component
  const EmptyState = () => (
    <div className="flex flex-col items-center justify-center py-12 px-4 text-center">
      <div className="bg-vista-dark-lighter rounded-full p-5 mb-4">
        <Download className="h-10 w-10 text-vista-blue/70" />
      </div>
      <h3 className="text-xl font-medium text-vista-light mb-2">No downloads available</h3>
      <p className="text-vista-light/70 max-w-sm mb-6">
        Download your favorite shows and movies to watch them offline anytime, anywhere.
      </p>
      <Link href="/">
        <Button className="bg-vista-blue hover:bg-vista-blue/90 text-white rounded-full px-6">
          Browse Content
        </Button>
      </Link>
    </div>
  );

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-semibold text-vista-light">Downloads</h2>
        </div>
        <SkeletonLoader count={4} layout="grid" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header section */}
      <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-6 gap-4">
        <div>
          <h2 className="text-2xl md:text-3xl font-semibold text-vista-light">Downloads</h2>
          <p className="text-vista-light/70">Watch your favorite shows and movies offline</p>
        </div>

        {/* Controls */}
        {downloads.length > 0 && (
          <div className="flex flex-wrap gap-2 items-center">
            <div className="flex items-center border border-vista-light/20 rounded-md overflow-hidden">
              <button
                onClick={() => setViewType('grid')}
                className={`p-2 ${viewType === 'grid' ? 'bg-vista-blue text-white' : 'text-vista-light/70 hover:bg-vista-light/10'}`}
                aria-label="Grid view"
              >
                <Grid2X2 className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewType('list')}
                className={`p-2 ${viewType === 'list' ? 'bg-vista-blue text-white' : 'text-vista-light/70 hover:bg-vista-light/10'}`}
                aria-label="List view"
              >
                <ListIcon className="h-4 w-4" />
              </button>
            </div>

            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-vista-light/70" />
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value as 'all' | 'completed' | 'active' | 'paused')}
                className="bg-vista-dark border border-vista-light/20 rounded p-1.5 text-vista-light text-sm"
              >
                <option value="all">All Downloads</option>
                <option value="active">Downloading</option>
                <option value="completed">Completed</option>
                <option value="paused">Paused</option>
              </select>
            </div>

            <div className="flex items-center gap-2">
              <SortDesc className="h-4 w-4 text-vista-light/70" />
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as 'date' | 'name' | 'size')}
                className="bg-vista-dark border border-vista-light/20 rounded p-1.5 text-vista-light text-sm"
              >
                <option value="date">Recently Added</option>
                <option value="name">Name</option>
                <option value="size">Size</option>
              </select>
            </div>
          </div>
        )}
      </div>

      {/* Stats Panel */}
      {downloads.length > 0 && (
        <div className="bg-vista-dark-lighter rounded-lg p-4 mb-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
            <div className="p-3">
              <div className="flex items-center text-vista-light/70 mb-1">
                <HardDrive className="w-4 h-4 mr-2" />
                <span className="text-sm">Storage</span>
              </div>
              <div className="flex justify-between items-center">
                <div>
                  <span className="text-lg font-medium text-vista-light">{stats.totalSpaceUsed}</span>
                  <span className="text-vista-light/50 text-sm ml-1">used</span>
                </div>
                <div className="text-sm text-vista-light/50">
                  {stats.availableSpace} available
                </div>
              </div>
              <div className="w-full bg-vista-dark h-2 rounded-full mt-2 overflow-hidden">
                <div
                  className="bg-vista-blue h-full rounded-full"
                  style={{ width: '15%' }}
                />
              </div>
            </div>

            <div className="p-3 flex flex-col">
              <div className="flex items-center text-vista-light/70 mb-1">
                <Download className="w-4 h-4 mr-2" />
                <span className="text-sm">Total Downloads</span>
              </div>
              <span className="text-lg font-medium text-vista-light">{stats.totalDownloads}</span>
              <span className="text-vista-light/50 text-sm mt-1">
                {stats.completedDownloads} completed
              </span>
            </div>

            <div className="p-3 flex flex-col">
              <div className="flex items-center text-vista-light/70 mb-1">
                <ArrowDownToLine className="w-4 h-4 mr-2" />
                <span className="text-sm">Downloading</span>
              </div>
              <span className="text-lg font-medium text-vista-light">{stats.activeDownloads}</span>
              <span className="text-vista-light/50 text-sm mt-1">
                {stats.pausedDownloads} paused
              </span>
            </div>

            <div className="p-3 flex flex-col">
              <div className="flex items-center text-vista-light/70 mb-1">
                <Wifi className="w-4 h-4 mr-2" />
                <span className="text-sm">Download Settings</span>
              </div>
              <div className="flex items-center">
                <Badge className="bg-vista-blue/20 text-vista-blue font-normal text-xs">Wi-Fi Only</Badge>
                <Badge className="bg-vista-dark text-vista-light/80 font-normal text-xs ml-2">Standard Quality</Badge>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="text-vista-blue hover:bg-vista-blue/10 text-xs mt-1 h-6 p-0"
              >
                Change settings
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Main content */}
      {downloads.length === 0 ? (
        <EmptyState />
      ) : (
        <>
          {filteredDownloads.length === 0 ? (
            <div className="text-center py-10">
              <h3 className="text-vista-light text-lg">No {filterStatus} downloads found</h3>
              <p className="text-vista-light/70 mt-2">
                Change the filter to see other downloads
              </p>
            </div>
          ) : viewType === 'grid' ? (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
              {sortedDownloads.map((download) => (
                <div
                  key={download.id}
                  className="bg-vista-dark-lighter rounded-lg overflow-hidden hover:bg-vista-dark-lighter/80 transition-colors"
                >
                  <div className="relative aspect-[2/3]">
                    <Image
                      src={download.image}
                      alt={download.title}
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent"></div>

                    {/* Status indicator */}
                    <div className="absolute top-2 right-2">
                      <Badge
                        className={`
                          ${download.status === 'downloading' ? 'bg-vista-blue text-white' : ''}
                          ${download.status === 'completed' ? 'bg-green-500/20 text-green-400' : ''}
                          ${download.status === 'paused' ? 'bg-yellow-500/20 text-yellow-400' : ''}
                          ${download.status === 'error' ? 'bg-red-500/20 text-red-400' : ''}
                        `}
                      >
                        {download.status === 'downloading' && 'Downloading'}
                        {download.status === 'completed' && 'Downloaded'}
                        {download.status === 'paused' && 'Paused'}
                        {download.status === 'error' && 'Failed'}
                      </Badge>
                    </div>

                    {/* Progress bar (for downloading/paused) */}
                    {(download.status === 'downloading' || download.status === 'paused') && (
                      <div className="absolute bottom-0 left-0 right-0 h-1 bg-black/50">
                        <div
                          className={`h-full ${download.status === 'downloading' ? 'bg-vista-blue' : 'bg-yellow-500'}`}
                          style={{ width: `${download.progress}%` }}
                        ></div>
                      </div>
                    )}

                    {/* Info overlay */}
                    <div className="absolute bottom-0 left-0 right-0 p-3">
                      <div className="flex justify-between items-center">
                        <div>
                          <span className="text-xs font-medium text-vista-light/80">{download.quality}</span>
                          <span className="text-vista-light/60 text-xs ml-2">{download.size}</span>
                        </div>
                        <div className="flex space-x-1">
                          {download.status === 'downloading' && (
                            <button
                              onClick={() => handleStatusChange(download.id, 'paused')}
                              className="p-1.5 bg-black/40 rounded-full hover:bg-black/60 transition-colors"
                              aria-label="Pause download"
                            >
                              <Pause className="w-3 h-3 text-white" />
                            </button>
                          )}
                          {download.status === 'paused' && (
                            <button
                              onClick={() => handleStatusChange(download.id, 'downloading')}
                              className="p-1.5 bg-black/40 rounded-full hover:bg-black/60 transition-colors"
                              aria-label="Resume download"
                            >
                              <Play className="w-3 h-3 text-white" />
                            </button>
                          )}
                          <button
                            onClick={() => {
                              setSelectedDownload(download);
                              setIsDeleteDialogOpen(true);
                            }}
                            className="p-1.5 bg-black/40 rounded-full hover:bg-black/60 transition-colors"
                            aria-label="Delete download"
                          >
                            <Trash2 className="w-3 h-3 text-white" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="p-3">
                    <Link href={`/watch/${download.contentId}`}>
                      <h3 className="text-sm font-medium text-vista-light line-clamp-2 hover:text-vista-blue transition-colors">
                        {download.title}
                      </h3>
                    </Link>
                    {download.type === 'show' && download.season && download.episodeTitle && (
                      <p className="text-xs text-vista-light/70 mt-1">
                        {download.episodeTitle}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-3">
              {sortedDownloads.map((download) => (
                <div
                  key={download.id}
                  className="flex gap-4 bg-vista-dark-lighter rounded-lg p-3 hover:bg-vista-dark-lighter/80 transition-colors"
                >
                  <Link href={`/watch/${download.contentId}`} className="flex-shrink-0">
                    <div className="relative h-24 w-16 md:h-28 md:w-20 rounded overflow-hidden">
                      <Image
                        src={download.image}
                        alt={download.title}
                        fill
                        className="object-cover"
                      />
                    </div>
                  </Link>

                  <div className="flex-1 min-w-0">
                    <div className="flex flex-col h-full justify-between">
                      <div>
                        <div className="flex items-start justify-between">
                          <div>
                            <Link href={`/watch/${download.contentId}`}>
                              <h3 className="text-vista-light font-medium hover:text-vista-blue transition-colors truncate">
                                {download.title}
                              </h3>
                            </Link>
                            {download.type === 'show' && download.season && download.episode && (
                              <p className="text-sm text-vista-light/70">
                                Season {download.season}, Episode {download.episode}
                                {download.episodeTitle && `: ${download.episodeTitle}`}
                              </p>
                            )}
                          </div>

                          <div className="flex items-center ml-4">
                            <Badge
                              className={`
                                ${download.status === 'downloading' ? 'bg-vista-blue text-white' : ''}
                                ${download.status === 'completed' ? 'bg-green-500/20 text-green-400' : ''}
                                ${download.status === 'paused' ? 'bg-yellow-500/20 text-yellow-400' : ''}
                                ${download.status === 'error' ? 'bg-red-500/20 text-red-400' : ''}
                              `}
                            >
                              <span className="flex items-center">
                                {getStatusIcon(download.status)}
                                <span className="ml-1.5">
                                  {download.status === 'downloading' && 'Downloading'}
                                  {download.status === 'completed' && 'Downloaded'}
                                  {download.status === 'paused' && 'Paused'}
                                  {download.status === 'error' && 'Failed'}
                                </span>
                              </span>
                            </Badge>
                          </div>
                        </div>

                        <div className="flex flex-wrap items-center text-sm text-vista-light/70 mt-1 gap-x-4">
                          <span>{download.quality}</span>
                          <span>{download.size}</span>
                          <span>
                            {download.status === 'completed'
                              ? `Added ${formatDate(download.addedDate)}`
                              : `${download.progress.toFixed(0)}% complete`
                            }
                          </span>
                          {download.expiresDate && download.status === 'completed' && (
                            <span className="flex items-center">
                              <Clock className="w-3.5 h-3.5 mr-1" />
                              Expires {formatDate(download.expiresDate)}
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Progress bar for downloading or paused items */}
                      {(download.status === 'downloading' || download.status === 'paused') && (
                        <div className="mt-3">
                          <div className="w-full bg-vista-dark h-1.5 rounded-full overflow-hidden">
                            <div
                              className={`h-full ${download.status === 'downloading' ? 'bg-vista-blue' : 'bg-yellow-500'}`}
                              style={{ width: `${download.progress}%` }}
                            ></div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex-shrink-0 flex items-start space-x-1">
                    {download.status === 'downloading' && (
                      <Button
                        size="icon"
                        variant="ghost"
                        onClick={() => handleStatusChange(download.id, 'paused')}
                        className="text-vista-light/70 hover:text-vista-light hover:bg-vista-light/10"
                        aria-label="Pause download"
                      >
                        <Pause className="h-4 w-4" />
                      </Button>
                    )}

                    {download.status === 'paused' && (
                      <Button
                        size="icon"
                        variant="ghost"
                        onClick={() => handleStatusChange(download.id, 'downloading')}
                        className="text-vista-light/70 hover:text-vista-light hover:bg-vista-light/10"
                        aria-label="Resume download"
                      >
                        <Play className="h-4 w-4" />
                      </Button>
                    )}

                    {download.status === 'error' && (
                      <Button
                        size="icon"
                        variant="ghost"
                        onClick={() => handleStatusChange(download.id, 'downloading')}
                        className="text-vista-light/70 hover:text-vista-light hover:bg-vista-light/10"
                        aria-label="Retry download"
                      >
                        <RefreshCw className="h-4 w-4" />
                      </Button>
                    )}

                    <Button
                      size="icon"
                      variant="ghost"
                      onClick={() => {
                        setSelectedDownload(download);
                        setIsDeleteDialogOpen(true);
                      }}
                      className="text-vista-light/70 hover:text-red-400 hover:bg-red-500/10"
                      aria-label="Delete download"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </>
      )}

      {/* Delete confirmation dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="bg-vista-dark-lighter border-vista-light/10 text-vista-light">
          <DialogHeader>
            <DialogTitle>Delete Download</DialogTitle>
            <DialogDescription className="text-vista-light/70">
              Are you sure you want to delete this download? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          {selectedDownload && (
            <div className="flex items-center gap-4 py-4">
              <div className="relative h-20 w-12 flex-shrink-0 rounded overflow-hidden">
                <Image
                  src={selectedDownload.image}
                  alt={selectedDownload.title}
                  fill
                  className="object-cover"
                />
              </div>
              <div>
                <h4 className="font-medium text-vista-light">{selectedDownload.title}</h4>
                <p className="text-sm text-vista-light/70">{selectedDownload.size} • {selectedDownload.quality}</p>
              </div>
            </div>
          )}

          <DialogFooter className="flex space-x-2">
            <Button
              variant="ghost"
              onClick={() => setIsDeleteDialogOpen(false)}
              className="text-vista-light/70 hover:text-vista-light"
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

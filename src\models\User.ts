import mongoose, { Schema, Document, Types } from 'mongoose';
import bcrypt from 'bcryptjs';

export interface IUser extends Document {
  _id: Types.ObjectId;
  name: string;
  email: string;
  password?: string; // Optional for Google auth users
  profileImage?: string;
  picture?: string;
  googleId?: string;
  role: 'user' | 'admin' | 'superadmin';
  status: 'active' | 'inactive' | 'pending' | 'suspended';
  emailVerified?: Date;
  lastLogin?: Date;
  subscription?: string;
  subscriptionStatus?: 'active' | 'canceled' | 'expired' | 'pending';
  subscriptionRenewal?: Date;
  createdAt: Date;
  updatedAt: Date;
  comparePassword(candidatePassword: string): Promise<boolean>;
}

// Define the User schema
const UserSchema = new Schema<IUser>({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: {
    type: String,
    required: function(this: { googleId?: string }) {
      // Password is required only if googleId is not present
      return !this.googleId;
    }
  },
  // The user's profile image should match the primary profile's avatar
  // This is used in the admin panel and other places where the user is displayed
  profileImage: {
    type: String,
    set: function(url: string) {
      // If it's a Cloudinary URL, ensure it has the correct cloud name (lowercase)
      if (url && url.includes('cloudinary.com') && !url.includes('cloudinary.com/streamvista/')) {
        return url.replace(/cloudinary\.com\/([^\/]+)\//, 'cloudinary.com/streamvista/');
      }
      return url;
    }
  },
  // For compatibility with Google auth - should be kept in sync with profileImage
  picture: {
    type: String,
    set: function(url: string) {
      // If it's a Cloudinary URL, ensure it has the correct cloud name (lowercase)
      if (url && url.includes('cloudinary.com') && !url.includes('cloudinary.com/streamvista/')) {
        return url.replace(/cloudinary\.com\/([^\/]+)\//, 'cloudinary.com/streamvista/');
      }
      return url;
    }
  },
  googleId: { type: String }, // For Google authentication
  role: {
    type: String,
    enum: ['user', 'admin', 'superadmin'],
    default: 'user'
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'pending', 'suspended'],
    default: 'active'
  },
  emailVerified: { type: Date },
  lastLogin: { type: Date },
  subscription: { type: String, default: 'Free Plan' },
  subscriptionStatus: {
    type: String,
    enum: ['active', 'canceled', 'expired', 'pending'],
    default: 'active'
  },
  subscriptionRenewal: { type: Date },
}, {
  timestamps: true
});

// Add indexes
UserSchema.index({ role: 1 });
UserSchema.index({ status: 1 });
UserSchema.index({ subscription: 1 });
UserSchema.index({ lastLogin: -1 });
UserSchema.index({ subscriptionStatus: 1 });
UserSchema.index({ lastLogin: 1 });

// Hash password before saving
UserSchema.pre('save', async function(next) {
  // Skip if password is not modified or if it's undefined/null (Google auth)
  if (!this.isModified('password') || !this.password) return next();

  try {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error as Error);
  }
});

// Method to compare password for login
UserSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  try {
    // If the password field doesn't exist (e.g., for Google auth users), return false
    if (!this.password) return false;

    // Compare the candidate password with the stored hash
    return await bcrypt.compare(candidatePassword, this.password);
  } catch (error) {
    console.error('Error comparing passwords:', error);
    return false;
  }
};

// Add a static method to ensure the model has the comparePassword method
UserSchema.statics.hasComparePassword = function() {
  return true;
};

// Define the interface for the User model with static methods
interface IUserModel extends mongoose.Model<IUser> {
  hasComparePassword(): boolean;
}

// Use mongoose.models.User if it exists, otherwise create a new model
const User = (mongoose.models.User as IUserModel) ||
             mongoose.model<IUser, IUserModel>('User', UserSchema);

export default User;
'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import Image from 'next/image';
import {
  ArrowLeft,
  Save,
  Trash2,
  ExternalLink,
  Film,
  Tv,
  Star,
  Calendar,
  Clock,
  Info,
  Tag,
  Layers,
  Target,
  TrendingUp,
  Eye
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

// Form schema validation
const contentFormSchema = z.object({
  title: z.string().min(1, { message: 'Title is required' }),
  type: z.enum(['movie', 'show'], {
    required_error: 'Please select a content type'
  }),
  status: z.enum(['published', 'draft'], {
    required_error: 'Please select a status'
  }),
  tmdbId: z.string().optional(),
  imdbId: z.string().optional(),
  posterPath: z.string().optional(),
  backdropPath: z.string().optional(),
  tagline: z.string().optional(),
  overview: z.string().optional(),
  year: z.string().optional(),
  genres: z.string().array().default([]),
  runtime: z.number().int().positive().optional(),
  rating: z.number().min(0).max(10).optional(),
  featured: z.boolean().default(false),
  trending: z.boolean().default(false),
});

type ContentFormValues = z.infer<typeof contentFormSchema>;

interface ContentDetailProps {
  params: {
    id: string;
  };
}

export default function ContentDetailPage({ params }: ContentDetailProps) {
  const router = useRouter();
  const { user, isAdmin } = useAuth();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Initialize form
  const form = useForm<ContentFormValues>({
    resolver: zodResolver(contentFormSchema),
    defaultValues: {
      title: '',
      type: 'movie',
      status: 'draft',
      genres: [],
      featured: false,
      trending: false,
    },
  });

  // Fetch content details
  useEffect(() => {
    // Redirect if not admin
    if (user && !isAdmin()) {
      router.push('/');
      return;
    }

    async function fetchContentDetails() {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/admin/content/${params.id}`);

        if (!response.ok) {
          throw new Error(`Failed to fetch content details (${response.status})`);
        }

        const contentData = await response.json();

        // Populate form with content data
        form.reset({
          title: contentData.title,
          type: contentData.type,
          status: contentData.status,
          tmdbId: contentData.tmdbId,
          imdbId: contentData.imdbId || undefined,
          posterPath: contentData.posterPath || undefined,
          backdropPath: contentData.backdropPath || undefined,
          tagline: contentData.tagline || undefined,
          overview: contentData.overview || undefined,
          year: contentData.year?.toString() || undefined,
          genres: contentData.genres || [],
          runtime: contentData.runtime || undefined,
          rating: contentData.rating || undefined,
          featured: contentData.featured || false,
          trending: contentData.trending || false,
        });
      } catch (err) {
        console.error('Error fetching content details:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch content details');
      } finally {
        setLoading(false);
      }
    }

    fetchContentDetails();
  }, [params.id, router, user, isAdmin, form]);

  // Handle form submission
  const onSubmit = async (data: ContentFormValues) => {
    setIsSaving(true);

    try {
      const response = await fetch(`/api/admin/content/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to update content (${response.status})`);
      }

      toast({
        title: 'Content updated',
        description: 'The content has been successfully updated.',
        variant: 'default',
      });
    } catch (err) {
      console.error('Error updating content:', err);
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to update content',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle content deletion
  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this content? This action cannot be undone.')) {
      return;
    }

    setIsDeleting(true);

    try {
      const response = await fetch(`/api/admin/content/${params.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to delete content (${response.status})`);
      }

      toast({
        title: 'Content deleted',
        description: 'The content has been successfully deleted.',
        variant: 'default',
      });

      // Navigate back to content list
      router.push('/admin/content');
    } catch (err) {
      console.error('Error deleting content:', err);
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to delete content',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  // Genres available in the platform
  const availableGenres = [
    'Action', 'Adventure', 'Animation', 'Comedy', 'Crime',
    'Documentary', 'Drama', 'Family', 'Fantasy', 'History',
    'Horror', 'Music', 'Mystery', 'Romance', 'Science Fiction',
    'Thriller', 'TV Movie', 'War', 'Western'
  ];

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push('/admin/content')}
            className="mr-2"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-3xl font-bold">
            {loading ? 'Loading Content...' : `Edit: ${form.watch('title')}`}
          </h1>
        </div>
        <div className="flex gap-2">
          <Button
            variant="default"
            onClick={form.handleSubmit(onSubmit)}
            disabled={isSaving || loading}
          >
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save Changes'}
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isDeleting || loading}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            {isDeleting ? 'Deleting...' : 'Delete'}
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <Skeleton className="h-[400px] w-full rounded-md" />
          </div>
          <div className="md:col-span-2 space-y-4">
            <Skeleton className="h-12 w-full rounded-md" />
            <Skeleton className="h-24 w-full rounded-md" />
            <Skeleton className="h-12 w-full rounded-md" />
            <Skeleton className="h-12 w-full rounded-md" />
          </div>
        </div>
      ) : (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Left Column - Poster & Quick Info */}
              <div className="space-y-4">
                <Card>
                  <CardContent className="p-6">
                    {form.watch('posterPath') ? (
                      <div className="relative aspect-[2/3] w-full rounded-md overflow-hidden mb-4">
                        <Image
                          src={form.watch('posterPath')?.startsWith('/')
                            ? `https://image.tmdb.org/t/p/w500${form.watch('posterPath')}`
                            : form.watch('posterPath') || ''}
                          alt={form.watch('title')}
                          fill
                          className="object-cover"
                        />
                      </div>
                    ) : (
                      <div className="aspect-[2/3] w-full rounded-md bg-gray-200 dark:bg-gray-800 mb-4 flex items-center justify-center">
                        <div className="text-center p-4">
                          <div className="mx-auto w-12 h-12 rounded-full bg-gray-300 dark:bg-gray-700 flex items-center justify-center mb-2">
                            {form.watch('type') === 'movie' ? (
                              <Film className="h-6 w-6 text-gray-500 dark:text-gray-400" />
                            ) : (
                              <Tv className="h-6 w-6 text-gray-500 dark:text-gray-400" />
                            )}
                          </div>
                          <p className="text-sm text-gray-500 dark:text-gray-400">No poster image</p>
                        </div>
                      </div>
                    )}

                    <div className="space-y-4">
                      <FormField
                        control={form.control}
                        name="posterPath"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Poster Image URL</FormLabel>
                            <FormControl>
                              <Input placeholder="Poster path or URL" {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="type"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Content Type</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select content type" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="movie">Movie</SelectItem>
                                <SelectItem value="show">TV Show</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="status"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Status</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select status" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="published">Published</SelectItem>
                                <SelectItem value="draft">Draft</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="featured"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                              <div className="space-y-0.5">
                                <FormLabel>Featured</FormLabel>
                                <FormDescription className="text-xs">
                                  Show on homepage
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="trending"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                              <div className="space-y-0.5">
                                <FormLabel>Trending</FormLabel>
                                <FormDescription className="text-xs">
                                  In trending section
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">External IDs</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="tmdbId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>TMDB ID</FormLabel>
                          <FormControl>
                            <Input placeholder="TMDB ID" {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="imdbId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>IMDB ID</FormLabel>
                          <FormControl>
                            <Input placeholder="IMDB ID" {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {form.watch('tmdbId') && (
                      <div className="mt-4">
                        <a
                          href={`https://www.themoviedb.org/${form.watch('type')}/${form.watch('tmdbId')}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center text-sm text-blue-500 hover:underline"
                        >
                          <ExternalLink className="h-4 w-4 mr-1" />
                          View on TMDB
                        </a>
                      </div>
                    )}

                    {form.watch('imdbId') && (
                      <div className="mt-2">
                        <a
                          href={`https://www.imdb.com/title/${form.watch('imdbId')}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center text-sm text-blue-500 hover:underline"
                        >
                          <ExternalLink className="h-4 w-4 mr-1" />
                          View on IMDB
                        </a>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Right Column - Main Content Details */}
              <div className="md:col-span-2 space-y-6">
                <Card>
                  <CardContent className="pt-6 space-y-6">
                    <FormField
                      control={form.control}
                      name="title"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Title</FormLabel>
                          <FormControl>
                            <Input placeholder="Content title" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="year"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Release Year</FormLabel>
                            <FormControl>
                              <Input placeholder="Year" {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="runtime"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Runtime (minutes)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder="Runtime in minutes"
                                {...field}
                                value={field.value || ''}
                                onChange={e => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="tagline"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tagline</FormLabel>
                          <FormControl>
                            <Input placeholder="Tagline" {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="overview"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Overview</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Content description"
                              className="min-h-[120px]"
                              {...field}
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="genres"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Genres</FormLabel>
                          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
                            {availableGenres.map(genre => (
                              <Badge
                                key={genre}
                                variant={field.value.includes(genre) ? "default" : "outline"}
                                className="cursor-pointer select-none"
                                onClick={() => {
                                  if (field.value.includes(genre)) {
                                    field.onChange(field.value.filter(g => g !== genre));
                                  } else {
                                    field.onChange([...field.value, genre]);
                                  }
                                }}
                              >
                                {genre}
                              </Badge>
                            ))}
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="rating"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Rating (0-10)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.1"
                              min="0"
                              max="10"
                              placeholder="Rating"
                              {...field}
                              value={field.value || ''}
                              onChange={e => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="backdropPath"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Backdrop Image URL</FormLabel>
                          <FormControl>
                            <Input placeholder="Backdrop path or URL" {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {form.watch('backdropPath') && (
                      <div className="relative w-full aspect-video rounded-md overflow-hidden">
                        <Image
                          src={form.watch('backdropPath')?.startsWith('/')
                            ? `https://image.tmdb.org/t/p/original${form.watch('backdropPath')}`
                            : form.watch('backdropPath') || ''}
                          alt={`${form.watch('title')} backdrop`}
                          fill
                          className="object-cover"
                        />
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </form>
        </Form>
      )}
    </div>
  );
}
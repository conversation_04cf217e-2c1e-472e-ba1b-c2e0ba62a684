'use client';

import React, { useState, useEffect } from 'react';
import { Navbar } from '@/components/Navbar';
import SimpleFooter from '@/components/SimpleFooter';
import UserSettingsPanel from '@/components/UserSettingsPanel';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { cn } from '@/lib/utils';

export default function SettingsClient() {
  const { user, isLoading: isAuthLoading } = useAuth(); // Get user and loading state
  const router = useRouter();

  // Redirect to auth if not authenticated *after* loading is complete
  useEffect(() => {
    // Wait for auth check to finish
    if (!isAuthLoading) {
      // If loading is done and there's no user, redirect
      if (!user) {
        console.log('SettingsClient: Auth loading finished, user not found, redirecting to /auth');
        router.push('/auth?redirect=/settings');
      }
    }
    // Depend on loading state and user presence
  }, [isAuthLoading, user, router]);

  // Show loading spinner while auth is loading
  if (isAuthLoading) {
    return (
      <div className="min-h-screen bg-vista-dark text-vista-light flex items-center justify-center">
        <div className="w-16 h-16 border-4 border-vista-light/20 border-t-vista-light rounded-full animate-spin"></div>
      </div>
    );
  }

  // If loading is done but still no user (should have been redirected, but as fallback)
  if (!user) {
    // This state should ideally not be reached due to the redirect effect
    // You could show an error or a link to login
    return (
      <div className="min-h-screen bg-vista-dark text-vista-light flex items-center justify-center">
        <p>Authentication required. Redirecting...</p>
      </div>
    );
  }

  // Render settings panel if authenticated and loading is done
  return (
    <div className="min-h-screen flex flex-col bg-vista-dark text-vista-light overflow-hidden">
      <Navbar className="fixed top-0 left-0 right-0 z-40" />
      <div className={cn(
        "flex-1 pt-10 pb-6 md:pt-20 w-full",
        "max-w-screen-2xl mx-auto px-4 sm:px-5 md:px-6 lg:px-8"
      )}>
        <UserSettingsPanel />
      </div>
      <SimpleFooter className="mt-auto py-4 md:py-6" />
    </div>
  );
}

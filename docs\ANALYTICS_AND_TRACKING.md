# Analytics and Tracking Service

## Overview
The Analytics and Tracking Service provides comprehensive monitoring and analysis of user interactions, watch party activities, and content consumption patterns within StreamVista. The service implements various tracking mechanisms for user behavior, performance metrics, and system health monitoring.

## Core Components

### Watch Statistics Interface
```typescript
interface WatchStats {
  totalWatchTime: number;              // in seconds
  genreDistribution: Record<string, number>;  // percentage
  contentTypeDistribution: {
    show: number;
    movie: number;
  };
  weeklyWatchTime: Record<string, number>;    // day: seconds
  watchTimePerTimeOfDay: {
    morning: number;
    afternoon: number;
    evening: number;
    night: number;
  };
  favoriteGenres: Array<{ genre: string; count: number }>;
  topWatched: WatchHistoryItem[];
  recentActivity: WatchHistoryItem[];
  watchStreak: number;                 // days in a row with activity
  unfinishedContent: WatchHistoryItem[];
  completionRate: number;              // percentage of started content that was finished
}
```

### Connection Statistics Interface
```typescript
interface ConnectionStats {
  latency: number;
  reconnectCount: number;
  connectCount: number;
  disconnectCount: number;
  lastConnected: Date | null;
  lastDisconnected: Date | null;
  transportType: string;
}
```

### Request Tracking Interface
```typescript
interface RequestCounts {
  'create-watch-party': number;
  'join-watch-party': number;
  'leave-watch-party': number;
  'playback-update': number;
  'new-message': number;
  'get-watch-parties': number;
}
```

## Features

### 1. Watch Party Analytics
- Party creation and deletion tracking
- Member join/leave monitoring
- Activity logging with timestamps
- Party state persistence and recovery
- Request frequency analysis
- Error tracking and logging

### 2. User Engagement Metrics
- Watch time tracking
- Genre preferences analysis
- Content type distribution
- Weekly activity patterns
- Time-of-day viewing habits
- Watch streaks
- Completion rates

### 3. Performance Monitoring
- Socket connection statistics
- Latency measurements
- Reconnection tracking
- Transport type monitoring
- Error rate analysis
- Request count tracking

### 4. Debug Logging
- Configurable log levels
- Event tracking
- Error logging
- Performance metrics
- State changes
- API request monitoring

## Implementation Details

### Watch Party Tracking
```typescript
function trackParty(partyId: string, action: 'create' | 'access' | 'join' | 'leave' | 'delete', party?: WatchParty) {
  // Validate inputs
  if (!partyId) {
    logApi('error', 'TRACKER', 'Attempted to track party with empty ID');
    return;
  }

  // Ensure global storage exists
  if (!global.storedParties) {
    global.storedParties = {};
  }

  // Handle party tracking based on action
  switch (action) {
    case 'create':
      // Add to Map and verify
      watchParties.set(partyId, party);
      // Add to global storage
      global.storedParties[partyId] = {
        timestamp: Date.now(),
        accessCount: 1,
        partyData: party
      };
      break;
    case 'delete':
      // Mark as deleted in storage
      global.storedParties[partyId] = {
        ...global.storedParties[partyId],
        deleted: true,
        deletedAt: Date.now()
      };
      break;
    default:
      // Update access count
      if (global.storedParties[partyId]) {
        global.storedParties[partyId].accessCount++;
        global.storedParties[partyId].timestamp = Date.now();
      }
  }
}
```

### User Insights Generation
```typescript
function generateUserInsights(watchHistory: WatchHistoryItem[], allContent: IContent[]): UserInsights {
  const genreCounts: Record<string, number> = {};
  let movieCount = 0;
  let showCount = 0;

  // Process watch history
  watchHistory.forEach(item => {
    // Count content types
    item.type === 'movie' ? movieCount++ : showCount++;

    // Weight genres by progress and completion
    const weight = (item.progress / 100) * (item.completed ? 1.5 : 1);
    item.genres.forEach(genre => {
      genreCounts[genre] = (genreCounts[genre] || 0) + weight;
    });
  });

  // Calculate preferences
  return {
    genrePreferences: normalizeGenreCounts(genreCounts),
    preferMoviesOverShows: calculateContentTypePreference(movieCount, showCount)
  };
}
```

### Connection Monitoring
```typescript
function useConnectionStats(socket: any) {
  const [stats, setStats] = useState<ConnectionStats>({
    latency: 0,
    reconnectCount: 0,
    connectCount: 0,
    disconnectCount: 0,
    lastConnected: null,
    lastDisconnected: null,
    transportType: 'websocket'
  });

  useEffect(() => {
    if (!socket) return;

    const updateStats = () => {
      const newStats = socket.getConnectionStats();
      setStats(newStats);
    };

    const interval = setInterval(updateStats, 2000);
    return () => clearInterval(interval);
  }, [socket]);

  return stats;
}
```

## Best Practices

### 1. Data Collection
- Implement proper error handling and validation
- Use appropriate data types and structures
- Maintain data consistency across components
- Handle edge cases and null values
- Implement retry mechanisms for failed tracking

### 2. Performance
- Optimize storage usage
- Implement efficient data structures
- Use appropriate intervals for updates
- Implement data cleanup strategies
- Cache frequently accessed data

### 3. Security
- Sanitize tracked data
- Implement proper access controls
- Handle sensitive information appropriately
- Validate input data
- Implement rate limiting

### 4. Reliability
- Implement proper error handling
- Use persistent storage when necessary
- Implement recovery mechanisms
- Handle network issues gracefully
- Maintain data integrity

## Integration Guidelines

### 1. Adding New Metrics
```typescript
// 1. Define the metric interface
interface NewMetric {
  name: string;
  value: number;
  timestamp: number;
}

// 2. Implement the tracking function
function trackNewMetric(metric: NewMetric) {
  // Validate
  if (!metric.name || typeof metric.value !== 'number') {
    console.error('Invalid metric data');
    return;
  }

  // Store
  try {
    // Add to storage
    metrics.push(metric);
    // Trigger updates
    notifyListeners(metric);
  } catch (error) {
    console.error('Error tracking metric:', error);
  }
}

// 3. Implement the consumer hook
function useNewMetric(metricName: string) {
  const [value, setValue] = useState<number>(0);

  useEffect(() => {
    // Subscribe to updates
    const unsubscribe = subscribeToMetric(metricName, setValue);
    return unsubscribe;
  }, [metricName]);

  return value;
}
```

### 2. Debugging
```typescript
const DEBUG = {
  LOG_ALL_REQUESTS: false,
  DETAILED_LOGS: false,
  LOG_STORAGE_CHANGES: false,
  LOG_PLAYBACK_UPDATES: false,
  LOG_GET_PARTIES: false,
  LOG_THROTTLE: 5000
};

function logApi(level: 'info' | 'debug' | 'error' | 'warn', operation: string, message: string) {
  if (level === 'debug' && !DEBUG.DETAILED_LOGS) return;
  
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [${level.toUpperCase()}] ${operation}: ${message}`);
}
```

### 3. Error Handling
```typescript
function handleTrackingError(error: Error, context: string) {
  // Log the error
  console.error(`Tracking error in ${context}:`, error);

  // Report to monitoring system
  reportError({
    context,
    error: error.message,
    timestamp: Date.now()
  });

  // Attempt recovery if possible
  try {
    recoverFromError(context);
  } catch (recoveryError) {
    console.error('Recovery failed:', recoveryError);
  }
}
```

## Development Tools

### 1. Testing Utilities
```typescript
const mockAnalytics = {
  trackEvent: jest.fn(),
  getStats: jest.fn(),
  resetStats: jest.fn()
};

function createTestWatchHistory(count: number): WatchHistoryItem[] {
  return Array.from({ length: count }, (_, i) => ({
    id: `test-${i}`,
    contentId: `content-${i}`,
    watchTime: Math.random() * 3600,
    progress: Math.random() * 100,
    completed: Math.random() > 0.5
  }));
}
```

### 2. Monitoring Tools
```typescript
function monitorMetrics() {
  setInterval(() => {
    const stats = {
      activeParties: watchParties.size,
      totalRequests: requestCount,
      errorRate: calculateErrorRate(),
      averageLatency: calculateAverageLatency()
    };

    console.table(stats);
  }, 60000);
}
```

### 3. Debug Helpers
```typescript
const debugAnalytics = {
  logEvents: true,
  logErrors: true,
  logMetrics: false,
  logInterval: 5000
};

function logAnalyticsEvent(event: string, data: any) {
  if (debugAnalytics.logEvents) {
    console.log(`[Analytics] ${event}:`, data);
  }
}
``` 
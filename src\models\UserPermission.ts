import mongoose, { Document, Schema } from 'mongoose';

export interface IUserPermission extends Document {
  userId: mongoose.Types.ObjectId;
  permissions: Record<string, boolean>;
  updatedAt: Date;
  createdAt: Date;
}

// Define the schema
const UserPermissionSchema = new Schema<IUserPermission>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      unique: true,
      index: true
    },
    permissions: {
      type: Schema.Types.Mixed,
      default: {}
    }
  },
  {
    timestamps: true
  }
);

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined';

// Only create the model on the server side
const UserPermission = isBrowser
  ? null // Return null in the browser
  : mongoose.models.UserPermission || mongoose.model<IUserPermission>('UserPermission', UserPermissionSchema);

export default UserPermission;

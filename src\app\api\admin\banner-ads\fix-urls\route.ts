import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import BannerAd from '@/models/BannerAd';
import User from '@/models/User';

/**
 * POST /api/admin/banner-ads/fix-urls
 * Fix banner ad URLs that have timestamp mismatches
 */
export async function POST(request: NextRequest) {
  try {
    // Try to get the user ID from the cookie directly
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    if (!userId) {
      console.error('Admin banner fix-urls API: No user ID found in request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await ensureMongooseConnection();

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as { role?: string }).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Get all banner ads
    const banners = await BannerAd.find({});
    const fixedBanners = [];
    const brokenBanners = [];

    for (const banner of banners) {
      const imageUrl = banner.imageUrl;
      
      // Check if it's a Cloudinary URL with potential timestamp mismatch
      if (imageUrl && imageUrl.includes('cloudinary.com')) {
        try {
          // Test if the URL is accessible
          const response = await fetch(imageUrl, { method: 'HEAD' });
          
          if (!response.ok) {
            console.log(`Banner ${banner._id} has broken URL: ${imageUrl}`);
            brokenBanners.push({
              id: banner._id,
              title: banner.title,
              originalUrl: imageUrl,
              status: response.status
            });

            // Try to fix the URL by removing the version parameter
            const urlWithoutVersion = imageUrl.replace(/\/v\d+\//, '/');
            const testResponse = await fetch(urlWithoutVersion, { method: 'HEAD' });
            
            if (testResponse.ok) {
              // Update the banner with the fixed URL
              await BannerAd.findByIdAndUpdate(banner._id, {
                imageUrl: urlWithoutVersion
              });
              
              fixedBanners.push({
                id: banner._id,
                title: banner.title,
                originalUrl: imageUrl,
                fixedUrl: urlWithoutVersion
              });
              
              console.log(`Fixed banner ${banner._id} URL: ${urlWithoutVersion}`);
            } else {
              // Try to find the correct URL by checking common patterns
              const publicIdMatch = imageUrl.match(/\/([^\/]+)\.(jpg|jpeg|png|gif|webp)$/i);
              if (publicIdMatch) {
                const filename = publicIdMatch[1];
                const extension = publicIdMatch[2];
                
                // Try different version numbers around the current one
                const currentVersion = imageUrl.match(/\/v(\d+)\//)?.[1];
                if (currentVersion) {
                  const baseTimestamp = parseInt(currentVersion);
                  const variations = [
                    baseTimestamp - 100,
                    baseTimestamp - 50,
                    baseTimestamp + 50,
                    baseTimestamp + 100
                  ];
                  
                  for (const variation of variations) {
                    const testUrl = imageUrl.replace(/\/v\d+\//, `/v${variation}/`);
                    const varResponse = await fetch(testUrl, { method: 'HEAD' });
                    
                    if (varResponse.ok) {
                      await BannerAd.findByIdAndUpdate(banner._id, {
                        imageUrl: testUrl
                      });
                      
                      fixedBanners.push({
                        id: banner._id,
                        title: banner.title,
                        originalUrl: imageUrl,
                        fixedUrl: testUrl
                      });
                      
                      console.log(`Fixed banner ${banner._id} URL with variation: ${testUrl}`);
                      break;
                    }
                  }
                }
              }
            }
          }
        } catch (error) {
          console.error(`Error checking URL for banner ${banner._id}:`, error);
          brokenBanners.push({
            id: banner._id,
            title: banner.title,
            originalUrl: imageUrl,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Banner URL fix completed',
      results: {
        totalBanners: banners.length,
        fixedBanners: fixedBanners.length,
        brokenBanners: brokenBanners.length,
        fixed: fixedBanners,
        broken: brokenBanners
      }
    });

  } catch (error) {
    console.error('Error fixing banner URLs:', error);
    return NextResponse.json(
      { error: 'Failed to fix banner URLs' },
      { status: 500 }
    );
  }
}

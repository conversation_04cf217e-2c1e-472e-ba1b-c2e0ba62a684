/**
 * Netlify Build MongoDB Connection Check
 *
 * This script is used during the Netlify build process to verify MongoDB connectivity
 * before proceeding with the build. It helps prevent builds with broken MongoDB connections.
 */

// Use CommonJS syntax for compatibility
const { MongoClient } = require('mongodb');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');

// Try to load environment variables from .env.local if it exists (local development)
const envPath = path.resolve(process.cwd(), '.env.local');
if (fs.existsSync(envPath)) {
  console.log('Loading environment variables from .env.local');
  dotenv.config({ path: envPath });
} else {
  console.log('No .env.local file found, using environment variables from deployment platform');
}

// Get MongoDB URI from environment variables
const MONGODB_URI = process.env.MONGODB_URI;
const MONGODB_DB = process.env.MONGODB_DB || 'streamvista';

if (!MONGODB_URI) {
  console.error('Error: MONGODB_URI environment variable is not defined');
  console.error('Make sure to set MONGODB_URI in your Netlify environment variables or .env.local file');
  process.exit(1);
}

// Connection options optimized for build environment
const options = {
  connectTimeoutMS: 15000,
  socketTimeoutMS: 30000,
  maxPoolSize: 5,
  minPoolSize: 1,
  retryWrites: true,
  w: 'majority',
};

async function checkMongoDBConnection() {
  console.log('=== Netlify Build MongoDB Connection Check ===');
  console.log(`Database: ${MONGODB_DB}`);
  // Don't log any part of the connection string as it may contain credentials
  console.log('Testing connection to MongoDB...');

  let client;

  try {
    console.time('Connection time');
    client = new MongoClient(MONGODB_URI, options);
    await client.connect();
    console.timeEnd('Connection time');

    console.log('✅ MongoDB connection successful');

    // Test ping
    console.time('Ping time');
    await client.db(MONGODB_DB).command({ ping: 1 });
    console.timeEnd('Ping time');
    console.log('✅ Ping successful');

    // Get server info
    const serverInfo = await client.db(MONGODB_DB).admin().serverInfo();
    console.log(`MongoDB version: ${serverInfo.version}`);

    // Get connection stats
    const stats = await client.db(MONGODB_DB).admin().serverStatus();
    console.log(`Active connections: ${stats.connections.current}`);
    console.log(`Available connections: ${stats.connections.available}`);

    return true;
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message);
    return false;
  } finally {
    if (client) {
      await client.close();
      console.log('MongoDB connection closed');
    }
  }
}

// Run the check
checkMongoDBConnection()
  .then(success => {
    if (success) {
      console.log('MongoDB connection check passed');
      process.exit(0);
    } else {
      console.error('MongoDB connection check failed');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Error during MongoDB connection check:', error);
    process.exit(1);
  });

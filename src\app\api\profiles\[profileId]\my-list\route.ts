import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongoose';
import Profile from '@/models/Profile';
import mongoose from 'mongoose'; // Import mongoose

// Define an interface for the specific structure returned by the lean query
interface LeanedMyListProfile {
  _id: mongoose.Types.ObjectId;
  myList?: string[]; // Make myList optional as it might not exist on a profile
}

/**
 * GET /api/profiles/[profileId]/my-list
 * Get the "My List" items for a profile
 */
export async function GET(
  request: NextRequest,
  context: { params: { profileId: string } }
) {
  try {
    await ensureMongooseConnection();

    const { profileId } = await context.params;

    if (!profileId) {
      return NextResponse.json(
        { error: 'Profile ID is required' },
        { status: 400 }
      );
    }

    // Find the profile, select myList, and cast the lean result
    const profile = await Profile.findById(profileId)
      .select('myList')
      .lean<LeanedMyListProfile | null>(); // Added type assertion here

    if (!profile) {
      return NextResponse.json(
        { error: 'Profile not found' },
        { status: 404 }
      );
    }

    // Get the myList items, using optional chaining and explicit cast
    const typedProfile = profile as LeanedMyListProfile | null;
    const myListIds = typedProfile?.myList || [];

    // Check if we have any items to return
    if (myListIds.length === 0) {
      return NextResponse.json({
        myList: [],
        contentDetails: [] // Empty array, not null, to be consistent
      });
    }

    // In a real app, we would fetch content details for each ID
    // from another service or database. For now, we'll return the IDs.

    // Returning only IDs without creating placeholders
    // The client will handle this appropriately and show an empty state
    // when no detailed content is available
    return NextResponse.json({
      myList: myListIds,
      // In a real app, this would be populated with actual content details
      // from a content database or API
      contentDetails: []
    });
  } catch (error) {
    console.error('Error fetching My List:', error);
    return NextResponse.json(
      { error: 'Failed to fetch My List' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/profiles/[profileId]/my-list
 * Add an item to a profile's "My List"
 */
export async function POST(
  request: NextRequest,
  context: { params: { profileId: string } }
) {
  try {
    await ensureMongooseConnection();

    const { profileId } = await context.params;
    const { contentId } = await request.json();

    if (!profileId || !contentId) {
      return NextResponse.json(
        { error: 'Profile ID and Content ID are required' },
        { status: 400 }
      );
    }

    // Use findByIdAndUpdate with $addToSet for atomic and idempotent addition
    const updatedProfile = await Profile.findByIdAndUpdate(
      profileId,
      { $addToSet: { myList: contentId } }, // Add contentId only if it doesn't exist
      { new: true } // Return the updated document
    ).select('myList'); // Select only the myList field for the response

    if (!updatedProfile) {
      return NextResponse.json(
        { error: 'Profile not found' },
        { status: 404 }
      );
    }

    // No need to manually check if it was already there, $addToSet handles it.
    // The message can reflect that the list now contains the item.
    return NextResponse.json({
      message: 'Item is now in My List', // Changed message slightly
      myList: updatedProfile.myList
    });

  } catch (error) {
    console.error('Error adding to My List:', error);
    return NextResponse.json(
      { error: 'Failed to add to My List' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/profiles/[profileId]/my-list
 * Remove an item from a profile's "My List"
 */
export async function DELETE(
  request: NextRequest,
  context: { params: { profileId: string } }
) {
  try {
    await ensureMongooseConnection();

    const { profileId } = await context.params;

    // For DELETE requests, handle getting contentId from body or query params
    let contentId;
    try {
      const body = await request.json();
      contentId = body.contentId;
    } catch (parseError) {
      const url = new URL(request.url);
      contentId = url.searchParams.get('contentId');
      if (!contentId) {
        console.error('Error parsing request body or missing contentId query param:', parseError);
        console.log('Request URL:', request.url);
        console.log('Request method:', request.method);
        console.log('Request headers:', Object.fromEntries(request.headers));
      }
    }

    if (!profileId || !contentId) {
      return NextResponse.json(
        { error: 'Profile ID and Content ID are required' },
        { status: 400 }
      );
    }

    // Use findByIdAndUpdate with $pull for atomic removal
    const updatedProfile = await Profile.findByIdAndUpdate(
      profileId,
      { $pull: { myList: contentId } }, // Remove contentId from the array
      { new: true } // Return the updated document
    ).select('myList'); // Select only the myList field for the response

    if (!updatedProfile) {
      return NextResponse.json(
        { error: 'Profile not found' },
        { status: 404 }
      );
    }

    // No need for manual filtering or save
    return NextResponse.json({
      message: 'Item removed from My List',
      myList: updatedProfile.myList
    });

  } catch (error) {
    console.error('Error removing from My List:', error);
    return NextResponse.json(
      { error: 'Failed to remove from My List' },
      { status: 500 }
    );
  }
}
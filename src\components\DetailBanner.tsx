import { But<PERSON> } from "@/components/ui/button";
import { Play, Plus, Download, Share2 } from "lucide-react";
import Image from "next/image";
import { Separator } from "@/components/ui/separator";

interface DetailBannerProps {
  title: string;
  description: string;
  year: number;
  ageRating: string;
  duration: string;
  genres: string[];
  starring: string[];
  creator: string;
  imagePath: string;
  trailerUrl?: string;
  // OMDB-specific fields
  director?: string;
  awards?: string;
  rated?: string;
  released?: string;
  metascore?: number;
  // Trailer functionality
  trailerVideoId?: string;
  onPlayTrailer?: () => void;
  onAddToList?: () => void;
  onShare?: () => void;
}

export default function DetailBanner({
  title,
  description,
  year,
  ageRating,
  duration,
  genres,
  starring,
  creator,
  imagePath,
  trailerUrl,
  // OMDB-specific fields
  director,
  awards,
  rated,
  released,
  metascore,
  // Trailer functionality
  trailerVideoId,
  onPlayTrailer,
  onAddToList,
  onShare
}: DetailBannerProps) {
  return (
    <div className="relative w-full">
      {/* Background Image with Gradient Overlay */}
      <div className="relative h-[40vh] w-full">
        <Image
          src={imagePath}
          alt={title}
          fill
          className="object-cover object-center"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-t from-vista-dark via-vista-dark/80 to-transparent" />
        <div className="absolute inset-0 bg-gradient-to-r from-vista-dark to-transparent" />
      </div>

      {/* Content Overlay - now static below image */}
      <div className="w-full flex flex-col items-start justify-end p-8 md:p-16 max-w-4xl -mt-16 z-10 relative text-left">
        <div className="space-y-4">
          <h1 className="text-3xl md:text-5xl font-bold tracking-tight text-white">{title}</h1>
          <div className="flex flex-wrap items-center gap-3 text-sm text-vista-light/80">
            <span>{year}</span>
            <span className="px-1 border border-vista-light/30 rounded">{ageRating}</span>
            <span>{duration}</span>
            <span>{genres.join(", ")}</span>
          </div>
          <p className="text-vista-light/90 max-w-3xl text-base md:text-lg">{description}</p>
          <div className="flex flex-wrap gap-4 pt-4">
            <Button className="bg-white text-vista-dark hover:bg-white/90" onClick={onPlayTrailer}>
              <Play className="mr-2 h-4 w-4" /> Play Trailer
            </Button>
            <Button variant="outline" className="border-white/20 bg-transparent hover:bg-white/10" onClick={onAddToList}>
              <Plus className="mr-2 h-4 w-4" /> Add
            </Button>
            <Button variant="outline" className="border-white/20 bg-transparent hover:bg-white/10" onClick={onShare}>
              <Share2 className="mr-2 h-4 w-4" /> Share
            </Button>
          </div>
        </div>
      </div>

      {/* Info Section */}
      <div className="bg-vista-dark text-vista-light px-8 md:px-16 py-12">
        <div className="max-w-4xl space-y-6">
          <div>
            <h3 className="text-sm font-medium text-vista-light/60 mb-1">Starring</h3>
            <p className="text-vista-light">{starring.join(", ")}</p>
          </div>

          <div>
            <h3 className="text-sm font-medium text-vista-light/60 mb-1">Creator</h3>
            <p className="text-vista-light">{creator}</p>
          </div>

          {director && (
            <div>
              <h3 className="text-sm font-medium text-vista-light/60 mb-1">Director</h3>
              <p className="text-vista-light">{director}</p>
            </div>
          )}

          {awards && (
            <div>
              <h3 className="text-sm font-medium text-vista-light/60 mb-1">Awards</h3>
              <p className="text-vista-light">{awards}</p>
            </div>
          )}

          <Separator className="bg-vista-light/10" />
        </div>
      </div>
    </div>
  );
}

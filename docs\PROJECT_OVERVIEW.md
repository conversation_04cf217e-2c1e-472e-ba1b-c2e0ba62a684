# StreamVista Project Overview

## Project Vision

StreamVista is a modern streaming platform that combines content discovery with social viewing experiences. Our vision is to create a seamless, engaging platform where users can discover, watch, and share content together in real-time.

## Key Features

### Content Discovery & Management
- Advanced movie and TV show browsing with dedicated routes (`/movies`, `/shows`)
- Smart search functionality with real-time results
- Personalized user profiles and recommendations
- Watchlist management via `/my-list`
- Detailed content pages with comprehensive metadata

### Streaming Integration
- Custom video player with advanced controls
- Support for movies and TV shows with episode selection
- Multiple streaming sources with fallback options
- Responsive and accessible video interface
- Picture-in-picture and fullscreen support

### Social Features
- Real-time watch parties with synchronized playback
- Integrated chat system with emoji support
- User presence indicators
- Profile customization and management
- Real-time notifications for social interactions

### User Experience
- Modern UI built with Next.js 15 and Tailwind CSS
- Dark mode support by default
- Responsive design for all devices
- Accessibility-first approach with ARIA support
- Fast page loads with optimized routing

### Real-time Features
- Socket.io integration for real-time communication
- Live user presence system
- Synchronized watch party controls
- Real-time chat with emoji picker
- Event-driven architecture for instant updates

## Technology Stack

### Frontend
- **Framework**: Next.js 15 with App Router
- **UI Components**: Radix UI primitives with Tailwind CSS
- **State Management**: React Context and Server Actions
- **Real-time**: Socket.io client
- **Forms**: React Hook Form with Zod validation
- **Animations**: Framer Motion
- **Type Safety**: TypeScript

### Backend
- **Server**: Next.js API Routes and Socket.io server
- **Real-time**: Socket.io for WebSocket communication
- **File Uploads**: React Dropzone
- **Date Handling**: date-fns
- **Validation**: Zod schema validation
- **Notifications**: Pusher

### Infrastructure
- **Development**: Local development with hot reloading
- **Build**: Next.js production build
- **Static Export**: Optional static site generation
- **Deployment**: Multiple deployment options (Vercel, Netlify)
- **Version Control**: Git

## Project Structure

```
streamvista/
├── src/                    # Source code directory
│   ├── app/               # Next.js App Router pages
│   │   ├── account/       # Account management
│   │   ├── api/          # API routes
│   │   ├── auth/         # Authentication
│   │   ├── discover/     # Content discovery
│   │   ├── movies/       # Movie browsing
│   │   ├── shows/        # TV show browsing
│   │   ├── watch/        # Video player
│   │   ├── watch-party/  # Watch party features
│   │   └── profiles/     # User profiles
│   ├── components/       # Reusable components
│   ├── contexts/         # React contexts
│   ├── hooks/           # Custom React hooks
│   ├── lib/             # Utility functions
│   ├── services/        # External service integrations
│   └── types/           # TypeScript type definitions
├── public/              # Static assets
├── docs/               # Project documentation
└── server.js           # Socket.io server
```

## Getting Started

For setup instructions and development workflow information, see [DEVELOPMENT_WORKFLOW.md](./DEVELOPMENT_WORKFLOW.md).

## Technical Architecture

For detailed technical architecture information, see [TECHNICAL_ARCHITECTURE.md](./TECHNICAL_ARCHITECTURE.md).

## Development Notes

For development notes, challenges, and solutions, see [DEV_NOTES.md](./DEV_NOTES.md).

## Next Steps

For planned features and upcoming work, see [NEXT_STEPS.md](./NEXT_STEPS.md). 
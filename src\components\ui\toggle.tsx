"use client"

import * as React from "react"
import * as TogglePrimitive from "@radix-ui/react-toggle"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const toggleVariants = cva(
  "inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-vista-dark transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-vista-blue focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-vista-dark-lighter data-[state=on]:text-vista-blue",
  {
    variants: {
      variant: {
        default: "bg-transparent",
        outline:
          "border border-vista-light/20 bg-transparent hover:bg-vista-light/10 hover:text-vista-light data-[state=on]:bg-vista-light/10 data-[state=on]:text-vista-blue",
      },
      size: {
        default: "h-10 px-3",
        sm: "h-8 px-2",
        lg: "h-12 px-4",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

const Toggle = React.forwardRef<
  React.ElementRef<typeof TogglePrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof TogglePrimitive.Root> &
    VariantProps<typeof toggleVariants>
>(({ className, variant, size, ...props }, ref) => (
  <TogglePrimitive.Root
    ref={ref}
    className={cn(toggleVariants({ variant, size }), className)}
    {...props}
  />
))
Toggle.displayName = TogglePrimitive.Root.displayName

// Toggle group component for multiple toggles
const ToggleGroup = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    type?: 'single' | 'multiple'
    value?: string
    defaultValue?: string
    onValueChange?: (value: string) => void
  }
>(({ className, children, type = 'multiple', ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn(
        "inline-flex items-center justify-center gap-1 rounded-md bg-vista-dark-lighter/50 p-1",
        className
      )}
      role="group"
      {...props}
    >
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child)) {
          return React.cloneElement(child, {
            ...child.props,
            variant: 'outline',
            size: 'sm',
            className: cn(
              "rounded-sm first:rounded-l-md last:rounded-r-md",
              child.props.className
            ),
          });
        }
        return child;
      })}
    </div>
  );
});
ToggleGroup.displayName = "ToggleGroup";

export { Toggle, ToggleGroup, toggleVariants }

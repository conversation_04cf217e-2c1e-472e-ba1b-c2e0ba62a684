'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { UserPlus, Pencil, Plus, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { useProfiles } from '@/contexts/ProfileContext';
import { UserAvatar } from '@/components/UserAvatar';
import { toast } from 'sonner';
import { ProfileSession } from '@/lib/types';
import React from 'react';
import { Navbar } from '@/components/Navbar';
import SimpleFooter from '@/components/SimpleFooter';

export default function ProfilesPage() {
  const router = useRouter();
  const { user } = useAuth();
  const { 
    profiles, 
    fetchProfiles, 
    setActiveProfile, 
    isLoadingProfiles,
    remainingProfiles 
  } = useProfiles();
  const [selectedProfileId, setSelectedProfileId] = useState<string | null>(null);
  const [initialLoad, setInitialLoad] = useState(true);
  const [loadError, setLoadError] = useState(false);
  const didInitialFetch = React.useRef(false);

  // Load profiles only on initial mount
  useEffect(() => {
    const loadData = async () => {
      if (didInitialFetch.current) return;
      
      didInitialFetch.current = true;
      setLoadError(false);
      
      // Create a timeout to prevent infinite loading state
      const loadingTimeout = setTimeout(() => {
        if (initialLoad) {
          setInitialLoad(false);
          console.log('Profiles loading timeout exceeded');
          toast.error('Loading profiles took too long. Please try again.');
          setLoadError(true);
        }
      }, 5000); // 5 second timeout
      
      try {
        await fetchProfiles();
      } catch (error) {
        console.error('Error loading profiles:', error);
        setLoadError(true);
        toast.error('Failed to load profiles');
      } finally {
        clearTimeout(loadingTimeout);
        setInitialLoad(false);
      }
    };
    
    if (user?.id) {
      loadData();
    } else {
      setInitialLoad(false);
    }
  }, [user?.id]); // Only depend on user id, not fetchProfiles

  // Retry loading profiles if there was an error
  const handleRetryLoad = async () => {
    setInitialLoad(true);
    setLoadError(false);
    
    try {
      await fetchProfiles();
    } catch (error) {
      console.error('Error retrying profile load:', error);
      setLoadError(true);
      toast.error('Failed to load profiles');
    } finally {
      setInitialLoad(false);
    }
  };

  const handleProfileSelect = (profile: ProfileSession) => {
    // Show selection animation before navigating
    setSelectedProfileId(profile.id);
    
    // Set the active profile and navigate to home
    setTimeout(() => {
      setActiveProfile(profile);
      router.push('/');
    }, 800);
  };

  const handleAddProfile = () => {
    router.push('/profiles/new');
  };

  const handleEditProfiles = () => {
    router.push('/profiles/edit');
  };

  // Create a default primary profile if none exists - once
  useEffect(() => {
    if (!initialLoad && !isLoadingProfiles && profiles.length === 0 && user?.id) {
      // Redirect to profile creation page with mainProfile=true parameter
      router.push('/profiles/new?mainProfile=true');
    }
  }, [initialLoad, isLoadingProfiles, profiles.length, router, user?.id]);

  // Variants for profile animation
  const profileVariants = {
    initial: { opacity: 0, y: 20 },
    animate: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.5,
        ease: [0.2, 0.65, 0.3, 0.9]
      }
    }),
    selected: {
      scale: 1.1,
      y: -10,
      transition: {
        duration: 0.4,
        ease: [0.2, 0.65, 0.3, 0.9]
      }
    },
    exit: {
      opacity: 0,
      scale: 0.9,
      transition: {
        duration: 0.3
      }
    }
  };

  const defaultAvatar = '/avatars/avatar-1.png';

  return (
    <div className="min-h-screen bg-gradient-to-b from-vista-dark via-vista-dark to-zinc-900 text-vista-light flex flex-col">
      <Navbar />
      
      <div className="flex-1 flex flex-col items-center justify-between py-24 px-4">
        <div className="w-full max-w-5xl">
          {/* Header with user info */}
          <div className="mb-16 mt-8 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-3">Who's Watching?</h1>
            {user && (
              <div className="text-vista-light/60">
                <div className="text-vista-blue">{user.name}'s StreamVista Account</div>
                {remainingProfiles > 0 && (
                  <div className="text-sm mt-2">
                    <span className="text-vista-light/80">{remainingProfiles} of 5 profiles remaining</span>
                  </div>
                )}
              </div>
            )}
          </div>
          
          {initialLoad || isLoadingProfiles ? (
            <div className="flex justify-center items-center py-12">
              <div className="h-12 w-12 border-4 border-vista-blue/30 border-t-vista-blue rounded-full animate-spin"></div>
            </div>
          ) : loadError ? (
            <div className="flex flex-col items-center justify-center py-12">
              <p className="text-red-400 mb-4">There was an error loading your profiles</p>
              <Button onClick={handleRetryLoad} variant="outline">
                Retry
              </Button>
            </div>
          ) : (
            <>
              {/* Main Profile Section (if exists) */}
              {profiles.some(p => p.isPrimary) && (
                <div className="mb-12">
                  <h2 className="text-center text-xl font-medium text-vista-light/70 mb-8">Main Profile</h2>
                  <div className="flex justify-center">
                    {profiles
                      .filter(profile => profile.isPrimary)
                      .map((profile, index) => (
                        <motion.div
                          key={profile.id}
                          custom={index}
                          initial="initial"
                          animate={selectedProfileId === profile.id ? "selected" : "animate"}
                          exit="exit"
                          variants={profileVariants}
                          className="flex flex-col items-center group"
                        >
                          <button
                            onClick={() => handleProfileSelect(profile)}
                            className="focus:outline-none group"
                            disabled={selectedProfileId !== null}
                            aria-label={`Select ${profile.name}'s profile`}
                          >
                            <div className="relative mb-4 transition-transform duration-300 group-hover:scale-105">
                              <div className="relative w-36 h-36 md:w-40 md:h-40 rounded-full overflow-hidden border-4 border-transparent group-hover:border-vista-blue transition-all duration-300 group-hover:shadow-glow">
                                <UserAvatar
                                  src={profile.avatar || defaultAvatar}
                                  alt={profile.name}
                                  size="lg"
                                  className="w-full h-full"
                                />
                                
                                {/* Hover Overlay */}
                                <div className="absolute inset-0 bg-gradient-to-t from-vista-dark/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full"></div>
                                
                                {/* Selection Indicator */}
                                {selectedProfileId === profile.id && (
                                  <motion.div
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    className="absolute inset-0 bg-vista-blue/30 flex items-center justify-center"
                                  >
                                    <div className="bg-vista-blue rounded-full p-2">
                                      <ArrowRight className="h-5 w-5 text-white" />
                                    </div>
                                  </motion.div>
                                )}
                                
                                {/* Primary Profile Badge */}
                                <div className="absolute top-2 right-2 bg-vista-blue text-white rounded-full w-7 h-7 flex items-center justify-center shadow-md">
                                  <UserPlus className="h-3.5 w-3.5" />
                                </div>
                              </div>
                            </div>
                            
                            <p className="text-center text-xl font-semibold transition-colors duration-300 group-hover:text-vista-blue mt-1">
                              {profile.name}
                            </p>
                            <p className="text-center text-sm text-vista-light/60 mt-1">
                              Main Profile
                            </p>
                          </button>
                        </motion.div>
                      ))}
                  </div>
                </div>
              )}

              {/* Other Profiles Section */}
              {profiles.some(p => !p.isPrimary) && (
                <div className="mb-12">
                  <h2 className="text-center text-xl font-medium text-vista-light/70 mb-8">
                    {profiles.some(p => p.isPrimary) ? 'Other Profiles' : 'Your Profiles'}
                  </h2>
                  
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-x-6 gap-y-8">
                    {profiles
                      .filter(profile => !profile.isPrimary)
                      .map((profile, index) => (
                        <motion.div
                          key={profile.id}
                          custom={index + (profiles.some(p => p.isPrimary) ? 1 : 0)}
                          initial="initial"
                          animate={selectedProfileId === profile.id ? "selected" : "animate"}
                          exit="exit"
                          variants={profileVariants}
                          className="flex flex-col items-center group"
                        >
                          <button
                            onClick={() => handleProfileSelect(profile)}
                            className="focus:outline-none w-full"
                            disabled={selectedProfileId !== null}
                            aria-label={`Select ${profile.name}\'s profile`}
                          >
                            <div className="relative mb-3 transition-transform duration-300 group-hover:scale-105">
                              <div className="relative w-28 h-28 md:w-32 md:h-32 rounded-lg overflow-hidden border-2 border-transparent group-hover:border-vista-blue/70 transition-all duration-300 group-hover:shadow-md">
                                <UserAvatar
                                  src={profile.avatar || defaultAvatar}
                                  alt={profile.name}
                                  size="lg"
                                  className="w-full h-full"
                                />
                                
                                {/* Hover Overlay */}
                                <div className="absolute inset-0 bg-gradient-to-t from-vista-dark/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></div>
                                
                                {/* Selection Indicator */}
                                {selectedProfileId === profile.id && (
                                  <motion.div
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    className="absolute inset-0 bg-vista-blue/40 flex items-center justify-center rounded-lg"
                                  >
                                    <div className="bg-vista-blue rounded-full p-2">
                                      <ArrowRight className="h-5 w-5 text-white" />
                                    </div>
                                  </motion.div>
                                )}
                              </div>
                            </div>
                            <p className="text-center text-base font-medium transition-colors duration-300 group-hover:text-vista-light mt-1 truncate w-full">
                              {profile.name}
                            </p>
                          </button>
                        </motion.div>
                      ))}
                
                    {/* Add New Profile Button */}
                    {profiles.length < 5 && (
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0, transition: { delay: (profiles.filter(p => !p.isPrimary).length) * 0.1 } }}
                        className="flex flex-col items-center group"
                      >
                        <button
                          onClick={handleAddProfile}
                          className="focus:outline-none w-full"
                          disabled={selectedProfileId !== null}
                          aria-label="Add new profile"
                        >
                          <div className="relative mb-3 transition-transform duration-300 group-hover:scale-105">
                            <div className="w-28 h-28 md:w-32 md:h-32 rounded-lg border-2 border-dashed border-vista-light/30 group-hover:border-vista-blue/70 bg-vista-dark/30 group-hover:bg-vista-dark/50 flex items-center justify-center transition-all duration-300">
                              <Plus className="w-10 h-10 text-vista-light/50 group-hover:text-vista-blue transition-colors duration-300" />
                            </div>
                          </div>
                          <p className="text-center text-base font-medium text-vista-light/70 transition-colors duration-300 group-hover:text-vista-light mt-1">
                            Add Profile
                          </p>
                        </button>
                      </motion.div>
                    )}
                  </div>
                </div>
              )}
              
              {/* Manage Profiles Button */}
              <div className="mt-12 text-center">
                <Button
                  onClick={handleEditProfiles}
                  variant="outline"
                  size="lg"
                  className="border-vista-light/30 hover:border-vista-light hover:bg-vista-light/10 text-vista-light/80 hover:text-vista-light transition-all duration-300 group px-8 py-3 text-base"
                >
                  <Pencil className="w-4 h-4 mr-2 group-hover:text-vista-blue transition-colors" /> Manage Profiles
                </Button>
              </div>
            </>
          )}
        </div>
      </div>
      <SimpleFooter />
    </div>
  );
}

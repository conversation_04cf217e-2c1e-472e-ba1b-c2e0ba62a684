interface BannerAd {
  _id: string
  title: string
  description: string
  imageUrl?: string
  linkUrl?: string
  bannerType?: string
  styling: {
    positions?: string[]
  }
  priority: number
  analytics: {
    impressions: number
    views: number
    clicks: number
  }
}

interface BannerResponse {
  banners: BannerAd[]
  count: number
}

interface BannerCacheEntry {
  data: BannerResponse | null
  timestamp: number
  promise?: Promise<BannerResponse>
}

interface AnalyticsEvent {
  bannerId: string
  action: 'view' | 'click'
  timestamp: number
}

interface CacheStats {
  totalRequests: number
  cacheHits: number
  cacheMisses: number
  analyticsEventsSent: number
  lastRequestTime: number | null
  cacheHitRate: number
}

class BannerCacheService {
  private cache = new Map<string, BannerCacheEntry>()
  private readonly CACHE_DURATION = 30 * 1000 // 30 seconds
  private readonly REQUEST_DEDUP_DURATION = 1000 // 1 second for request deduplication
  private analyticsQueue: AnalyticsEvent[] = []
  private analyticsTimer: NodeJS.Timeout | null = null
  private readonly ANALYTICS_BATCH_SIZE = 10
  private readonly ANALYTICS_BATCH_DELAY = 2000 // 2 seconds
  
  // Performance monitoring
  private stats: CacheStats = {
    totalRequests: 0,
    cacheHits: 0,
    cacheMisses: 0,
    analyticsEventsSent: 0,
    lastRequestTime: null,
    cacheHitRate: 0
  }

  /**
   * Get active banners with caching and request deduplication
   */
  async getActiveBanners(): Promise<BannerResponse> {
    const cacheKey = 'active-banners'
    const now = Date.now()
    const cached = this.cache.get(cacheKey)

    this.stats.totalRequests++
    this.stats.lastRequestTime = now

    // Return cached data if still valid
    if (cached && cached.data && (now - cached.timestamp) < this.CACHE_DURATION) {
      this.stats.cacheHits++
      this.updateCacheHitRate()
      return cached.data
    }

    this.stats.cacheMisses++
    this.updateCacheHitRate()

    // If there's already a pending request, wait for it
    if (cached?.promise) {
      return cached.promise
    }

    // Create new request and cache the promise for deduplication
    const requestPromise = this.fetchActiveBanners()
    
    this.cache.set(cacheKey, {
      data: null,
      timestamp: now,
      promise: requestPromise
    })

    try {
      const data = await requestPromise
      
      // Update cache with successful data
      this.cache.set(cacheKey, {
        data,
        timestamp: now
      })
      
      return data
    } catch (error) {
      // Remove failed request from cache
      this.cache.delete(cacheKey)
      throw error
    }
  }

  private updateCacheHitRate(): void {
    this.stats.cacheHitRate = this.stats.totalRequests > 0 
      ? (this.stats.cacheHits / this.stats.totalRequests) * 100 
      : 0
  }

  private async fetchActiveBanners(): Promise<BannerResponse> {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 10000) // 10 second timeout

    try {
      const response = await fetch('/api/banner-ads/active', {
        headers: {
          'Cache-Control': 'public, max-age=30'
        },
        signal: controller.signal
      })
      
      clearTimeout(timeoutId)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch banners: ${response.status} ${response.statusText}`)
      }
      
      return response.json()
    } catch (error) {
      clearTimeout(timeoutId)
      throw error
    }
  }

  /**
   * Track analytics with batching to reduce API calls
   */
  trackAnalytics(bannerId: string, action: 'view' | 'click'): void {
    // Prevent duplicate tracking for the same banner
    const existingEvent = this.analyticsQueue.find(
      event => event.bannerId === bannerId && event.action === action
    )
    
    if (existingEvent) {
      return // Already in queue
    }

    // Add to queue
    this.analyticsQueue.push({
      bannerId,
      action,
      timestamp: Date.now()
    })

    // Process immediately if queue is full, otherwise batch
    if (this.analyticsQueue.length >= this.ANALYTICS_BATCH_SIZE) {
      this.flushAnalytics()
    } else if (!this.analyticsTimer) {
      this.analyticsTimer = setTimeout(() => {
        this.flushAnalytics()
      }, this.ANALYTICS_BATCH_DELAY)
    }
  }

  private async flushAnalytics(): Promise<void> {
    if (this.analyticsQueue.length === 0) return

    const events = [...this.analyticsQueue]
    this.analyticsQueue = []
    
    if (this.analyticsTimer) {
      clearTimeout(this.analyticsTimer)
      this.analyticsTimer = null
    }

    try {
      // Group events by bannerId and action for efficient processing
      const groupedEvents = new Map<string, { views: number; clicks: number }>()
      
      events.forEach(event => {
        const key = event.bannerId
        const existing = groupedEvents.get(key) || { views: 0, clicks: 0 }
        
        if (event.action === 'view') {
          existing.views++
        } else {
          existing.clicks++
        }
        
        groupedEvents.set(key, existing)
      })

      // Send batched analytics
      const response = await fetch('/api/banner-ads/analytics/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          events: Array.from(groupedEvents.entries()).map(([bannerId, counts]) => ({
            bannerId,
            views: counts.views,
            clicks: counts.clicks
          }))
        })
      })

      if (response.ok) {
        this.stats.analyticsEventsSent += events.length
      }
      
    } catch (error) {
      console.warn('Failed to flush analytics:', error)
      // Don't retry to avoid infinite loops
    }
  }

  /**
   * Preload banners for better performance
   */
  async preloadBanners(): Promise<void> {
    try {
      await this.getActiveBanners()
    } catch (error) {
      console.warn('Failed to preload banners:', error)
    }
  }

  /**
   * Clear cache (useful for testing or manual refresh)
   */
  clearCache(): void {
    this.cache.clear()
    // Reset stats but keep some history
    this.stats = {
      ...this.stats,
      totalRequests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      cacheHitRate: 0
    }
  }

  /**
   * Get cache stats for debugging
   */
  getCacheStats(): CacheStats & { entries: string[] } {
    return {
      ...this.stats,
      entries: Array.from(this.cache.keys())
    }
  }

  /**
   * Get performance insights
   */
  getPerformanceReport(): string {
    const stats = this.getCacheStats()
    return `
Banner Cache Performance Report:
- Total Requests: ${stats.totalRequests}
- Cache Hit Rate: ${stats.cacheHitRate.toFixed(1)}%
- Analytics Events Sent: ${stats.analyticsEventsSent}
- Last Request: ${stats.lastRequestTime ? new Date(stats.lastRequestTime).toLocaleTimeString() : 'Never'}
- Active Cache Entries: ${stats.entries.length}
    `.trim()
  }

  /**
   * Force analytics flush (useful for page unload)
   */
  async flushAnalyticsNow(): Promise<void> {
    if (this.analyticsQueue.length > 0) {
      await this.flushAnalytics()
    }
  }
}

// Singleton instance
export const bannerCache = new BannerCacheService()

// Preload banners on module load for better performance
if (typeof window !== 'undefined') {
  // Add event listener to flush analytics on page unload
  window.addEventListener('beforeunload', () => {
    bannerCache.flushAnalyticsNow()
  })
  
  // Optional: preload banners after a short delay
  setTimeout(() => {
    bannerCache.preloadBanners()
  }, 1000)
} 
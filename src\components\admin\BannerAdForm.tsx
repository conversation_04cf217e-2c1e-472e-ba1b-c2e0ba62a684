'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import BannerAdUploadWidget from './BannerAdUploadWidget';
import { IBannerAd } from '@/models/BannerAd';
import { Loader2, Palette, Clock, Settings } from 'lucide-react';

interface BannerAdFormProps {
  bannerAd?: Partial<IBannerAd>;
  onSubmit: (data: BannerAdFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  mode: 'create' | 'edit';
}

export interface BannerAdFormData {
  title: string;
  description?: string;
  imageUrl?: string;
  linkUrl?: string;
  isActive: boolean;
  endDate?: string;
  duration?: number;
  priority: number;
  bannerType: 'image' | 'solid-color';
  styling: {
    backgroundColor: string;
    textColor: string;
    titleSize: string;
    descriptionSize: string;
    borderRadius: string;
    padding: string;
    animation: string;
    animationDuration: string;
    positions: string[];
    layout: string;
    textAlign: string;
  };
}

export default function BannerAdForm({
  bannerAd,
  onSubmit,
  onCancel,
  isLoading = false,
  mode
}: BannerAdFormProps) {
  const [formData, setFormData] = useState<BannerAdFormData>({
    title: bannerAd?.title || '',
    description: bannerAd?.description || '',
    imageUrl: bannerAd?.imageUrl || '',
    linkUrl: bannerAd?.linkUrl || '',
    isActive: bannerAd?.isActive !== undefined ? bannerAd.isActive : true,
    endDate: bannerAd?.endDate ? new Date(bannerAd.endDate).toISOString().split('T')[0] : '',
    duration: bannerAd?.duration || undefined,
    priority: bannerAd?.priority || 1,
    bannerType: (bannerAd as any)?.bannerType || 'image',
    styling: {
      backgroundColor: bannerAd?.styling?.backgroundColor || '#1a1a1a',
      textColor: bannerAd?.styling?.textColor || '#ffffff',
      titleSize: bannerAd?.styling?.titleSize || '1.5rem',
      descriptionSize: bannerAd?.styling?.descriptionSize || '1rem',
      borderRadius: bannerAd?.styling?.borderRadius || '0.5rem',
      padding: bannerAd?.styling?.padding || '1rem',
      animation: bannerAd?.styling?.animation || 'fadeIn',
      animationDuration: bannerAd?.styling?.animationDuration || '0.5s',
      positions: bannerAd?.styling?.positions || ['top'],
      layout: (bannerAd?.styling as any)?.layout || 'horizontal',
      textAlign: (bannerAd?.styling as any)?.textAlign || 'left'
    }
  });

  const [durationType, setDurationType] = useState<'infinite' | 'days' | 'endDate'>(
    bannerAd?.duration ? 'days' : bannerAd?.endDate ? 'endDate' : 'infinite'
  );

  const handleInputChange = (field: keyof BannerAdFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleStylingChange = (field: keyof BannerAdFormData['styling'], value: string | string[]) => {
    setFormData(prev => ({
      ...prev,
      styling: {
        ...prev.styling,
        [field]: value
      }
    }));
  };

  const handlePositionChange = (position: string, checked: boolean) => {
    setFormData(prev => {
      const currentPositions = prev.styling.positions || [];
      if (checked) {
        return {
          ...prev,
          styling: {
            ...prev.styling,
            positions: [...currentPositions, position]
          }
        };
      } else {
        return {
          ...prev,
          styling: {
            ...prev.styling,
            positions: currentPositions.filter(p => p !== position)
          }
        };
      }
    });
  };

  const handleDurationTypeChange = (type: 'infinite' | 'days' | 'endDate') => {
    setDurationType(type);
    if (type === 'infinite') {
      setFormData(prev => ({ ...prev, duration: undefined, endDate: '' }));
    } else if (type === 'days') {
      setFormData(prev => ({ ...prev, endDate: '', duration: 7 }));
    } else {
      setFormData(prev => ({ ...prev, duration: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim() || !formData.imageUrl) {
      return;
    }

    const submitData = { ...formData };

    // Clean up duration/endDate based on type
    if (durationType === 'infinite') {
      submitData.duration = undefined;
      submitData.endDate = undefined;
    } else if (durationType === 'days') {
      submitData.endDate = undefined;
    } else {
      submitData.duration = undefined;
    }

    await onSubmit(submitData);
  };

  const titleSizeOptions = [
    { value: '1rem', label: 'Small (1rem)' },
    { value: '1.25rem', label: 'Medium (1.25rem)' },
    { value: '1.5rem', label: 'Large (1.5rem)' },
    { value: '1.75rem', label: 'X-Large (1.75rem)' },
    { value: '2rem', label: 'XX-Large (2rem)' },
    { value: '2.5rem', label: 'XXX-Large (2.5rem)' },
    { value: '3rem', label: 'Huge (3rem)' }
  ];

  const descriptionSizeOptions = [
    { value: '0.75rem', label: 'X-Small (0.75rem)' },
    { value: '0.875rem', label: 'Small (0.875rem)' },
    { value: '1rem', label: 'Medium (1rem)' },
    { value: '1.125rem', label: 'Large (1.125rem)' },
    { value: '1.25rem', label: 'X-Large (1.25rem)' }
  ];

  const animationOptions = [
    { value: 'none', label: 'None' },
    { value: 'fadeIn', label: 'Fade In' },
    { value: 'slideIn', label: 'Slide In' },
    { value: 'bounce', label: 'Bounce' },
    { value: 'pulse', label: 'Pulse' }
  ];

  const positionOptions = [
    { value: 'top', label: 'Top of Page', description: 'Above hero section' },
    { value: 'hero-overlay', label: 'Hero Overlay', description: 'Over the hero section' },
    { value: 'center', label: 'Center of Page', description: 'Between content sections' },
    { value: 'between-sections', label: 'Between Sections', description: 'Multiple spots between content' },
    { value: 'bottom', label: 'Bottom of Page', description: 'Before footer' }
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="basic">Basic Info</TabsTrigger>
          <TabsTrigger value="styling">
            <Palette className="h-4 w-4 mr-2" />
            Styling
          </TabsTrigger>
          <TabsTrigger value="scheduling">
            <Clock className="h-4 w-4 mr-2" />
            Scheduling
          </TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Title *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="Enter banner title"
                  maxLength={100}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Enter banner description (optional)"
                  maxLength={200}
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label>Banner Type *</Label>
                <Select
                  value={formData.bannerType}
                  onValueChange={(value: 'image' | 'solid-color') => handleInputChange('bannerType', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select banner type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="image">Image Banner</SelectItem>
                    <SelectItem value="solid-color">Solid Color Banner</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {formData.bannerType === 'image' && (
                <BannerAdUploadWidget
                  currentImageUrl={formData.imageUrl}
                  onImageUploaded={(url) => handleInputChange('imageUrl', url)}
                  onImageRemoved={() => handleInputChange('imageUrl', '')}
                  disabled={isLoading}
                />
              )}

              <div className="space-y-2">
                <Label htmlFor="linkUrl">Link URL</Label>
                <Input
                  id="linkUrl"
                  type="url"
                  value={formData.linkUrl}
                  onChange={(e) => handleInputChange('linkUrl', e.target.value)}
                  placeholder="https://example.com (optional)"
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <Label htmlFor="priority">Priority (1-10)</Label>
                  <Input
                    id="priority"
                    type="number"
                    min="1"
                    max="10"
                    value={formData.priority}
                    onChange={(e) => handleInputChange('priority', parseInt(e.target.value))}
                    className="w-20"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => handleInputChange('isActive', checked)}
                  />
                  <Label htmlFor="isActive">Active</Label>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="styling" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Visual Styling</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="backgroundColor">Background Color</Label>
                  <div className="flex space-x-2">
                    <Input
                      id="backgroundColor"
                      type="color"
                      value={formData.styling.backgroundColor}
                      onChange={(e) => handleStylingChange('backgroundColor', e.target.value)}
                      className="w-16 h-10 p-1"
                    />
                    <Input
                      value={formData.styling.backgroundColor}
                      onChange={(e) => handleStylingChange('backgroundColor', e.target.value)}
                      placeholder="#1a1a1a"
                      className="flex-1"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="textColor">Text Color</Label>
                  <div className="flex space-x-2">
                    <Input
                      id="textColor"
                      type="color"
                      value={formData.styling.textColor}
                      onChange={(e) => handleStylingChange('textColor', e.target.value)}
                      className="w-16 h-10 p-1"
                    />
                    <Input
                      value={formData.styling.textColor}
                      onChange={(e) => handleStylingChange('textColor', e.target.value)}
                      placeholder="#ffffff"
                      className="flex-1"
                    />
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Title Size</Label>
                  <Select
                    value={formData.styling.titleSize}
                    onValueChange={(value) => handleStylingChange('titleSize', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {titleSizeOptions.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Description Size</Label>
                  <Select
                    value={formData.styling.descriptionSize}
                    onValueChange={(value) => handleStylingChange('descriptionSize', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {descriptionSizeOptions.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label>Border Radius</Label>
                  <Select
                    value={formData.styling.borderRadius}
                    onValueChange={(value) => handleStylingChange('borderRadius', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">None</SelectItem>
                      <SelectItem value="0.25rem">Small</SelectItem>
                      <SelectItem value="0.5rem">Medium</SelectItem>
                      <SelectItem value="0.75rem">Large</SelectItem>
                      <SelectItem value="1rem">X-Large</SelectItem>
                      <SelectItem value="1.5rem">Rounded</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Padding</Label>
                  <Select
                    value={formData.styling.padding}
                    onValueChange={(value) => handleStylingChange('padding', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0.5rem">Small</SelectItem>
                      <SelectItem value="0.75rem">Medium</SelectItem>
                      <SelectItem value="1rem">Large</SelectItem>
                      <SelectItem value="1.25rem">X-Large</SelectItem>
                      <SelectItem value="1.5rem">XX-Large</SelectItem>
                      <SelectItem value="2rem">Huge</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Display Positions *</Label>
                  <p className="text-sm text-vista-light/60">Select where this banner should appear on the page</p>
                  {formData.styling.positions.length === 0 && (
                    <p className="text-sm text-red-400">Please select at least one position</p>
                  )}
                  <div className="space-y-3">
                    {positionOptions.map(option => (
                      <div key={option.value} className="flex items-start space-x-3">
                        <Checkbox
                          id={`position-${option.value}`}
                          checked={formData.styling.positions.includes(option.value)}
                          onCheckedChange={(checked) => handlePositionChange(option.value, checked as boolean)}
                        />
                        <div className="grid gap-1.5 leading-none">
                          <Label
                            htmlFor={`position-${option.value}`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {option.label}
                          </Label>
                          <p className="text-xs text-vista-light/60">
                            {option.description}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <Separator />

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Banner Layout</Label>
                  <Select
                    value={formData.styling.layout}
                    onValueChange={(value) => handleStylingChange('layout', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="horizontal">Horizontal (Image + Text)</SelectItem>
                      <SelectItem value="full-width">Full Width Banner</SelectItem>
                      <SelectItem value="centered">Centered Content</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Text Alignment</Label>
                  <Select
                    value={formData.styling.textAlign}
                    onValueChange={(value) => handleStylingChange('textAlign', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="left">Left</SelectItem>
                      <SelectItem value="center">Center</SelectItem>
                      <SelectItem value="right">Right</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Separator />

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Animation</Label>
                  <Select
                    value={formData.styling.animation}
                    onValueChange={(value) => handleStylingChange('animation', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {animationOptions.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Animation Duration</Label>
                  <Select
                    value={formData.styling.animationDuration}
                    onValueChange={(value) => handleStylingChange('animationDuration', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0.3s">Fast (0.3s)</SelectItem>
                      <SelectItem value="0.5s">Normal (0.5s)</SelectItem>
                      <SelectItem value="0.7s">Slow (0.7s)</SelectItem>
                      <SelectItem value="1s">Very Slow (1s)</SelectItem>
                      <SelectItem value="1.5s">Extra Slow (1.5s)</SelectItem>
                      <SelectItem value="2s">Ultra Slow (2s)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="scheduling" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Duration Settings</CardTitle>
              <p className="text-sm text-vista-light/60">
                Banner will go live immediately when created. Set how long it should run.
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <Label>Duration Type</Label>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="infinite"
                      name="durationType"
                      checked={durationType === 'infinite'}
                      onChange={() => handleDurationTypeChange('infinite')}
                      className="text-vista-blue"
                    />
                    <Label htmlFor="infinite">Run indefinitely</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="days"
                      name="durationType"
                      checked={durationType === 'days'}
                      onChange={() => handleDurationTypeChange('days')}
                      className="text-vista-blue"
                    />
                    <Label htmlFor="days">Run for specific number of days</Label>
                  </div>

                  {durationType === 'days' && (
                    <div className="ml-6 space-y-2">
                      <Label htmlFor="duration">Number of days</Label>
                      <Input
                        id="duration"
                        type="number"
                        min="1"
                        value={formData.duration || ''}
                        onChange={(e) => handleInputChange('duration', parseInt(e.target.value))}
                        placeholder="7"
                        className="w-32"
                      />
                    </div>
                  )}

                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="endDate"
                      name="durationType"
                      checked={durationType === 'endDate'}
                      onChange={() => handleDurationTypeChange('endDate')}
                      className="text-vista-blue"
                    />
                    <Label htmlFor="endDate">Run until specific end date</Label>
                  </div>

                  {durationType === 'endDate' && (
                    <div className="ml-6 space-y-2">
                      <Label htmlFor="endDateInput">End Date</Label>
                      <Input
                        id="endDateInput"
                        type="date"
                        value={formData.endDate}
                        onChange={(e) => handleInputChange('endDate', e.target.value)}
                      />
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="flex justify-end space-x-4 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isLoading || !formData.title.trim() || (formData.bannerType === 'image' && !formData.imageUrl) || formData.styling.positions.length === 0}
          className="bg-vista-blue hover:bg-vista-blue/90"
        >
          {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
          {mode === 'create' ? 'Create Banner' : 'Update Banner'}
        </Button>
      </div>
    </form>
  );
}

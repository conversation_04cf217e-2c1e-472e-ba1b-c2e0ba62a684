import { NextRequest, NextResponse } from 'next/server';
import mongoose from 'mongoose';
import User from '@/models/User';
import Profile from '@/models/Profile';
import { getCloudinaryPublicId, deleteFromCloudinary } from '@/utils/cloudinary-utils';

// Utility function to get API error message
function getApiErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  return String(error);
}

export async function PUT(req: NextRequest) {
  try {
    const { userId, imageUrl, previousImageUrl } = await req.json();

    if (!userId || !imageUrl) {
      return NextResponse.json(
        { success: false, error: 'UserId and imageUrl are required' },
        { status: 400 }
      );
    }

    // Connect to MongoDB
    if (!mongoose.connection.readyState) {
      await mongoose.connect(process.env.MONGODB_URI as string);
    }

    // Update user profile image in the database
    // Update both picture and profileImage fields to ensure consistency
    const updatedUser = await User.findByIdAndUpdate(
      userId,
      {
        picture: imageUrl,
        profileImage: imageUrl // Also update profileImage for admin panel display
      },
      { new: true, select: '-password' }
    );

    if (!updatedUser) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // If there was a previous image and it's a Cloudinary URL, delete it
    if (previousImageUrl && previousImageUrl !== imageUrl) {
      const publicId = getCloudinaryPublicId(previousImageUrl);
      if (publicId) {
        const deleted = await deleteFromCloudinary(publicId);
        if (!deleted) {
          console.warn(`Failed to delete previous image: ${publicId}`);
        }
      }
    }

    // Update only the primary profile associated with this user
    // Each profile has its own avatar, but the user's profile image should match the primary profile
    try {
      // Find the primary profile
      const primaryProfile = await Profile.findOne({
        userId: userId,
        isPrimary: true
      });

      if (primaryProfile) {
        // Update only the primary profile with the same image
        await Profile.findByIdAndUpdate(primaryProfile._id, {
          $set: { avatar: imageUrl }
        });
        console.log(`Updated primary profile for user ${userId} with new avatar`);
      } else {
        console.log(`No primary profile found for user ${userId}`);
      }
    } catch (profileError) {
      console.error('Error updating primary profile with new avatar:', profileError);
      // Continue even if profile update fails
    }

    return NextResponse.json({
      success: true,
      user: updatedUser,
      timestamp: new Date().toISOString() // Add timestamp for cache busting
    });
  } catch (error) {
    console.error('Error updating profile image:', error);
    return NextResponse.json(
      { success: false, error: getApiErrorMessage(error) },
      { status: 500 }
    );
  }
}
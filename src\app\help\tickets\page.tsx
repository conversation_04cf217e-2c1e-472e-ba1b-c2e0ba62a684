'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Navbar } from '@/components/Navbar';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Search, 
  Plus, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  MessageSquare,
  Calendar,
  Filter,
  Eye
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';

interface Ticket {
  _id: string;
  ticketNumber: string;
  subject: string;
  category: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'open' | 'in_progress' | 'waiting_for_user' | 'resolved' | 'closed';
  createdAt: string;
  updatedAt: string;
  lastResponseAt?: string;
  responses: string[];
}

const statusConfig = {
  open: { label: 'Open', color: 'bg-blue-500/20 text-blue-400', icon: AlertCircle },
  in_progress: { label: 'In Progress', color: 'bg-yellow-500/20 text-yellow-400', icon: Clock },
  waiting_for_user: { label: 'Waiting for You', color: 'bg-orange-500/20 text-orange-400', icon: MessageSquare },
  resolved: { label: 'Resolved', color: 'bg-green-500/20 text-green-400', icon: CheckCircle },
  closed: { label: 'Closed', color: 'bg-gray-500/20 text-gray-400', icon: CheckCircle }
};

const priorityConfig = {
  low: { label: 'Low', color: 'bg-gray-500/20 text-gray-400' },
  medium: { label: 'Medium', color: 'bg-blue-500/20 text-blue-400' },
  high: { label: 'High', color: 'bg-orange-500/20 text-orange-400' },
  urgent: { label: 'Urgent', color: 'bg-red-500/20 text-red-400' }
};

export default function MyTicketsPage() {
  const router = useRouter();
  const { user, isLoading } = useAuth();
  const [tickets, setTickets] = useState<Ticket[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Check authentication
  useEffect(() => {
    if (!isLoading && !user) {
      toast.error('Please sign in to view your support tickets');
      router.push('/auth');
    }
  }, [user, isLoading, router]);

  useEffect(() => {
    if (user) {
      fetchTickets();
    }
  }, [page, statusFilter, categoryFilter, searchQuery, user]);

  const fetchTickets = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10',
        userId: user.id // Add userId to query parameters
      });

      if (statusFilter !== 'all') params.append('status', statusFilter);
      if (categoryFilter !== 'all') params.append('category', categoryFilter);
      if (searchQuery) params.append('search', searchQuery);

      const response = await fetch(`/api/help/tickets?${params}`, {
        credentials: 'include' // Include cookies in the request
      });
      if (!response.ok) {
        throw new Error('Failed to fetch tickets');
      }

      const data = await response.json();
      setTickets(data.tickets);
      setTotalPages(data.pagination.pages);
    } catch (error) {
      console.error('Error fetching tickets:', error);
      toast.error('Failed to load tickets');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-vista-dark text-vista-light">
        <Navbar />
        <div className="container mx-auto max-w-6xl py-16 px-4">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-vista-light/20 rounded w-1/3"></div>
            <div className="h-32 bg-vista-light/20 rounded"></div>
            <div className="h-64 bg-vista-light/20 rounded"></div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  // Don't render if not authenticated (will redirect)
  if (!user) {
    return null;
  }

  const getTimeSince = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    return formatDate(dateString);
  };

  return (
    <div className="min-h-screen bg-vista-dark text-vista-light">
      <Navbar />
      
      {/* Header */}
      <section className="py-16 px-4">
        <div className="container mx-auto max-w-6xl">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
            <div>
              <h1 className="text-4xl font-bold text-vista-light mb-2">My Support Tickets</h1>
              <p className="text-vista-light/70">Track and manage your support requests</p>
            </div>
            <Link href="/help/tickets/new">
              <Button className="bg-vista-blue hover:bg-vista-blue/90">
                <Plus className="w-4 h-4 mr-2" />
                New Ticket
              </Button>
            </Link>
          </div>

          {/* Filters */}
          <Card className="bg-vista-card border-vista-light/10 mb-6">
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-vista-light/60 w-4 h-4" />
                  <Input
                    placeholder="Search tickets..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 bg-vista-dark border-vista-light/20 text-vista-light"
                  />
                </div>
                
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="bg-vista-dark border-vista-light/20 text-vista-light">
                    <SelectValue placeholder="All Statuses" />
                  </SelectTrigger>
                  <SelectContent className="bg-vista-dark border-vista-light/20">
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="open">Open</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="waiting_for_user">Waiting for You</SelectItem>
                    <SelectItem value="resolved">Resolved</SelectItem>
                    <SelectItem value="closed">Closed</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger className="bg-vista-dark border-vista-light/20 text-vista-light">
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent className="bg-vista-dark border-vista-light/20">
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="subscription">Subscription</SelectItem>
                    <SelectItem value="technical">Technical</SelectItem>
                    <SelectItem value="billing">Billing</SelectItem>
                    <SelectItem value="content">Content</SelectItem>
                    <SelectItem value="account">Account</SelectItem>
                    <SelectItem value="bug_report">Bug Report</SelectItem>
                    <SelectItem value="feature_request">Feature Request</SelectItem>
                  </SelectContent>
                </Select>

                <Button 
                  variant="outline" 
                  onClick={() => {
                    setSearchQuery('');
                    setStatusFilter('all');
                    setCategoryFilter('all');
                  }}
                  className="border-vista-light/20 text-vista-light hover:bg-vista-light/10"
                >
                  <Filter className="w-4 h-4 mr-2" />
                  Clear Filters
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Tickets List */}
          {loading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <Card key={i} className="bg-vista-card border-vista-light/10">
                  <CardContent className="p-6">
                    <div className="animate-pulse">
                      <div className="h-4 bg-vista-light/20 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-vista-light/20 rounded w-1/2 mb-4"></div>
                      <div className="flex gap-2">
                        <div className="h-6 bg-vista-light/20 rounded w-16"></div>
                        <div className="h-6 bg-vista-light/20 rounded w-20"></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : tickets.length > 0 ? (
            <div className="space-y-4">
              {tickets.map((ticket) => {
                const StatusIcon = statusConfig[ticket.status].icon;
                return (
                  <Link key={ticket._id} href={`/help/tickets/${ticket._id}`}>
                    <Card className="bg-vista-card border-vista-light/10 hover:border-vista-blue/30 transition-colors group cursor-pointer">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between gap-4">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <h3 className="text-lg font-semibold text-vista-light group-hover:text-vista-blue transition-colors">
                                {ticket.subject}
                              </h3>
                              <Badge variant="outline" className="border-vista-light/20 text-vista-light/70 text-xs">
                                #{ticket.ticketNumber}
                              </Badge>
                            </div>
                            
                            <div className="flex flex-wrap items-center gap-2 mb-3">
                              <Badge className={statusConfig[ticket.status].color}>
                                <StatusIcon className="w-3 h-3 mr-1" />
                                {statusConfig[ticket.status].label}
                              </Badge>
                              <Badge className={priorityConfig[ticket.priority].color}>
                                {priorityConfig[ticket.priority].label}
                              </Badge>
                              <Badge variant="outline" className="border-vista-light/20 text-vista-light/60 text-xs">
                                {ticket.category.replace('_', ' ')}
                              </Badge>
                            </div>

                            <div className="flex items-center gap-4 text-sm text-vista-light/60">
                              <div className="flex items-center gap-1">
                                <Calendar className="w-4 h-4" />
                                Created {getTimeSince(ticket.createdAt)}
                              </div>
                              {ticket.lastResponseAt && (
                                <div className="flex items-center gap-1">
                                  <MessageSquare className="w-4 h-4" />
                                  Last response {getTimeSince(ticket.lastResponseAt)}
                                </div>
                              )}
                              <div className="flex items-center gap-1">
                                <MessageSquare className="w-4 h-4" />
                                {ticket.responses.length} responses
                              </div>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <Eye className="w-4 h-4 text-vista-light/60 group-hover:text-vista-blue transition-colors" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                );
              })}

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center gap-2 mt-8">
                  <Button
                    variant="outline"
                    onClick={() => setPage(p => Math.max(1, p - 1))}
                    disabled={page === 1}
                    className="border-vista-light/20 text-vista-light"
                  >
                    Previous
                  </Button>
                  <span className="flex items-center px-4 text-vista-light/70">
                    Page {page} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                    disabled={page === totalPages}
                    className="border-vista-light/20 text-vista-light"
                  >
                    Next
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <Card className="bg-vista-card border-vista-light/10">
              <CardContent className="p-12 text-center">
                <MessageSquare className="w-16 h-16 text-vista-light/40 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-vista-light mb-2">No tickets found</h3>
                <p className="text-vista-light/70 mb-6">
                  {searchQuery || statusFilter !== 'all' || categoryFilter !== 'all' 
                    ? 'Try adjusting your filters to see more results.'
                    : 'You haven\'t created any support tickets yet.'
                  }
                </p>
                <Link href="/help/tickets/new">
                  <Button className="bg-vista-blue hover:bg-vista-blue/90">
                    <Plus className="w-4 h-4 mr-2" />
                    Create Your First Ticket
                  </Button>
                </Link>
              </CardContent>
            </Card>
          )}
        </div>
      </section>

      <Footer />
    </div>
  );
}

"use client";

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, Filter, GridIcon, List as ListIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import ContentCard from '@/components/ContentCard';
import { ContentCardType } from '@/lib/content-utils';

export interface Category {
  id: string;
  name: string;
  count: number;
}

export interface FilterOption {
  id: string;
  name: string;
  options: { id: string; name: string }[];
}

interface CategoryBrowserProps {
  title: string;
  categories: Category[];
  content: ContentCardType[];
  filters?: FilterOption[];
  initialCategory?: string;
}

export default function CategoryBrowser({
  title,
  categories,
  content,
  filters = [],
  initialCategory = 'all'
}: CategoryBrowserProps) {
  const [selectedCategory, setSelectedCategory] = useState(initialCategory);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [activeFilters, setActiveFilters] = useState<Record<string, string[]>>({});
  const [filteredContent, setFilteredContent] = useState<ContentCardType[]>(content);

  // Apply filters whenever dependencies change
  useEffect(() => {
    // Apply category filter first
    let results = content;

    if (selectedCategory !== 'all') {
      results = results.filter(item => item.type === selectedCategory);
    }

    // Apply active filters (in a real app, this would be more sophisticated)
    // Just a simplified example of additional filters
    const hasActiveFilters = Object.values(activeFilters).some(options => options.length > 0);

    if (hasActiveFilters) {
      // Example filter for year
      const yearFilters = activeFilters['year'] || [];
      if (yearFilters.length > 0) {
        results = results.filter(item => {
          // Simple filtering - in real app would be more sophisticated
          if (item.year) {
            const year = parseInt(item.year as string);
            return yearFilters.some(filter => {
              if (filter === '2023') return year === 2023;
              if (filter === '2022') return year === 2022;
              if (filter === '2021') return year === 2021;
              if (filter === '2020') return year === 2020;
              if (filter === '2010-2019') return year >= 2010 && year <= 2019;
              if (filter === '2000-2009') return year >= 2000 && year <= 2009;
              if (filter === 'older') return year < 2000;
              return false;
            });
          }
          return false;
        });
      }
    }

    setFilteredContent(results);
  }, [content, selectedCategory, activeFilters]);

  const toggleFilter = (filterId: string, optionId: string) => {
    setActiveFilters(prev => {
      const current = prev[filterId] || [];
      const updated = current.includes(optionId)
        ? current.filter(id => id !== optionId)
        : [...current, optionId];

      return {
        ...prev,
        [filterId]: updated
      };
    });
  };

  // Clear all filters
  const clearFilters = () => {
    setActiveFilters({});
    setSelectedCategory('all');
  };

  const isEmpty = filteredContent.length === 0;

  return (
    <div className="bg-vista-dark min-h-screen pb-16">
      <div className="container mx-auto px-4 md:px-6 pt-24">
        {/* Header */}
        <div className="mb-10">
          <h1 className="text-3xl font-bold text-vista-light mb-4">{title}</h1>
          <p className="text-vista-light/70 max-w-2xl">
            Discover our extensive collection of movies and shows across various genres and categories.
          </p>
        </div>

        {/* Category Tabs */}
        <div className="mb-8 overflow-x-auto pb-2">
          <div className="flex space-x-2 min-w-max">
            <Button
              variant={selectedCategory === 'all' ? 'default' : 'outline'}
              className={selectedCategory === 'all'
                ? "bg-vista-blue text-white hover:bg-vista-blue/90 rounded-full"
                : "bg-transparent border-vista-light/20 text-vista-light hover:bg-vista-light/10 rounded-full"
              }
              onClick={() => setSelectedCategory('all')}
            >
              All Content
            </Button>

            {categories.map(category => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? 'default' : 'outline'}
                className={selectedCategory === category.id
                  ? "bg-vista-blue text-white hover:bg-vista-blue/90 rounded-full"
                  : "bg-transparent border-vista-light/20 text-vista-light hover:bg-vista-light/10 rounded-full"
                }
                onClick={() => setSelectedCategory(category.id)}
              >
                {category.name} <span className="ml-1.5 text-xs opacity-70">({category.count})</span>
              </Button>
            ))}
          </div>
        </div>

        {/* Filters and Controls */}
        <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
          <div className="flex flex-wrap items-center gap-3">
            <Button
              variant="outline"
              className="border-vista-light/20 bg-transparent text-vista-light hover:bg-vista-light/10"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
              <ChevronDown className={`ml-2 h-4 w-4 transition-transform duration-200 ${showFilters ? 'rotate-180' : ''}`} />
            </Button>

            {Object.entries(activeFilters).flatMap(([filterId, optionIds]) =>
              optionIds.map(optionId => {
                const filterOption = filters.find(f => f.id === filterId);
                const option = filterOption?.options.find(o => o.id === optionId);

                if (!option) return null;

                return (
                  <Badge
                    key={`${filterId}-${optionId}`}
                    variant="accent"
                    className="rounded-full"
                  >
                    {filterOption?.name}: {option.name}
                    <button
                      className="ml-1.5 hover:text-white/80"
                      onClick={() => toggleFilter(filterId, optionId)}
                    >
                      ×
                    </button>
                  </Badge>
                );
              })
            )}
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-vista-light/70 text-sm mr-2">View:</span>
            <Button
              variant="ghost"
              size="icon"
              className={`w-8 h-8 ${viewMode === 'grid' ? 'bg-vista-light/10 text-vista-light' : 'text-vista-light/50'}`}
              onClick={() => setViewMode('grid')}
            >
              <GridIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className={`w-8 h-8 ${viewMode === 'list' ? 'bg-vista-light/10 text-vista-light' : 'text-vista-light/50'}`}
              onClick={() => setViewMode('list')}
            >
              <ListIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Filter Panel */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="mb-8 bg-vista-dark-lighter rounded-xl p-6 overflow-hidden"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {filters.map(filter => (
                  <div key={filter.id} className="space-y-3">
                    <h3 className="text-vista-light/90 font-medium">{filter.name}</h3>
                    <div className="space-y-2">
                      {filter.options.map(option => (
                        <label key={option.id} className="flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            className="rounded border-vista-light/30 text-vista-blue focus:ring-vista-blue/50 bg-vista-dark h-4 w-4 mr-2"
                            checked={(activeFilters[filter.id] || []).includes(option.id)}
                            onChange={() => toggleFilter(filter.id, option.id)}
                          />
                          <span className="text-vista-light/80 text-sm">{option.name}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                ))}
              </div>

              <div className="flex justify-end mt-6">
                <Button
                  variant="outline"
                  className="border-vista-light/20 bg-transparent text-vista-light hover:bg-vista-light/10 mr-3"
                  onClick={() => setActiveFilters({})}
                >
                  Clear Filters
                </Button>
                <Button
                  className="bg-vista-blue text-white hover:bg-vista-blue/90"
                  onClick={() => setShowFilters(false)}
                >
                  Apply Filters
                </Button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Content Grid */}
        {!isEmpty ? (
          <div className={viewMode === 'grid'
            ? "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 md:gap-6"
            : "flex flex-col space-y-4"
          }>
            {filteredContent.map((item, index) => (
              viewMode === 'grid' ? (
                <ContentCard
                  key={item.id}
                  id={item.id}
                  title={item.title}
                  imagePath={item.imagePath}
                  type={item.type}
                  year={item.year}
                  ageRating={item.ageRating}
                  index={index}
                  link={`/watch/${item.id}?forcePlay=true&contentType=${item.type === 'shows' ? 'show' : 'movie'}`}
                />
              ) : (
                <div
                  key={item.id}
                  className="flex items-center bg-vista-dark-lighter p-4 rounded-lg hover:bg-vista-dark-lighter/70 transition-colors duration-200"
                >
                  <div className="relative w-16 h-24 rounded overflow-hidden mr-4">
                    <Image
                      src={item.imagePath || "https://placehold.co/300x450/171717/CCCCCC?text=No+Image"}
                      alt={item.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-vista-light font-medium">{item.title}</h3>
                    <div className="flex items-center text-vista-light/60 text-sm mt-1">
                      <span>{item.year}</span>
                      {item.ageRating && (
                        <>
                          <span className="mx-1.5">•</span>
                          <span>{item.ageRating}</span>
                        </>
                      )}
                      <span className="mx-1.5">•</span>
                      <span className="capitalize">{item.type.slice(0, -1)}</span>
                    </div>
                  </div>
                  <Link href={`/details/${item.type}/${item.id}`}>
                    <Button variant="ghost" className="ml-4 text-vista-light hover:bg-vista-light/10">
                      View Details
                    </Button>
                  </Link>
                </div>
              )
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-16 text-center">
            <div className="rounded-full bg-vista-dark-lighter p-6 mb-4">
              <Filter className="h-10 w-10 text-vista-light/40" />
            </div>
            <h3 className="text-vista-light text-xl mb-2">No content found</h3>
            <p className="text-vista-light/60 max-w-md">
              We couldn't find any content matching your selected filters. Try adjusting your filters or browsing a different category.
            </p>
            <Button
              className="mt-6 bg-vista-blue text-white hover:bg-vista-blue/90"
              onClick={clearFilters}
            >
              Clear All Filters
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}

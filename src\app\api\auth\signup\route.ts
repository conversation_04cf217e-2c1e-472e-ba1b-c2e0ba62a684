import { NextRequest, NextResponse } from 'next/server';
import User from '@/models/User';
import Profile from '@/models/Profile';
import { ensureMongooseConnection } from '@/lib/mongodb';
import mongoose from 'mongoose';
import { UserSession } from '@/lib/types';
import bcrypt from 'bcryptjs';
import { withMongo } from '@/middleware/mongoMiddleware';

// Define a type for the saved user document
interface SavedUser {
  _id: mongoose.Types.ObjectId;
  email: string;
  name: string;
  password: string;
  googleId?: string;
  picture?: string;
  role?: 'user' | 'admin' | 'superadmin';
  createdAt: Date;
}

// Define the handler function
async function handler(request: NextRequest) {
  console.log('Starting signup process');

  try {
    // Extract user data from request
    let userData;
    try {
      userData = await request.json();
      console.log('Request parsed successfully');
    } catch (parseError) {
      console.error('Error parsing request body:', parseError);
      return NextResponse.json(
        { error: 'Invalid request data' },
        { status: 400 }
      );
    }

    const { name, email, password, picture } = userData;

    // Basic validation
    if (!name || !email || !password) {
      return NextResponse.json(
        { error: 'Name, email, and password are required' },
        { status: 400 }
      );
    }

    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Password strength validation (minimum 8 characters)
    if (password.length < 8) {
      return NextResponse.json(
        { error: 'Password must be at least 8 characters long' },
        { status: 400 }
      );
    }

    // Check if user already exists
    console.log('Checking for existing user with email:', email);
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return NextResponse.json({ success: false, message: 'Email already in use' }, { status: 400 });
    }

    // Create new user
    console.log("Creating new user...");
    // Use the default avatar URL if no picture is provided - ensure lowercase cloud name
    const defaultAvatarUrl = "https://res.cloudinary.com/streamvista/image/upload/v1743812698/defaults/default_avatar.jpg";

    // Ensure the cloud name is lowercase in the URL
    const fixedDefaultAvatarUrl = defaultAvatarUrl.replace(/cloudinary\.com\/([^\/]+)\//, 'cloudinary.com/streamvista/');

    // Log the default avatar URL for debugging
    console.log('Default avatar URL:', defaultAvatarUrl);
    console.log('Fixed default avatar URL:', fixedDefaultAvatarUrl);

    // Validate the default avatar URL
    try {
      new URL(fixedDefaultAvatarUrl);
      console.log('Default avatar URL is valid');
    } catch (e) {
      console.error('Invalid default avatar URL:', e);
    }

    // Don't hash the password here - let the User schema pre-save hook handle it
    const pictureToUse = picture || fixedDefaultAvatarUrl;
    const newUser = new User({
      name,
      email,
      password, // Use the plain password - it will be hashed by the pre-save hook
      picture: pictureToUse,
      profileImage: pictureToUse, // Also set profileImage for admin panel display
      role: 'user', // Default role for new users
      lastLogin: new Date() // Set initial lastLogin timestamp since user is effectively logging in
    });

    // Save the user to the database
    try {
      // Start a session for transaction
      const session = await mongoose.startSession();

      // Define userSession outside the transaction to ensure it's accessible
      let userSession: UserSession | undefined;

      await session.withTransaction(async () => {
        // Save the user
        const savedUser = await newUser.save({ session });
        console.log('User saved successfully with ID:', savedUser._id);

        // Cast the saved user to our type
        const typedUser = savedUser.toObject() as SavedUser;

        // Create a primary profile for the user
        const userProfile = new Profile({
          userId: typedUser._id,
          name: typedUser.name,
          avatar: typedUser.picture || fixedDefaultAvatarUrl,
          isKids: false,
          isPrimary: true // This is the primary profile
        });

        await userProfile.save({ session });
        console.log('Primary profile created for user with ID:', typedUser._id);

        // Validate the role value to ensure it matches the expected union type
        let userRole: 'user' | 'admin' | 'superadmin' = 'user'; // Default to 'user'
        if (typedUser.role === 'admin' || typedUser.role === 'superadmin') {
          userRole = typedUser.role;
        }

        // Create a user session object without sensitive data
        userSession = {
          id: typedUser._id.toString(),
          googleId: typedUser.googleId, // Include googleId if present
          email: typedUser.email,
          name: typedUser.name,
          picture: typedUser.picture,
          role: userRole,
          createdAt: typedUser.createdAt.toISOString(),
        };
      });

      await session.endSession();

      // Ensure userSession is defined before proceeding
      if (!userSession) {
        throw new Error('Failed to create user session');
      }

      console.log('Signup successful, returning user session');

      // Get host information for debugging
      const host = request.headers.get('host');
      console.log('Host:', host);
      console.log('Environment:', process.env.NODE_ENV);
      console.log('Is Netlify:', process.env.NETLIFY === 'true');

      // Create response with user data
      const response = NextResponse.json({
        success: true,
        user: userSession
      });

      // Set cookies for authentication with production-ready settings
      // First, ensure no existing cookie with the same name exists
      response.cookies.delete('userId');

      // Then set the new cookie
      response.cookies.set('userId', userSession.id, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax', // 'lax' is more compatible across browsers and environments
        maxAge: 7 * 24 * 60 * 60, // 7 days
        path: '/'
      });

      // Log user activity
      try {
        const { logUserActivity } = await import('@/lib/activity-logger');
        await logUserActivity(
          userSession.id,
          'auth',
          'signup',
          'User registered successfully',
          request,
          { method: 'credentials' }
        );
      } catch (logError) {
        console.error('Error logging user activity:', logError);
        // Continue even if logging fails
      }

      // Mark visitor as converted if they were previously tracked as an anonymous visitor
      try {
        const { markVisitorAsConverted } = await import('@/lib/visitor-utils');
        await markVisitorAsConverted(userSession.id, request);
      } catch (visitorError) {
        console.error('Error marking visitor as converted:', visitorError);
        // Continue even if visitor marking fails
      }

      return response;
    } catch (saveError) {
      console.error('Error saving user:', saveError);

      // Handle specific MongoDB validation errors
      if (saveError instanceof mongoose.Error.ValidationError) {
        const validationErrors = Object.values(saveError.errors).map(e => e.message).join(', ');
        return NextResponse.json(
          { error: `Validation error: ${validationErrors}` },
          { status: 400 }
        );
      }

      throw saveError; // Re-throw for general error handler
    }

  } catch (error) {
    console.error('Signup error details:', error);

    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }

    // Handle specific MongoDB errors
    if (error instanceof mongoose.Error) {
      console.error('MongoDB error type:', error.constructor.name);
      return NextResponse.json(
        { error: `Database error during signup: ${error.message}` },
        { status: 500 }
      );
    }

    // Generic error response
    return NextResponse.json(
      { error: 'An unexpected error occurred during signup' },
      { status: 500 }
    );
  }
}

// Export the handler with MongoDB connection middleware
export const POST = withMongo(handler);
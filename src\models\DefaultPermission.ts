import mongoose, { Document, Schema } from 'mongoose';

export interface IDefaultPermission extends Document {
  permissions: {
    [role: string]: {
      [permission: string]: boolean;
    };
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const DefaultPermissionSchema = new Schema<IDefaultPermission>(
  {
    permissions: {
      type: Schema.Types.Mixed,
      required: true
    },
    isActive: {
      type: Boolean,
      default: true,
      index: true
    }
  },
  {
    timestamps: true
  }
);

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined';

// Only create the model on the server side
const DefaultPermission = isBrowser
  ? null // Return null in the browser
  : mongoose.models.DefaultPermission || 
    mongoose.model<IDefaultPermission>('DefaultPermission', DefaultPermissionSchema);

export default DefaultPermission;

import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import User from '@/models/User';

/**
 * GET /api/admin/visitors/report
 * Generate a report of visitor activity
 * 
 * This endpoint is only accessible to admin users.
 * It generates a report of visitor activity including trends and statistics.
 */
export async function GET(request: NextRequest) {
  try {
    // Get the userId from cookies or query string
    const { searchParams } = new URL(request.url);
    let userId = request.cookies.get('userId')?.value;

    // If no userId in cookies, try query string (for client-side admin verification)
    if (!userId) {
      const userIdParam = searchParams.get('userId');
      if (userIdParam) userId = userIdParam;
    }

    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Connect to database
    await ensureMongooseConnection();

    // Find the user by ID
    const user = await User.findById(userId).lean();

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if user has admin role
    const isUserAdmin = user.role === 'admin' || user.role === 'superadmin';

    if (!isUserAdmin) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Import the AnonymousVisitor model
    const AnonymousVisitor = (await import('@/models/AnonymousVisitor')).default;

    // Get date range parameters
    const startDate = searchParams.get('startDate') 
      ? new Date(searchParams.get('startDate') as string) 
      : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // Default to last 30 days
    
    const endDate = searchParams.get('endDate')
      ? new Date(searchParams.get('endDate') as string)
      : new Date();

    // Get basic statistics
    const totalVisitors = await AnonymousVisitor.countDocuments();
    const newVisitors = await AnonymousVisitor.countDocuments({
      firstVisit: { $gte: startDate, $lte: endDate }
    });
    const convertedVisitors = await AnonymousVisitor.countDocuments({
      convertedToUser: true
    });
    const conversionRate = totalVisitors > 0 ? (convertedVisitors / totalVisitors) * 100 : 0;

    // Get daily visitor counts
    const dailyVisitors = await AnonymousVisitor.aggregate([
      {
        $match: {
          firstVisit: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m-%d', date: '$firstVisit' } },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    // Get top referrers
    const topReferrers = await AnonymousVisitor.aggregate([
      {
        $match: {
          referrer: { $exists: true, $ne: null, $ne: '' }
        }
      },
      {
        $group: {
          _id: '$referrer',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      },
      {
        $limit: 10
      }
    ]);

    // Get device distribution
    const deviceDistribution = await AnonymousVisitor.aggregate([
      {
        $group: {
          _id: '$device',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    // Get browser distribution
    const browserDistribution = await AnonymousVisitor.aggregate([
      {
        $group: {
          _id: '$browser',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    // Get country distribution
    const countryDistribution = await AnonymousVisitor.aggregate([
      {
        $match: {
          country: { $exists: true, $ne: null, $ne: '' }
        }
      },
      {
        $group: {
          _id: '$country',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      },
      {
        $limit: 10
      }
    ]);

    // Get conversion timeline
    const conversionTimeline = await AnonymousVisitor.aggregate([
      {
        $match: {
          convertedToUser: true
        }
      },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m-%d', date: '$updatedAt' } },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    // Return the report
    return NextResponse.json({
      success: true,
      report: {
        dateRange: {
          startDate,
          endDate
        },
        summary: {
          totalVisitors,
          newVisitors,
          convertedVisitors,
          conversionRate: conversionRate.toFixed(2)
        },
        dailyVisitors,
        topReferrers,
        deviceDistribution,
        browserDistribution,
        countryDistribution,
        conversionTimeline
      }
    });
  } catch (error) {
    console.error('Error generating visitor report:', error);
    return NextResponse.json(
      { error: 'Failed to generate visitor report', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

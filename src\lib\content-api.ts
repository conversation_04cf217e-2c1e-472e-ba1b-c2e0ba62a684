import {
  getLatestMoviesUrl,
  getLatestShowsUrl,
  getLatestEpisodesUrl,
  VIDSRC_DOMAINS,
  VidSrcDomain
} from '@/lib/vidsrc-api';
import {
  getPopularMovies,
  getPopularTVShows,
  getTVShowsOnTheAir,
  getTrendingDaily,
  MappedContent
} from '@/lib/tmdb-api';

// Fallback data for when all APIs fail - using TMDB data instead of mock data
async function getFallbackData(contentType: string) {
  try {
    console.log('Using TMDB fallback data for', contentType);
    switch (contentType) {
      case 'movies':
        return await getPopularMovies();
      case 'shows':
        return await getPopularTVShows();
      case 'episodes':
        return await getTVShowsOnTheAir();
      default:
        return [];
    }
  } catch (error) {
    console.error('Error getting fallback data:', error);
    return [];
  }
}

// Check if we're using the export output
const isStaticExport = process.env.NEXT_PHASE === 'phase-production-build' &&
                      process.env.NODE_ENV === 'production';

/**
 * Fetch content data from TMDb/VidSrc
 * This is a client-side function that can be used in place of API routes
 * when using static export
 *
 * @param contentType The type of content to fetch ('movies', 'shows', 'episodes')
 * @param page The page number to fetch
 * @param options Additional options for fetching
 * @returns An array of content items
 */
export async function fetchContentData(contentType: string, page: number = 1, options?: {
  useTmdb?: boolean;
  forceDomain?: string;
}) {
  const {
    useTmdb = true,
    forceDomain
  } = options || {};

  // For static export, use fallback data from TMDB
  if (isStaticExport) {
    console.log('Using fallback data for static export:', contentType);
    return await getFallbackData(contentType);
  }

  // Try to use TMDb if enabled
  if (useTmdb) {
    try {
      console.log(`Using TMDb API for ${contentType}`);

      let results: MappedContent[] = [];

      switch (contentType) {
        case 'movies':
          results = await getPopularMovies(page);
          break;
        case 'shows':
          results = await getPopularTVShows(page);
          break;
        case 'episodes':
          // Episodes are a bit special - we'll get on-air TV shows and treat them as episodes
          results = await getTVShowsOnTheAir(page);

          // Add fake season/episode data for compatibility
          results = results.map((item, index) => ({
            ...item,
            season: 1,
            episode: index + 1,
            showTitle: item.title
          }));
          break;
        default:
          throw new Error('Invalid content type');
      }

      // Transform TMDb results to match expected format for VidSrc client
      const transformedResults = results.map(item => {
        const baseItem = {
          id: item.tmdbId,
          tmdb_id: item.tmdbId,
          title: item.title,
          year: item.year?.toString(),
          poster: item.posterUrl,
          added: item.releaseDate,
          // Including VidSrc embed URLs for direct use
          embed_url_tmdb: `https://vidsrc.xyz/embed/${contentType === 'movies' ? 'movie' : 'tv'}?tmdb=${item.tmdbId}`
        };

        // Add episode-specific fields if needed
        if (contentType === 'episodes') {
          return {
            ...baseItem,
            season: '1',
            episode: '1',
            show_title: item.title
          };
        }

        return baseItem;
      });

      return transformedResults;
    } catch (tmdbError) {
      console.error('Error fetching from TMDb:', tmdbError);
      // Fall back to VidSrc if TMDb fails
    }
  }

  // If TMDb fails or is disabled, try VidSrc API
  // Try each domain until one works
  const errors = [];
  
  // Use forceDomain if specified, otherwise try all domains
  const domainsToTry = forceDomain ? [forceDomain] : VIDSRC_DOMAINS;
  
  for (const domainString of domainsToTry) {
    try {
      // Cast to proper domain type if it's a valid domain
      const domain = VIDSRC_DOMAINS.includes(domainString as VidSrcDomain) 
        ? domainString as VidSrcDomain 
        : VIDSRC_DOMAINS[0]; // Fallback to first domain
        
      console.log(`Trying VidSrc domain: ${domain} for ${contentType}`);
      
      let url: string;
      
      // Get the appropriate URL based on content type
      switch (contentType) {
        case 'movies':
          url = getLatestMoviesUrl(page, domain);
          break;
        case 'shows':
          url = getLatestShowsUrl(page, domain);
          break;
        case 'episodes':
          url = getLatestEpisodesUrl(page, domain);
          break;
        default:
          throw new Error('Invalid content type');
      }
      
      console.log(`Fetching from: ${url}`);
      
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'en-US,en;q=0.9',
          'Cache-Control': 'no-cache'
        },
        cache: 'no-store'
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Validate response data
      if (!data || (!Array.isArray(data) && !data.data && !data.results)) {
        throw new Error('Invalid response format from VidSrc API');
      }
      
      // Extract array from response
      let items = Array.isArray(data) ? data : (data.data || data.results || []);
      
      // Add domain info to each item for tracking
      items = items.map((item: Record<string, unknown>) => ({
        ...item,
        _domain: domain,
        _source: 'vidsrc'
      }));
      
      console.log(`Successfully fetched ${items.length} ${contentType} from ${domain}`);
      return items;
      
    } catch (domainError) {
      // Log the error and continue to the next domain
      const errorMessage = domainError instanceof Error ? domainError.message : String(domainError);
      errors.push(`${domainString}: ${errorMessage}`);
      console.error(`Error with domain ${domainString}:`, errorMessage);
      // Continue to the next domain
    }
  }

  // If we get here, all domains failed
  console.error('All data sources failed:', errors);

  // Try to get fallback data from TMDB
  console.log('Attempting to get fallback data from TMDB');
  return await getFallbackData(contentType);
}
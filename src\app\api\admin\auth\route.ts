import { NextRequest, NextResponse } from 'next/server';
import { isAdmin } from '@/lib/middleware';

/**
 * GET /api/admin/auth
 * Check if the current user has admin permissions
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is admin
    const adminCheck = await isAdmin(request);
    
    if (adminCheck.isAuthorized) {
      return NextResponse.json({
        isAdmin: true,
        user: adminCheck.user
      });
    } else {
      return NextResponse.json({
        isAdmin: false,
        message: adminCheck.message
      }, { status: 403 });
    }
  } catch (error) {
    console.error('Error checking admin status:', error);
    return NextResponse.json(
      { error: 'Failed to check admin status', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

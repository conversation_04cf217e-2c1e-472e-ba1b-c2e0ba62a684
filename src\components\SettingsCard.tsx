'use client';

import { ReactNode } from 'react';
import { cn } from '@/lib/utils';
import { LucideIcon } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';

interface SettingsCardProps {
  title: string;
  description?: string;
  icon?: LucideIcon;
  children: ReactNode;
  footer?: ReactNode;
  className?: string;
  isLoading?: boolean;
  accentColor?: string;
  compact?: boolean;
}

export function SettingsCard({
  title,
  description,
  icon: Icon,
  children,
  footer,
  className,
  isLoading = false,
  accentColor = "vista-blue",
  compact = false
}: SettingsCardProps) {
  // Define accent color classes based on the accentColor prop
  const accentColorClasses = {
    "vista-blue": "from-blue-500 to-blue-500/20 text-blue-500",
    "purple": "from-purple-500 to-purple-500/20 text-purple-500",
    "emerald": "from-emerald-500 to-emerald-500/20 text-emerald-500",
    "amber": "from-amber-500 to-amber-500/20 text-amber-500",
    "rose": "from-rose-500 to-rose-500/20 text-rose-500",
    "slate": "from-slate-500 to-slate-500/20 text-slate-500",
  }[accentColor] || "from-blue-500 to-blue-500/20 text-blue-500";

  // Extract the text color class for the icon
  const iconColorClass = accentColorClasses.split(' ').find(cls => cls.startsWith('text-')) || 'text-blue-500';

  return (
    <Card
      className={cn(
        "overflow-hidden bg-vista-dark-lighter border border-vista-dark-lighter/80 shadow-md transition-all duration-300",
        "hover:border-white/10 hover:shadow-lg",
        "backdrop-blur-sm relative",
        "rounded-xl",
        className
      )}
    >
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r opacity-80"
        style={{
          backgroundImage: `linear-gradient(to right, var(--${accentColor.startsWith('vista-') ? accentColor : `color-${accentColor}-500`}), transparent)`
        }}
      />

      <CardHeader
        className={cn(
          "border-b border-vista-dark/40",
          compact ? "p-4" : "p-5"
        )}
      >
        <CardTitle className="flex items-center text-vista-light">
          {Icon && (
            <div className={cn(
              "flex items-center justify-center rounded-md",
              compact ? "w-7 h-7 mr-3" : "w-9 h-9 mr-3",
              "bg-gradient-to-br from-black/40 to-black/10 border border-white/5"
            )}>
              <Icon className={cn(iconColorClass, compact ? "h-4 w-4" : "h-5 w-5")} />
            </div>
          )}
          <span className={compact ? "text-lg" : "text-xl font-medium"}>{title}</span>
        </CardTitle>
        {description && (
          <CardDescription className={cn(
            "text-vista-light/70 mt-1.5",
            compact ? "text-xs" : "text-sm"
          )}>
            {description}
          </CardDescription>
        )}
      </CardHeader>

      {isLoading ? (
        <CardContent className="p-4 md:p-6 flex items-center justify-center min-h-[100px] md:min-h-[120px]">
          <div className="w-6 h-6 md:w-8 md:h-8 border-3 border-vista-light/10 border-t-vista-blue rounded-full animate-spin"></div>
        </CardContent>
      ) : (
        <>
          <CardContent className={cn(
            "transition-all duration-200",
            compact ? "p-3 md:p-4" : "p-4 md:p-6"
          )}>
            {children}
          </CardContent>

          {footer && (
            <CardFooter
              className={cn(
                "bg-black/20 border-t border-vista-dark/40 flex items-center justify-end",
                compact ? "px-3 py-2 md:px-4 md:py-3" : "px-4 py-3 md:px-6 md:py-4"
              )}
            >
              {footer}
            </CardFooter>
          )}
        </>
      )}
    </Card>
  );
}

// A special form field for settings that includes a label and description
interface SettingsFieldProps {
  label: string;
  description?: string;
  children: ReactNode;
  className?: string;
  icon?: ReactNode;
}

export function SettingsField({
  label,
  description,
  children,
  className,
  icon
}: SettingsFieldProps) {
  return (
    <div
      className={cn(
        "flex items-center justify-between gap-4 group py-3.5 px-3 rounded-lg hover:bg-black/20 transition-all duration-200 border border-transparent hover:border-white/5",
        className
      )}
    >
      <div className="flex items-center gap-3">
        {icon && (
          <div className="flex-shrink-0 w-9 h-9 rounded-md bg-black/30 flex items-center justify-center text-vista-blue border border-white/5 shadow-sm">
            {icon}
          </div>
        )}
        <div className="space-y-0.5">
          <h3 className="text-vista-light font-medium group-hover:text-white transition-colors">{label}</h3>
          {description && <p className="text-sm text-vista-light/70 group-hover:text-vista-light/80 transition-colors">{description}</p>}
        </div>
      </div>
      <div className="flex-shrink-0">
        {children}
      </div>
    </div>
  );
}
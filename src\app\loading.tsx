'use client';

import Image from 'next/image';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

// Variants for the container to stagger children animations
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.3,
    },
  },
};

// Variants for individual elements like logo and text
const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: 'spring',
      stiffness: 100,
      damping: 10,
    },
  },
};

// Variants for the animated text fill effect
const textFillVariants = {
  initial: { backgroundPosition: '200% 0' }, // Start mask off-screen to the right
  animate: {
    backgroundPosition: '-200% 0', // Move mask across the text to the left
    transition: {
      repeat: Infinity,
      duration: 2.5,
      ease: 'linear',
    },
  },
};

export default function Loading() {
  return (
    <motion.div
      className="relative flex min-h-screen flex-col items-center justify-center overflow-hidden bg-gradient-to-b from-[#0a0d14] via-[#101828] to-[#0a1022]"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      {/* Enhanced Background Animated Elements */}
      <div className="absolute inset-0 z-0 overflow-hidden">
        {/* Pulsating Orbs */}
        <motion.div
          className="absolute top-1/4 left-1/4 h-40 w-40 rounded-full bg-vista-blue/10 blur-3xl"
          animate={{ scale: [1, 1.1, 1], opacity: [0.5, 0.7, 0.5] }}
          transition={{ duration: 4, repeat: Infinity, ease: 'easeInOut' }}
        />
        <motion.div
          className="absolute bottom-1/4 right-1/4 h-48 w-48 rounded-full bg-vista-accent/10 blur-3xl"
          animate={{ scale: [1, 0.9, 1], opacity: [0.6, 0.8, 0.6] }}
          transition={{ duration: 5, repeat: Infinity, ease: 'easeInOut', delay: 1 }}
        />

        {/* Refined Twinkling Stars */}
        {Array.from({ length: 30 }).map((_, i) => (
          <motion.div
            key={i}
            className="absolute h-1 w-1 rounded-full bg-white/80"
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: [0, 1, 0], scale: 1 }}
            transition={{
              duration: Math.random() * 3 + 2,
              repeat: Infinity,
              repeatType: 'loop',
              ease: 'easeInOut',
              delay: Math.random() * 5,
            }}
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <motion.div className="z-10 flex flex-col items-center" variants={itemVariants}>
        {/* Animated Logo with Glow */}
        <motion.div
          className="relative mb-8"
          variants={itemVariants}
          animate={{ scale: [1, 1.05, 1], rotate: [0, 2, -2, 0] }}
          transition={{ duration: 5, repeat: Infinity, ease: 'easeInOut' }}
        >
          <div className="absolute inset-[-10px] rounded-full bg-vista-blue/20 blur-2xl animate-pulse" />
          <motion.div
             initial={{ scale: 0.8, opacity: 0 }}
             animate={{ scale: 1, opacity: 1 }}
             transition={{ delay: 0.2, type: 'spring', stiffness: 120 }}
          >
            <Image
              src="/logo.webp"
              alt="StreamVista Logo"
              width={128}
              height={128}
              quality={100}
              className="relative z-10 object-contain drop-shadow-[0_0_20px_rgba(66,153,225,0.6)]"
              priority
            />
          </motion.div>
        </motion.div>

        {/* Animated Text Fill */}
        <motion.h1
          className="relative mb-6 text-center text-3xl font-semibold tracking-wider text-vista-light"
          variants={itemVariants}
        >
          <motion.span
            className="bg-gradient-to-r from-vista-blue via-vista-accent to-vista-blue bg-[length:200%_auto] bg-clip-text text-transparent"
            variants={textFillVariants}
            initial="initial"
            animate="animate"
          >
            StreamVista
          </motion.span>
          <span className="text-vista-light/60"> is loading...</span>
        </motion.h1>

        {/* Optional subtle loading dots */}
        <motion.div
          className="mt-2 flex space-x-1.5"
          variants={itemVariants}
          transition={{ staggerChildren: 0.2 }}
        >
          {[...Array(3)].map((_, i) => (
            <motion.div
              key={i}
              className="h-2 w-2 rounded-full bg-vista-light/40"
              animate={{ y: [0, -4, 0] }}
              transition={{ duration: 0.8, repeat: Infinity, ease: 'easeInOut', delay: i * 0.2 }}
            />
          ))}
        </motion.div>
      </motion.div>
    </motion.div>
  );
}

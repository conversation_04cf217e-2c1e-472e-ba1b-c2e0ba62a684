'use client';

import { Navbar } from '@/components/Navbar';
import Footer from '@/components/Footer';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Smartphone, 
  Tablet, 
  Monitor, 
  Tv, 
  Gamepad2, 
  Laptop, 
  CheckCircle, 
  Download,
  Wifi,
  HardDrive,
  Cpu,
  Eye
} from 'lucide-react';

const deviceCategories = [
  {
    id: 'mobile',
    name: 'Mobile Devices',
    icon: Smartphone,
    devices: [
      {
        name: 'iPhone',
        requirements: 'iOS 14.0 or later',
        features: ['4K HDR', 'Offline Downloads', 'AirPlay', 'Picture-in-Picture'],
        downloadLink: 'https://apps.apple.com/app/streamvista'
      },
      {
        name: 'Android Phone',
        requirements: 'Android 8.0 (API level 26) or later',
        features: ['4K HDR', 'Offline Downloads', 'Chromecast', 'Picture-in-Picture'],
        downloadLink: 'https://play.google.com/store/apps/details?id=com.streamvista'
      }
    ]
  },
  {
    id: 'tablets',
    name: 'Tablets',
    icon: Tablet,
    devices: [
      {
        name: 'iPad',
        requirements: 'iPadOS 14.0 or later',
        features: ['4K HDR', 'Split View', 'Offline Downloads', 'Apple Pencil Support'],
        downloadLink: 'https://apps.apple.com/app/streamvista'
      },
      {
        name: 'Android Tablet',
        requirements: 'Android 8.0 or later with 3GB+ RAM',
        features: ['4K HDR', 'Multi-window', 'Offline Downloads', 'Chromecast'],
        downloadLink: 'https://play.google.com/store/apps/details?id=com.streamvista'
      }
    ]
  },
  {
    id: 'computers',
    name: 'Computers',
    icon: Monitor,
    devices: [
      {
        name: 'Windows PC',
        requirements: 'Windows 10 version 1903 or later',
        features: ['4K HDR', 'Multiple Monitors', 'Keyboard Shortcuts', 'Offline Downloads'],
        downloadLink: 'https://www.microsoft.com/store/apps/streamvista'
      },
      {
        name: 'Mac',
        requirements: 'macOS 10.15 Catalina or later',
        features: ['4K HDR', 'Touch Bar Support', 'AirPlay', 'Offline Downloads'],
        downloadLink: 'https://apps.apple.com/app/streamvista'
      },
      {
        name: 'Chromebook',
        requirements: 'Chrome OS 88 or later',
        features: ['HD Streaming', 'Android App Support', 'Touchscreen Support'],
        downloadLink: 'https://play.google.com/store/apps/details?id=com.streamvista'
      }
    ]
  },
  {
    id: 'tv',
    name: 'Smart TVs & Streaming',
    icon: Tv,
    devices: [
      {
        name: 'Apple TV',
        requirements: 'Apple TV 4K (2nd gen) or later, tvOS 14+',
        features: ['4K HDR10+', 'Dolby Vision', 'Dolby Atmos', 'Siri Remote'],
        downloadLink: 'Available on Apple TV App Store'
      },
      {
        name: 'Roku',
        requirements: 'Roku OS 9.4 or later',
        features: ['4K HDR', 'Voice Remote', 'Private Listening', 'Mobile App Control'],
        downloadLink: 'Available on Roku Channel Store'
      },
      {
        name: 'Amazon Fire TV',
        requirements: 'Fire OS 6 or later',
        features: ['4K HDR', 'Alexa Voice Control', 'X-Ray Features', 'Prime Integration'],
        downloadLink: 'Available on Amazon Appstore'
      },
      {
        name: 'Samsung Smart TV',
        requirements: 'Tizen OS 4.0 or later (2018+ models)',
        features: ['4K HDR10+', 'Samsung Remote', 'Bixby Voice', 'SmartThings'],
        downloadLink: 'Available on Samsung App Store'
      },
      {
        name: 'LG Smart TV',
        requirements: 'webOS 4.5 or later (2019+ models)',
        features: ['4K HDR', 'Magic Remote', 'ThinQ AI', 'Gallery Mode'],
        downloadLink: 'Available on LG Content Store'
      }
    ]
  },
  {
    id: 'gaming',
    name: 'Gaming Consoles',
    icon: Gamepad2,
    devices: [
      {
        name: 'PlayStation 5',
        requirements: 'System software 4.0 or later',
        features: ['4K HDR', 'DualSense Haptics', 'Activity Cards', 'Share Button'],
        downloadLink: 'Available on PlayStation Store'
      },
      {
        name: 'PlayStation 4',
        requirements: 'System software 8.0 or later',
        features: ['4K HDR (Pro)', 'Share Button', 'Remote Play', 'Second Screen'],
        downloadLink: 'Available on PlayStation Store'
      },
      {
        name: 'Xbox Series X|S',
        requirements: 'OS version 10.0.22000 or later',
        features: ['4K HDR', 'Quick Resume', 'Smart Delivery', 'Game Bar'],
        downloadLink: 'Available on Microsoft Store'
      },
      {
        name: 'Xbox One',
        requirements: 'OS version 10.0.19041 or later',
        features: ['4K HDR (X/S)', 'Cortana', 'Game DVR', 'Snap Mode'],
        downloadLink: 'Available on Microsoft Store'
      }
    ]
  }
];

const systemRequirements = {
  internet: {
    icon: Wifi,
    title: 'Internet Connection',
    requirements: [
      'Minimum: 5 Mbps for HD streaming',
      'Recommended: 25 Mbps for 4K streaming',
      'Stable broadband connection required',
      'Wi-Fi 802.11n or Ethernet connection'
    ]
  },
  storage: {
    icon: HardDrive,
    title: 'Storage Space',
    requirements: [
      'App installation: 100MB - 2GB',
      'Offline downloads: 1-5GB per title',
      'Cache storage: 500MB recommended',
      'Available space varies by device'
    ]
  },
  performance: {
    icon: Cpu,
    title: 'Performance',
    requirements: [
      'Minimum: Dual-core 1.5GHz processor',
      'Recommended: Quad-core 2.0GHz+',
      'RAM: 2GB minimum, 4GB+ recommended',
      'Hardware video decoding support'
    ]
  },
  display: {
    icon: Eye,
    title: 'Display',
    requirements: [
      'Minimum resolution: 720p HD',
      'Recommended: 1080p Full HD or higher',
      '4K content requires 4K display',
      'HDR support for enhanced viewing'
    ]
  }
};

export default function DevicesPage() {
  return (
    <div className="min-h-screen bg-vista-dark text-vista-light">
      <Navbar />
      
      {/* Hero Section */}
      <section className="relative py-20 px-4 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-vista-blue/10 to-transparent" />
        <div className="container mx-auto text-center relative z-10">
          <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-vista-light to-vista-blue bg-clip-text text-transparent">
            Supported Devices
          </h1>
          <p className="text-xl md:text-2xl text-vista-light/80 max-w-3xl mx-auto mb-8">
            Stream StreamVista on all your favorite devices. From phones to smart TVs, we've got you covered.
          </p>
          <Badge variant="secondary" className="bg-vista-blue/20 text-vista-blue text-lg px-4 py-2">
            Available on 50+ Device Types
          </Badge>
        </div>
      </section>

      {/* Device Categories */}
      <section className="py-20 px-4">
        <div className="container mx-auto">
          <Tabs defaultValue="mobile" className="w-full">
            <TabsList className="grid w-full grid-cols-5 bg-vista-card border-vista-light/10 mb-8">
              {deviceCategories.map((category) => (
                <TabsTrigger 
                  key={category.id} 
                  value={category.id}
                  className="data-[state=active]:bg-vista-blue data-[state=active]:text-white"
                >
                  <category.icon className="w-4 h-4 mr-2" />
                  <span className="hidden sm:inline">{category.name}</span>
                </TabsTrigger>
              ))}
            </TabsList>

            {deviceCategories.map((category) => (
              <TabsContent key={category.id} value={category.id}>
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {category.devices.map((device, index) => (
                    <Card key={index} className="bg-vista-card border-vista-light/10 hover:border-vista-blue/30 transition-colors">
                      <CardHeader>
                        <CardTitle className="flex items-center gap-3 text-vista-light">
                          <div className="w-10 h-10 bg-vista-blue/20 rounded-full flex items-center justify-center">
                            <category.icon className="w-5 h-5 text-vista-blue" />
                          </div>
                          {device.name}
                        </CardTitle>
                        <p className="text-vista-light/70 text-sm">{device.requirements}</p>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div>
                            <h4 className="text-sm font-semibold text-vista-light mb-2">Features:</h4>
                            <div className="space-y-1">
                              {device.features.map((feature, featureIndex) => (
                                <div key={featureIndex} className="flex items-center gap-2">
                                  <CheckCircle className="w-3 h-3 text-green-400" />
                                  <span className="text-xs text-vista-light/70">{feature}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                          <Button className="w-full bg-vista-blue hover:bg-vista-blue/90" size="sm">
                            <Download className="w-4 h-4 mr-2" />
                            Get App
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>
            ))}
          </Tabs>
        </div>
      </section>

      <Separator className="bg-vista-light/10" />

      {/* System Requirements */}
      <section className="py-20 px-4 bg-vista-card/30">
        <div className="container mx-auto">
          <h2 className="text-4xl md:text-5xl font-bold text-center mb-16 text-vista-light">System Requirements</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {Object.entries(systemRequirements).map(([key, req]) => (
              <Card key={key} className="bg-vista-card border-vista-light/10">
                <CardContent className="p-6">
                  <div className="w-12 h-12 mb-4 bg-vista-blue/20 rounded-full flex items-center justify-center">
                    <req.icon className="w-6 h-6 text-vista-blue" />
                  </div>
                  <h3 className="text-lg font-semibold mb-4 text-vista-light">{req.title}</h3>
                  <ul className="space-y-2">
                    {req.requirements.map((requirement, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <CheckCircle className="w-3 h-3 text-green-400 mt-1 shrink-0" />
                        <span className="text-xs text-vista-light/70">{requirement}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Setup Instructions */}
      <section className="py-20 px-4">
        <div className="container mx-auto max-w-4xl">
          <h2 className="text-4xl md:text-5xl font-bold text-center mb-16 text-vista-light">Quick Setup Guide</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="bg-vista-card border-vista-light/10 text-center">
              <CardContent className="p-6">
                <div className="w-16 h-16 mx-auto mb-4 bg-vista-blue/20 rounded-full flex items-center justify-center">
                  <span className="text-2xl font-bold text-vista-blue">1</span>
                </div>
                <h3 className="text-lg font-semibold mb-3 text-vista-light">Download & Install</h3>
                <p className="text-vista-light/70 text-sm">
                  Download the StreamVista app from your device's app store or visit our website.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-vista-card border-vista-light/10 text-center">
              <CardContent className="p-6">
                <div className="w-16 h-16 mx-auto mb-4 bg-vista-blue/20 rounded-full flex items-center justify-center">
                  <span className="text-2xl font-bold text-vista-blue">2</span>
                </div>
                <h3 className="text-lg font-semibold mb-3 text-vista-light">Sign In</h3>
                <p className="text-vista-light/70 text-sm">
                  Log in with your StreamVista account or create a new one if you're new to our service.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-vista-card border-vista-light/10 text-center">
              <CardContent className="p-6">
                <div className="w-16 h-16 mx-auto mb-4 bg-vista-blue/20 rounded-full flex items-center justify-center">
                  <span className="text-2xl font-bold text-vista-blue">3</span>
                </div>
                <h3 className="text-lg font-semibold mb-3 text-vista-light">Start Streaming</h3>
                <p className="text-vista-light/70 text-sm">
                  Browse our content library and start watching your favorite shows and movies instantly.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Troubleshooting */}
      <section className="py-20 px-4 bg-gradient-to-r from-vista-blue/10 to-vista-accent/10">
        <div className="container mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-8 text-vista-light">Need Help?</h2>
          <p className="text-xl text-vista-light/80 max-w-2xl mx-auto mb-8">
            Having trouble with installation or streaming? Our support team is here to help you get started.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-vista-blue hover:bg-vista-blue/90">
              Contact Support
            </Button>
            <Button size="lg" variant="outline" className="border-vista-light/20 text-vista-light hover:bg-vista-light/10">
              View Help Center
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}

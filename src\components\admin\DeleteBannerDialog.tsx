'use client';

import React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Trash2, Loader2 } from 'lucide-react';

interface DeleteBannerDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  bannerTitle: string;
  isDeleting?: boolean;
}

export default function DeleteBannerDialog({
  isOpen,
  onClose,
  onConfirm,
  bannerTitle,
  isDeleting = false
}: DeleteBannerDialogProps) {
  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent className="bg-vista-dark border-vista-light/20 max-w-md sm:max-w-lg">
        <AlertDialogHeader className="space-y-4">
          <AlertDialogTitle className="text-vista-light flex items-center gap-3 text-lg">
            <Trash2 className="h-6 w-6 text-red-400" />
            Delete Banner Ad
          </AlertDialogTitle>
          <AlertDialogDescription className="text-vista-light/70 text-base leading-relaxed">
            Are you sure you want to delete the banner ad{' '}
            <span className="font-semibold text-vista-light">"{bannerTitle}"</span>?
            <br /><br />
            This action cannot be undone and will permanently remove the banner
            from all display positions.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="flex flex-col sm:flex-row gap-3 pt-6">
          <AlertDialogCancel
            onClick={onClose}
            disabled={isDeleting}
            className="bg-vista-light/10 text-vista-light border-vista-light/20 hover:bg-vista-light/20 px-6 py-2"
          >
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            disabled={isDeleting}
            className="bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 px-6 py-2"
          >
            {isDeleting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Banner
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

import { NextRequest, NextResponse } from 'next/server';
import User from '@/models/User';
import clientPromise, { ensureMongooseConnection } from '@/lib/mongodb';
import mongoose from 'mongoose';
import { UserSession } from '@/lib/types';

// Simple in-memory cache for settings
const settingsCache = new Map<string, { data: any, timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

export async function GET(request: NextRequest) {
  try {
    // Get the user ID from the query parameters
    const userId = request.nextUrl.searchParams.get('userId');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }
    
    // Check cache first
    const cachedSettings = settingsCache.get(userId);
    if (cachedSettings && Date.now() - cachedSettings.timestamp < CACHE_TTL) {
      console.log('Serving settings from cache for userId:', userId);
      return NextResponse.json({
        success: true,
        settings: cachedSettings.data,
        fromCache: true
      });
    }
    
    // Connect to MongoDB
    await ensureMongooseConnection();
    
    // Find the user by ID
    const user = await User.findById(userId);
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    // Prepare settings data
    const settings = {
      preferences: user.preferences || {},
      subscription: user.subscription || {
        plan: 'free',
        status: 'active',
        renewalDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
      }
    };
    
    // Update cache
    settingsCache.set(userId, {
      data: settings,
      timestamp: Date.now()
    });
    
    // Return the user settings
    return NextResponse.json({
      success: true,
      settings
    });
  } catch (error) {
    console.error('Error fetching user settings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user settings' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Parse the request body
    const { userId, settings } = await request.json();
    
    if (!userId || !settings) {
      return NextResponse.json(
        { error: 'User ID and settings are required' },
        { status: 400 }
      );
    }
    
    // Connect to MongoDB
    await ensureMongooseConnection();
    
    // Find the user by ID
    const user = await User.findById(userId);
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    // Update user preferences
    if (settings.preferences) {
      // Use deep merge to avoid overwriting fields that aren't being updated
      user.preferences = {
        ...user.preferences || {},
        ...settings.preferences,
        playback: {
          ...(user.preferences?.playback || {}),
          ...(settings.preferences.playback || {})
        },
        notifications: {
          ...(user.preferences?.notifications || {}),
          ...(settings.preferences.notifications || {})
        },
        privacy: {
          ...(user.preferences?.privacy || {}),
          ...(settings.preferences.privacy || {})
        }
      };
    }
    
    // We don't update subscription here since it's meant to be mock data
    // In a real app, this would be handled by a payment processor webhook

    // Save the updated user
    await user.save();
    
    // Prepare the updated settings
    const updatedSettings = {
      preferences: user.preferences,
      subscription: user.subscription || {
        plan: 'free',
        status: 'active',
        renewalDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
      }
    };
    
    // Update cache
    settingsCache.set(userId, {
      data: updatedSettings,
      timestamp: Date.now()
    });
    
    // Return the updated settings
    return NextResponse.json({
      success: true,
      settings: updatedSettings
    });
  } catch (error) {
    console.error('Error updating user settings:', error);
    return NextResponse.json(
      { error: 'Failed to update user settings' },
      { status: 500 }
    );
  }
} 
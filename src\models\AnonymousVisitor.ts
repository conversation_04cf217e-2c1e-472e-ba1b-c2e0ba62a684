import mongoose, { Schema, Document } from 'mongoose';

export interface IAnonymousVisitor extends Document {
  visitorId: string;
  nickname?: string; // Human-readable nickname for easier identification
  ipAddress?: string;
  userAgent?: string;
  firstVisit: Date;
  lastVisit: Date;
  visitCount: number;
  pagesViewed: number;
  referrer?: string;
  country?: string;
  countryCode?: string;
  region?: string;
  city?: string;
  timezone?: string;
  latitude?: number;
  longitude?: number;
  device?: string;
  browser?: string;
  os?: string;
  convertedToUser?: boolean;
  convertedUserId?: mongoose.Types.ObjectId;
  userId?: string; // String version of user ID for easier reference
  metadata?: Record<string, unknown>;
  fingerprint?: string; // Track previous IDs this visitor had
}

const AnonymousVisitorSchema = new Schema<IAnonymousVisitor>(
  {
    visitorId: {
      type: String,
      required: true,
      unique: true,
      index: true
    },
    fingerprint: {
      type: String
    },
    nickname: {
      type: String,
      index: true
    },
    ipAddress: {
      type: String
    },
    userAgent: {
      type: String
    },
    firstVisit: {
      type: Date,
      default: Date.now,
      index: true
    },
    lastVisit: {
      type: Date,
      default: Date.now,
      index: true
    },
    visitCount: {
      type: Number,
      default: 1
    },
    pagesViewed: {
      type: Number,
      default: 0
    },
    referrer: {
      type: String
    },
    country: {
      type: String
    },
    countryCode: {
      type: String
    },
    region: {
      type: String
    },
    city: {
      type: String
    },
    timezone: {
      type: String
    },
    latitude: {
      type: Number
    },
    longitude: {
      type: Number
    },
    device: {
      type: String
    },
    browser: {
      type: String
    },
    os: {
      type: String
    },
    convertedToUser: {
      type: Boolean,
      default: false,
      index: true
    },
    convertedUserId: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    userId: {
      type: String,
      index: true
    },
    metadata: {
      type: Schema.Types.Mixed
    }
  },
  {
    timestamps: true
  }
);

// Create indexes for common queries
AnonymousVisitorSchema.index({ lastVisit: -1 });
AnonymousVisitorSchema.index({ visitCount: -1 });
AnonymousVisitorSchema.index({ country: 1 });
AnonymousVisitorSchema.index({ device: 1 });
AnonymousVisitorSchema.index({ browser: 1 });
AnonymousVisitorSchema.index({ os: 1 });
AnonymousVisitorSchema.index({ ipAddress: 1 });
AnonymousVisitorSchema.index({ fingerprint: 1 });

// Create or retrieve the model
const AnonymousVisitor = mongoose.models.AnonymousVisitor ||
                         mongoose.model<IAnonymousVisitor>('AnonymousVisitor', AnonymousVisitorSchema);

export default AnonymousVisitor;

'use client';

import { Navbar } from '@/components/Navbar';
import Footer from '@/components/Footer';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Play, Users, Globe, Award, Heart, Zap, Shield, Star } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

const teamMembers = [
  {
    name: "<PERSON>",
    role: "Chief Executive Officer",
    bio: "Former Netflix executive with 15+ years in streaming technology and content strategy.",
    image: "/team/ceo.webp"
  },
  {
    name: "<PERSON>",
    role: "Chief Technology Officer", 
    bio: "Ex-Apple engineer specializing in scalable streaming infrastructure and user experience.",
    image: "/team/cto.webp"
  },
  {
    name: "<PERSON>",
    role: "Head of Content",
    bio: "Award-winning producer with deep connections in Hollywood and international markets.",
    image: "/team/content.webp"
  },
  {
    name: "<PERSON>",
    role: "Head of Product",
    bio: "Product visionary focused on creating intuitive and engaging streaming experiences.",
    image: "/team/product.webp"
  }
];

const stats = [
  { icon: Users, label: "Active Users", value: "50M+" },
  { icon: Globe, label: "Countries", value: "190+" },
  { icon: Play, label: "Hours Watched", value: "2B+" },
  { icon: Award, label: "Awards Won", value: "150+" }
];

const values = [
  {
    icon: Heart,
    title: "User-Centric",
    description: "Every decision we make puts our users first, creating experiences that delight and inspire."
  },
  {
    icon: Zap,
    title: "Innovation",
    description: "We push the boundaries of streaming technology to deliver cutting-edge entertainment."
  },
  {
    icon: Shield,
    title: "Trust & Safety",
    description: "We maintain the highest standards of security and privacy for our global community."
  },
  {
    icon: Star,
    title: "Quality Content",
    description: "We curate and create premium content that resonates with diverse audiences worldwide."
  }
];

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-vista-dark text-vista-light">
      <Navbar />
      
      {/* Hero Section */}
      <section className="relative py-20 px-4 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-vista-blue/10 to-transparent" />
        <div className="container mx-auto text-center relative z-10">
          <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-vista-light to-vista-blue bg-clip-text text-transparent">
            About StreamVista
          </h1>
          <p className="text-xl md:text-2xl text-vista-light/80 max-w-3xl mx-auto mb-8">
            Redefining entertainment through innovative streaming technology and premium content experiences.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/careers">
              <Button size="lg" className="bg-vista-blue hover:bg-vista-blue/90">
                Join Our Team
              </Button>
            </Link>
            <Link href="/contact">
              <Button size="lg" variant="outline" className="border-vista-light/20 text-vista-light hover:bg-vista-light/10">
                Contact Us
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-vista-blue/20 rounded-full flex items-center justify-center">
                  <stat.icon className="w-8 h-8 text-vista-blue" />
                </div>
                <div className="text-3xl md:text-4xl font-bold text-vista-light mb-2">{stat.value}</div>
                <div className="text-vista-light/60">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      <Separator className="bg-vista-light/10" />

      {/* Mission Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl md:text-5xl font-bold mb-6 text-vista-light">Our Mission</h2>
              <p className="text-lg text-vista-light/80 mb-6">
                To democratize premium entertainment by making world-class content accessible to everyone, 
                everywhere. We believe that great stories have the power to connect, inspire, and transform lives.
              </p>
              <p className="text-lg text-vista-light/80 mb-8">
                Through cutting-edge technology and strategic partnerships with creators worldwide, 
                we're building the future of entertainment—one that's more inclusive, innovative, and inspiring.
              </p>
              <Badge variant="secondary" className="bg-vista-blue/20 text-vista-blue border-vista-blue/30">
                Founded in 2020
              </Badge>
            </div>
            <div className="relative">
              <div className="aspect-video bg-vista-card rounded-lg overflow-hidden">
                <div className="w-full h-full bg-gradient-to-br from-vista-blue/20 to-vista-accent/20 flex items-center justify-center">
                  <Play className="w-16 h-16 text-vista-light/60" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 px-4 bg-vista-card/30">
        <div className="container mx-auto">
          <h2 className="text-4xl md:text-5xl font-bold text-center mb-16 text-vista-light">Our Values</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="bg-vista-card border-vista-light/10 hover:border-vista-blue/30 transition-colors">
                <CardContent className="p-6 text-center">
                  <div className="w-12 h-12 mx-auto mb-4 bg-vista-blue/20 rounded-full flex items-center justify-center">
                    <value.icon className="w-6 h-6 text-vista-blue" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-vista-light">{value.title}</h3>
                  <p className="text-vista-light/70">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto">
          <h2 className="text-4xl md:text-5xl font-bold text-center mb-16 text-vista-light">Leadership Team</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {teamMembers.map((member, index) => (
              <Card key={index} className="bg-vista-card border-vista-light/10 hover:border-vista-blue/30 transition-colors">
                <CardContent className="p-6 text-center">
                  <div className="w-24 h-24 mx-auto mb-4 bg-vista-blue/20 rounded-full flex items-center justify-center">
                    <Users className="w-12 h-12 text-vista-blue" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2 text-vista-light">{member.name}</h3>
                  <p className="text-vista-blue mb-3">{member.role}</p>
                  <p className="text-sm text-vista-light/70">{member.bio}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-vista-blue/10 to-vista-accent/10">
        <div className="container mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-vista-light">Ready to Join the Future?</h2>
          <p className="text-xl text-vista-light/80 max-w-2xl mx-auto mb-8">
            Experience premium streaming like never before. Join millions of users worldwide who trust StreamVista.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/">
              <Button size="lg" className="bg-vista-blue hover:bg-vista-blue/90">
                Start Watching
              </Button>
            </Link>
            <Link href="/careers">
              <Button size="lg" variant="outline" className="border-vista-light/20 text-vista-light hover:bg-vista-light/10">
                Work With Us
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}

'use client';

import { WatchPartyProvider } from '@/lib/WatchPartyContext';
import { ReactNode, useEffect, useState } from 'react';

interface WatchPartyWrapperProps {
  children: ReactNode;
}

export function WatchPartyWrapper({ children }: WatchPartyWrapperProps) {
  const [isPusherEnabled, setIsPusherEnabled] = useState(true);
  const [hasCheckedPusher, setHasCheckedPusher] = useState(false);

  useEffect(() => {
    // Check if we have a Pusher key configured
    const pusherKey = process.env.NEXT_PUBLIC_PUSHER_KEY;

    // Ensure Pusher is properly set up
    if (pusherKey) {
      console.log('Pusher is configured with key detected');
      setIsPusherEnabled(true);
    } else {
      console.warn('No Pusher key found in environment variables. Watch party features may be limited.');
      setIsPusherEnabled(false);
    }

    setHasCheckedPusher(true);
  }, []);

  // Only render the provider once we've determined whether Pusher is enabled
  if (!hasCheckedPusher) {
    return null; // Return empty while checking
  }

  return (
    <WatchPartyProvider>
      {children}
    </WatchPartyProvider>
  );
}
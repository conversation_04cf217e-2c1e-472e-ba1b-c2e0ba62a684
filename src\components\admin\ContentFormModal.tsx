'use client';

import { useState } from 'react';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import { Loader2, Film, Tv } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import Image from 'next/image';

interface ContentFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  editContent?: {
    id: string;
    title: string;
    type: 'movie' | 'tv';
    overview: string;
    releaseDate?: string;
    posterPath?: string;
    backdropPath?: string;
    genres?: string[];
    status?: string;
    featured?: boolean;
  };
}

export default function ContentFormModal({ 
  isOpen, 
  onClose, 
  onSuccess, 
  editContent 
}: ContentFormModalProps) {
  const isEditing = !!editContent;
  
  // Form state
  const [formData, setFormData] = useState({
    title: editContent?.title || '',
    type: editContent?.type || 'movie',
    overview: editContent?.overview || '',
    releaseDate: editContent?.releaseDate || '',
    posterPath: editContent?.posterPath || '',
    backdropPath: editContent?.backdropPath || '',
    genres: editContent?.genres || [],
    status: editContent?.status || 'published',
    featured: editContent?.featured || false
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [activeTab, setActiveTab] = useState<string>('basic');

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error for this field when user types
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle checkbox changes
  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }
    
    if (!formData.overview.trim()) {
      newErrors.overview = 'Overview is required';
    }
    
    if (!formData.type) {
      newErrors.type = 'Content type is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Prepare data for API
      const apiData = {
        ...formData
      };
      
      // Determine API endpoint and method
      const url = isEditing 
        ? `/api/admin/content/${editContent.id}` 
        : '/api/admin/content';
      const method = isEditing ? 'PUT' : 'POST';
      
      // Make API request
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(apiData),
        credentials: 'include'
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save content');
      }
      
      // Show success message
      toast({
        title: isEditing ? 'Content Updated' : 'Content Created',
        description: isEditing 
          ? `"${formData.title}" has been updated successfully.` 
          : `"${formData.title}" has been added to the system.`,
        variant: 'success'
      });
      
      // Close modal and refresh content list
      onSuccess();
      onClose();
      
    } catch (error) {
      console.error('Error saving content:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Available genres
  const availableGenres = [
    'Action', 'Adventure', 'Animation', 'Comedy', 'Crime', 'Documentary',
    'Drama', 'Family', 'Fantasy', 'History', 'Horror', 'Music', 'Mystery',
    'Romance', 'Science Fiction', 'Thriller', 'War', 'Western'
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{isEditing ? 'Edit Content' : 'Add New Content'}</DialogTitle>
          <DialogDescription>
            {isEditing 
              ? 'Update content details and metadata.' 
              : 'Add new content to the platform.'}
          </DialogDescription>
        </DialogHeader>
        
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="basic">Basic Info</TabsTrigger>
            <TabsTrigger value="media">Media</TabsTrigger>
            <TabsTrigger value="metadata">Metadata</TabsTrigger>
          </TabsList>
          
          <form onSubmit={handleSubmit}>
            <TabsContent value="basic" className="space-y-4 py-4">
              <div className="grid grid-cols-1 gap-4">
                {/* Title */}
                <div className="space-y-2">
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleChange}
                    placeholder="Enter content title"
                    className={errors.title ? 'border-red-500' : ''}
                  />
                  {errors.title && (
                    <p className="text-sm text-red-500">{errors.title}</p>
                  )}
                </div>
                
                {/* Content Type */}
                <div className="space-y-2">
                  <Label htmlFor="type">Content Type</Label>
                  <Select
                    value={formData.type}
                    onValueChange={(value) => handleSelectChange('type', value)}
                  >
                    <SelectTrigger id="type">
                      <SelectValue placeholder="Select content type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="movie">Movie</SelectItem>
                      <SelectItem value="tv">TV Show</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.type && (
                    <p className="text-sm text-red-500">{errors.type}</p>
                  )}
                </div>
                
                {/* Overview */}
                <div className="space-y-2">
                  <Label htmlFor="overview">Overview</Label>
                  <Textarea
                    id="overview"
                    name="overview"
                    value={formData.overview}
                    onChange={handleChange}
                    placeholder="Enter content description"
                    className={`min-h-[120px] ${errors.overview ? 'border-red-500' : ''}`}
                  />
                  {errors.overview && (
                    <p className="text-sm text-red-500">{errors.overview}</p>
                  )}
                </div>
                
                {/* Release Date */}
                <div className="space-y-2">
                  <Label htmlFor="releaseDate">Release Date</Label>
                  <Input
                    id="releaseDate"
                    name="releaseDate"
                    type="date"
                    value={formData.releaseDate}
                    onChange={handleChange}
                    className={errors.releaseDate ? 'border-red-500' : ''}
                  />
                  {errors.releaseDate && (
                    <p className="text-sm text-red-500">{errors.releaseDate}</p>
                  )}
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="media" className="space-y-4 py-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Poster Image */}
                <div className="space-y-2">
                  <Label htmlFor="posterPath">Poster Image URL</Label>
                  <Input
                    id="posterPath"
                    name="posterPath"
                    value={formData.posterPath}
                    onChange={handleChange}
                    placeholder="Enter poster image URL"
                    className={errors.posterPath ? 'border-red-500' : ''}
                  />
                  {errors.posterPath && (
                    <p className="text-sm text-red-500">{errors.posterPath}</p>
                  )}
                  
                  {formData.posterPath && (
                    <div className="mt-4 relative aspect-[2/3] w-full max-w-[200px] mx-auto overflow-hidden rounded-md border border-vista-light/20">
                      <Image
                        src={formData.posterPath}
                        alt="Poster preview"
                        fill
                        className="object-cover"
                        onError={(e) => {
                          e.currentTarget.src = 'https://via.placeholder.com/300x450?text=No+Image';
                        }}
                      />
                    </div>
                  )}
                </div>
                
                {/* Backdrop Image */}
                <div className="space-y-2">
                  <Label htmlFor="backdropPath">Backdrop Image URL</Label>
                  <Input
                    id="backdropPath"
                    name="backdropPath"
                    value={formData.backdropPath}
                    onChange={handleChange}
                    placeholder="Enter backdrop image URL"
                    className={errors.backdropPath ? 'border-red-500' : ''}
                  />
                  {errors.backdropPath && (
                    <p className="text-sm text-red-500">{errors.backdropPath}</p>
                  )}
                  
                  {formData.backdropPath && (
                    <div className="mt-4 relative aspect-video w-full max-w-[300px] mx-auto overflow-hidden rounded-md border border-vista-light/20">
                      <Image
                        src={formData.backdropPath}
                        alt="Backdrop preview"
                        fill
                        className="object-cover"
                        onError={(e) => {
                          e.currentTarget.src = 'https://via.placeholder.com/1280x720?text=No+Image';
                        }}
                      />
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="metadata" className="space-y-4 py-4">
              <div className="grid grid-cols-1 gap-4">
                {/* Genres */}
                <div className="space-y-2">
                  <Label htmlFor="genres">Genres</Label>
                  <Select
                    value={formData.genres[0] || ''}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, genres: [value] }))}
                  >
                    <SelectTrigger id="genres">
                      <SelectValue placeholder="Select primary genre" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableGenres.map(genre => (
                        <SelectItem key={genre} value={genre}>{genre}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                {/* Status */}
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) => handleSelectChange('status', value)}
                  >
                    <SelectTrigger id="status">
                      <SelectValue placeholder="Select content status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="published">Published</SelectItem>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="scheduled">Scheduled</SelectItem>
                      <SelectItem value="archived">Archived</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                {/* Featured */}
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="featured"
                    checked={formData.featured}
                    onChange={(e) => handleCheckboxChange('featured', e.target.checked)}
                    className="h-4 w-4 rounded border-vista-light/20 text-vista-blue focus:ring-vista-blue"
                  />
                  <Label htmlFor="featured">Feature on homepage</Label>
                </div>
              </div>
            </TabsContent>
            
            <DialogFooter className="mt-6 pt-4 border-t border-vista-light/10">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isEditing ? 'Update Content' : 'Create Content'}
              </Button>
            </DialogFooter>
          </form>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}

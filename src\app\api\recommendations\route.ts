import { NextRequest, NextResponse } from 'next/server';
import { fetchFromTMDB } from '@/lib/tmdb';

/**
 * API route for content recommendations
 * Fetches recommendations for similar content based on TMDb ID
 */
export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const id = searchParams.get('id');
  const type = searchParams.get('type') || 'movie';

  if (!id) {
    return NextResponse.json(
      { error: 'Missing required parameter: id' },
      { status: 400 }
    );
  }

  try {
    // Determine the proper endpoint based on content type
    const endpoint = type === 'movie' 
      ? `movie/${id}/recommendations` 
      : `tv/${id}/recommendations`;
    
    const data = await fetchFromTMDB(endpoint);
    
    if (!data || !data.results) {
      console.log('No recommendations found or invalid response format');
      return NextResponse.json([]);
    }

    // Map TMDb results to our format
    const recommendations = data.results.map((item: any) => ({
      id: item.id,
      tmdbId: item.id,
      title: type === 'movie' ? item.title : item.name,
      overview: item.overview,
      posterPath: item.poster_path 
        ? `https://image.tmdb.org/t/p/w300${item.poster_path}`
        : null,
      backdropPath: item.backdrop_path
        ? `https://image.tmdb.org/t/p/w1280${item.backdrop_path}`
        : null,
      year: type === 'movie' 
        ? item.release_date ? new Date(item.release_date).getFullYear() : null
        : item.first_air_date ? new Date(item.first_air_date).getFullYear() : null,
      rating: item.vote_average,
      type: type === 'movie' ? 'movie' : 'show'
    }));

    return NextResponse.json(recommendations);
  } catch (error) {
    console.error('Error fetching recommendations:', error);
    return NextResponse.json(
      { error: 'Failed to fetch recommendations' },
      { status: 500 }
    );
  }
} 
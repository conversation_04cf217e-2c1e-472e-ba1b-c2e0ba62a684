"use client"

import React, { useCallback, useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { motion } from 'framer-motion'
import { AlertCircle, Upload, File, Video, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { useContentUpload } from './ContentUploadProvider'

export function FileUploadStep() {
  const { uploadData, updateFileUpload } = useContentUpload()
  const { fileUpload } = uploadData
  const [error, setError] = useState<string | null>(null)

  // Define accepted file types and size limit (500MB)
  const maxSize = 500 * 1024 * 1024
  const acceptedFileTypes = {
    'video/mp4': ['.mp4'],
    'video/quicktime': ['.mov'],
    'video/x-msvideo': ['.avi'],
    'video/webm': ['.webm']
  }

  // Define the FileRejection type explicitly
  interface FileRejection {
    file: File;
    errors: Array<{
      code: string;
      message: string;
    }>;
  }

  // Handle file drop
  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return

    const file = acceptedFiles[0]
    setError(null)

    // Create a preview URL
    const previewUrl = URL.createObjectURL(file)

    // Update the file upload data
    updateFileUpload({
      file,
      preview: previewUrl,
      progress: 0
    })
  }, [updateFileUpload])

  // Handle file validation
  const onDropRejected = useCallback((fileRejections: FileRejection[]) => {
    const { errors } = fileRejections[0]
    if (errors[0]?.code === 'file-too-large') {
      setError(`File is too large. Maximum size is ${maxSize / (1024 * 1024)}MB`)
    } else if (errors[0]?.code === 'file-invalid-type') {
      setError('Invalid file type. Please upload a video file (MP4, MOV, AVI, WEBM)')
    } else {
      setError(errors[0]?.message || 'Error uploading file')
    }
  }, [maxSize])

  // Set up dropzone
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    onDropRejected,
    accept: acceptedFileTypes,
    maxSize,
    multiple: false
  })

  // Function to remove the uploaded file
  const removeFile = () => {
    // Revoke the object URL to avoid memory leaks
    if (fileUpload.preview) {
      URL.revokeObjectURL(fileUpload.preview)
    }

    updateFileUpload({
      file: null,
      preview: null,
      progress: 0
    })
    setError(null)
  }

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h2 className="text-xl font-semibold text-vista-light">Upload Content</h2>
        <p className="text-vista-light/70">
          Select a video file to upload. You can drag and drop the file or click to browse your files.
        </p>
      </div>

      {!fileUpload.file ? (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div
            {...getRootProps()}
            className={`
              border-2 border-dashed rounded-lg p-8
              flex flex-col items-center justify-center
              min-h-[250px] cursor-pointer transition-colors
              ${isDragActive
                ? 'border-vista-blue bg-vista-blue/5'
                : 'border-vista-light/20 hover:border-vista-light/40'}
              ${error ? 'border-red-500 bg-red-500/5' : ''}
            `}
          >
            <input {...getInputProps()} />

            <div className="flex flex-col items-center text-center">
              {isDragActive ? (
                <>
                  <Video className="w-16 h-16 text-vista-blue mb-4" />
                  <p className="text-vista-light mb-2 font-medium">Drop your video file here</p>
                </>
              ) : (
                <>
                  <Upload className="w-12 h-12 text-vista-light/50 mb-4" />
                  <p className="text-vista-light mb-2 font-medium">Drag and drop your video file here</p>
                  <p className="text-vista-light/60 text-sm">or click to browse</p>

                  <div className="mt-4 text-vista-light/50 text-sm">
                    <p>MP4, MOV, AVI, WEBM up to 500MB</p>
                  </div>
                </>
              )}
            </div>
          </div>

          {error && (
            <div className="mt-2 text-red-500 flex items-center gap-2 text-sm">
              <AlertCircle className="w-4 h-4" />
              <span>{error}</span>
            </div>
          )}
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-vista-dark-lighter rounded-lg p-4"
        >
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-3">
              <div className="bg-vista-blue/10 p-2 rounded">
                <File className="w-6 h-6 text-vista-blue" />
              </div>
              <div>
                <p className="font-medium text-vista-light truncate max-w-[300px]">
                  {fileUpload.file.name}
                </p>
                <p className="text-vista-light/60 text-sm">
                  {(fileUpload.file.size / (1024 * 1024)).toFixed(2)} MB
                </p>
              </div>
            </div>

            <Button
              variant="ghost"
              size="icon"
              className="rounded-full text-vista-light/60 hover:text-red-500"
              onClick={removeFile}
            >
              <X className="w-5 h-5" />
            </Button>
          </div>

          {/* Video preview if available */}
          {fileUpload.preview && (
            <div className="my-3 aspect-video bg-black rounded overflow-hidden">
              <video
                src={fileUpload.preview}
                controls
                className="w-full h-full object-contain"
              />
            </div>
          )}

          {/* Progress indicator (only shown when actually uploading) */}
          {fileUpload.progress > 0 && (
            <div className="mt-4">
              <Progress
                value={fileUpload.progress}
                max={100}
                className="h-2"
                showValue={true}
                formatValue={(v) => `${Math.round(v)}%`}
              />
            </div>
          )}
        </motion.div>
      )}
    </div>
  )
}

'use client';

import React, { useState, useMemo, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { CardContent } from '@/components/ui/card';
import { Film, Tv, Eye, TrendingUp, Timer, ArrowUpDown, Search, CalendarDays } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Skeleton } from '@/components/ui/skeleton';
import Image from 'next/image';

// Define a type that can handle both our component's data format and the dashboard's format
type SortKey = keyof Pick<ContentItem, 'title' | 'type' | 'views' | 'rating' | 'avgProgress' | 'completionRate'>;

interface ContentItem {
  id: string;
  title: string;
  type: 'movie' | 'show' | 'tv'; // Include 'tv' for dashboard compatibility
  views: number;
  rating?: number;
  watchTime?: number;
  posterPath?: string;
  avgProgress?: number;
  completionRate?: number;
  releaseDate?: string;
}

interface PopularContentTableProps {
  data: ContentItem[];
  isLoading?: boolean;
  onContentClick?: (id: string, type: string) => void;
}

export function PopularContentTable({ 
  data = [], 
  isLoading = false,
  onContentClick
}: PopularContentTableProps) {
  const router = useRouter();
  const [sortColumn, setSortColumn] = useState<SortKey>('views');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'movie' | 'show'>('all');
  const [initialDataLoaded, setInitialDataLoaded] = useState(false);

  // Mark when we first get data
  useEffect(() => {
    if (data.length > 0 && !initialDataLoaded) {
      setInitialDataLoaded(true);
    }
  }, [data, initialDataLoaded]);

  // Process the data to ensure it has all required fields
  const processedData = useMemo(() => data.map(item => ({
    ...item,
    // Normalize type field
    type: item.type === 'tv' ? 'show' as const : item.type,
    // Use real data or reasonable defaults
    rating: item.rating ?? 0, // Default rating of 0 if not provided
    watchTime: item.watchTime ?? 0, // Default watch time of 0 if not provided
    avgProgress: item.avgProgress ?? 0, // Default progress of 0% if not provided
    completionRate: item.completionRate ?? 0 // Default completion rate of 0% if not provided
  })), [data]);

  // Filter data based on search term and content type
  const filteredData = useMemo(() => {
    return processedData.filter(item => {
      const matchesSearch = searchTerm === '' || 
        item.title.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesType = filterType === 'all' || item.type === filterType;
      
      return matchesSearch && matchesType;
    });
  }, [processedData, searchTerm, filterType]);

  // Sorting logic based on state
  const sortedData = useMemo(() => {
    return [...filteredData].sort((a, b) => {
      const aValue = a[sortColumn];
      const bValue = b[sortColumn];

      let comparison = 0;
      if (aValue === undefined || bValue === undefined) {
        comparison = aValue === undefined ? 1 : -1; // Handle undefined values
      } else if (typeof aValue === 'string' && typeof bValue === 'string') {
        comparison = aValue.localeCompare(bValue);
      } else if (typeof aValue === 'number' && typeof bValue === 'number') {
        comparison = aValue - bValue;
      } else {
        // Fallback for mixed types or other types (e.g., type)
        comparison = String(aValue).localeCompare(String(bValue));
      }

      return sortDirection === 'asc' ? comparison : -comparison;
    });
  }, [filteredData, sortColumn, sortDirection]);

  // Handle sort click
  const handleSort = (column: SortKey) => {
    if (sortColumn === column) {
      setSortDirection(prev => (prev === 'asc' ? 'desc' : 'asc'));
    } else {
      setSortColumn(column);
      setSortDirection('desc'); // Default to descending for new column
    }
  };

  // Handle content click
  const handleContentClick = (id: string, type: string) => {
    if (onContentClick) {
      onContentClick(id, type);
      return;
    }
    
    // Default behavior - navigate to content page
    router.push(`/admin/content/${id}`);
  };

  // Calculate average rating for data set (for stats)
  const averageRating = useMemo(() => {
    if (filteredData.length === 0) return 0;
    const sum = filteredData.reduce((acc, item) => acc + (item.rating || 0), 0);
    return (sum / filteredData.length).toFixed(1);
  }, [filteredData]);

  // Helper to render sortable header
  const renderSortableHeader = (column: SortKey, label: string, className?: string) => (
    <TableHead
      className={cn("cursor-pointer hover:bg-vista-dark/50", className)}
      onClick={() => handleSort(column)}
    >
      <div className="flex items-center gap-1">
        {label}
        {sortColumn === column ? (
          <ArrowUpDown className={`h-3 w-3 ${sortDirection === 'asc' ? 'rotate-180' : ''}`} />
        ) : (
          <ArrowUpDown className="h-3 w-3 opacity-30" />
        )}
      </div>
    </TableHead>
  );

  // Render content poster with fallback
  const renderPoster = (item: ContentItem) => {
    if (!item.posterPath) {
      return (
        <div className="w-8 h-12 bg-vista-dark-lighter rounded flex items-center justify-center">
          {item.type === 'movie' ? (
            <Film className="w-4 h-4 text-vista-light/50" />
          ) : (
            <Tv className="w-4 h-4 text-vista-light/50" />
          )}
        </div>
      );
    }

    return (
      <div className="relative w-8 h-12 rounded overflow-hidden">
        <Image
          src={item.posterPath}
          alt={item.title}
          fill
          sizes="32px"
          className="object-cover"
          onError={(e) => {
            // On error, replace with icon
            e.currentTarget.style.display = 'none';
            const parent = e.currentTarget.parentElement;
            if (parent) {
              parent.innerHTML = `<div class="w-full h-full flex items-center justify-center bg-vista-dark-lighter">
                ${item.type === 'movie' ? 
                  '<svg class="w-4 h-4 text-vista-light/50" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19.82 2H4.18A2.18 2.18 0 0 0 2 4.18v15.64A2.18 2.18 0 0 0 4.18 22h15.64A2.18 2.18 0 0 0 22 19.82V4.18A2.18 2.18 0 0 0 19.82 2Z"></path><path d="M7 2v20"></path><path d="M17 2v20"></path><path d="M2 12h20"></path><path d="M2 7h5"></path><path d="M2 17h5"></path><path d="M17 17h5"></path><path d="M17 7h5"></path></svg>' : 
                  '<svg class="w-4 h-4 text-vista-light/50" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="7" width="20" height="15" rx="2" ry="2"></rect><polyline points="17 2 12 7 7 2"></polyline></svg>'
                }
              </div>`;
            }
          }}
        />
      </div>
    );
  };

  return (
    <CardContent className="p-0">
      <div className="p-4 flex flex-col gap-4">
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-between gap-2">
          <div className="relative">
            <Search className="absolute left-2 top-1/2 -translate-y-1/2 h-4 w-4 text-vista-light/50" />
            <Input
              placeholder="Search content..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8 w-full sm:w-[250px] bg-vista-dark-lighter"
            />
          </div>
          
          <div className="flex items-center gap-2">
            <Select value={filterType} onValueChange={(value) => setFilterType(value as 'all' | 'movie' | 'show')}>
              <SelectTrigger className="w-[130px]">
                <SelectValue placeholder="All types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All types</SelectItem>
                <SelectItem value="movie">Movies</SelectItem>
                <SelectItem value="show">TV Shows</SelectItem>
              </SelectContent>
            </Select>
            
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    variant="outline" 
                    size="icon"
                    className="rounded-full w-8 h-8"
                    onClick={() => {
                      setSearchTerm('');
                      setFilterType('all');
                      setSortColumn('views');
                      setSortDirection('desc');
                    }}
                  >
                    <ArrowUpDown className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Reset filters & sorting</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
        
        {filteredData.length > 0 && (
          <div className="flex flex-wrap gap-2 text-xs text-vista-light/70">
            <div className="flex items-center gap-1 bg-vista-dark-lighter px-2 py-1 rounded">
              <Eye className="h-3 w-3" />
              <span>{filteredData.reduce((acc, item) => acc + item.views, 0).toLocaleString()} total views</span>
            </div>
            
            {Number(averageRating) > 0 && (
              <div className="flex items-center gap-1 bg-vista-dark-lighter px-2 py-1 rounded">
                <div className={`h-2 w-2 rounded-full ${getRatingColorClass(Number(averageRating))}`}></div>
                <span>Avg rating: {averageRating}</span>
              </div>
            )}
            
            <div className="flex items-center gap-1 bg-vista-dark-lighter px-2 py-1 rounded">
              <span>{filteredData.length} item{filteredData.length !== 1 ? 's' : ''}</span>
            </div>
          </div>
        )}
      </div>
      
      <div className="overflow-auto">
        <Table className="min-w-full">
          <TableHeader>
            <TableRow className="hover:bg-transparent">
              {renderSortableHeader('title', 'Title', 'w-[30%]')}
              {renderSortableHeader('type', 'Type')}
              {renderSortableHeader('views', 'Views')}
              {renderSortableHeader('avgProgress', 'Progress')}
              {renderSortableHeader('completionRate', 'Completion')}
              {renderSortableHeader('rating', 'Rating')}
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading || (!initialDataLoaded && data.length === 0) ? (
              // Loading skeletons
              Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={`skeleton-${index}`}>
                  <TableCell><Skeleton className="h-6 w-full max-w-[200px]" /></TableCell>
                  <TableCell><Skeleton className="h-6 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-6 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-6 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-6 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-6 w-16" /></TableCell>
                </TableRow>
              ))
            ) : sortedData.length > 0 ? (
              sortedData.map((item) => (
                <TableRow
                  key={item.id}
                  className="hover:bg-vista-dark cursor-pointer"
                  onClick={() => handleContentClick(item.id, item.type)}
                >
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-3">
                      {renderPoster(item)}
                      <div>
                        <div className="flex items-center gap-2">
                          {item.title}
                          {item.views > 1000 && (
                            <Badge variant="outline" className="ml-1 bg-vista-blue/10 text-vista-blue text-[10px]">
                              <TrendingUp className="h-3 w-3 mr-1" />
                              Trending
                            </Badge>
                          )}
                        </div>
                        {item.releaseDate && (
                          <div className="text-xs text-vista-light/50 flex items-center mt-1">
                            <CalendarDays className="h-3 w-3 mr-1" />
                            {new Date(item.releaseDate).getFullYear()}
                          </div>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={item.type === 'movie' ? 'default' : 'secondary'}>
                      {item.type}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Eye className="h-3 w-3 text-vista-light/50" />
                      {item.views.toLocaleString()}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <div className="w-16 h-1 bg-vista-dark-lighter rounded-full overflow-hidden">
                        <div
                          className="h-full bg-vista-blue"
                          style={{ width: `${item.avgProgress}%` }}
                        />
                      </div>
                      <span className="ml-2 text-xs">{item.avgProgress}%</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <div className="w-16 h-1 bg-vista-dark-lighter rounded-full overflow-hidden">
                        <div
                          className={`h-full ${getCompletionColor(item.completionRate)}`}
                          style={{ width: `${item.completionRate}%` }}
                        />
                      </div>
                      <span className="ml-2 text-xs">{item.completionRate}%</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      {item.rating ? (
                        <>
                          <div className="w-8 h-1 bg-vista-dark-lighter rounded-full overflow-hidden">
                            <div
                              className={`h-full ${getRatingColor(item.rating)}`}
                              style={{ width: `${(item.rating / 10) * 100}%` }}
                            />
                          </div>
                          <span className="ml-2 text-xs">{item.rating.toFixed(1)}</span>
                        </>
                      ) : (
                        <span className="text-xs text-vista-light/50">N/A</span>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8 text-vista-light/50">
                  {searchTerm || filterType !== 'all' ? (
                    <div className="flex flex-col items-center gap-2">
                      <p>No content matches your filters</p>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => {
                          setSearchTerm('');
                          setFilterType('all');
                        }}
                      >
                        Clear filters
                      </Button>
                    </div>
                  ) : (
                    "No content data available"
                  )}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </CardContent>
  );
}

// Helper functions
function getRatingColor(rating: number): string {
  if (rating >= 8) return 'bg-green-500';
  if (rating >= 6) return 'bg-yellow-500';
  if (rating >= 4) return 'bg-orange-500';
  return 'bg-red-500';
}

function getRatingColorClass(rating: number): string {
  if (rating >= 8) return 'bg-green-500';
  if (rating >= 6) return 'bg-yellow-500';
  if (rating >= 4) return 'bg-orange-500';
  return 'bg-red-500';
}

function getCompletionColor(rate: number): string {
  if (rate >= 80) return 'bg-green-500';
  if (rate >= 60) return 'bg-green-400';
  if (rate >= 40) return 'bg-yellow-500';
  if (rate >= 20) return 'bg-orange-500';
  return 'bg-red-500';
}

function formatWatchTime(minutes: number): string {
  if (minutes < 60) {
    return `${minutes}m`;
  }
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
}

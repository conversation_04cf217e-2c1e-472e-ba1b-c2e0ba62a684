import { getTMDbImageUrl } from './tmdb-api';
import { isBrowser, safeWindow } from './browser-utils';

// Cache for storing retrieved image URLs to minimize API calls
// Using a more robust cache with timestamps to allow refreshing after a period
interface CacheEntry {
  url: string;
  timestamp: number;
}

const imageCache = new Map<string, CacheEntry>();
const CACHE_TTL = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

/**
 * Get an actor's or crew member's profile image from multiple sources
 * Falls back through different providers to ensure maximum image availability
 */
export async function getPersonImage(
  personId: string | number,
  personName: string,
  profilePath: string | null,
  gender?: number
): Promise<string | null> {
  const cacheKey = `person-${personId}`;
  const now = Date.now();

  // Return cached result if available and not expired
  const cachedEntry = imageCache.get(cacheKey);
  if (cachedEntry && (now - cachedEntry.timestamp < CACHE_TTL)) {
    console.log(`[getPersonImage] Using cached image for ${personName} (ID: ${personId})`);
    return cachedEntry.url;
  }

  // First try TMDb image if available (fastest and most reliable)
  if (profilePath) {
    // Use a larger size for better quality
    const tmdbImageUrl = getTMDbImageUrl(profilePath, 'w342');
    console.log(`[getPersonImage] Using TMDb image for ${personName}: ${profilePath}`);
    imageCache.set(cacheKey, { url: tmdbImageUrl, timestamp: now });
    return tmdbImageUrl;
  }

  try {
    console.log(`[getPersonImage] No profile path for ${personName}, trying alternative sources...`);

    // Option 1: Try fetching extra details from TMDb person API
    console.log(`[getPersonImage] Trying TMDb person API for ${personName} (ID: ${personId})`);
    const enhancedResult = await fetchEnhancedPersonDetails(personId);

    if (enhancedResult?.profile_path) {
      const tmdbEnhancedUrl = getTMDbImageUrl(enhancedResult.profile_path, 'w342');
      console.log(`[getPersonImage] Found enhanced TMDb image for ${personName}: ${enhancedResult.profile_path}`);
      imageCache.set(cacheKey, { url: tmdbEnhancedUrl, timestamp: now });
      return tmdbEnhancedUrl;
    }

    // Option 2: Search external APIs (like Wikipedia)
    console.log(`[getPersonImage] Trying Wikipedia/WikiData for ${personName}`);
    const wikiResult = await searchWikiData(personName);

    if (wikiResult) {
      console.log(`[getPersonImage] Found wiki image for ${personName}`);

      // For Wikimedia URLs, use our proxy API to avoid CORS issues
      if (wikiResult.includes('wikimedia.org')) {
        // Store the original URL in the cache
        imageCache.set(cacheKey, { url: wikiResult, timestamp: now });

        // But return a URL that goes through our proxy
        console.log(`[getPersonImage] Using wiki-image API proxy for Wikimedia URL`);
        return wikiResult;
      }

      // For other URLs, use them directly
      imageCache.set(cacheKey, { url: wikiResult, timestamp: now });
      return wikiResult;
    }

    console.log(`[getPersonImage] No images found for ${personName}, generating placeholder`);

    // Option 3: Generate a placeholder image based on gender
    // This creates a consistent image for each person based on their name
    const colorIndex = personName.charCodeAt(0) % 360;
    const placeholder = generatePersonPlaceholder(colorIndex, gender);
    imageCache.set(cacheKey, { url: placeholder, timestamp: now });
    return placeholder;

  } catch (error) {
    console.error(`[getPersonImage] Error fetching image for ${personName} (ID: ${personId}):`, error);
    // Fallback to null which will trigger the initials display
    return null;
  }
}

/**
 * Fetch enhanced details from TMDb person API
 */
async function fetchEnhancedPersonDetails(personId: string | number) {
  try {
    console.log(`[fetchEnhancedPersonDetails] Fetching details for person ID: ${personId}`);
    const res = await fetch(`/api/person/${personId}`, {
      // Add cache control headers to prevent browser caching
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

    if (!res.ok) {
      console.warn(`[fetchEnhancedPersonDetails] API returned ${res.status} for person ID: ${personId}`);
      return null;
    }

    const data = await res.json();
    console.log(`[fetchEnhancedPersonDetails] Got data for ${data.name}, has profile: ${!!data.profile_path}`);
    return data;
  } catch (error) {
    console.error('[fetchEnhancedPersonDetails] Error:', error);
    return null;
  }
}

/**
 * Search for person image on WikiData/Wikipedia
 */
async function searchWikiData(personName: string): Promise<string | null> {
  try {
    const encodedName = encodeURIComponent(personName);
    const url = `/api/wiki-image?name=${encodedName}`;
    console.log(`[searchWikiData] Searching for ${personName}`);

    const res = await fetch(url, {
      // Add cache control headers to prevent browser caching
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

    if (!res.ok) {
      console.warn(`[searchWikiData] API returned ${res.status} for ${personName}`);
      return null;
    }

    const data = await res.json();
    console.log(`[searchWikiData] Result for ${personName}: ${data.imageUrl ? 'Found image' : 'No image found'}`);
    return data.imageUrl || null;
  } catch (error) {
    console.error(`[searchWikiData] Error for ${personName}:`, error);
    return null;
  }
}

/**
 * Generate a placeholder image based on the person's name and gender
 */
function generatePersonPlaceholder(hue: number, gender?: number): string {
  // Adjust saturation and lightness based on gender for subtle differences
  // TMDb uses: 0 = Not specified, 1 = Female, 2 = Male
  const saturation = gender === 1 ? 80 : gender === 2 ? 70 : 75;
  const lightness = gender === 1 ? 65 : gender === 2 ? 60 : 62;

  // Create a HSL color
  const bgColor = `hsl(${hue}, ${saturation}%, ${lightness}%)`;
  const textColor = '#ffffff';

  // Create an SVG placeholder with person silhouette
  const svg = `
  <svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">
    <rect width="200" height="200" fill="${bgColor}" />
    <circle cx="100" cy="70" r="40" fill="${textColor}" opacity="0.9" />
    <rect x="60" y="120" width="80" height="60" rx="10" fill="${textColor}" opacity="0.9" />
  </svg>
  `;

  // Convert SVG to a data URL
  // Use Buffer for Node.js environments (when available) or safe fallback
  let base64;
  try {
    // Try to use window.btoa for browser environments
    const win = safeWindow();
    if (isBrowser() && win && win.btoa) {
      base64 = win.btoa(svg);
    } else if (typeof Buffer !== 'undefined') {
      // Use Buffer for Node.js environments
      base64 = Buffer.from(svg).toString('base64');
    } else {
      // Fallback implementation if neither is available
      base64 = customBtoa(svg);
    }
  } catch (e) {
    console.error('Error encoding SVG to base64:', e);
    // Return a simple transparent pixel if encoding fails
    return 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';
  }

  return `data:image/svg+xml;base64,${base64}`;
}

// Polyfill for btoa if needed
function customBtoa(str: string): string {
  // A simple polyfill implementation for btoa
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
  let output = '';
  let i = 0;

  while (i < str.length) {
    const chr1 = str.charCodeAt(i++);
    const chr2 = i < str.length ? str.charCodeAt(i++) : Number.NaN;
    const chr3 = i < str.length ? str.charCodeAt(i++) : Number.NaN;

    const enc1 = chr1 >> 2;
    const enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
    const enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
    const enc4 = chr3 & 63;

    if (isNaN(chr2)) {
      output += chars.charAt(enc1) + chars.charAt(enc2) + '==';
    } else if (isNaN(chr3)) {
      output += chars.charAt(enc1) + chars.charAt(enc2) + chars.charAt(enc3) + '=';
    } else {
      output += chars.charAt(enc1) + chars.charAt(enc2) + chars.charAt(enc3) + chars.charAt(enc4);
    }
  }

  return output;
}
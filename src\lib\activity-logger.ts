import { NextRequest } from 'next/server';
import { ensureMongooseConnection } from './mongoose';
// Import type only
import type { IUserActivity } from '@/models/UserActivity';
import mongoose, { Model } from 'mongoose';
import { shouldLogActivity, LOG_RETENTION_DAYS } from '@/config/logging';

/**
 * Get a consistent UserActivity model for use across all API endpoints
 * This ensures all endpoints use the same schema definition
 */
export function getUserActivityModel() {
  // Use the centralized model if it exists
  if (mongoose.models.UserActivity) {
    return mongoose.models.UserActivity as Model<IUserActivity>;
  }

  // Create the schema if it doesn't exist
  const UserActivitySchema = new mongoose.Schema<IUserActivity>({
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true
    },
    type: {
      type: String,
      required: true,
      enum: ['auth', 'content', 'profile', 'admin', 'payment', 'error', 'system'],
      index: true
    },
    action: {
      type: String,
      required: true,
      index: true
    },
    details: {
      type: String,
      required: true
    },
    ipAddress: {
      type: String
    },
    userAgent: {
      type: String
    },
    timestamp: {
      type: Date,
      default: Date.now,
      index: true
    },
    metadata: {
      type: mongoose.Schema.Types.Mixed
    }
  }, {
    timestamps: true
  });

  // Create indexes for efficient querying
  UserActivitySchema.index({ userId: 1, timestamp: -1 });
  UserActivitySchema.index({ type: 1, action: 1 });
  UserActivitySchema.index({ timestamp: -1 });

  return mongoose.model<IUserActivity>('UserActivity', UserActivitySchema);
}

// Define a type for the lean log object
type LeanUserActivity = Omit<IUserActivity, '_id' | 'userId'> & {
  _id: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
};

/**
 * Log user activity
 * @param userId User ID
 * @param type Activity type (auth, admin, payment, system)
 * @param action Specific action performed
 * @param details Description of the activity
 * @param request Optional NextRequest object to extract IP and user agent
 * @param metadata Optional additional data
 */
export async function logUserActivity(
  userId: string,
  type: 'auth' | 'content' | 'profile' | 'admin' | 'payment' | 'error' | 'system',
  action: string,
  details: string,
  request?: NextRequest | Request,
  metadata?: Record<string, any>
) {
  try {
    // Check if this activity should be logged based on our configuration
    if (!shouldLogActivity(type, action)) {
      // Skip logging for this activity
      return true;
    }

    // Connect to MongoDB
    await ensureMongooseConnection();
    // Use the consistent model function
    const UserActivity = getUserActivityModel();

    // Extract IP address and user agent if request is provided
    let ipAddress: string | undefined;
    let userAgent: string | undefined;

    if (request) {
      // Handle NextRequest
      if ('headers' in request && typeof request.headers.get === 'function') { // Check if it looks like NextRequest or standard Request
        ipAddress = request.headers.get('x-forwarded-for')?.split(',')[0] || // Use header
                    request.headers.get('x-real-ip') || // Fallback header
                    'unknown';
        userAgent = request.headers.get('user-agent') || 'unknown';
      }
    }

    // Create activity log
    await UserActivity.create({
      userId: new mongoose.Types.ObjectId(userId),
      type,
      action,
      details,
      ipAddress,
      userAgent,
      timestamp: new Date(),
      metadata
    });

    return true;
  } catch (error) {
    console.error('Error logging user activity:', error);
    return false;
  }
}

/**
 * Get user activity logs
 * @param userId Optional user ID to filter by
 * @param type Optional activity type to filter by
 * @param limit Number of logs to return
 * @param skip Number of logs to skip (for pagination)
 */
export async function getUserActivityLogs(
  userId?: string,
  type?: string,
  limit: number = 50,
  skip: number = 0
) {
  try {
    // Connect to MongoDB
    await ensureMongooseConnection();
    // Use the consistent model function
    const UserActivity = getUserActivityModel();

    // Clean up old logs to prevent database overload
    // Only do this occasionally (1% chance per call) to avoid performance impact
    if (Math.random() < 0.01) {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - LOG_RETENTION_DAYS);

      // Delete logs older than the retention period
      // Run this as a background operation
      UserActivity.deleteMany({
        timestamp: { $lt: cutoffDate }
      }).exec().catch((err: Error) => console.error('Error cleaning up old logs:', err));

      // Also occasionally clean up expired notifications (0.5% chance)
      if (Math.random() < 0.5) {
        import('./cleanup-utils').then(({ cleanupExpiredNotifications }) => {
          cleanupExpiredNotifications()
            .then(count => {
              if (count > 0) {
                console.log(`Auto-cleaned ${count} expired notifications`);
              }
            })
            .catch(err => console.error('Error auto-cleaning expired notifications:', err));
        }).catch(err => console.error('Error importing cleanup utils:', err));
      }
    }

    // Build query
    const query: any = {};
    if (userId) {
      query.userId = new mongoose.Types.ObjectId(userId);
    }
    if (type) {
      query.type = type;
    }

    // Get activity logs
    const logs = await UserActivity.find(query)
      .sort({ timestamp: -1 })
      .skip(skip)
      .limit(limit)
      .lean<LeanUserActivity[]>(); // Apply lean type

    // Get total count for pagination
    const total = await UserActivity.countDocuments(query);

    return {
      logs: logs.map((log: LeanUserActivity) => ({ // Use LeanUserActivity type here
        id: log._id.toString(),
        userId: log.userId.toString(),
        type: log.type,
        action: log.action,
        details: log.details,
        ipAddress: log.ipAddress || 'unknown',
        userAgent: log.userAgent || 'unknown',
        timestamp: log.timestamp,
        metadata: log.metadata
      })),
      pagination: {
        total,
        limit,
        skip
      }
    };
  } catch (error) {
    console.error('Error getting user activity logs:', error);
    throw error;
  }
}

'use client';

import dynamic from 'next/dynamic';

// Dynamically import the client component with SSR disabled
const DetailedPageClient = dynamic(() => import('./DetailedPageClient'), { 
  ssr: false,
  loading: () => (
    <div className="min-h-screen bg-vista-dark flex items-center justify-center">
      <div className="w-16 h-16 border-4 border-vista-blue/20 border-t-vista-blue rounded-full animate-spin"></div>
    </div>
  )
});

interface DetailWrapperProps {
  params: {
    type: string;
    id: string;
  };
}

export default function DetailWrapper({ params }: DetailWrapperProps) {
  return <DetailedPageClient params={params} />;
}

'use client';

import { useState, useRef } from 'react';

interface CustomVideoPlayerProps {
  videoId: string;
  title?: string;
  autoPlay?: boolean;
  className?: string;
  height?: string;
  onError?: (error: string) => void;
  onLoad?: () => void;
}

export default function CustomVideoPlayer({
  videoId,
  title,
  autoPlay = false,
  className,
  height = '56.25vw',
  onError,
  onLoad
}: CustomVideoPlayerProps) {

  const containerRef = useRef<HTMLDivElement>(null);
  
  // Validate YouTube video ID format
  const isValidYouTubeId = (id: string): boolean => {
    const regex = /^[a-zA-Z0-9_-]{11}$/;
    return regex.test(id);
  };

  // Build simple YouTube URL that matches working simple iframes
  const buildSimpleYouTubeUrl = (videoId: string): string => {
    if (!isValidYouTubeId(videoId)) {
      console.warn('CustomVideoPlayer: Invalid YouTube video ID format:', videoId);
    }
    
    // Use minimal parameters that match the working simple iframes
    return `https://www.youtube.com/embed/${videoId}?autoplay=${autoPlay ? '1' : '0'}&controls=1`;
  };

  // Simple handlers that don't interfere with loading
  const handleLoad = () => {
    onLoad?.();
  };

  const handleError = () => {
    console.error('CustomVideoPlayer: Failed to load video:', videoId);
    onError?.(`Failed to load video: ${videoId}`);
  };

  const embedUrl = buildSimpleYouTubeUrl(videoId);

  return (
    <div 
      ref={containerRef}
      className={`${className} relative bg-black rounded-lg overflow-hidden`}
      style={{ height }}
    >
      {/* Simple iframe that matches working test iframes */}
      <iframe
        src={embedUrl}
        className="w-full h-full border-0"
        frameBorder="0"  // Use frameBorder like working simple iframes
        allowFullScreen
        onLoad={handleLoad}
        onError={handleError}
        title={title || 'Video player'}
      />
      



    </div>
  );
} 
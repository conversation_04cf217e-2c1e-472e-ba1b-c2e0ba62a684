import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import { generateNickname } from '@/lib/nickname-generator';
import User from '@/models/User';

/**
 * POST /api/admin/visitors/migrate
 * Migrate existing anonymous visitors to add nicknames
 * 
 * This endpoint is only accessible to admin users.
 * It adds nicknames to all existing anonymous visitors that don't have one.
 */
export async function POST(request: NextRequest) {
  try {
    // Get the userId from cookies or query string
    const { searchParams } = new URL(request.url);
    let userId = request.cookies.get('userId')?.value;

    // If no userId in cookies, try query string (for client-side admin verification)
    if (!userId) {
      const userIdParam = searchParams.get('userId');
      if (userIdParam) userId = userIdParam;
    }

    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Connect to database
    await ensureMongooseConnection();

    // Find the user by ID
    const user = await User.findById(userId).lean();

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if user has admin role
    const isUserAdmin = user.role === 'admin' || user.role === 'superadmin';

    if (!isUserAdmin) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Import the AnonymousVisitor model
    const AnonymousVisitor = (await import('@/models/AnonymousVisitor')).default;

    // Find all visitors without nicknames
    const visitors = await AnonymousVisitor.find({ nickname: { $exists: false } }).lean();

    // Update each visitor with a nickname
    let updatedCount = 0;
    for (const visitor of visitors) {
      const nickname = generateNickname(visitor.visitorId);
      
      await AnonymousVisitor.updateOne(
        { _id: visitor._id },
        { $set: { nickname } }
      );
      
      updatedCount++;
    }

    // Check for duplicate nicknames and add a number suffix if needed
    const nicknameCounts = await AnonymousVisitor.aggregate([
      { $match: { nickname: { $exists: true } } },
      { $group: { _id: "$nickname", count: { $sum: 1 } } },
      { $match: { count: { $gt: 1 } } }
    ]);

    let deduplicatedCount = 0;
    for (const { _id: nickname, count } of nicknameCounts) {
      // Get all visitors with this nickname
      const duplicates = await AnonymousVisitor.find({ nickname }).sort({ firstVisit: 1 }).lean();
      
      // Skip the first one (keep original)
      for (let i = 1; i < duplicates.length; i++) {
        const visitor = duplicates[i];
        const newNickname = `${nickname}${i + 1}`;
        
        await AnonymousVisitor.updateOne(
          { _id: visitor._id },
          { $set: { nickname: newNickname } }
        );
        
        deduplicatedCount++;
      }
    }

    // Return success response
    return NextResponse.json({
      success: true,
      message: `Added nicknames to ${updatedCount} visitors and deduplicated ${deduplicatedCount} nicknames`,
      updatedCount,
      deduplicatedCount
    });
  } catch (error) {
    console.error('Error migrating visitors:', error);
    return NextResponse.json(
      { error: 'Failed to migrate visitors', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

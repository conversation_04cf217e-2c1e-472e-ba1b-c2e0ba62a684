import React, { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Send } from 'lucide-react'

export interface ChatInputProps {
  onSendMessage: (message: string) => void
  onStartTyping?: () => void
  onStopTyping?: () => void
  disabled?: boolean
  placeholder?: string
}

export function ChatInput({
  onSendMessage,
  onStartTyping,
  onStopTyping,
  disabled = false,
  placeholder = 'Type a message...'
}: ChatInputProps) {
  const [message, setMessage] = useState('')
  const typingTimeoutRef = useRef<NodeJS.Timeout>()

  // Handle typing indicator
  useEffect(() => {
    // Clean up on unmount
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current)
        // Make sure to call onStopTyping on unmount if we were typing
        if (message.length > 0 && onStopTyping) {
          onStopTyping()
        }
      }
    }
  }, [message, onStopTyping])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newMessage = e.target.value
    setMessage(newMessage)

    // Only trigger typing events if we have handlers
    if (onStartTyping && onStopTyping) {
      // If starting to type
      if (newMessage.length > 0 && message.length === 0) {
        onStartTyping()
      }

      // If we have a timeout, clear it
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current)
      }

      // Set a new timeout - when it fires, we've stopped typing
      typingTimeoutRef.current = setTimeout(() => {
        if (newMessage.length > 0) {
          onStopTyping()
        }
      }, 2000)

      // If cleared the input
      if (newMessage.length === 0 && message.length > 0) {
        onStopTyping()
      }
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (message.trim() && !disabled) {
      onSendMessage(message.trim())
      setMessage('')
      
      // Stop typing when message is sent
      if (onStopTyping) {
        onStopTyping()
        if (typingTimeoutRef.current) {
          clearTimeout(typingTimeoutRef.current)
        }
      }
    }
  }

  return (
    <form onSubmit={handleSubmit} className="p-4 border-t">
      <div className="flex items-center space-x-2">
        <Input
          value={message}
          onChange={handleInputChange}
          placeholder={placeholder}
          disabled={disabled}
          className="flex-1"
          autoComplete="off"
        />
        <Button 
          type="submit" 
          size="icon"
          disabled={disabled || !message.trim()}
        >
          <Send className="h-4 w-4" />
        </Button>
      </div>
    </form>
  )
} 
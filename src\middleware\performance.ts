import { NextRequest, NextResponse } from 'next/server';

interface PerformanceMetrics {
  path: string;
  method: string;
  duration: number;
  timestamp: number;
  statusCode: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = [];
  private readonly MAX_METRICS = 1000;

  recordMetric(metric: PerformanceMetrics): void {
    this.metrics.push(metric);
    
    // Keep only the latest metrics
    if (this.metrics.length > this.MAX_METRICS) {
      this.metrics = this.metrics.slice(-this.MAX_METRICS);
    }

    // Log slow requests
    if (metric.duration > 1000) {
      console.warn(`🐌 Slow API request: ${metric.method} ${metric.path} took ${metric.duration}ms`);
    }
  }

  getMetrics(): PerformanceMetrics[] {
    return [...this.metrics];
  }

  getAverageResponseTime(path?: string): number {
    const filtered = path 
      ? this.metrics.filter(m => m.path === path)
      : this.metrics;
    
    if (filtered.length === 0) return 0;
    
    const total = filtered.reduce((sum, m) => sum + m.duration, 0);
    return total / filtered.length;
  }

  getSlowestEndpoints(limit = 10): Array<{ path: string; avgDuration: number; count: number }> {
    const pathStats = new Map<string, { total: number; count: number }>();
    
    for (const metric of this.metrics) {
      const existing = pathStats.get(metric.path) || { total: 0, count: 0 };
      existing.total += metric.duration;
      existing.count += 1;
      pathStats.set(metric.path, existing);
    }

    return Array.from(pathStats.entries())
      .map(([path, stats]) => ({
        path,
        avgDuration: stats.total / stats.count,
        count: stats.count
      }))
      .sort((a, b) => b.avgDuration - a.avgDuration)
      .slice(0, limit);
  }
}

export const performanceMonitor = new PerformanceMonitor();

export function performanceMiddleware(request: NextRequest): NextResponse | null {
  const startTime = Date.now();
  
  // Only monitor API routes
  if (!request.nextUrl.pathname.startsWith('/api/')) {
    return null;
  }

  // Create a response that will be modified by the middleware
  const response = NextResponse.next();
  
  // Add performance headers
  response.headers.set('X-Response-Time', '0ms');
  response.headers.set('X-Performance-Monitor', 'enabled');

  // Use setTimeout to ensure this runs after the response is sent
  setTimeout(() => {
    const duration = Date.now() - startTime;
    
    performanceMonitor.recordMetric({
      path: request.nextUrl.pathname,
      method: request.method,
      duration,
      timestamp: startTime,
      statusCode: 200 // We can't easily get the actual status code here
    });

    // Log performance metrics periodically
    if (Math.random() < 0.1) { // 10% chance to log
      const slowest = performanceMonitor.getSlowestEndpoints(5);
      if (slowest.length > 0) {
        console.log('📊 Performance Report:', slowest.map(s => 
          `${s.path}: ${s.avgDuration.toFixed(0)}ms avg (${s.count} requests)`
        ));
      }
    }
  }, 0);

  return response;
} 
import nodemailer from 'nodemailer';
import { ensureMongooseConnection } from '@/lib/mongodb';
import EmailTemplate from '@/models/EmailTemplate';
import mongoose from 'mongoose';

interface SendEmailOptions {
  to: string | string[];
  templateName: string;
  data?: Record<string, any>;
  from?: string;
  cc?: string | string[];
  bcc?: string | string[];
  attachments?: any[];
}

/**
 * Send an email using a template
 */
export async function sendTemplatedEmail({
  to,
  templateName,
  data = {},
  from,
  cc,
  bcc,
  attachments
}: SendEmailOptions): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    // Connect to MongoDB
    await ensureMongooseConnection();

    // Find the template
    const template = await EmailTemplate.findOne({
      name: templateName,
      active: true
    });

    if (!template) {
      throw new Error(`Email template "${templateName}" not found or inactive`);
    }

    // Replace variables in subject and body
    let subject = template.subject;
    let body = template.body;

    // Replace variables
    for (const [key, value] of Object.entries(data)) {
      const regex = new RegExp(`{{${key}}}`, 'g');
      subject = subject.replace(regex, value as string);
      body = body.replace(regex, value as string);
    }

    // Create a transporter
    const transporter = nodemailer.createTransport({
      host: process.env.EMAIL_SERVER_HOST,
      port: parseInt(process.env.EMAIL_SERVER_PORT || '587'),
      secure: process.env.EMAIL_SERVER_SECURE === 'true',
      auth: {
        user: process.env.EMAIL_SERVER_USER,
        pass: process.env.EMAIL_SERVER_PASSWORD
      }
    });

    // Send the email
    const info = await transporter.sendMail({
      from: from || process.env.EMAIL_FROM,
      to,
      cc,
      bcc,
      subject,
      html: body,
      attachments
    });

    return {
      success: true,
      messageId: info.messageId
    };
  } catch (error) {
    console.error('Error sending email:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Create default email templates if they don't exist
 */
export async function createDefaultEmailTemplates(adminUserId: string): Promise<void> {
  try {
    // Connect to MongoDB
    await ensureMongooseConnection();

    // Define default templates
    const defaultTemplates = [
      {
        name: 'welcome',
        subject: 'Welcome to StreamVista!',
        body: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h1 style="color: #3b82f6;">Welcome to StreamVista!</h1>
            <p>Hello {{name}},</p>
            <p>Thank you for joining StreamVista. We're excited to have you on board!</p>
            <p>With your new account, you can:</p>
            <ul>
              <li>Stream thousands of movies and TV shows</li>
              <li>Create watchlists and favorites</li>
              <li>Get personalized recommendations</li>
            </ul>
            <p>Get started by exploring our content library or updating your profile.</p>
            <p>
              <a href="{{loginUrl}}" style="background-color: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;">
                Start Watching Now
              </a>
            </p>
            <p>If you have any questions, please don't hesitate to contact our support team.</p>
            <p>Happy streaming!</p>
            <p>The StreamVista Team</p>
          </div>
        `,
        description: 'Welcome email sent to new users after registration',
        variables: ['name', 'loginUrl'],
        isDefault: true
      },
      {
        name: 'password-reset',
        subject: 'Reset Your StreamVista Password',
        body: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h1 style="color: #3b82f6;">Reset Your Password</h1>
            <p>Hello {{name}},</p>
            <p>We received a request to reset your password. If you didn't make this request, you can safely ignore this email.</p>
            <p>To reset your password, click the button below:</p>
            <p>
              <a href="{{resetUrl}}" style="background-color: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;">
                Reset Password
              </a>
            </p>
            <p>This link will expire in 1 hour.</p>
            <p>If you're having trouble clicking the button, copy and paste the URL below into your web browser:</p>
            <p>{{resetUrl}}</p>
            <p>The StreamVista Team</p>
          </div>
        `,
        description: 'Email sent to users who request a password reset',
        variables: ['name', 'resetUrl'],
        isDefault: true
      },
      {
        name: 'new-content-notification',
        subject: 'New Content on StreamVista: {{contentTitle}}',
        body: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h1 style="color: #3b82f6;">New Content Alert!</h1>
            <p>Hello {{name}},</p>
            <p>We've just added new content that we think you'll love:</p>
            <div style="margin: 20px 0; padding: 15px; border: 1px solid #e5e7eb; border-radius: 8px;">
              <h2 style="margin-top: 0; color: #3b82f6;">{{contentTitle}}</h2>
              <p>{{contentDescription}}</p>
              <p><strong>Type:</strong> {{contentType}}</p>
              <p><strong>Genre:</strong> {{contentGenre}}</p>
            </div>
            <p>
              <a href="{{contentUrl}}" style="background-color: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;">
                Watch Now
              </a>
            </p>
            <p>Happy streaming!</p>
            <p>The StreamVista Team</p>
          </div>
        `,
        description: 'Notification email for new content additions',
        variables: ['name', 'contentTitle', 'contentDescription', 'contentType', 'contentGenre', 'contentUrl'],
        isDefault: true
      }
    ];

    // Check and create each default template
    for (const template of defaultTemplates) {
      const existingTemplate = await EmailTemplate.findOne({ name: template.name });
      if (!existingTemplate) {
        await EmailTemplate.create({
          ...template,
          createdBy: new mongoose.Types.ObjectId(adminUserId),
          updatedBy: new mongoose.Types.ObjectId(adminUserId)
        });
        console.log(`Created default email template: ${template.name}`);
      }
    }
  } catch (error) {
    console.error('Error creating default email templates:', error);
  }
}

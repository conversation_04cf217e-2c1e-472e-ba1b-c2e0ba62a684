import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import mongoose from 'mongoose';
import User from '@/models/User';

// Interface for MongoDB filter/query object
interface VisitorFilter {
  $or?: Array<Record<string, unknown>>;
  lastVisit?: {
    $gte?: Date;
    $lte?: Date;
  };
  country?: string;
  device?: string;
  browser?: string;
  os?: string;
  convertedToUser?: boolean;
  [key: string]: unknown;
}

/**
 * GET /api/admin/visitors/export
 * Export anonymous visitor data as CSV or JSON
 */
export async function GET(request: NextRequest) {
  try {
    // Get the userId from cookies or query string
    const { searchParams } = new URL(request.url);
    let userId = request.cookies.get('userId')?.value;

    // If no userId in cookies, try query string (for client-side admin verification)
    if (!userId) {
      const userIdParam = searchParams.get('userId');
      if (userIdParam) userId = userIdParam;
      
      // Only log this in development
      if (process.env.NODE_ENV === 'development') {
        console.log('Using query string userId for authentication');
      }
    }

    if (!userId) {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Connect to database
    await ensureMongooseConnection();

    // Find the user by ID
    const user = await User.findById(userId).lean();

    if (!user) {
      console.log(`User not found for ID ${userId}`);
      return NextResponse.json({ error: "Forbidden: User not found." }, { status: 403 });
    }

    // Check if user has admin role
    const isUserAdmin = user.role === 'admin' || user.role === 'superadmin';

    if (!isUserAdmin) {
      console.log(`User ${userId} does not have admin role (role: ${user.role})`);
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Import the AnonymousVisitor model
    const AnonymousVisitor = (await import('@/models/AnonymousVisitor')).default;

    // Get export parameters from the same searchParams
    const search = searchParams.get('search') || '';
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');
    const country = searchParams.get('country') || '';
    const device = searchParams.get('device') || '';
    const browser = searchParams.get('browser') || '';
    const os = searchParams.get('os') || '';
    const converted = searchParams.get('converted');
    const format = searchParams.get('format') || 'csv';

    // Build filter object
    const filter: VisitorFilter = {};

    // Add search filter if provided
    if (search) {
      filter.$or = [
        { visitorId: { $regex: search, $options: 'i' } },
        { ipAddress: { $regex: search, $options: 'i' } },
        { country: { $regex: search, $options: 'i' } },
        { city: { $regex: search, $options: 'i' } }
      ];
    }

    // Add date range filter if provided
    if (dateFrom || dateTo) {
      filter.lastVisit = {};
      if (dateFrom) {
        filter.lastVisit.$gte = new Date(dateFrom);
      }
      if (dateTo) {
        filter.lastVisit.$lte = new Date(dateTo);
      }
    }

    // Add other filters if provided
    if (country) filter.country = country;
    if (device) filter.device = device;
    if (browser) filter.browser = browser;
    if (os) filter.os = os;
    if (converted !== null && converted !== undefined) {
      filter.convertedToUser = converted === 'true';
    }

    // Get visitors
    const visitors = await AnonymousVisitor.find(filter)
      .sort({ lastVisit: -1 })
      .lean();

    // Format data based on requested format
    if (format === 'json') {
      // Return JSON data
      return NextResponse.json(visitors);
    } else {
      // Format as CSV
      const headers = [
        'Visitor ID',
        'First Visit',
        'Last Visit',
        'Visit Count',
        'Pages Viewed',
        'Country',
        'City',
        'Device',
        'Browser',
        'OS',
        'Converted',
        'Referrer'
      ];

      // Format date for CSV
      const formatDate = (date: Date) => {
        return date.toISOString().replace('T', ' ').substring(0, 19);
      };

      // Create CSV rows
      const rows = visitors.map(visitor => [
        visitor.visitorId,
        formatDate(new Date(visitor.firstVisit)),
        formatDate(new Date(visitor.lastVisit)),
        visitor.visitCount,
        visitor.pagesViewed,
        visitor.country || 'Unknown',
        visitor.city || 'Unknown',
        visitor.device || 'Unknown',
        visitor.browser || 'Unknown',
        visitor.os || 'Unknown',
        visitor.convertedToUser ? 'Yes' : 'No',
        visitor.referrer || 'Direct'
      ]);

      // Combine headers and rows
      const csvContent = [
        headers.join(','),
        ...rows.map(row => row.map(cell => {
          // Escape commas and quotes in cell values
          const cellStr = String(cell).replace(/"/g, '""');
          return cellStr.includes(',') ? `"${cellStr}"` : cellStr;
        }).join(','))
      ].join('\n');

      // Create response with CSV content
      const response = new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="streamvista_visitors_${new Date().toISOString().split('T')[0]}.csv"`
        }
      });

      return response;
    }
  } catch (error) {
    console.error('Error exporting anonymous visitors:', error);
    return NextResponse.json(
      { error: 'Failed to export anonymous visitors', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

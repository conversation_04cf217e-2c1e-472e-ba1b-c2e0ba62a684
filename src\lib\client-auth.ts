/**
 * Client-side authentication utilities
 */

/**
 * Check if the user is authenticated
 * @returns {boolean} True if the user is authenticated
 */
export function isAuthenticated(): boolean {
  // Check if we're on the client side
  if (typeof window === 'undefined') {
    return false;
  }

  // Check for userId cookie
  const cookies = document.cookie.split(';');
  const userIdCookie = cookies.find(cookie => cookie.trim().startsWith('userId='));
  
  return !!userIdCookie;
}

/**
 * Check if the user is an admin (client-side)
 * This is a basic check that should be supplemented with server-side validation
 * @returns {boolean} True if the user appears to be an admin
 */
export function isAdminClient(): boolean {
  // Check if we're on the client side
  if (typeof window === 'undefined') {
    return false;
  }

  // First check if the user is authenticated
  if (!isAuthenticated()) {
    return false;
  }

  // Check for admin role in localStorage (this is just a UI hint, real auth happens server-side)
  try {
    const userRole = localStorage.getItem('userRole');
    return userRole === 'admin' || userRole === 'superadmin';
  } catch (e) {
    console.error('Error checking admin status:', e);
    return false;
  }
}

/**
 * Redirect to login page if not authenticated
 * @param {string} redirectPath - Path to redirect to after login
 */
export function redirectToLogin(redirectPath: string = window.location.pathname): void {
  if (typeof window === 'undefined') {
    return;
  }

  window.location.href = `/auth/signin?redirect=${encodeURIComponent(redirectPath)}`;
}

/**
 * Redirect to home page with access denied message
 */
export function redirectAccessDenied(): void {
  if (typeof window === 'undefined') {
    return;
  }

  window.location.href = '/?accessDenied=true';
}

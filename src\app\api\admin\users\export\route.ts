import { NextRequest, NextResponse } from 'next/server';

/**
 * GET /api/admin/users/export
 * Export users data in JSON or CSV format
 */
export async function GET(request: NextRequest) {
  try {
    // Get the user ID from the cookie directly
    const userId = request.cookies.get('userId')?.value;
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const UserSchema = new mongoose.default.Schema({
      name: String,
      email: { type: String, required: true, unique: true },
      password: {
        type: String,
        required: function(this: { googleId?: string }) {
          // Password is required only if googleId is not present
          return !this.googleId;
        }
      },
      profileImage: String,
      picture: String,
      googleId: String,
      role: { type: String, default: 'user' },
      status: { type: String, default: 'active' },
      emailVerified: Date,
      lastLogin: Date,
      subscription: String,
      subscriptionStatus: String,
      subscriptionRenewal: Date
    }, {
      timestamps: true
    });

    const User = mongoose.default.models.User ||
                mongoose.default.model('User', UserSchema);

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as { role?: string }).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Get format from query params (default to JSON)
    const format = request.nextUrl.searchParams.get('format') || 'json';

    // Get all users (excluding password)
    const users = await User.find({}).select('-password');

    // Define a type for the user document
    interface UserDocument {
      _id: { toString: () => string };
      name?: string;
      email?: string;
      profileImage?: string;
      picture?: string;
      role?: string;
      emailVerified?: Date | null;
      lastLogin?: Date | null;
      subscription?: string;
      subscriptionStatus?: string;
      subscriptionRenewal?: Date | null;
      createdAt?: Date;
      updatedAt?: Date;
    }

    // Map users to the response format
    const mappedUsers = users.map((user: UserDocument) => ({
      id: user._id.toString(),
      name: user.name,
      email: user.email,
      profileImage: user.profileImage || user.picture || '',
      role: user.role || 'user',
      emailVerified: user.emailVerified ? 'Yes' : 'No',
      lastLogin: user.lastLogin ? new Date(user.lastLogin).toISOString() : '',
      subscription: user.subscription || 'Free Plan',
      subscriptionStatus: user.subscriptionStatus || 'active',
      subscriptionRenewal: user.subscriptionRenewal ? new Date(user.subscriptionRenewal).toISOString() : '',
      createdAt: user.createdAt ? new Date(user.createdAt).toISOString() : '',
      updatedAt: user.updatedAt ? new Date(user.updatedAt).toISOString() : ''
    }));

    // Return data in requested format
    if (format.toLowerCase() === 'csv') {
      // Convert to CSV
      const csvHeaders = ['id', 'name', 'email', 'profileImage', 'role', 'emailVerified', 'lastLogin', 'subscription', 'subscriptionStatus', 'subscriptionRenewal', 'createdAt', 'updatedAt'];
      const csvRows = [
        csvHeaders.join(','), // Header row
        ...mappedUsers.map(user => {
          return csvHeaders.map(header => {
            // Handle special cases for CSV formatting
            const value = user[header as keyof typeof user];
            if (value === null || value === undefined) return '';
            // Escape commas and quotes in string values
            if (typeof value === 'string') {
              return `"${value.replace(/"/g, '""')}"`;
            }
            return value;
          }).join(',');
        })
      ];

      const csvContent = csvRows.join('\n');

      // Set headers for CSV download
      const responseHeaders = new Headers();
      responseHeaders.set('Content-Type', 'text/csv');
      responseHeaders.set('Content-Disposition', 'attachment; filename="streamvista_users.csv"');

      return new NextResponse(csvContent, {
        status: 200,
        headers: responseHeaders
      });
    } else {
      // Return JSON
      return NextResponse.json({
        users: mappedUsers
      });
    }
  } catch (error) {
    console.error('Error exporting users:', error);
    return NextResponse.json(
      { error: 'Failed to export users', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

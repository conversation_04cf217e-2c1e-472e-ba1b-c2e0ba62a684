import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import User from '@/models/User';
import mongoose from 'mongoose';

/**
 * GET /api/debug/auth-status
 * Debug endpoint to check authentication status and user info
 * This helps diagnose authentication issues in production
 */
export async function GET(request: NextRequest) {
  try {
    const userId = request.cookies.get('userId')?.value;
    const allCookies = request.cookies.getAll();

    // Basic info that's safe to return
    const debugInfo = {
      hasUserId: !!userId,
      userIdLength: userId ? userId.length : 0,
      cookieCount: allCookies.length,
      cookieNames: allCookies.map(c => c.name),
      environment: process.env.NODE_ENV,
      timestamp: new Date().toISOString()
    };

    // If no userId, return basic info
    if (!userId) {
      return NextResponse.json({
        ...debugInfo,
        authenticated: false,
        message: 'No userId cookie found'
      });
    }

    // Validate ObjectId format
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return NextResponse.json({
        ...debugInfo,
        authenticated: false,
        validObjectId: false,
        message: 'Invalid userId format'
      });
    }

    // Try to connect to database
    try {
      await ensureMongooseConnection();
    } catch (dbError) {
      return NextResponse.json({
        ...debugInfo,
        authenticated: false,
        databaseConnected: false,
        message: 'Database connection failed',
        error: dbError instanceof Error ? dbError.message : 'Unknown database error'
      });
    }

    // Try to find user
    const user = await User.findById(userId).select('email role createdAt').lean();

    if (!user) {
      return NextResponse.json({
        ...debugInfo,
        authenticated: false,
        validObjectId: true,
        databaseConnected: true,
        userFound: false,
        message: 'User not found in database'
      });
    }

    // User found - return safe info
    return NextResponse.json({
      ...debugInfo,
      authenticated: true,
      validObjectId: true,
      databaseConnected: true,
      userFound: true,
      user: {
        hasEmail: !!user.email,
        role: user.role || 'user',
        isAdmin: user.role === 'admin' || user.role === 'superadmin',
        accountAge: user.createdAt ? 
          Math.floor((Date.now() - new Date(user.createdAt).getTime()) / (1000 * 60 * 60 * 24)) : 
          null
      },
      message: 'Authentication successful'
    });

  } catch (error) {
    console.error('Debug auth status error:', error);
    
    return NextResponse.json({
      authenticated: false,
      error: true,
      message: 'Server error during authentication check',
      details: error instanceof Error ? error.message : 'Unknown error',
      environment: process.env.NODE_ENV,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
} 
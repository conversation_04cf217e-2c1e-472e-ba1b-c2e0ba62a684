import { NextRequest, NextResponse } from 'next/server';
import { pusherServer } from '@/lib/pusher-server';

/**
 * GET /api/admin/notifications
 * Get unique system/update notifications for the admin dashboard
 */

// Define interface for user document from lean query
interface UserDocument {
  _id: string;
  role?: string;
}

export async function GET(request: NextRequest) {
  try {
    // Try to get the user ID from the cookie directly
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, try to get it from the request body
    if (!userId) {
      try {
        const body = await request.json();
        userId = body.userId;
        // Clone the request since we've consumed the body
        request = new NextRequest(request.url, {
          headers: request.headers,
          method: request.method,
          body: JSON.stringify(body),
        });
      } catch (error) {
        // Ignore JSON parsing errors
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    if (!userId) {
      console.error('Admin notifications API: No user ID found in request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const User = mongoose.default.models.User ||
                mongoose.default.model('User', new mongoose.default.Schema({
                  role: String
                }));

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean() as UserDocument;
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the Notification schema directly
    const NotificationSchema = new mongoose.default.Schema({
      title: String,
      message: String,
      type: String,
      userId: mongoose.default.Schema.Types.ObjectId,
      contentId: String,
      contentType: String,
      image: String,
      read: Boolean,
      link: String,
      createdAt: Date,
      deletedBy: [mongoose.default.Schema.Types.ObjectId],
      isGlobal: Boolean,
      expiresAt: Date
    }, {
      timestamps: true
    });

    // Get the Notification model
    const Notification = mongoose.default.models.Notification ||
                        mongoose.default.model('Notification', NotificationSchema);

    // Get query parameters for pagination
    const searchParams = request.nextUrl.searchParams;
    const limit = parseInt(searchParams.get('limit') || '50');
    const page = parseInt(searchParams.get('page') || '1');
    const skip = (page - 1) * limit;

    // Add a timestamp to prevent caching
    const timestamp = new Date().getTime();
    console.log(`Fetching notifications with timestamp: ${timestamp}`);

    // Aggregation pipeline to get unique broadcasts
    const aggregationPipeline = [
      // Match all notification types, exclude deleted ones, and filter out expired notifications
      {
        $match: {
          type: { $in: ['system', 'update', 'new_content', 'recommendation'] },
          // Filter out expired notifications
          $or: [
            { expiresAt: { $exists: false } },  // No expiration date
            { expiresAt: null },                // Null expiration date
            { expiresAt: { $gt: new Date() } }  // Not yet expired
          ]
          // We don't need to filter by deletedBy here since we're permanently deleting notifications
        }
      },
      // Sort by creation date descending to pick the latest for each group
      {
        $sort: { createdAt: -1 as -1 }
      },
      // Group by title, message, and createdAt to identify unique broadcasts
      // This ensures we only show one notification per broadcast, not one per user
      {
        $group: {
          _id: {
            title: '$title',
            message: '$message',
            type: '$type',
            createdAt: '$createdAt',
            contentId: '$contentId',
            contentType: '$contentType'
          },
          doc: { $first: '$$ROOT' }, // Get the full document
          recipientCount: { $sum: 1 } // Count how many users received this notification
        }
      },
      // Add the recipient count to the document
      {
        $addFields: {
          "doc.recipientCount": '$recipientCount'
        }
      },
      // Promote the document back to the root
      {
        $replaceRoot: { newRoot: '$doc' }
      },
      // Sort by creation date again
      {
        $sort: { createdAt: -1 as -1 }
      },
      // Apply pagination
      {
        $skip: skip
      },
      {
        $limit: limit
      }
    ];

    // Execute aggregation to get the paginated unique notifications
    const notifications = await Notification.aggregate(aggregationPipeline);

    // Need total count of *unique* broadcasts for pagination
    const totalPipeline = [
      {
        $match: {
          type: { $in: ['system', 'update', 'new_content', 'recommendation'] },
          // Filter out expired notifications
          $or: [
            { expiresAt: { $exists: false } },  // No expiration date
            { expiresAt: null },                // Null expiration date
            { expiresAt: { $gt: new Date() } }  // Not yet expired
          ]
        }
      },
      {
        $group: {
          _id: {
            title: '$title',
            message: '$message',
            type: '$type',
            createdAt: '$createdAt',
            contentId: '$contentId',
            contentType: '$contentType'
          }
        }
      },
      { $count: 'total' }
    ];
    const totalResult = await Notification.aggregate(totalPipeline);
    const total = totalResult.length > 0 ? totalResult[0].total : 0;

    return NextResponse.json({
      notifications,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching aggregated notifications:', error);
    // Ensure error is logged with stack trace if available
    if (error instanceof Error) {
      console.error(error.stack);
    }
    return NextResponse.json(
      { error: 'Failed to fetch notifications', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/notifications
 * Delete all notifications in the system
 */
export async function DELETE(request: NextRequest) {
  try {
    // Try to get the user ID from the cookie directly
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    // For DELETE requests, we'll try to extract the userId from the body
    if (!userId) {
      try {
        const bodyData = await request.json();
        userId = bodyData.userId;
        // Clone the request since we've consumed the body
        request = new NextRequest(request.url, {
          headers: request.headers,
          method: request.method,
          body: JSON.stringify(bodyData),
        });
      } catch (error) {
        // Ignore JSON parsing errors
      }
    }

    if (!userId) {
      console.error('Admin notifications API (DELETE): No user ID found in request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const User = mongoose.default.models.User ||
                mongoose.default.model('User', new mongoose.default.Schema({
                  role: String
                }));

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean() as UserDocument;
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the Notification schema directly
    const NotificationSchema = new mongoose.default.Schema({
      title: String,
      message: String,
      type: String,
      userId: mongoose.default.Schema.Types.ObjectId,
      contentId: String,
      contentType: String,
      image: String,
      read: Boolean,
      link: String,
      createdAt: Date,
      deletedBy: [mongoose.default.Schema.Types.ObjectId],
      isGlobal: Boolean,
      expiresAt: Date
    }, {
      timestamps: true
    });

    // Get the Notification model
    const Notification = mongoose.default.models.Notification ||
                        mongoose.default.model('Notification', NotificationSchema);

    // Delete all notifications
    const result = await Notification.deleteMany({});
    console.log('Deleted all notifications:', result);

    // Broadcast the deletion event to all clients
    try {
      console.log('Broadcasting notification deletion event');
      await pusherServer.trigger(
        'global-notifications',
        'notification-deleted-all',
        {
          timestamp: new Date().toISOString(),
          adminId: userId,
          deletedCount: result.deletedCount || 0
        }
      );
    } catch (error) {
      console.error('Error broadcasting notification deletion:', error);
    }

    return NextResponse.json({
      success: true,
      deletedCount: result.deletedCount
    });
  } catch (error) {
    console.error('Error deleting notifications:', error);
    return NextResponse.json(
      { error: 'Failed to delete notifications', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

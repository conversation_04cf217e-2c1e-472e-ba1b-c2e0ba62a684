"use client"

import { usePusher } from "@/hooks/usePusher"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Wifi, WifiOff, AlertCircle } from "lucide-react"
import { cn } from "@/lib/utils"

interface ConnectionStatusIndicatorProps {
  className?: string
  showTooltip?: boolean
}

export function ConnectionStatusIndicator({
  className,
  showTooltip = true
}: ConnectionStatusIndicatorProps) {
  // Use Pusher hook to get connection status
  const { status, reconnectIn } = usePusher()
  
  // Define status variants
  const getStatusDetails = () => {
    switch (status) {
      case "connected":
        return {
          icon: Wifi,
          color: "bg-green-500",
          label: "Connected",
          description: "Your connection is stable"
        }
      case "connecting":
        return {
          icon: Wifi,
          color: "bg-yellow-500",
          label: "Connecting",
          description: "Establishing connection..."
        }
      case "unavailable":
        return {
          icon: WifiOff,
          color: "bg-orange-500",
          label: "Reconnecting",
          description: reconnectIn 
            ? `Reconnecting in ${reconnectIn}s` 
            : "Connection unavailable"
        }
      case "failed":
        return {
          icon: AlertCircle,
          color: "bg-red-500",
          label: "Disconnected",
          description: "Unable to connect"
        }
      default:
        return {
          icon: WifiOff,
          color: "bg-gray-500",
          label: "Disconnected",
          description: "No connection"
        }
    }
  }
  
  const details = getStatusDetails()
  const Icon = details.icon
  
  // Simple badge if tooltips are disabled
  if (!showTooltip) {
    return (
      <div className={cn("flex items-center", className)}>
        <Badge 
          variant="outline" 
          className={cn(
            "px-2 py-1 gap-1 border-transparent", 
            details.color === "bg-green-500" ? "bg-green-500/10 text-green-500" :
            details.color === "bg-yellow-500" ? "bg-yellow-500/10 text-yellow-500" :
            details.color === "bg-orange-500" ? "bg-orange-500/10 text-orange-500" :
            details.color === "bg-red-500" ? "bg-red-500/10 text-red-500" :
            "bg-gray-500/10 text-gray-400"
          )}
        >
          <Icon className="h-3 w-3" />
          <span className="text-xs font-medium">{details.label}</span>
        </Badge>
      </div>
    )
  }
  
  // Tooltip version for more details
  return (
    <TooltipProvider>
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>
          <div className={cn("flex items-center cursor-help", className)}>
            <div className={cn(
              "h-2.5 w-2.5 rounded-full mr-1", 
              details.color
            )}>
              <span className="sr-only">{details.label}</span>
            </div>
            <Icon className={cn(
              "h-3.5 w-3.5",
              details.color === "bg-green-500" ? "text-green-500" :
              details.color === "bg-yellow-500" ? "text-yellow-500" :
              details.color === "bg-orange-500" ? "text-orange-500" :
              details.color === "bg-red-500" ? "text-red-500" :
              "text-gray-400"
            )} />
          </div>
        </TooltipTrigger>
        <TooltipContent side="bottom" className="text-sm bg-vista-dark-lighter border-vista-light/20">
          <div className="flex flex-col">
            <span className="font-medium">{details.label}</span>
            <span className="text-xs text-vista-light/70">{details.description}</span>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
} 
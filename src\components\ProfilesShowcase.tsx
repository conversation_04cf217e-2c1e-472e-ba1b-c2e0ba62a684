'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { 
  Users, 
  Settings, 
  ChevronRight, 
  Shield, 
  Sliders, 
  UserPlus,
  Lock
} from 'lucide-react';
import { UserAvatar } from '@/components/UserAvatar';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

// Feature showcase profiles for demonstration
const showcaseProfiles = [
  {
    id: '1',
    name: 'Dad',
    avatar: '/avatars/avatar-1.png',
    isActive: true
  },
  {
    id: '2',
    name: 'Mom',
    avatar: '/avatars/avatar-2.png',
    isActive: false
  },
  {
    id: '3',
    name: 'Kids',
    avatar: '/avatars/avatar-3.png',
    isKids: true,
    isActive: false
  }
];

export default function ProfilesShowcase() {
  const [isHovered, setIsHovered] = useState(false);
  const [activeProfile, setActiveProfile] = useState(0);
  const { isAuthenticated, user } = useAuth();
  const router = useRouter();

  // Auto rotate profiles
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveProfile((prev) => (prev + 1) % showcaseProfiles.length);
    }, 3000);
    
    return () => clearInterval(interval);
  }, []);
  
  // Handle manage profiles click with authentication check
  const handleManageProfilesClick = (e: React.MouseEvent) => {
    if (!isAuthenticated) {
      e.preventDefault();
      toast.error('Sign In Required', {
        description: 'Please sign in to manage your profiles',
        duration: 3000,
      });
    } else {
      router.push('/profiles');
    }
  };
  
  return (
    <section className="py-8">
      <div className="container mx-auto px-4">
        <motion.div
          className="relative overflow-hidden rounded-xl bg-gradient-to-br from-vista-dark-lighter to-vista-dark"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 p-8 md:p-12">
            {/* Left side: Content */}
            <div className="flex flex-col justify-center">
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-vista-light mb-3">
                Personalized for Everyone
              </h2>
              
              <p className="text-vista-light/80 text-base md:text-lg mb-6 max-w-2xl">
                Create multiple profiles for everyone in your household. Each profile gets its own personalized recommendations, watch history, and settings.
              </p>
              
              <p className="text-sm text-vista-light/60 italic mb-4">
                Feature demonstration - showing how profiles would appear
              </p>
              
              <div className="space-y-4 mb-6">
                <div className="flex items-start gap-3">
                  <div className="p-2 bg-vista-blue/20 rounded-lg text-vista-blue mt-1">
                    <Users className="h-5 w-5" />
                  </div>
                  <div>
                    <h3 className="text-vista-light font-medium text-lg">Multiple Profiles</h3>
                    <p className="text-vista-light/70">Create profiles for everyone in your household</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="p-2 bg-vista-blue/20 rounded-lg text-vista-blue mt-1">
                    <Shield className="h-5 w-5" />
                  </div>
                  <div>
                    <h3 className="text-vista-light font-medium text-lg">Kids Profiles</h3>
                    <p className="text-vista-light/70">Special profiles with age-appropriate content</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="p-2 bg-vista-blue/20 rounded-lg text-vista-blue mt-1">
                    <Sliders className="h-5 w-5" />
                  </div>
                  <div>
                    <h3 className="text-vista-light font-medium text-lg">Customizable Settings</h3>
                    <p className="text-vista-light/70">Personalize your viewing experience</p>
                  </div>
                </div>
              </div>
              
              <div className="flex flex-wrap gap-4">
                <Button 
                  className="bg-vista-blue hover:bg-vista-blue/90 gap-2"
                  onClick={handleManageProfilesClick}
                >
                  <Settings className="h-5 w-5" />
                  Manage Profiles
                </Button>
                
                <Link href="/settings">
                  <Button variant="outline" className="border-vista-light/30 hover:bg-vista-dark-lighter gap-2">
                    <Sliders className="h-5 w-5" />
                    Account Settings
                  </Button>
                </Link>
              </div>
            </div>
            
            {/* Right side: Profile Showcase */}
            <div className="flex items-center justify-center">
              <div className="relative w-full max-w-md aspect-square">
                {/* Decorative circles */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-[90%] h-[90%] rounded-full border-2 border-vista-blue/20 animate-pulse" />
                </div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-[70%] h-[70%] rounded-full border-2 border-vista-blue/30 animate-pulse" style={{ animationDelay: '0.5s' }} />
                </div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-[50%] h-[50%] rounded-full border-2 border-vista-blue/40 animate-pulse" style={{ animationDelay: '1s' }} />
                </div>
                
                {/* Profile avatars */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <AnimatePresence mode="wait">
                    <motion.div
                      key={activeProfile}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      transition={{ duration: 0.5 }}
                      className="relative"
                    >
                      <div className={`relative rounded-full overflow-hidden border-4 ${
                        showcaseProfiles[activeProfile].isKids 
                          ? 'border-yellow-500' 
                          : showcaseProfiles[activeProfile].isActive 
                            ? 'border-vista-blue' 
                            : 'border-vista-light/30'
                      }`}>
                        <div className="w-32 h-32 md:w-40 md:h-40 bg-vista-dark-lighter flex items-center justify-center">
                          <UserAvatar 
                            src={showcaseProfiles[activeProfile].avatar || '/favicon.svg'} 
                            alt={showcaseProfiles[activeProfile].name}
                            size="xl"
                          />
                        </div>
                      </div>
                      
                      {/* Profile name */}
                      <div className="absolute -bottom-10 left-1/2 transform -translate-x-1/2 text-center">
                        <p className="text-vista-light font-medium text-lg">{showcaseProfiles[activeProfile].name}</p>
                        {showcaseProfiles[activeProfile].isKids && (
                          <span className="text-xs text-yellow-500 font-medium">KIDS PROFILE</span>
                        )}
                      </div>
                    </motion.div>
                  </AnimatePresence>
                </div>
                
                {/* Add profile button */}
                <div className="absolute bottom-0 right-0 md:bottom-10 md:right-10">
                  <motion.div
                    whileHover={{ scale: 1.1 }}
                    className="bg-vista-blue/90 rounded-full p-3 shadow-lg cursor-pointer"
                    onClick={handleManageProfilesClick}
                  >
                    <UserPlus className="h-6 w-6 text-white" />
                  </motion.div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}

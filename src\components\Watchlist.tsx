'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
  Clock, X, Plus, Play, Edit, SortDesc, Grid2X2, List as ListIcon, Info
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { useWatchlist } from '@/contexts/WatchlistContext';

// Local interface for the component (we'll adapt from the context's WatchlistItem)
interface LocalWatchlistItem {
  id: string;
  title: string;
  imagePath: string;
  type: 'shows' | 'movies';
  year: number | string;
  addedDate: string;
  genres: string[];
}

export default function Watchlist({ title = "My List" }) {
  const { watchlist, removeFromWatchlist } = useWatchlist();
  const [loading, setLoading] = useState(true);
  const [viewType, setViewType] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'date-added' | 'name' | 'year'>('date-added');
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedItem, setSelectedItem] = useState<LocalWatchlistItem | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [items, setItems] = useState<LocalWatchlistItem[]>([]);

  // Convert watchlist items to the format expected by this component
  useEffect(() => {
    // Simulate a short loading time for better UX
    const timer = setTimeout(() => {
      // Don't process anything if watchlist is empty
      if (watchlist.length === 0) {
        setItems([]);
        setLoading(false);
        return;
      }

      // Map the watchlist items to the format expected by this component
      const mappedItems = watchlist.map(item => {
        // Enhanced debug logging to check the type value
        console.log(`[DEBUG] Watchlist item type for ${item.title}:`, item.type);

        return {
          id: item.id,
          title: item.title,
          // Ensure imagePath is never an empty string
          imagePath: item.imagePath && item.imagePath.trim() !== ''
            ? item.imagePath
            : '/favicon.svg',
          // Keep the type as-is to maintain consistency
          type: item.type,
          year: typeof item.year === 'string' ? parseInt(item.year) || item.year : item.year,
          addedDate: item.addedDate,
          // Use default genres if not available
          genres: item.genres || ['Drama', 'Action']
        };
      });

      setItems(mappedItems);
      setLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, [watchlist]);

  // Sort items based on chosen criteria
  const sortedItems = [...items].sort((a, b) => {
    switch(sortBy) {
      case 'name':
        return a.title.localeCompare(b.title);
      case 'year':
        // Convert year to number if it's a string to avoid type errors
        const yearA = typeof a.year === 'string' ? parseInt(a.year) || 0 : Number(a.year) || 0;
        const yearB = typeof b.year === 'string' ? parseInt(b.year) || 0 : Number(b.year) || 0;
        return yearB - yearA;
      case 'date-added':
      default:
        return new Date(b.addedDate).getTime() - new Date(a.addedDate).getTime();
    }
  });

  // Track if a delete operation is in progress to prevent multiple clicks
  const [deleteInProgress, setDeleteInProgress] = useState<{[key: string]: boolean}>({});

  // Remove item from watchlist
  const removeItem = (id: string, e?: React.MouseEvent) => {
    // Prevent default behavior if event is provided
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    // Prevent multiple clicks on the same item
    if (deleteInProgress[id]) {
      console.log(`Delete already in progress for item ${id}`);
      return false;
    }

    try {
      // Mark this item as being deleted
      setDeleteInProgress(prev => ({ ...prev, [id]: true }));

      // Only call the context function to update global state
      // The useEffect will handle updating the local state based on the watchlist changes
      // Use setTimeout to ensure this happens after the current event cycle
      setTimeout(() => {
        try {
          removeFromWatchlist(id);

          // Reset the delete in progress flag after a delay
          setTimeout(() => {
            setDeleteInProgress(prev => {
              const newState = { ...prev };
              delete newState[id];
              return newState;
            });
          }, 1000); // Wait 1 second before allowing this item to be deleted again
        } catch (error) {
          console.error('Error in removeFromWatchlist:', error);
          // Reset the delete in progress flag if there's an error
          setDeleteInProgress(prev => {
            const newState = { ...prev };
            delete newState[id];
            return newState;
          });
        }
      }, 50); // Small delay to ensure the UI has time to process the click event
    } catch (error) {
      console.error('Error in removeItem:', error);
      // Reset the delete in progress flag if there's an error
      setDeleteInProgress(prev => {
        const newState = { ...prev };
        delete newState[id];
        return newState;
      });
    }

    // Return false to prevent any default form submission
    return false;
  };

  // Show item details
  const showItemDetails = (item: LocalWatchlistItem) => {
    setSelectedItem(item);
    setIsDialogOpen(true);
  };

  // Empty state component
  const EmptyState = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="flex flex-col items-center justify-center py-16 px-4 text-center"
    >
      {/* Enhanced visual element with animation */}
      <div className="relative mb-6">
        <div className="absolute -inset-8 rounded-full bg-gradient-to-r from-vista-blue/10 to-purple-500/10 blur-xl animate-pulse"></div>
        <motion.div
          initial={{ scale: 0.8 }}
          animate={{ scale: [0.8, 1.1, 1] }}
          transition={{ duration: 1, ease: "easeOut" }}
          className="relative bg-vista-dark-lighter rounded-full p-6 mb-6 shadow-xl border border-vista-light/10 backdrop-blur-sm"
        >
          <Clock className="h-14 w-14 text-vista-blue" />
        </motion.div>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.5 }}
      >
        <h3 className="text-3xl font-medium text-vista-light mb-3">Your watchlist is waiting</h3>
        <p className="text-vista-light/70 max-w-md mb-8 text-lg">
          Discover amazing content and save it here to build your perfect watch queue.
        </p>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 0.4, duration: 0.5 }}
        className="mb-12"
      >
        <Link href="/">
          <Button className="bg-vista-blue hover:bg-vista-blue/90 text-white rounded-full px-8 py-6 h-auto text-lg shadow-lg shadow-vista-blue/20 transition-all hover:shadow-vista-blue/30 hover:scale-105">
            <Plus className="h-5 w-5 mr-2" /> Explore Content
          </Button>
        </Link>
      </motion.div>

      {/* Recommendation categories with icons */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6, duration: 0.5 }}
        className="w-full max-w-4xl"
      >
        <h4 className="text-xl font-medium text-vista-light/90 mb-6">Popular Categories to Explore</h4>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 md:gap-6">
          {[
            { name: 'Action', icon: '🔥', color: 'from-red-500/20 to-orange-500/20' },
            { name: 'Comedy', icon: '😂', color: 'from-yellow-500/20 to-green-500/20' },
            { name: 'Drama', icon: '🎭', color: 'from-blue-500/20 to-purple-500/20' },
            { name: 'Sci-Fi', icon: '🚀', color: 'from-indigo-500/20 to-vista-blue/20' },
            { name: 'Horror', icon: '👻', color: 'from-purple-900/20 to-red-600/20' },
            { name: 'Romance', icon: '💖', color: 'from-pink-500/20 to-red-400/20' },
            { name: 'Documentary', icon: '🌍', color: 'from-green-500/20 to-teal-500/20' },
            { name: 'Animation', icon: '✨', color: 'from-vista-blue/20 to-purple-500/20' }
          ].map((genre) => (
            <Link href={`/?genre=${genre.name}`} key={genre.name}>
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.98 }}
                className={`bg-vista-dark-lighter/50 hover:bg-vista-dark-lighter backdrop-blur-sm rounded-xl p-5 text-center transition-all border border-vista-light/5 hover:border-vista-light/10 shadow-lg hover:shadow-xl relative overflow-hidden group`}
              >
                <div className={`absolute inset-0 bg-gradient-to-br ${genre.color} opacity-30 group-hover:opacity-50 transition-opacity`}></div>
                <div className="relative z-10">
                  <p className="text-2xl mb-1">{genre.icon}</p>
                  <p className="text-vista-light font-medium mb-1">{genre.name}</p>
                  <p className="text-xs text-vista-light/70">Explore</p>
                </div>
              </motion.div>
            </Link>
          ))}
        </div>
      </motion.div>

      {/* Tips section */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.8, duration: 0.5 }}
        className="mt-16 bg-vista-dark-lighter/30 border border-vista-light/5 rounded-xl p-6 max-w-4xl w-full"
      >
        <h4 className="text-lg font-medium text-vista-light mb-4 flex items-center">
          <Info className="h-5 w-5 mr-2 text-vista-blue" /> Quick Tips
        </h4>
        <ul className="text-vista-light/80 text-sm space-y-2">
          <li className="flex items-start">
            <span className="text-vista-blue mr-2">•</span>
            <span>Click the "+" button on any movie or show card to add it to your watchlist</span>
          </li>
          <li className="flex items-start">
            <span className="text-vista-blue mr-2">•</span>
            <span>Use the "My List" section to keep track of content you want to watch later</span>
          </li>
          <li className="flex items-start">
            <span className="text-vista-blue mr-2">•</span>
            <span>Your watchlist syncs across all your devices when you're logged in</span>
          </li>
        </ul>
      </motion.div>
    </motion.div>
  );

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 min-h-[300px] flex items-center justify-center">
        <div className="w-16 h-16 border-4 border-vista-light/20 border-t-vista-blue rounded-full animate-spin"></div>
      </div>
    );
  }

  // If watchlist is empty, show the empty state component
  if (items.length === 0) {
    return <EmptyState />;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header with controls */}
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="flex flex-col md:flex-row md:justify-between md:items-center mb-8 gap-4"
      >
        {title && <h2 className="text-2xl font-bold text-vista-light">{title}</h2>}

        <div className="flex flex-wrap gap-3 items-center">
          {items.length > 0 && (
            <>
              <div className="flex items-center bg-vista-dark-lighter/50 border border-vista-light/10 rounded-full overflow-hidden p-1 shadow-sm">
                <button
                  onClick={() => setViewType('grid')}
                  className={`p-2 rounded-full transition-all ${viewType === 'grid' ? 'bg-vista-blue text-white shadow-md' : 'text-vista-light/70 hover:bg-vista-light/10'}`}
                >
                  <Grid2X2 className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewType('list')}
                  className={`p-2 rounded-full transition-all ${viewType === 'list' ? 'bg-vista-blue text-white shadow-md' : 'text-vista-light/70 hover:bg-vista-light/10'}`}
                >
                  <ListIcon className="h-4 w-4" />
                </button>
              </div>

              <div className="flex items-center gap-2 bg-vista-dark-lighter/50 border border-vista-light/10 rounded-full px-3 py-1.5 shadow-sm">
                <SortDesc className="h-4 w-4 text-vista-blue/80" />
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as 'date-added' | 'name' | 'year')}
                  className="bg-transparent border-none focus:outline-none focus:ring-0 text-vista-light text-sm"
                >
                  <option value="date-added" className="bg-vista-dark-lighter">Recently Added</option>
                  <option value="name" className="bg-vista-dark-lighter">Name</option>
                  <option value="year" className="bg-vista-dark-lighter">Release Year</option>
                </select>
              </div>

              <Button
                variant="outline"
                className={`border-vista-light/20 text-vista-light hover:bg-vista-light/10 rounded-full transition-all ${isEditMode ? 'bg-vista-light/10 border-vista-light/30' : ''}`}
                onClick={() => setIsEditMode(!isEditMode)}
              >
                <Edit className={`h-4 w-4 mr-2 ${isEditMode ? 'text-vista-blue' : ''}`} />
                {isEditMode ? 'Done' : 'Edit List'}
              </Button>
            </>
          )}
        </div>
      </motion.div>

      {/* Content grid/list */}
      {items.length === 0 ? (
        <EmptyState />
      ) : (
        <>
          {viewType === 'grid' ? (
            <motion.div
              initial="hidden"
              animate="visible"
              variants={{
                hidden: { opacity: 0 },
                visible: {
                  opacity: 1,
                  transition: { staggerChildren: 0.05 }
                }
              }}
              className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-6"
            >
              {sortedItems.map((item) => (
                <motion.div
                  key={item.id}
                  variants={{
                    hidden: { opacity: 0, y: 20 },
                    visible: {
                      opacity: 1,
                      y: 0,
                      transition: {
                        type: 'spring',
                        damping: 15,
                        stiffness: 100
                      }
                    }
                  }}
                  className="relative group"
                >
                  <div className="relative aspect-[2/3] rounded-lg overflow-hidden shadow-lg transition-all duration-300 group-hover:shadow-xl group-hover:shadow-vista-blue/10 border border-vista-light/5 group-hover:border-vista-light/10">
                    <Image
                      src={item.imagePath || '/favicon.svg'}
                      alt={item.title}
                      fill
                      className="object-cover transition-transform duration-500 group-hover:scale-105 group-hover:opacity-80"
                      unoptimized={item.imagePath.includes('placehold.co')}
                      onError={(e) => {
                        // Replace broken images with placeholder
                        const target = e.target as HTMLImageElement;
                        target.src = '/favicon.svg';
                      }}
                    />

                    {/* Gradient overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-70 group-hover:opacity-90 transition-opacity"></div>

                    {/* Overlay controls */}
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 scale-90 group-hover:scale-100">
                      <Link
                        href={`/watch/${item.id}?contentType=${item.type === 'shows' ? 'show' : 'movie'}`}
                        className="bg-vista-blue text-white p-3 rounded-full hover:bg-vista-blue/90 transition-all duration-300 hover:scale-110 shadow-lg shadow-vista-blue/20"
                      >
                        <Play className="h-6 w-6 fill-current" />
                      </Link>
                    </div>

                    {/* Edit mode overlay */}
                    {isEditMode && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ type: 'spring', damping: 15 }}
                        className="absolute top-2 right-2 z-10"
                      >
                        <button
                          type="button"
                          disabled={deleteInProgress[item.id]}
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            removeItem(item.id, e);
                          }}
                          className={`bg-vista-light/10 backdrop-blur-sm p-2 rounded-full ${deleteInProgress[item.id] ? 'text-gray-400 cursor-not-allowed' : 'text-red-400 hover:bg-red-500/20 hover:text-red-300'} transition-colors`}
                        >
                          {deleteInProgress[item.id] ? (
                            <div className="h-5 w-5 animate-spin rounded-full border-2 border-solid border-current border-r-transparent"></div>
                          ) : (
                            <X className="h-5 w-5" />
                          )}
                        </button>
                      </motion.div>
                    )}

                    {/* Type badge */}
                    <div className="absolute bottom-3 left-3 z-10">
                      <Badge className="bg-black/60 backdrop-blur-sm text-xs font-medium text-vista-light border border-vista-light/10">
                        {item.type === 'shows' ? 'Series' : 'Movie'}
                      </Badge>
                    </div>
                  </div>

                  {/* Title and year below card */}
                  <div className="mt-2">
                    <button
                      onClick={() => showItemDetails(item)}
                      className="text-sm font-medium text-vista-light transition-colors text-left w-full truncate"
                    >
                      {item.title}
                    </button>
                    <p className="text-xs text-vista-light/70">{item.year}</p>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          ) : (
            <motion.div
              initial="hidden"
              animate="visible"
              variants={{
                hidden: { opacity: 0 },
                visible: {
                  opacity: 1,
                  transition: { staggerChildren: 0.05 }
                }
              }}
              className="space-y-4"
            >
              {sortedItems.map((item) => (
                <motion.div
                  key={item.id}
                  variants={{
                    hidden: { opacity: 0, x: -20 },
                    visible: {
                      opacity: 1,
                      x: 0,
                      transition: {
                        type: 'spring',
                        damping: 15,
                        stiffness: 100
                      }
                    }
                  }}
                  className="flex gap-5 bg-vista-dark-lighter/50 rounded-xl p-4 hover:bg-vista-dark-lighter transition-all duration-300 border border-vista-light/5 hover:border-vista-light/10 shadow-sm hover:shadow-md group"
                >
                  <div className="relative h-28 w-20 flex-shrink-0 rounded-lg overflow-hidden shadow-md group-hover:shadow-lg transition-all duration-300">
                    <Image
                      src={item.imagePath || '/favicon.svg'}
                      alt={item.title}
                      fill
                      className="object-cover transition-transform duration-500 group-hover:scale-105"
                      unoptimized={item.imagePath.includes('placehold.co')}
                      onError={(e) => {
                        // Replace broken images with placeholder
                        const target = e.target as HTMLImageElement;
                        target.src = '/favicon.svg';
                      }}
                    />
                    {/* Gradient overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-60"></div>

                    {/* Type badge */}
                    <div className="absolute bottom-2 left-2">
                      <Badge className="bg-black/60 backdrop-blur-sm text-xs font-medium text-vista-light border border-vista-light/10">
                        {item.type === 'shows' ? 'Series' : 'Movie'}
                      </Badge>
                    </div>
                  </div>

                  <div className="flex-1 flex flex-col justify-between">
                    <div>
                      <div className="flex justify-between items-start">
                        <div>
                          <button
                            onClick={() => showItemDetails(item)}
                            className="text-vista-light font-medium text-lg transition-colors"
                          >
                            {item.title}
                          </button>
                          <p className="text-sm text-vista-light/70">{item.year} • {item.type === 'shows' ? 'Series' : 'Movie'}</p>
                          <div className="flex flex-wrap gap-1 mt-2">
                            {item.genres.map(genre => (
                              <Badge key={genre} variant="outline" className="text-xs bg-vista-dark text-vista-light/80 hover:bg-vista-dark-lighter transition-colors">
                                {genre}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        <div className="flex gap-2">
                          {isEditMode ? (
                            <motion.div
                              initial={{ scale: 0.8, opacity: 0 }}
                              animate={{ scale: 1, opacity: 1 }}
                              transition={{ type: 'spring', damping: 15 }}
                            >
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                disabled={deleteInProgress[item.id]}
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  removeItem(item.id, e);
                                }}
                                className={`rounded-full ${deleteInProgress[item.id] ? 'text-gray-400 cursor-not-allowed' : 'text-red-400 hover:text-red-300 hover:bg-red-500/10'}`}
                              >
                                {deleteInProgress[item.id] ? (
                                  <div className="h-5 w-5 animate-spin rounded-full border-2 border-solid border-current border-r-transparent"></div>
                                ) : (
                                  <X className="h-5 w-5" />
                                )}
                              </Button>
                            </motion.div>
                          ) : (
                            <div className="flex gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => showItemDetails(item)}
                                className="border-vista-light/20 text-vista-light hover:bg-vista-light/10 rounded-full"
                              >
                                <Info className="h-3.5 w-3.5 mr-1" /> Details
                              </Button>

                              <Link href={`/watch/${item.id}?contentType=${item.type === 'shows' ? 'show' : 'movie'}`}>
                                <Button
                                  variant="default"
                                  size="sm"
                                  className="bg-vista-blue hover:bg-vista-blue/90 text-white rounded-full shadow-sm shadow-vista-blue/20 hover:shadow-vista-blue/30"
                                >
                                  <Play className="h-3.5 w-3.5 mr-1 fill-current" /> Watch
                                </Button>
                              </Link>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="text-xs text-vista-light/60 mt-2">
                      Added {new Date(item.addedDate).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })}
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          )}
        </>
      )}

      {/* Item details dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="bg-vista-dark-lighter border-vista-light/10 text-vista-light max-w-3xl">
          <DialogHeader>
            <DialogTitle className="text-2xl font-bold">{selectedItem?.title}</DialogTitle>
          </DialogHeader>

          {selectedItem && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="flex flex-col md:flex-row gap-6"
            >
              <div className="relative aspect-[2/3] h-56 md:h-72 rounded-xl overflow-hidden flex-shrink-0 shadow-xl border border-vista-light/5 group">
                <Image
                  src={selectedItem.imagePath || '/favicon.svg'}
                  alt={selectedItem.title}
                  fill
                  className="object-cover transition-transform duration-10000 group-hover:scale-105"
                  unoptimized={selectedItem.imagePath.includes('placehold.co')}
                  onError={(e) => {
                    // Replace broken images with placeholder
                    const target = e.target as HTMLImageElement;
                    target.src = '/favicon.svg';
                  }}
                />
                {/* Gradient overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-60"></div>

                {/* Type badge */}
                <div className="absolute top-3 left-3">
                  <Badge className="bg-vista-blue/90 backdrop-blur-sm text-sm font-medium text-white border border-vista-blue/30 px-3 py-1">
                    {selectedItem.type === 'shows' ? 'TV Series' : 'Movie'}
                  </Badge>
                </div>
              </div>

              <div className="flex-1">
                <div className="space-y-5">
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <p className="text-lg text-vista-light/90 font-medium">
                        {selectedItem.year}
                      </p>
                      <span className="text-vista-light/40">•</span>
                      <div className="bg-vista-light/10 px-2 py-0.5 rounded text-sm text-vista-light/80">
                        {selectedItem.type === 'shows' ? 'TV-MA' : 'PG-13'}
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-1.5 mt-3">
                      {selectedItem.genres.map(genre => (
                        <Badge key={genre} variant="outline" className="text-sm bg-vista-dark-lighter border-vista-light/20 text-vista-light/90 hover:bg-vista-dark transition-colors px-3 py-1">
                          {genre}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="bg-vista-dark/50 rounded-lg p-4 border border-vista-light/5">
                    <h3 className="text-sm font-medium text-vista-light/90 mb-1">About this title</h3>
                    <p className="text-sm text-vista-light/70 leading-relaxed">
                      {selectedItem.type === 'shows'
                        ? `Follow the journey in this captivating ${selectedItem.genres.join(', ')} series from ${selectedItem.year}.`
                        : `Experience this thrilling ${selectedItem.genres.join(', ')} film from ${selectedItem.year}.`}
                    </p>
                  </div>

                  <div className="flex items-center gap-3 text-sm text-vista-light/70 bg-vista-blue/5 p-3 rounded-lg">
                    <Clock className="h-5 w-5 text-vista-blue/70" />
                    <div>
                      <p>Added to your list on <span className="text-vista-light/90 font-medium">{new Date(selectedItem.addedDate).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}</span></p>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-3 pt-2">
                    <Link href={`/watch/${selectedItem.id}?contentType=${selectedItem.type === 'shows' ? 'show' : 'movie'}`}>
                      <Button className="bg-vista-blue hover:bg-vista-blue/90 text-white rounded-full px-6 shadow-md shadow-vista-blue/20 hover:shadow-vista-blue/30 transition-all hover:scale-105">
                        <Play className="h-4 w-4 mr-2 fill-current" />
                        Watch Now
                      </Button>
                    </Link>
                    <Link href={`/details/${selectedItem.type}/${selectedItem.id}`}>
                      <Button
                        variant="outline"
                        className="border-vista-light/20 text-vista-light hover:bg-vista-light/10 rounded-full px-5"
                      >
                        <Play className="h-4 w-4 mr-2" />
                        More Info
                      </Button>
                    </Link>
                    <Button
                      type="button"
                      variant="outline"
                      disabled={selectedItem && deleteInProgress[selectedItem.id]}
                      className={`border-red-500/30 rounded-full px-5 ml-auto ${selectedItem && deleteInProgress[selectedItem.id] ? 'text-gray-400 cursor-not-allowed' : 'text-red-400 hover:bg-red-500/10 hover:text-red-300'}`}
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        if (selectedItem) {
                          removeItem(selectedItem.id, e);
                          // Only close the dialog after a short delay to prevent UI jumps
                          setTimeout(() => setIsDialogOpen(false), 300);
                        }
                      }}
                    >
                      {selectedItem && deleteInProgress[selectedItem.id] ? (
                        <>
                          <div className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-solid border-current border-r-transparent"></div>
                          Removing...
                        </>
                      ) : (
                        <>
                          <X className="h-4 w-4 mr-2" />
                          Remove
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}

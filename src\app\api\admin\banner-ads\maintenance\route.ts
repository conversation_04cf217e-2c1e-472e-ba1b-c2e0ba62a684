import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import User from '@/models/User';
import { BannerScheduler } from '@/lib/banner-scheduler';

/**
 * POST /api/admin/banner-ads/maintenance
 * Run banner maintenance tasks (expire old banners, activate scheduled ones)
 */
export async function POST(request: NextRequest) {
  try {
    // Try to get the user ID from the cookie directly
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    if (!userId) {
      console.error('Admin banner maintenance API: No user ID found in request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await ensureMongooseConnection();

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as { role?: string }).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Run maintenance tasks
    const results = await BannerScheduler.runMaintenance();

    return NextResponse.json({
      success: true,
      message: 'Banner maintenance completed successfully',
      results
    });

  } catch (error) {
    console.error('Error running banner maintenance:', error);
    return NextResponse.json(
      { error: 'Failed to run banner maintenance' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/admin/banner-ads/maintenance
 * Get banner analytics and maintenance status
 */
export async function GET(request: NextRequest) {
  try {
    // Try to get the user ID from the cookie directly
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    if (!userId) {
      console.error('Admin banner maintenance API: No user ID found in request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await ensureMongooseConnection();

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as { role?: string }).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Get analytics and top performing banners
    const [analytics, topBanners] = await Promise.all([
      BannerScheduler.getBannerAnalytics(),
      BannerScheduler.getTopPerformingBanners(10)
    ]);

    return NextResponse.json({
      analytics,
      topBanners,
      timestamp: new Date()
    });

  } catch (error) {
    console.error('Error fetching banner maintenance data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch banner maintenance data' },
      { status: 500 }
    );
  }
}

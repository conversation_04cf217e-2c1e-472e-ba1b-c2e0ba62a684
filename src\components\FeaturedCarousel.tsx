'use client';

import { useState, useCallback, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Play, Info, Volume2, VolumeX, ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useLanguage } from '@/lib/i18n/LanguageContext';
import useEmblaCarousel from 'embla-carousel-react';
import AutoPlay from 'embla-carousel-autoplay';

// High-quality featured content
const featuredContentData = [
  {
    id: 'dune-part-two',
    title: 'Dune: Part Two',
    type: 'movie',
    description: '<PERSON> unites with <PERSON><PERSON> and the Fremen while seeking revenge against the conspirators who destroyed his family. Facing a choice between the love of his life and the fate of the universe, he must prevent a terrible future only he can foresee.',
    image: 'https://images.unsplash.com/photo-1506355683710-bd071c0a5828?q=80&w=2070',
    year: 2024,
    genres: ['Sci-Fi', 'Adventure', 'Drama'],
    rating: 'PG-13',
    duration: '2h 46m',
  },
  {
    id: 'fallout',
    title: 'Fallout',
    type: 'show',
    description: 'In a post-apocalyptic world, the inhabitants of luxury fallout shelters are forced to return to the irradiated hellscape their ancestors left behind—and are shocked to discover an incredibly complex, gleefully weird, and highly violent universe waiting for them.',
    image: 'https://images.unsplash.com/photo-1504192010706-dd7f569ee2be?q=80&w=2071',
    year: 2024,
    genres: ['Sci-Fi', 'Action', 'Adventure'],
    rating: 'TV-MA',
    creator: 'Jonathan Nolan & Lisa Joy',
  },
  {
    id: 'house-of-the-dragon',
    title: 'House of the Dragon',
    type: 'show',
    description: 'Set 200 years before the events of Game of Thrones, this epic series tells the story of House Targaryen as they fight through a civil war known as the "Dance of the Dragons" that threatens to tear the Seven Kingdoms apart.',
    image: 'https://images.unsplash.com/photo-1553979459-d2229ba7433b?q=80&w=2068',
    year: 2024,
    genres: ['Fantasy', 'Drama', 'Action'],
    rating: 'TV-MA',
    creator: 'Ryan Condal & George R.R. Martin',
  },
  {
    id: 'shogun',
    title: 'Shōgun',
    type: 'show',
    description: 'Set in Japan in the year 1600, at the dawn of a century-defining civil war, this epic saga follows Lord Yoshii Toranaga as he fights for his life and the future of Japan, with the help of a mysterious European ship pilot named John Blackthorne.',
    image: 'https://images.unsplash.com/photo-1493514789931-586cb221d7a7?q=80&w=2071',
    year: 2024,
    genres: ['Drama', 'Historical', 'Action'],
    rating: 'TV-MA',
    creator: 'Justin Marks & Rachel Kondo',
  },
];

export default function FeaturedCarousel() {
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true }, [
    AutoPlay({ playOnInit: true, delay: 8000 })
  ]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isMuted, setIsMuted] = useState(true);
  const { t } = useLanguage();

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setCurrentIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;

    emblaApi.on('select', onSelect);
    onSelect();

    return () => {
      emblaApi.off('select', onSelect);
    };
  }, [emblaApi, onSelect]);

  return (
    <div className="relative">
      {/* Main Carousel */}
      <div className="overflow-hidden" ref={emblaRef}>
        <div className="flex">
          {featuredContentData.map((content, index) => (
            <div
              className="relative min-w-full aspect-[21/9]"
              key={content.id}
            >
              {/* Background Image with Gradient Overlay */}
              <div className="absolute inset-0">
                <Image
                  src={content.image}
                  alt={content.title}
                  fill
                  priority={index === 0}
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-r from-vista-dark via-vista-dark/50 to-transparent" />
                <div className="absolute inset-0 bg-gradient-to-t from-vista-dark via-transparent to-transparent" />
              </div>

              {/* Content */}
              <div className="relative h-full container mx-auto px-4 flex items-center">
                <motion.div
                  className="max-w-3xl space-y-6"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                >
                  {/* Release Info Badge */}
                  <Badge variant="outline" className="bg-vista-dark/40 text-vista-light border-vista-light/20">
                    {content.type === 'movie' ? 'Movie' : 'Series'} • {content.year}
                  </Badge>

                  {/* Title */}
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-vista-light tracking-tight">
                    {content.title}
                  </h1>

                  {/* Description */}
                  <p className="text-lg text-vista-light/90 max-w-2xl line-clamp-3 md:line-clamp-none">
                    {content.description}
                  </p>

                  {/* Highlights */}
                  <div className="flex flex-wrap gap-3">
                    {content.genres.map((genre, idx) => (
                      <Badge
                        key={idx}
                        variant="secondary"
                        className="bg-vista-blue/10 text-vista-blue border-vista-blue/20"
                      >
                        {genre}
                      </Badge>
                    ))}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center gap-4">
                    <Link href={`/watch/${content.id}?forcePlay=true`}>
                      <Button className="bg-vista-blue hover:bg-vista-blue/90 gap-2">
                        <Play className="h-4 w-4" />
                        Watch Now
                      </Button>
                    </Link>
                    <Link href={`/details/${content.type === 'shows' ? 'shows' : 'movies'}/${content.id}`}>
                      <Button variant="outline" className="border-vista-light/20 hover:bg-vista-dark/40 gap-2">
                        <Info className="h-4 w-4" />
                        More Info
                      </Button>
                    </Link>
                  </div>
                </motion.div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="absolute inset-0 flex items-center justify-between px-4">
        <Button
          variant="outline"
          size="icon"
          className="border-vista-light/20 hover:bg-vista-dark/40"
          onClick={scrollPrev}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="icon"
          className="border-vista-light/20 hover:bg-vista-dark/40"
          onClick={scrollNext}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Volume Control */}
      <div className="absolute bottom-4 right-4">
        <Button
          variant="outline"
          size="icon"
          className="border-vista-light/20 hover:bg-vista-dark/40"
          onClick={() => setIsMuted(!isMuted)}
        >
          {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
        </Button>
      </div>
    </div>
  );
}

'use client';

import React, { useState, useEffect } from 'react';
import { Navbar } from '@/components/Navbar';
import SimpleFooter from '@/components/SimpleFooter';
import Watchlist from '@/components/Watchlist';
import { motion } from 'framer-motion';
import { Bookmark, Film, Tv, Clock } from 'lucide-react';
import { useWatchlist } from '@/contexts/WatchlistContext';

export default function MyListClient() {
  const [isMounted, setIsMounted] = useState(false);
  const { watchlist, isLoading } = useWatchlist();
  const [hasContent, setHasContent] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Update hasContent whenever the watchlist changes
  useEffect(() => {
    setHasContent(watchlist.length > 0);
  }, [watchlist]);

  // Placeholder for pre-hydration
  if (!isMounted || isLoading) {
    return (
      <div className="min-h-screen bg-vista-dark text-vista-light flex items-center justify-center">
        <div className="w-16 h-16 border-4 border-vista-light/20 border-t-vista-light rounded-full animate-spin"></div>
      </div>
    );
  }

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 12
      }
    }
  };

  return (
    <main className="min-h-screen bg-vista-dark text-vista-light">
      <Navbar notificationCount={0} />

      {/* Hero Section - Adjusted based on whether user has content */}
      <div className={`relative bg-vista-dark pt-24 ${hasContent ? 'pb-10' : 'pb-6'}`}>
        <div className="absolute inset-0 bg-gradient-to-b from-vista-blue/10 to-vista-dark"></div>
        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="flex flex-col items-center text-center mb-8"
          >
            <div className="bg-vista-blue/20 p-4 rounded-full mb-4 shadow-lg shadow-vista-blue/5">
              <Bookmark className="h-8 w-8 text-vista-blue" />
            </div>
            <h1 className="text-3xl md:text-4xl font-bold mb-2 text-vista-light drop-shadow-md">My List</h1>
            <p className="text-vista-light/90 max-w-2xl text-lg">
              {hasContent 
                ? "Your personalized collection of favorite movies and shows, ready to watch anytime." 
                : "Start building your personal collection of must-watch content."}
            </p>
          </motion.div>

          {/* Only show categories when user has content */}
          {hasContent && (
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              className="grid grid-cols-2 md:grid-cols-3 gap-4 max-w-3xl mx-auto mb-8"
            >
              <motion.div variants={itemVariants} className="bg-vista-dark-lighter/80 border border-vista-light/10 shadow-md rounded-xl p-4 flex items-center">
                <div className="bg-vista-blue/20 p-2 rounded-full mr-3 shadow-sm">
                  <Film className="h-5 w-5 text-vista-blue" />
                </div>
                <div>
                  <h3 className="text-vista-light font-medium">Movies</h3>
                  <p className="text-xs text-vista-light/80">Your favorite films</p>
                </div>
              </motion.div>

              <motion.div variants={itemVariants} className="bg-vista-dark-lighter/80 border border-vista-light/10 shadow-md rounded-xl p-4 flex items-center">
                <div className="bg-vista-blue/20 p-2 rounded-full mr-3 shadow-sm">
                  <Tv className="h-5 w-5 text-vista-blue" />
                </div>
                <div>
                  <h3 className="text-vista-light font-medium">TV Shows</h3>
                  <p className="text-xs text-vista-light/80">Series you love</p>
                </div>
              </motion.div>

              <motion.div variants={itemVariants} className="bg-vista-dark-lighter/80 border border-vista-light/10 shadow-md rounded-xl p-4 flex items-center">
                <div className="bg-vista-blue/20 p-2 rounded-full mr-3 shadow-sm">
                  <Clock className="h-5 w-5 text-vista-blue" />
                </div>
                <div>
                  <h3 className="text-vista-light font-medium">Watch Later</h3>
                  <p className="text-xs text-vista-light/80">Saved for later</p>
                </div>
              </motion.div>
            </motion.div>
          )}
        </div>

        {/* Decorative gradient */}
        <div className="absolute inset-0 bg-gradient-to-t from-vista-dark to-transparent pointer-events-none"></div>
      </div>

      <div className={`${hasContent ? 'pb-16' : 'pb-0'}`}>
        <Watchlist title="" /> {/* Removed title as we now have a hero section with title */}
      </div>

      <SimpleFooter />
    </main>
  );
}

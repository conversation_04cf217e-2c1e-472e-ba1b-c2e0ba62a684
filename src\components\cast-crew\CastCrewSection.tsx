'use client';

import { useRef, useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Users, UserCheck } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { CastCrewCard } from './CastCrewCard';

interface CastMember {
  id: string;
  name: string;
  character?: string;
  profile_path?: string | null;
}

interface CrewMember {
  id: string;
  name: string;
  job: string;
  profile_path: string | null;
}

interface CastCrewSectionProps {
  cast: CastMember[];
  crew: CrewMember[];
}

export function CastCrewSection({ cast, crew }: CastCrewSectionProps) {
  const castScrollRef = useRef<HTMLDivElement>(null);
  const crewScrollRef = useRef<HTMLDivElement>(null);
  const [showCastControls, setShowCastControls] = useState(false);
  const [showCrewControls, setShowCrewControls] = useState(false);
  const [castCanScrollLeft, setCastCanScrollLeft] = useState(false);
  const [castCanScrollRight, setCastCanScrollRight] = useState(false);
  const [crewCanScrollLeft, setCrewCanScrollLeft] = useState(false);
  const [crewCanScrollRight, setCrewCanScrollRight] = useState(false);

  const checkScrollability = (ref: React.RefObject<HTMLDivElement>, setCanScrollLeft: (val: boolean) => void, setCanScrollRight: (val: boolean) => void) => {
    if (ref.current) {
      const { scrollLeft, scrollWidth, clientWidth } = ref.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 10);
    }
  };

  const scroll = (ref: React.RefObject<HTMLDivElement>, direction: 'left' | 'right') => {
    if (ref.current) {
      const scrollAmount = 300;
      const newScrollLeft = direction === 'left' 
        ? ref.current.scrollLeft - scrollAmount 
        : ref.current.scrollLeft + scrollAmount;
      
      ref.current.scrollTo({
        left: newScrollLeft,
        behavior: 'smooth'
      });
    }
  };

  useEffect(() => {
    const castRef = castScrollRef.current;
    const crewRef = crewScrollRef.current;

    const handleCastScroll = () => checkScrollability(castScrollRef, setCastCanScrollLeft, setCastCanScrollRight);
    const handleCrewScroll = () => checkScrollability(crewScrollRef, setCrewCanScrollLeft, setCrewCanScrollRight);

    if (castRef) {
      castRef.addEventListener('scroll', handleCastScroll);
      handleCastScroll(); // Initial check
      setShowCastControls(castRef.scrollWidth > castRef.clientWidth);
    }

    if (crewRef) {
      crewRef.addEventListener('scroll', handleCrewScroll);
      handleCrewScroll(); // Initial check
      setShowCrewControls(crewRef.scrollWidth > crewRef.clientWidth);
    }

    return () => {
      if (castRef) castRef.removeEventListener('scroll', handleCastScroll);
      if (crewRef) crewRef.removeEventListener('scroll', handleCrewScroll);
    };
  }, [cast, crew]);

  if (!cast.length && !crew.length) return null;

  return (
    <div className="space-y-8">
      {/* Cast Section */}
      {cast.length > 0 && (
        <div className="relative">
          <div className="flex items-center gap-3 mb-4">
            <Users className="h-5 w-5 text-vista-blue" />
            <h2 className="text-xl font-semibold text-vista-light">Cast</h2>
            <span className="text-sm text-vista-light/60">({cast.length})</span>
          </div>
          
          <div className="relative group">
            {/* Left scroll button */}
            {showCastControls && castCanScrollLeft && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-black/80 text-vista-light hover:bg-black/90 rounded-full h-8 w-8 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                onClick={() => scroll(castScrollRef, 'left')}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
            )}

            {/* Right scroll button */}
            {showCastControls && castCanScrollRight && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-black/80 text-vista-light hover:bg-black/90 rounded-full h-8 w-8 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                onClick={() => scroll(castScrollRef, 'right')}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            )}

            {/* Scrollable content */}
            <div
              ref={castScrollRef}
              className="flex gap-3 overflow-x-auto scrollbar-hide pb-2"
              style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
            >
              {cast.map((member, index) => (
                <CastCrewCard
                  key={`cast-${member.id}-${index}`}
                  name={member.name}
                  role={member.character}
                  imageUrl={member.profile_path}
                  index={index}
                />
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Crew Section */}
      {crew.length > 0 && (
        <div className="relative">
          <div className="flex items-center gap-3 mb-4">
            <UserCheck className="h-5 w-5 text-vista-blue" />
            <h2 className="text-xl font-semibold text-vista-light">Crew</h2>
            <span className="text-sm text-vista-light/60">({crew.length})</span>
          </div>
          
          <div className="relative group">
            {/* Left scroll button */}
            {showCrewControls && crewCanScrollLeft && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-black/80 text-vista-light hover:bg-black/90 rounded-full h-8 w-8 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                onClick={() => scroll(crewScrollRef, 'left')}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
            )}

            {/* Right scroll button */}
            {showCrewControls && crewCanScrollRight && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-black/80 text-vista-light hover:bg-black/90 rounded-full h-8 w-8 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                onClick={() => scroll(crewScrollRef, 'right')}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            )}

            {/* Scrollable content */}
            <div
              ref={crewScrollRef}
              className="flex gap-3 overflow-x-auto scrollbar-hide pb-2"
              style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
            >
              {crew.map((member, index) => (
                <CastCrewCard
                  key={`crew-${member.id}-${index}`}
                  name={member.name}
                  role={member.job}
                  imageUrl={member.profile_path}
                  index={index}
                />
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

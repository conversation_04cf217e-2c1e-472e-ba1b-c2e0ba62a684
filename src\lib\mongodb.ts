import mongoose from 'mongoose';

// Connection state tracking
let isConnected = false;
let connectionPromise: Promise<typeof mongoose> | null = null;

// Connection options for better performance
const connectionOptions = {
  maxPoolSize: 10, // Limit concurrent connections
  serverSelectionTimeoutMS: 5000, // Faster timeout
  socketTimeoutMS: 45000, // Socket timeout
  bufferMaxEntries: 0, // Disable mongoose buffering
  bufferCommands: false, // Disable mongoose buffering
  useNewUrlParser: true,
  useUnifiedTopology: true,
};

/**
 * Optimized MongoDB connection with connection pooling
 */
export async function ensureMongooseConnection(): Promise<typeof mongoose> {
  // Return existing connection if already connected
  if (isConnected && mongoose.connection.readyState === 1) {
    return mongoose;
  }

  // Return existing promise if connection is in progress
  if (connectionPromise) {
    return connectionPromise;
  }

  const MONGODB_URI = process.env.MONGODB_URI;
  if (!MONGODB_URI) {
    throw new Error('MONGODB_URI is not defined');
  }

  // Create new connection promise
  connectionPromise = mongoose.connect(MONGODB_URI, connectionOptions)
    .then(() => {
      isConnected = true;
      console.log('✅ MongoDB connected successfully');
      return mongoose;
    })
    .catch((error) => {
      isConnected = false;
      connectionPromise = null;
      console.error('❌ MongoDB connection failed:', error);
      throw error;
    });

  return connectionPromise;
}

/**
 * Graceful connection cleanup
 */
export async function closeMongooseConnection(): Promise<void> {
  if (mongoose.connection.readyState !== 0) {
    await mongoose.connection.close();
    isConnected = false;
    connectionPromise = null;
    console.log('🔌 MongoDB connection closed');
  }
}

// Legacy export for backward compatibility
export default ensureMongooseConnection;

'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { UserCheck, AlertCircle, Check, UserPlus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { UserAvatar } from '@/components/UserAvatar';
import { useAuth } from '@/contexts/AuthContext';
import { useProfiles } from '@/contexts/ProfileContext';
import { CloudinaryUploadWidget } from '@/components/CloudinaryUploadWidget';
import { Navbar } from '@/components/Navbar';
import Footer from '@/components/Footer';
import { toast } from 'sonner';

// Define the avatar options
const defaultAvatars = [
  '/avatars/avatar-1.png',
  '/avatars/avatar-2.png',
  '/avatars/avatar-3.png',
  '/avatars/avatar-4.png',
  '/avatars/avatar-5.png',
  '/avatars/avatar-6.png',
];

export default function NewProfilePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const isMainProfile = searchParams.get('mainProfile') === 'true';
  const { user } = useAuth();
  const { createProfile, profiles, remainingProfiles } = useProfiles();
  const [name, setName] = useState('');
  const [isKids, setIsKids] = useState(false);
  const [selectedAvatar, setSelectedAvatar] = useState(defaultAvatars[0]);
  const [nameError, setNameError] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [isCustomAvatar, setIsCustomAvatar] = useState(false);
  const [avatarUploadLoading, setAvatarUploadLoading] = useState(false);
  
  // Initialize name with user's name if creating main profile
  useEffect(() => {
    if (isMainProfile && user?.name) {
      setName(user.name);
    }
  }, [isMainProfile, user]);
  
  // Redirect if not logged in
  useEffect(() => {
    if (!user?.id) {
      router.push('/signin');
    }
  }, [user, router]);
  
  // Check if user already has the maximum number of profiles
  useEffect(() => {
    if (remainingProfiles <= 0) {
      toast.error('You have reached the maximum number of profiles allowed (5)');
      router.push('/profiles');
    }
  }, [remainingProfiles, router]);
  
  // Validate form on submit
  const validateForm = () => {
    if (!name.trim()) {
      setNameError('Please enter a profile name');
      return false;
    }
    if (name.length > 20) {
      setNameError('Profile name must be 20 characters or less');
      return false;
    }
    setNameError('');
    return true;
  };
  
  // Handle profile creation
  const handleCreateProfile = async () => {
    if (!validateForm()) return;
    if (!user?.id) {
      toast.error('You need to be logged in to create a profile');
      return;
    }
    
    setIsCreating(true);
    
    try {
      // Set isPrimary to true if this is the main profile
      const result = await createProfile(
        name.trim(), 
        selectedAvatar, 
        isKids, 
        isMainProfile // Pass isMainProfile as isPrimary
      );
      
      if (result.success) {
        toast.success(`${isMainProfile ? 'Main profile' : 'Profile'} created successfully`);
        router.push('/profiles');
      } else {
        toast.error(result.error || 'Failed to create profile');
      }
    } catch (error) {
      console.error('Error creating profile:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsCreating(false);
    }
  };
  
  // Handle Cloudinary upload success
  const handleUploadSuccess = (url: string) => {
    setSelectedAvatar(url);
    setIsCustomAvatar(true);
    setAvatarUploadLoading(false);
    toast.success('Avatar uploaded successfully');
  };
  
  // Create upload button click handler
  const handleUploadClick = () => {
    setAvatarUploadLoading(true);
  };
  
  return (
    <div className="min-h-screen bg-vista-dark text-vista-light flex flex-col">
      <Navbar />
      
      {/* Header */}
      <div className="border-b border-vista-light/10 px-4 py-4 mt-16">
        <div className="container max-w-4xl mx-auto flex items-center justify-between">
          <h1 className="text-xl md:text-2xl font-bold">
            {isMainProfile ? 'Create Main Profile' : 'Create Profile'}
          </h1>
          <div className="flex items-center">
            {!isMainProfile && (
              <div className="mr-4 text-sm text-vista-light/70">
                <span className="font-medium text-vista-light">{remainingProfiles}</span> of 5 profiles remaining
              </div>
            )}
            <Button
              variant="ghost"
              onClick={() => router.back()}
              className="text-vista-light/70 hover:text-vista-light"
            >
              Cancel
            </Button>
          </div>
        </div>
      </div>
      
      {/* Content */}
      <div className="flex-1 container max-w-4xl mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-vista-dark-lighter rounded-xl p-6 md:p-8"
        >
          {isMainProfile && (
            <div className="mb-6 p-4 bg-vista-blue/10 border border-vista-blue/30 rounded-lg">
              <h3 className="text-vista-blue font-medium text-lg flex items-center mb-2">
                <UserPlus className="h-5 w-5 mr-2" />
                Main Profile
              </h3>
              <p className="text-vista-light/80">
                This will be your main profile tied to your account. You'll use this profile for most of your viewing.
                Your account settings like billing and personal information are connected to this profile.
              </p>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Avatar Selection */}
            <div className="flex flex-col items-center justify-start">
              <div className="relative mb-6 group">
                {avatarUploadLoading ? (
                  <div className="h-32 w-32 flex items-center justify-center border-4 border-vista-blue rounded-full">
                    <div className="h-8 w-8 border-4 border-vista-blue/30 border-t-vista-blue rounded-full animate-spin"></div>
                  </div>
                ) : (
                  <UserAvatar
                    src={selectedAvatar}
                    alt="Profile Avatar"
                    size="xl"
                    className={`h-32 w-32 border-4 ${isKids ? 'border-amber-500' : isMainProfile ? 'border-vista-blue' : 'border-vista-light/50'}`}
                  />
                )}
                {isKids && (
                  <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 bg-amber-500 text-black text-xs font-bold px-3 py-0.5 rounded-full">
                    KIDS
                  </div>
                )}
                {isMainProfile && (
                  <div className="absolute -top-2 -right-2 bg-vista-blue rounded-full w-7 h-7 flex items-center justify-center border-2 border-vista-dark">
                    <UserPlus className="h-4 w-4 text-white" />
                  </div>
                )}
              </div>
              
              <h3 className="font-medium text-lg mb-3">Choose Avatar</h3>
              
              <div className="grid grid-cols-3 gap-3">
                {defaultAvatars.map((avatar) => (
                  <button
                    key={avatar}
                    onClick={() => {
                      setSelectedAvatar(avatar);
                      setIsCustomAvatar(false);
                    }}
                    className={`relative w-14 h-14 rounded-lg overflow-hidden border-2 transition-all ${
                      selectedAvatar === avatar && !isCustomAvatar
                        ? isKids 
                          ? 'border-amber-500 ring-2 ring-amber-500/30' 
                          : 'border-vista-blue ring-2 ring-vista-blue/30'
                        : 'border-transparent hover:border-vista-light/30'
                    }`}
                  >
                    <Image
                      src={avatar}
                      alt="Avatar option"
                      fill
                      sizes="56px"
                      className="object-cover"
                    />
                    {selectedAvatar === avatar && !isCustomAvatar && (
                      <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                        <Check className="h-5 w-5 text-white" />
                      </div>
                    )}
                  </button>
                ))}
              </div>
              
              <div className="mt-4 w-full flex justify-center">
                <CloudinaryUploadWidget
                  onUploadSuccess={handleUploadSuccess}
                  onUploadStart={handleUploadClick}
                  buttonText="Upload Custom Avatar"
                  className="text-sm"
                />
              </div>
              
              {isCustomAvatar && (
                <div className="mt-2 text-xs text-vista-light/60">
                  Using custom uploaded avatar
                </div>
              )}
            </div>
            
            {/* Profile Details */}
            <div className="md:col-span-2 space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="profile-name">Profile Name</Label>
                  <Input
                    id="profile-name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    placeholder={isMainProfile ? "Main profile name" : "Enter a profile name"}
                    className="bg-vista-dark/60 border-vista-light/20 focus:border-vista-blue"
                  />
                  {nameError && (
                    <div className="flex items-center text-red-400 text-sm mt-1">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {nameError}
                    </div>
                  )}
                </div>
                
                <div className="flex items-center justify-between pt-3">
                  <div className="space-y-1">
                    <Label htmlFor="kids-profile" className="text-base flex items-center">
                      Kids Profile
                      <Badge className="ml-2 bg-amber-500 text-black text-xs">KIDS</Badge>
                    </Label>
                    <p className="text-vista-light/70 text-sm">
                      Content will be limited to child-friendly ratings
                    </p>
                  </div>
                  <Switch
                    id="kids-profile"
                    checked={isKids}
                    onCheckedChange={setIsKids}
                  />
                </div>
                
                {isKids && (
                  <div className="p-4 bg-vista-dark/40 rounded-lg border border-amber-500/30 mt-4">
                    <h4 className="font-medium text-amber-400 flex items-center mb-2">
                      <UserCheck className="h-4 w-4 mr-2" />
                      Kids Profile Features
                    </h4>
                    <ul className="text-sm text-vista-light/80 space-y-1 ml-6 list-disc">
                      <li>Child-friendly content only</li>
                      <li>No mature themes or explicit language</li>
                      <li>Simplified navigation experience</li>
                      <li>Parental controls and monitoring</li>
                    </ul>
                  </div>
                )}
              </div>
              
              <div className="pt-6 flex flex-col md:flex-row md:justify-end space-y-3 md:space-y-0 md:space-x-3">
                <Button
                  variant="outline"
                  onClick={() => router.back()}
                  className="border-vista-light/20 hover:bg-vista-light/10"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateProfile}
                  disabled={isCreating}
                  className="bg-vista-blue hover:bg-vista-blue/90"
                >
                  {isCreating ? (
                    <>
                      <div className="h-4 w-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                      Creating...
                    </>
                  ) : (
                    `Create ${isMainProfile ? 'Main Profile' : 'Profile'}`
                  )}
                </Button>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
      
      <Footer />
    </div>
  );
} 
import mongoose, { Document, Schema } from 'mongoose';

export interface ContentViewDocument extends Document {
  contentId: mongoose.Types.ObjectId;
  contentType: 'movie' | 'show' | 'episode';
  userId: mongoose.Types.ObjectId;
  timestamp: Date;
  duration: number; // in seconds
  completed: boolean;
  progress: number; // percentage 0-100
}

const ContentViewSchema = new Schema<ContentViewDocument>(
  {
    contentId: {
      type: Schema.Types.ObjectId,
      required: true,
      refPath: 'contentType'
    },
    contentType: {
      type: String,
      required: true,
      enum: ['movie', 'show', 'episode']
    },
    userId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: 'User'
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    duration: {
      type: Number,
      default: 0
    },
    completed: {
      type: Boolean,
      default: false
    },
    progress: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    }
  },
  {
    timestamps: true
  }
);

// Add indexes to improve query performance
ContentViewSchema.index({ contentId: 1, userId: 1 });
ContentViewSchema.index({ userId: 1, timestamp: -1 });
ContentViewSchema.index({ contentId: 1, timestamp: -1 });
ContentViewSchema.index({ contentType: 1, timestamp: -1 });

// Create or retrieve the model
const ContentView = mongoose.models.ContentView || mongoose.model<ContentViewDocument>('ContentView', ContentViewSchema);

export default ContentView;

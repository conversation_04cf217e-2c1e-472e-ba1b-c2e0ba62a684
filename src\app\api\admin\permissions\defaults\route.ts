import { NextRequest, NextResponse } from 'next/server';
import { withMongo } from '@/middleware/mongoMiddleware';
import { isAdmin } from '@/lib/auth';
import { DEFAULT_PERMISSIONS } from '@/lib/permissions';

// Import these for direct use in the handlers
import mongoose from 'mongoose';

/**
 * GET /api/admin/permissions/defaults
 * Get default permissions for all roles
 */
async function getHandler(request: NextRequest) {
  try {
    // Get userId from query string as a fallback
    const { searchParams } = new URL(request.url);
    const queryUserId = searchParams.get('userId');

    // Try cookie-based auth first
    const adminCheck = await isAdmin(request);

    // If cookie auth fails but we have a userId in the query, verify admin status directly
    if (!adminCheck.isAuthorized && queryUserId) {
      console.log('Cookie-based auth failed, trying query param userId:', queryUserId);

      // Define the User schema directly
      const UserSchema = new mongoose.default.Schema({
        role: String
      });

      // Get the User model
      const User = mongoose.default.models.User ||
                  mongoose.default.model('User', UserSchema);

      // Find the user and check if they're an admin
      const user = await User.findById(queryUserId).select('role').lean();

      if (!user) {
        return NextResponse.json({
          error: 'User not found'
        }, { status: 404 });
      }

      if ((user as any).role !== 'admin') {
        return NextResponse.json({
          error: 'Forbidden: You do not have permission to access this resource'
        }, { status: 403 });
      }

      // User is authenticated and is an admin
      console.log(`User ${queryUserId} verified as admin via query param`);
      const userId = queryUserId;
    } else if (!adminCheck.isAuthorized) {
      // Both cookie auth and query param auth failed
      return NextResponse.json({ error: adminCheck.message || "Unauthorized" }, { status: adminCheck.status || 401 });
    } else {
      // Cookie auth succeeded
      console.log(`User ${adminCheck.userId} verified as admin via cookie`);
    }

    // User is authenticated and is an admin
    const userId = adminCheck.isAuthorized ? adminCheck.userId : queryUserId;

    // Define the Setting schema directly
    const SettingSchema = new mongoose.default.Schema({
      key: { type: String, required: true, unique: true },
      value: mongoose.default.Schema.Types.Mixed,
      group: { type: String, required: true, index: true },
      label: String,
      description: String,
      type: { type: String, enum: ['string', 'number', 'boolean', 'json', 'array'] },
      options: [mongoose.default.Schema.Types.Mixed],
      isPublic: { type: Boolean, default: false },
      isProtected: { type: Boolean, default: false }
    }, {
      timestamps: true
    });

    // Get the Setting model
    const Setting = mongoose.default.models.Setting ||
                  mongoose.default.model('Setting', SettingSchema);

    // Get default permissions from settings
    let defaultPermissions = {};
    try {
      // Try to get permissions from settings
      const permissionSettings = await Setting.findOne({ key: 'defaultPermissions' }).lean();
      if (permissionSettings && permissionSettings.value) {
        defaultPermissions = permissionSettings.value;
      } else {
        // If not found, use system defaults
        defaultPermissions = DEFAULT_PERMISSIONS;
      }
    } catch (settingError) {
      console.error('Error fetching permission settings:', settingError);
      // Fallback to system defaults
      defaultPermissions = DEFAULT_PERMISSIONS;
    }

    // Return default permissions
    return NextResponse.json({
      permissions: defaultPermissions,
      systemDefaults: DEFAULT_PERMISSIONS
    });
  } catch (error) {
    console.error('Error fetching default permissions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch default permissions', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/permissions/defaults
 * Update default permissions for all roles
 */
async function putHandler(request: NextRequest) {
  try {
    // Get userId from query string as a fallback
    const { searchParams } = new URL(request.url);
    const queryUserId = searchParams.get('userId');

    // Try cookie-based auth first
    const adminCheck = await isAdmin(request);

    // If cookie auth fails but we have a userId in the query, verify admin status directly
    if (!adminCheck.isAuthorized && queryUserId) {
      console.log('Cookie-based auth failed, trying query param userId:', queryUserId);

      // Define the User schema directly
      const UserSchema = new mongoose.default.Schema({
        role: String
      });

      // Get the User model
      const User = mongoose.default.models.User ||
                  mongoose.default.model('User', UserSchema);

      // Find the user and check if they're an admin
      const user = await User.findById(queryUserId).select('role').lean();

      if (!user) {
        return NextResponse.json({
          error: 'User not found'
        }, { status: 404 });
      }

      if ((user as any).role !== 'admin') {
        return NextResponse.json({
          error: 'Forbidden: You do not have permission to access this resource'
        }, { status: 403 });
      }

      // User is authenticated and is an admin
      console.log(`User ${queryUserId} verified as admin via query param`);
      const userId = queryUserId;
    } else if (!adminCheck.isAuthorized) {
      // Both cookie auth and query param auth failed
      return NextResponse.json({ error: adminCheck.message || "Unauthorized" }, { status: adminCheck.status || 401 });
    } else {
      // Cookie auth succeeded
      console.log(`User ${adminCheck.userId} verified as admin via cookie`);
    }

    // User is authenticated and is an admin
    const userId = adminCheck.isAuthorized ? adminCheck.userId : queryUserId;

    // Define the Setting schema directly
    const SettingSchema = new mongoose.default.Schema({
      key: { type: String, required: true, unique: true },
      value: mongoose.default.Schema.Types.Mixed,
      group: { type: String, required: true, index: true },
      label: String,
      description: String,
      type: { type: String, enum: ['string', 'number', 'boolean', 'json', 'array'] },
      options: [mongoose.default.Schema.Types.Mixed],
      isPublic: { type: Boolean, default: false },
      isProtected: { type: Boolean, default: false }
    }, {
      timestamps: true
    });

    // Get the Setting model
    const Setting = mongoose.default.models.Setting ||
                  mongoose.default.model('Setting', SettingSchema);

    // Define the UserActivity schema directly
    const UserActivitySchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      action: String,
      details: String,
      ipAddress: String,
      userAgent: String,
      timestamp: { type: Date, default: Date.now },
      metadata: mongoose.default.Schema.Types.Mixed
    }, {
      timestamps: true
    });

    // Get the UserActivity model
    const UserActivity = mongoose.default.models.UserActivity ||
                        mongoose.default.model('UserActivity', UserActivitySchema);

    // Get request data
    const data = await request.json();

    // Validate permissions
    if (!data.permissions || typeof data.permissions !== 'object') {
      return NextResponse.json(
        { error: 'Invalid permissions data' },
        { status: 400 }
      );
    }

    // Update default permissions directly
    await Setting.updateOne(
      { key: 'defaultPermissions' },
      {
        $set: {
          value: data.permissions,
          group: 'permissions',
          label: 'Default Permissions',
          description: 'Default permissions for each user role',
          type: 'json'
        }
      },
      { upsert: true }
    );

    // Log admin activity directly
    await UserActivity.create({
      userId: new mongoose.default.Types.ObjectId(userId),
      type: 'admin',
      action: 'update_default_permissions',
      details: `Admin updated default permissions for all roles`,
      ipAddress: request.ip || request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      timestamp: new Date(),
      metadata: {
        roles: Object.keys(data.permissions)
      }
    });

    // Return success
    return NextResponse.json({
      success: true,
      message: 'Default permissions updated successfully'
    });
  } catch (error) {
    console.error('Error updating default permissions:', error);
    return NextResponse.json(
      { error: 'Failed to update default permissions', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/permissions/defaults
 * Reset default permissions to system defaults
 */
async function deleteHandler(request: NextRequest) {
  try {
    // Get userId from query string as a fallback
    const { searchParams } = new URL(request.url);
    const queryUserId = searchParams.get('userId');

    // Try cookie-based auth first
    const adminCheck = await isAdmin(request);

    // If cookie auth fails but we have a userId in the query, verify admin status directly
    if (!adminCheck.isAuthorized && queryUserId) {
      console.log('Cookie-based auth failed, trying query param userId:', queryUserId);

      // Define the User schema directly
      const UserSchema = new mongoose.default.Schema({
        role: String
      });

      // Get the User model
      const User = mongoose.default.models.User ||
                  mongoose.default.model('User', UserSchema);

      // Find the user and check if they're an admin
      const user = await User.findById(queryUserId).select('role').lean();

      if (!user) {
        return NextResponse.json({
          error: 'User not found'
        }, { status: 404 });
      }

      if ((user as any).role !== 'admin') {
        return NextResponse.json({
          error: 'Forbidden: You do not have permission to access this resource'
        }, { status: 403 });
      }

      // User is authenticated and is an admin
      console.log(`User ${queryUserId} verified as admin via query param`);
      const userId = queryUserId;
    } else if (!adminCheck.isAuthorized) {
      // Both cookie auth and query param auth failed
      return NextResponse.json({ error: adminCheck.message || "Unauthorized" }, { status: adminCheck.status || 401 });
    } else {
      // Cookie auth succeeded
      console.log(`User ${adminCheck.userId} verified as admin via cookie`);
    }

    // User is authenticated and is an admin
    const userId = adminCheck.isAuthorized ? adminCheck.userId : queryUserId;

    // Define the Setting schema directly
    const SettingSchema = new mongoose.default.Schema({
      key: { type: String, required: true, unique: true },
      value: mongoose.default.Schema.Types.Mixed,
      group: { type: String, required: true, index: true },
      label: String,
      description: String,
      type: { type: String, enum: ['string', 'number', 'boolean', 'json', 'array'] },
      options: [mongoose.default.Schema.Types.Mixed],
      isPublic: { type: Boolean, default: false },
      isProtected: { type: Boolean, default: false }
    }, {
      timestamps: true
    });

    // Get the Setting model
    const Setting = mongoose.default.models.Setting ||
                  mongoose.default.model('Setting', SettingSchema);

    // Define the UserActivity schema directly
    const UserActivitySchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      action: String,
      details: String,
      ipAddress: String,
      userAgent: String,
      timestamp: { type: Date, default: Date.now },
      metadata: mongoose.default.Schema.Types.Mixed
    }, {
      timestamps: true
    });

    // Get the UserActivity model
    const UserActivity = mongoose.default.models.UserActivity ||
                        mongoose.default.model('UserActivity', UserActivitySchema);

    // Reset default permissions directly
    await Setting.updateOne(
      { key: 'defaultPermissions' },
      {
        $set: {
          value: DEFAULT_PERMISSIONS,
          group: 'permissions',
          label: 'Default Permissions',
          description: 'Default permissions for each user role',
          type: 'json'
        }
      },
      { upsert: true }
    );

    // Log admin activity directly
    await UserActivity.create({
      userId: new mongoose.default.Types.ObjectId(userId),
      type: 'admin',
      action: 'reset_default_permissions',
      details: `Admin reset default permissions to system defaults`,
      ipAddress: request.ip || request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      timestamp: new Date()
    });

    // Return success
    return NextResponse.json({
      success: true,
      message: 'Default permissions reset to system defaults',
      permissions: DEFAULT_PERMISSIONS
    });
  } catch (error) {
    console.error('Error resetting default permissions:', error);
    return NextResponse.json(
      { error: 'Failed to reset default permissions', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// Export the handlers with MongoDB connection middleware
export const GET = withMongo(getHandler);
export const PUT = withMongo(putHandler);
export const DELETE = withMongo(deleteHandler);

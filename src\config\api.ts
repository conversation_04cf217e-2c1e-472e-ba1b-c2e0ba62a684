/**
 * API Configuration
 *
 * Centralized API configuration values
 */

// TMDb API configuration
export const TMDB_CONFIG = {
  API_KEY: process.env.NEXT_PUBLIC_TMDB_API_KEY || '',
  ACCESS_TOKEN: process.env.NEXT_PUBLIC_TMDB_ACCESS_TOKEN || '',
  BASE_URL: process.env.NEXT_PUBLIC_TMDB_BASE_URL || 'https://api.themoviedb.org/3',
  IMAGE_BASE_URL: process.env.NEXT_PUBLIC_TMDB_IMAGE_BASE_URL || 'https://image.tmdb.org/t/p',
  AUTH_METHOD: process.env.NEXT_PUBLIC_TMDB_AUTH_METHOD || 'bearer_token',
  POSTER_SIZES: {
    small: 'w185',
    medium: 'w342',
    large: 'w500',
    original: 'original'
  }
};

// OMDB API configuration
export const OMDB_CONFIG = {
  API_KEY: process.env.NEXT_PUBLIC_OMDB_API_KEY || '',
  BASE_URL: process.env.NEXT_PUBLIC_OMDB_BASE_URL || 'http://www.omdbapi.com'
};

// Validate API key
export const isValidApiKey = (key: string): boolean => {
  return Boolean(key && key.length >= 20 && key !== 'YOUR_API_KEY_HERE');
};

// Validate OMDB API key (different format than TMDB keys)
export const isValidOmdbApiKey = (key: string): boolean => {
  return Boolean(key && key.length > 0 && key !== 'YOUR_API_KEY_HERE');
};
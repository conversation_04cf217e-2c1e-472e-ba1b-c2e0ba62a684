"use client"

import React, { useState, useEffect, useCallback } from "react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { X, AlertTriangle, AlertCircle, CheckCircle, Info } from "lucide-react"
import { cn } from "@/lib/utils"
import { Alert } from "@/components/ui/alert"

export type ModalType = 'default' | 'success' | 'error' | 'warning' | 'info'
export type ModalSize = 'sm' | 'md' | 'lg' | 'xl' | 'full'

const MODAL_TYPE_STYLES = {
  default: "",
  success: "border-green-500/30",
  error: "border-red-500/30",
  warning: "border-amber-500/30",
  info: "border-vista-blue/30",
}

const MODAL_ICONS = {
  success: <CheckCircle className="h-6 w-6 text-green-500" />,
  error: <AlertCircle className="h-6 w-6 text-red-500" />,
  warning: <AlertTriangle className="h-6 w-6 text-amber-500" />,
  info: <Info className="h-6 w-6 text-vista-blue" />,
}

const MODAL_SIZES = {
  sm: "max-w-sm",
  md: "max-w-md",
  lg: "max-w-lg",
  xl: "max-w-xl",
  full: "max-w-screen-lg"
}

interface ModalProps {
  title?: React.ReactNode;
  description?: React.ReactNode;
  children?: React.ReactNode;
  open?: boolean;
  defaultOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
  type?: ModalType;
  size?: ModalSize;
  showCloseButton?: boolean;
  className?: string;
  contentClassName?: string;
  showIcon?: boolean;
  footer?: React.ReactNode;
  closeOnClickOutside?: boolean;
}

export function Modal({
  title,
  description,
  children,
  open,
  defaultOpen,
  onOpenChange,
  type = "default",
  size = "md",
  showCloseButton = true,
  className,
  contentClassName,
  showIcon = false,
  footer,
  closeOnClickOutside = true,
}: ModalProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen || false);

  // Controlled mode via props
  const isControlled = open !== undefined;
  const showModal = isControlled ? open : isOpen;

  const handleOpenChange = useCallback((value: boolean) => {
    if (!isControlled) {
      setIsOpen(value);
    }
    onOpenChange?.(value);
  }, [isControlled, onOpenChange]);

  useEffect(() => {
    // Add escape key handler
    const handleEscapeKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && showModal) {
        handleOpenChange(false);
      }
    };

    document.addEventListener('keydown', handleEscapeKey);
    return () => document.removeEventListener('keydown', handleEscapeKey);
  }, [showModal, handleOpenChange]);

  return (
    <Dialog
      open={showModal}
      onOpenChange={handleOpenChange}
    >
      <DialogContent
        className={cn(
          "bg-vista-dark-lighter border-vista-light/10",
          MODAL_TYPE_STYLES[type],
          MODAL_SIZES[size],
          "p-0 overflow-hidden",
          contentClassName
        )}
        onPointerDownOutside={(e) => {
          if (!closeOnClickOutside) {
            e.preventDefault();
          }
        }}
      >
        {showCloseButton && (
          <DialogClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
            <X className="h-4 w-4 text-vista-light" />
            <span className="sr-only">Close</span>
          </DialogClose>
        )}

        <div className={cn("p-6", className)}>
          {(title || description) && (
            <DialogHeader className="flex text-left gap-4">
              {showIcon && type !== 'default' && (
                <div className="flex-shrink-0">
                  {MODAL_ICONS[type]}
                </div>
              )}
              <div>
                {title && (
                  <DialogTitle className="text-xl text-vista-light">
                    {title}
                  </DialogTitle>
                )}
                {description && (
                  <DialogDescription className="text-vista-light/70 mt-1.5">
                    {description}
                  </DialogDescription>
                )}
              </div>
            </DialogHeader>
          )}

          {children && (
            <div className={cn("py-4", { "pt-0": !title && !description })}>
              {children}
            </div>
          )}

          {footer && (
            <DialogFooter className="flex justify-end gap-3 mt-2">
              {footer}
            </DialogFooter>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Confirmation modal with Yes/No buttons
interface ConfirmModalProps extends Omit<ModalProps, 'footer'> {
  confirmLabel?: string;
  cancelLabel?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
  isDestructive?: boolean;
  isLoading?: boolean;
  alertMessage?: string; // Added new prop for alert message
  alertType?: 'info' | 'warning' | 'error'; // Added new prop for alert type
}

export function ConfirmModal({
  confirmLabel = "Confirm",
  cancelLabel = "Cancel",
  onConfirm,
  onCancel,
  isDestructive = false,
  isLoading = false,
  alertMessage,
  alertType = "warning",
  ...props
}: ConfirmModalProps) {
  const handleConfirm = () => {
    onConfirm?.();
  };

  const handleCancel = () => {
    onCancel?.();
    props.onOpenChange?.(false);
  };

  const footerContent = (
    <>
      <Button
        variant="outline"
        onClick={handleCancel}
        className="border-vista-light/20 text-vista-light hover:bg-vista-light/10"
        disabled={isLoading}
      >
        {cancelLabel}
      </Button>
      <Button
        variant={isDestructive ? "destructive" : "default"}
        onClick={handleConfirm}
        className={cn({
          "bg-red-600 hover:bg-red-700": isDestructive,
          "bg-vista-blue hover:bg-vista-blue/90": !isDestructive,
        })}
        disabled={isLoading}
      >
        {isLoading ? "Loading..." : confirmLabel}
      </Button>
    </>
  );

  // Determine modal content including alert if specified
  const modalContent = alertMessage ? (
    <div className="space-y-4">
      <Alert variant={alertType as "info" | "warning" | "error"}>
        <div className="font-medium">
          {alertType === "error" ? "Warning" :
           alertType === "warning" ? "Caution" : "Information"}
        </div>
        <div className="mt-1">{alertMessage}</div>
      </Alert>
      {props.children}
    </div>
  ) : props.children;

  return (
    <Modal
      {...props}
      showIcon={props.showIcon !== undefined ? props.showIcon : true}
      type={props.type || (isDestructive ? "error" : "info")}
      footer={footerContent}
    >
      {modalContent}
    </Modal>
  );
}

// Create an AlertConfirmModal component specifically for critical actions
export function AlertConfirmModal({
  title = "Confirm Action",
  description,
  confirmLabel = "Confirm",
  cancelLabel = "Cancel",
  alertMessage = "This action cannot be undone. Please confirm that you want to proceed.",
  alertType = "warning",
  isDestructive = true,
  ...props
}: ConfirmModalProps) {
  return (
    <ConfirmModal
      title={title}
      description={description}
      confirmLabel={confirmLabel}
      cancelLabel={cancelLabel}
      alertMessage={alertMessage}
      alertType={alertType}
      isDestructive={isDestructive}
      {...props}
    />
  );
}

// Function to create a modal programmatically
export function useModal() {
  const [isOpen, setIsOpen] = useState(false);

  const open = () => setIsOpen(true);
  const close = () => setIsOpen(false);

  return {
    isOpen,
    open,
    close,
    onOpenChange: setIsOpen
  };
}

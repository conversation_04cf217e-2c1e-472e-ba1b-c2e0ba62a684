/**
 * Application Initialization System
 * 
 * This module handles the initialization of the application including:
 * - Database seeding
 * - Required data setup
 * - System health checks
 */

import { ensureMongooseConnection } from '@/lib/mongodb';
import { seedHelpCategories } from '@/scripts/seedHelpCategories';
import { initializeDefaultSettings } from '@/lib/settings';

interface InitializationResult {
  success: boolean;
  message: string;
  details: {
    database: boolean;
    helpCategories: boolean;
    settings: boolean;
  };
  errors: string[];
}

/**
 * Initialize the application with all required data and configurations
 */
export async function initializeApplication(): Promise<InitializationResult> {
  const result: InitializationResult = {
    success: false,
    message: '',
    details: {
      database: false,
      helpCategories: false,
      settings: false
    },
    errors: []
  };

  console.log('🚀 Starting application initialization...');

  try {
    // 1. Ensure database connection
    console.log('📊 Connecting to database...');
    await ensureMongooseConnection();
    result.details.database = true;
    console.log('✅ Database connection established');

    // 2. Seed help categories
    console.log('🏷️  Initializing help categories...');
    try {
      const categoryResult = await seedHelpCategories();
      result.details.helpCategories = true;
      console.log(`✅ Help categories initialized: ${categoryResult.message}`);
    } catch (error) {
      const errorMsg = `Failed to seed help categories: ${error instanceof Error ? error.message : String(error)}`;
      result.errors.push(errorMsg);
      console.error('❌', errorMsg);
    }

    // 3. Initialize default settings
    console.log('⚙️  Initializing default settings...');
    try {
      await initializeDefaultSettings();
      result.details.settings = true;
      console.log('✅ Default settings initialized');
    } catch (error) {
      const errorMsg = `Failed to initialize settings: ${error instanceof Error ? error.message : String(error)}`;
      result.errors.push(errorMsg);
      console.error('❌', errorMsg);
    }

    // Determine overall success
    const criticalSystemsReady = result.details.database && result.details.helpCategories;
    result.success = criticalSystemsReady;
    
    if (result.success) {
      result.message = 'Application initialized successfully';
      console.log('🎉 Application initialization completed successfully!');
    } else {
      result.message = 'Application initialization completed with errors';
      console.log('⚠️  Application initialization completed with some errors');
    }

  } catch (error) {
    const errorMsg = `Critical initialization error: ${error instanceof Error ? error.message : String(error)}`;
    result.errors.push(errorMsg);
    result.message = errorMsg;
    console.error('💥 Critical initialization error:', error);
  }

  return result;
}

/**
 * Check if the application is properly initialized
 */
export async function checkInitializationStatus(): Promise<boolean> {
  try {
    await ensureMongooseConnection();
    
    // Import here to avoid circular dependencies
    const { HelpCategory } = await import('@/models/HelpTicket');
    
    // Check if help categories exist
    const categoryCount = await HelpCategory.countDocuments({ isActive: true });
    
    return categoryCount > 0;
  } catch (error) {
    console.error('Error checking initialization status:', error);
    return false;
  }
}

/**
 * Initialize application if not already initialized
 * This is safe to call multiple times
 */
export async function ensureApplicationInitialized(): Promise<void> {
  const isInitialized = await checkInitializationStatus();
  
  if (!isInitialized) {
    console.log('🔄 Application not fully initialized, running initialization...');
    await initializeApplication();
  } else {
    console.log('✅ Application already initialized');
  }
}

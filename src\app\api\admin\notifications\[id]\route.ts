import { NextRequest, NextResponse } from 'next/server';
import { pusherServer } from '@/lib/pusher-server';

/**
 * DELETE /api/admin/notifications/[id]
 * Delete a specific notification by ID
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  // Extract the ID from the URL directly to avoid the async params issue
  // First try to get it from the URL path
  let notificationId = '';
  try {
    const url = new URL(request.url);
    const pathSegments = url.pathname.split('/');
    // The ID should be the last segment in the path
    notificationId = pathSegments[pathSegments.length - 1];

    // If the ID contains a query string, remove it
    if (notificationId.includes('?')) {
      notificationId = notificationId.split('?')[0];
    }

    console.log('Extracted notification ID from URL path:', notificationId);
  } catch (error) {
    // If URL parsing fails, fall back to params
    notificationId = params.id;
    console.log('Falling back to params.id:', notificationId);
  }

  // If we still don't have a valid ID, try to get it from the request body
  if (!notificationId || notificationId === '') {
    try {
      // Clone the request to avoid consuming the body
      const clonedRequest = request.clone();
      const body = await clonedRequest.json();
      if (body.notificationId) {
        notificationId = body.notificationId;
        console.log('Using notification ID from request body:', notificationId);
      }
    } catch (error) {
      // Ignore JSON parsing errors
      console.log('Failed to parse request body for notification ID');
    }
  }

  try {
    // Try to get the user ID from the cookie directly
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    // For DELETE requests, we'll try to extract the userId from the body
    if (!userId) {
      try {
        // Clone the request to avoid consuming the body
        const clonedRequest = request.clone();
        const bodyData = await clonedRequest.json();
        userId = bodyData.userId;
      } catch (error) {
        // Ignore JSON parsing errors
        console.log('Failed to parse request body for userId');
      }
    }

    if (!userId) {
      console.error('Admin notifications API (DELETE [id]): No user ID found in request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const User = mongoose.default.models.User ||
                mongoose.default.model('User', new mongoose.default.Schema({
                  role: String
                }));

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the Notification schema directly
    const NotificationSchema = new mongoose.default.Schema({
      title: String,
      message: String,
      type: String,
      userId: mongoose.default.Schema.Types.ObjectId,
      read: Boolean,
      link: String,
      createdAt: Date,
      deletedBy: [mongoose.default.Schema.Types.ObjectId],
      isGlobal: Boolean,
      expiresAt: Date
    }, {
      timestamps: true
    });

    // Get the Notification model
    const Notification = mongoose.default.models.Notification ||
                        mongoose.default.model('Notification', NotificationSchema);

    // Use the ID we extracted at the beginning
    if (!notificationId) {
      return NextResponse.json(
        { error: 'Notification ID is required' },
        { status: 400 }
      );
    }

    // Find the notification
    const notification = await Notification.findById(notificationId);
    if (!notification) {
      return NextResponse.json(
        { error: 'Notification not found' },
        { status: 404 }
      );
    }

    // Check if this is a global notification (sent to all users)
    const isGlobal = notification.isGlobal;

    // If it's a global notification, we need to delete all instances with the same title and message
    let result;
    let deletedCount = 1;

    if (isGlobal) {
      // For global notifications, delete all matching notifications
      const deleteResult = await Notification.deleteMany({
        title: notification.title,
        message: notification.message,
        type: notification.type,
        isGlobal: true,
        createdAt: notification.createdAt
      });

      deletedCount = deleteResult.deletedCount || 1;
      result = { deletedCount };
      console.log(`Deleted ${deletedCount} global notifications with ID ${notificationId}`);

      // Notify all users via the global channel
      try {
        await pusherServer.trigger(
          'global-notifications',
          'notification-deleted-all',
          {
            timestamp: new Date().toISOString(),
            adminId: userId,
            deletedCount: deletedCount
          }
        );
      } catch (error) {
        console.error('Error broadcasting notification deletion:', error);
      }
    } else {
      // For individual notifications, we need to delete all instances with the same content
      try {
        // First, get the notification details
        const notificationDetails = await Notification.findById(notificationId);
        if (!notificationDetails) {
          throw new Error(`Notification ${notificationId} not found`);
        }

        // Delete all notifications with the same title, message, type, and createdAt
        // This ensures we delete all instances of the same notification sent to different users
        result = await Notification.deleteMany({
          title: notificationDetails.title,
          message: notificationDetails.message,
          type: notificationDetails.type,
          createdAt: notificationDetails.createdAt
        });

        console.log(`Deleted all instances of notification ${notificationId}:`, result);
        deletedCount = result.deletedCount || 1;

        // Double-check that the specific notification was deleted
        const checkDeleted = await Notification.findById(notificationId);
        if (checkDeleted) {
          // If it still exists, try direct deletion
          console.log(`Original notification ${notificationId} still exists, deleting directly`);
          await Notification.findByIdAndDelete(notificationId);
        }

        // Broadcast the content deletion to all clients
        try {
          await pusherServer.trigger(
            'global-notifications',
            'notification-content-deleted',
            {
              title: notificationDetails.title,
              message: notificationDetails.message,
              type: notificationDetails.type,
              createdAt: notificationDetails.createdAt.toISOString(),
              timestamp: new Date().toISOString(),
              adminId: userId,
              deletedCount: deletedCount
            }
          );
          console.log('Broadcast notification content deletion event');
        } catch (error) {
          console.error('Error broadcasting notification content deletion:', error);
        }
      } catch (deleteError) {
        console.error(`Error during deletion attempts:`, deleteError);
      }

      // If the notification was for a specific user, notify them via Pusher
      if (notification.userId) {
        try {
          // Only use Pusher for user-specific notifications to minimize usage
          await pusherServer.trigger(
            `user-${notification.userId}`,
            'notification-deleted',
            {
              notificationId: notificationId,
              timestamp: new Date().toISOString(),
              adminId: userId
            }
          );
        } catch (error) {
          console.error('Error sending Pusher notification:', error);
        }
      }
    }

    return NextResponse.json({
      success: true,
      deletedId: notificationId,
      deletedCount: deletedCount,
      isGlobal: isGlobal
    });
  } catch (error) {
    console.error(`Error deleting notification ${notificationId}:`, error);
    return NextResponse.json(
      { error: 'Failed to delete notification', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

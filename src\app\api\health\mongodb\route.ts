import { NextResponse } from 'next/server';
import { checkMongoHealth, resetMongoConnections } from '@/lib/mongodb';

/**
 * GET /api/health/mongodb
 * 
 * Health check endpoint for MongoDB connection
 * Returns detailed information about the MongoDB connection status
 */
export async function GET(request: Request) {
  try {
    const health = await checkMongoHealth();
    
    return NextResponse.json(health, {
      status: health.status === 'healthy' ? 200 : 503
    });
  } catch (error) {
    return NextResponse.json({
      status: 'error',
      message: (error as Error).message
    }, { status: 500 });
  }
}

/**
 * POST /api/health/mongodb/reset
 * 
 * Reset MongoDB connections
 * This endpoint can be used to manually reset all MongoDB connections
 * when experiencing connection issues
 */
export async function POST(request: Request) {
  try {
    // Check if this is a reset request
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    
    if (action === 'reset') {
      const result = await resetMongoConnections();
      
      if (result) {
        return NextResponse.json({
          status: 'success',
          message: 'MongoDB connections reset successfully'
        });
      } else {
        return NextResponse.json({
          status: 'error',
          message: 'Failed to reset MongoDB connections'
        }, { status: 500 });
      }
    }
    
    return NextResponse.json({
      status: 'error',
      message: 'Invalid action'
    }, { status: 400 });
  } catch (error) {
    return NextResponse.json({
      status: 'error',
      message: (error as Error).message
    }, { status: 500 });
  }
}

'use client';

import { useState, useRef } from 'react';
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/use-toast';
import { Loader2, Upload, AlertCircle, CheckCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface ImportUsersModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export default function ImportUsersModal({ 
  isOpen, 
  onClose, 
  onSuccess 
}: ImportUsersModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [file, setFile] = useState<File | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [importResults, setImportResults] = useState<any | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0] || null;
    setFile(selectedFile);
    setError(null);
    setImportResults(null);
  };

  // Handle file upload
  const handleUpload = async () => {
    if (!file) {
      setError('Please select a file to import');
      return;
    }

    // Check file type
    if (!file.name.endsWith('.json') && !file.name.endsWith('.csv')) {
      setError('Only JSON and CSV files are supported');
      return;
    }

    setIsSubmitting(true);
    setError(null);
    
    try {
      // Read file content
      const fileContent = await readFileContent(file);
      let usersData;
      
      // Parse file content based on file type
      if (file.name.endsWith('.json')) {
        try {
          usersData = JSON.parse(fileContent);
        } catch (e) {
          throw new Error('Invalid JSON format');
        }
      } else if (file.name.endsWith('.csv')) {
        usersData = parseCSV(fileContent);
      }
      
      // Validate users data
      if (!usersData || !usersData.users || !Array.isArray(usersData.users) || usersData.users.length === 0) {
        throw new Error('No valid users data found in the file');
      }
      
      // Send data to API
      const response = await fetch('/api/admin/users/import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ users: usersData.users }),
        credentials: 'include'
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to import users');
      }
      
      const result = await response.json();
      setImportResults(result);
      
      if (result.results.success > 0) {
        toast({
          title: 'Import Successful',
          description: `${result.results.success} users imported successfully.`,
          variant: 'success'
        });
        
        // Only call onSuccess if at least one user was imported
        onSuccess();
      }
    } catch (error) {
      console.error('Error importing users:', error);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Read file content as text
  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = (e) => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  };
  
  // Parse CSV to JSON
  const parseCSV = (csvContent: string) => {
    const lines = csvContent.split('\n');
    if (lines.length < 2) return { users: [] };
    
    const headers = lines[0].split(',').map(header => 
      header.trim().replace(/^"(.*)"$/, '$1') // Remove quotes if present
    );
    
    const users = lines.slice(1)
      .filter(line => line.trim() !== '')
      .map(line => {
        // Handle CSV parsing with potential quoted values containing commas
        const values: string[] = [];
        let currentValue = '';
        let insideQuotes = false;
        
        for (let i = 0; i < line.length; i++) {
          const char = line[i];
          
          if (char === '"') {
            if (insideQuotes && i + 1 < line.length && line[i + 1] === '"') {
              // Handle escaped quotes
              currentValue += '"';
              i++; // Skip the next quote
            } else {
              // Toggle quote state
              insideQuotes = !insideQuotes;
            }
          } else if (char === ',' && !insideQuotes) {
            // End of value
            values.push(currentValue);
            currentValue = '';
          } else {
            currentValue += char;
          }
        }
        
        // Add the last value
        values.push(currentValue);
        
        // Create user object from headers and values
        const user: Record<string, any> = {};
        headers.forEach((header, index) => {
          if (index < values.length) {
            let value = values[index].trim();
            
            // Convert "Yes"/"No" to boolean for emailVerified
            if (header === 'emailVerified') {
              value = value.toLowerCase() === 'yes' ? true : false;
            }
            
            user[header] = value;
          }
        });
        
        return user;
      });
    
    return { users };
  };
  
  // Handle dialog close
  const handleDialogClose = () => {
    if (!isSubmitting) {
      setFile(null);
      setError(null);
      setImportResults(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      onClose();
    }
  };
  
  // Reset form
  const handleReset = () => {
    setFile(null);
    setError(null);
    setImportResults(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleDialogClose}>
      <DialogContent className="sm:max-w-[500px] bg-vista-dark border-vista-light/20">
        <DialogHeader>
          <DialogTitle className="text-vista-light">Import Users</DialogTitle>
          <DialogDescription className="text-vista-light/70">
            Upload a JSON or CSV file to import multiple users at once.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          {importResults && (
            <Alert variant={importResults.results.failed > 0 ? "warning" : "success"}>
              <CheckCircle className="h-4 w-4" />
              <AlertTitle>Import Results</AlertTitle>
              <AlertDescription>
                <p>{importResults.message}</p>
                {importResults.results.failed > 0 && (
                  <details className="mt-2">
                    <summary className="cursor-pointer text-sm font-medium">
                      View {importResults.results.errors.length} errors
                    </summary>
                    <ul className="mt-2 text-xs space-y-1 max-h-40 overflow-y-auto">
                      {importResults.results.errors.map((error: string, index: number) => (
                        <li key={index} className="text-red-400">{error}</li>
                      ))}
                    </ul>
                  </details>
                )}
              </AlertDescription>
            </Alert>
          )}
          
          <div className="space-y-2">
            <Label htmlFor="file-upload" className="text-vista-light font-medium">
              Select File
            </Label>
            <Input
              id="file-upload"
              ref={fileInputRef}
              type="file"
              accept=".json,.csv"
              onChange={handleFileChange}
              className="bg-vista-dark-lighter border-vista-light/20 text-vista-light"
            />
            <p className="text-xs text-vista-light/60">
              Accepted formats: JSON, CSV. Maximum file size: 5MB.
            </p>
          </div>
          
          <div className="bg-vista-dark-lighter/50 rounded-md p-3 border border-vista-light/10">
            <h4 className="text-sm font-medium text-vista-light mb-2">File Format</h4>
            <p className="text-xs text-vista-light/70 mb-1">
              Your file should contain an array of user objects with the following fields:
            </p>
            <pre className="text-xs bg-vista-dark p-2 rounded overflow-x-auto">
{`{
  "users": [
    {
      "name": "User Name",
      "email": "<EMAIL>",
      "password": "optional_password",
      "role": "user",
      "emailVerified": true
    },
    ...
  ]
}`}
            </pre>
            <p className="text-xs text-vista-light/70 mt-2">
              Note: If password is not provided, a random password will be generated.
            </p>
          </div>
        </div>
        
        <DialogFooter>
          <Button 
            type="button" 
            variant="outline" 
            onClick={handleReset}
            disabled={isSubmitting || !file}
            className="border-vista-light/20 text-vista-light hover:bg-vista-dark-lighter hover:text-vista-light"
          >
            Reset
          </Button>
          <Button 
            type="button" 
            variant="outline" 
            onClick={handleDialogClose}
            disabled={isSubmitting}
            className="border-vista-light/20 text-vista-light hover:bg-vista-dark-lighter hover:text-vista-light"
          >
            Cancel
          </Button>
          <Button 
            type="button" 
            onClick={handleUpload}
            disabled={isSubmitting || !file}
            className="bg-vista-blue hover:bg-vista-blue/90 text-white"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Importing...
              </>
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                Import
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

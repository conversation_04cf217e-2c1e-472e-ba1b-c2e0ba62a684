import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import User from '@/models/User';

/**
 * DELETE /api/admin/visitors/delete
 * Delete a single visitor record or all visitor records
 * 
 * This endpoint is only accessible to admin users.
 * It can delete a single visitor by visitorId or all visitors.
 */
export async function DELETE(request: NextRequest) {
  try {
    // Get the userId from cookies or query string for authentication
    const { searchParams } = new URL(request.url);
    let userId = request.cookies.get('userId')?.value;

    // If no userId in cookies, try query string (for client-side admin verification)
    if (!userId) {
      const userIdParam = searchParams.get('userId');
      if (userIdParam) userId = userIdParam;
    }

    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Connect to database
    await ensureMongooseConnection();

    // Find the user by ID
    const user = await User.findById(userId).lean();

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if user has admin role
    const isUserAdmin = user.role === 'admin' || user.role === 'superadmin';

    if (!isUserAdmin) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Import the AnonymousVisitor model
    const AnonymousVisitor = (await import('@/models/AnonymousVisitor')).default;

    // Check if we're deleting a specific visitor or all visitors
    const visitorId = searchParams.get('visitorId');
    const deleteAll = searchParams.get('deleteAll') === 'true';

    let result;
    let message;

    if (deleteAll) {
      // Delete all visitor records (with confirmation)
      const confirmDelete = searchParams.get('confirm') === 'true';
      
      if (!confirmDelete) {
        return NextResponse.json({ 
          error: "Confirmation required", 
          message: "To delete all visitor records, you must include confirm=true in your request" 
        }, { status: 400 });
      }

      result = await AnonymousVisitor.deleteMany({});
      message = `Deleted all visitor records: ${result.deletedCount} records removed`;
    } else if (visitorId) {
      // Delete a specific visitor
      result = await AnonymousVisitor.deleteOne({ visitorId });
      
      if (result.deletedCount === 0) {
        return NextResponse.json({ 
          error: "Visitor not found", 
          message: `No visitor found with ID: ${visitorId}` 
        }, { status: 404 });
      }
      
      message = `Deleted visitor: ${visitorId}`;
    } else {
      // No visitorId or deleteAll parameter
      return NextResponse.json({ 
        error: "Missing parameters", 
        message: "You must provide either a visitorId or set deleteAll=true" 
      }, { status: 400 });
    }

    // Return success response
    return NextResponse.json({
      success: true,
      message,
      result
    });
  } catch (error) {
    console.error('Error deleting visitor(s):', error);
    return NextResponse.json(
      { error: 'Failed to delete visitor(s)', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
} 
import PusherServer from 'pusher';

// Pusher server instance
export const pusherServer = new PusherServer({
  appId: process.env.PUSHER_APP_ID!,
  key: process.env.NEXT_PUBLIC_PUSHER_KEY!,
  secret: process.env.PUSHER_SECRET!,
  cluster: process.env.NEXT_PUBLIC_PUSHER_CLUSTER || 'us2',
  useTLS: true,
});

// Watch party event constants
export const WATCH_PARTY_EVENTS = {
  MEMBER_JOINED: 'member-joined',
  MEMBER_LEFT: 'member-left',
  MESSAGE_SENT: 'message-sent',
  PLAYBACK_UPDATE: 'playback-update',
  PLAYBACK_SYNC: 'playback-sync',
  PARTY_STATE_UPDATE: 'party-state-update',
  REACTION_SENT: 'reaction-sent',
  TYPING_START: 'typing-start',
  TYPING_STOP: 'typing-stop',
} as const;

// Notification event constants
export const NOTIFICATION_EVENTS = {
  NEW_NOTIFICATION: 'new-notification',
  NOTIFICATION_READ: 'notification-read',
  NOTIFICATION_DELETED: 'notification-deleted',
} as const;

// Content update event constants
export const CONTENT_EVENTS = {
  NEW_RELEASE: 'new-release',
  CONTENT_UPDATED: 'content-updated',
  FEATURED_UPDATED: 'featured-updated',
} as const;

// Helper function to trigger events
export async function triggerEvent(
  channel: string,
  event: string,
  data: Record<string, unknown>
): Promise<void> {
  try {
    await pusherServer.trigger(channel, event, data);
  } catch (error) {
    console.error(`Failed to trigger event ${event} on channel ${channel}:`, error);
    throw error;
  }
}

// Helper function to trigger to multiple channels
export async function triggerToChannels(
  channels: string[],
  event: string,
  data: Record<string, unknown>
): Promise<void> {
  try {
    await pusherServer.trigger(channels, event, data);
  } catch (error) {
    console.error(`Failed to trigger event ${event} to channels:`, error);
    throw error;
  }
}

// Helper function to trigger to all users
export async function triggerToAll(
  event: string,
  data: Record<string, unknown>
): Promise<void> {
  try {
    await pusherServer.trigger('presence-all', event, data);
  } catch (error) {
    console.error(`Failed to trigger event ${event} to all users:`, error);
    throw error;
  }
}

// Helper function to get channel info
export async function getChannelInfo(channelName: string): Promise<unknown> {
  try {
    const response = await pusherServer.get({ path: `/channels/${channelName}` });
    return response;
  } catch (error) {
    console.error(`Failed to get channel info for ${channelName}:`, error);
    throw error;
  }
}

// Helper function to get channel users (for presence channels)
export async function getChannelUsers(channelName: string): Promise<unknown> {
  try {
    const response = await pusherServer.get({ path: `/channels/${channelName}/users` });
    return response;
  } catch (error) {
    console.error(`Failed to get channel users for ${channelName}:`, error);
    throw error;
  }
} 
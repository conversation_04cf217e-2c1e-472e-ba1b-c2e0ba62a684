'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { ChevronRight, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  type CarouselApi,
} from "@/components/ui/carousel";

import { ContentCardType } from '@/lib/content-utils';
import { useLanguage } from '@/lib/i18n/LanguageContext';
import ContentCard from '@/components/ContentCard';
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll';

interface InfiniteRecommendationsProps {
  title?: string;
  initialItems?: ContentCardType[];
  className?: string;
}

export default function InfiniteRecommendations({
  title = "Recommended for You",
  initialItems = [],
  className = ""
}: InfiniteRecommendationsProps) {
  const { t } = useLanguage();
  const [isMounted, setIsMounted] = useState(false);
  const [carouselApi, setCarouselApi] = useState<CarouselApi | null>(null);
  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(true);

  // Detect if we're on mobile and if it's a low-end device
  const [isMobile, setIsMobile] = useState(false);
  const [isLowEndDevice, setIsLowEndDevice] = useState(false);

  // Use the infinite scroll hook
  const {
    items: recommendedContent,
    isLoading,
    hasMore,
    loadMore,
  } = useInfiniteScroll({
    initialItems,
    fetchMoreItems: async (page) => {
      const response = await fetch(`/api/content/recommendations?page=${page}`, {
        cache: 'force-cache'
      });
      const data = await response.json();
      return data.success ? data.data : [];
    },
    itemsPerPage: 12,
    maxItems: 60,
    cooldownMs: 1000
  });

  // Check for mobile and low-end devices on mount
  useEffect(() => {
    const checkDevice = () => {
      const mobile = window.innerWidth < 768 ||
                    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      setIsMobile(mobile);
      const isLowEnd = mobile && (
        // @ts-expect-error - These properties exist on some browsers but aren't in the standard TS types
        (navigator.deviceMemory && navigator.deviceMemory < 4) ||
        // No longer expect an error here, as hardwareConcurrency seems recognized
        (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4)
      );
      setIsLowEndDevice(isLowEnd);
    };
    checkDevice();
    setIsMounted(true);

    // Listen for resize events to update mobile status
    window.addEventListener('resize', checkDevice);
    return () => window.removeEventListener('resize', checkDevice);
  }, []);

  // Handle Carousel API initialization and scroll events
  useEffect(() => {
    if (!carouselApi) {
      return;
    }

    const onSelect = () => {
      setCanScrollPrev(carouselApi.canScrollPrev());
      setCanScrollNext(carouselApi.canScrollNext());

      // Load more when near the end
      const currentScroll = carouselApi.scrollProgress();
      if (currentScroll > 0.8 && hasMore && !isLoading) {
        loadMore();
      }
    };

    carouselApi.on('select', onSelect);
    carouselApi.on('reInit', onSelect);
    // Initial check
    onSelect();

    return () => {
      carouselApi.off('select', onSelect);
      carouselApi.off('reInit', onSelect);
    };
  }, [carouselApi, hasMore, isLoading, loadMore]);

  if (!isMounted) {
    return null; // Prevent hydration issues
  }

  // Function to render skeleton loaders
  const renderSkeletons = () => {
    return Array(6).fill(0).map((_, i) => (
      <div key={`skeleton-${i}`} className="animate-pulse flex-shrink-0 w-32 sm:w-[170px] md:w-[170px] lg:w-[170px] pl-3 md:pl-4">
        <div className="aspect-[2/3] rounded-lg bg-vista-dark-lighter mb-2"></div>
        <div className="h-4 bg-vista-dark-lighter rounded w-3/4 mb-1"></div>
        <div className="h-3 bg-vista-dark-lighter rounded w-1/2"></div>
      </div>
    ));
  };

  // Show message if no recommendations are available
  if (!isLoading && recommendedContent.length === 0) {
    return (
      <section className={`py-8 ${className}`}>
        <div className="container px-4 md:px-6 mx-auto">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-vista-light">{title}</h2>
          </div>
          <div className="flex justify-center items-center p-8 bg-vista-dark-lighter rounded-lg">
            <p className="text-vista-light text-center">
              No recommendations found at this time.
              <br />
              <span className="text-sm text-vista-light/60">Explore other categories!</span>
            </p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className={`py-6 md:py-8 ${className}`}>
      <div className="container px-4 md:px-6 mx-auto">
        {/* Row Header */}
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl md:text-2xl font-semibold text-vista-light tracking-tight">
              {title}
            </h2>
          </div>
          {/* Optional See All link can be added here if needed */}
        </div>

        <Carousel
          setApi={setCarouselApi}
          opts={{
            align: "start",
            containScroll: "trimSnaps",
            dragFree: true,
            loop: false,
          }}
          className="relative w-full -ml-3 md:-ml-4"
        >
          <CarouselContent className="">
            {recommendedContent.map((content, index) => (
              // On low-end devices, only render visible items plus a few more
              (!isLowEndDevice || index < 8) && (
                <CarouselItem key={`${content.id}-${index}`} className="pl-3 md:pl-4 basis-auto flex-shrink-0 w-32 sm:w-[170px] md:w-[170px] lg:w-[170px]">
                  <ContentCard
                    id={content.id}
                    title={content.title}
                    imagePath={content.imagePath}
                    type={content.type}
                    year={content.year}
                    ageRating={content.ageRating}
                    userRating={content.userRating}
                    index={index}
                    isAwardWinning={content.isAwardWinning}
                    dataSource={content.dataSource}
                    link={`/watch/${content.id}?forcePlay=true&contentType=${content.type === 'shows' ? 'show' : 'movie'}`}
                  />
                </CarouselItem>
              )
            ))}
            {isLoading && (
               <CarouselItem className="pl-3 md:pl-4 basis-auto flex-shrink-0 w-32 sm:w-[170px] md:w-[170px] lg:w-[170px] flex items-center justify-center">
                 <Loader2 className="h-8 w-8 text-vista-blue animate-spin" />
               </CarouselItem>
            )}
            {isLoading && recommendedContent.length === 0 && renderSkeletons()}
          </CarouselContent>

          {!isMobile && (
              <>
                <CarouselPrevious
                  className={`absolute left-[-5px] top-1/2 -translate-y-1/2 z-20 bg-black/60 hover:bg-black/80 border-none text-white w-8 h-8 rounded-full transition-opacity ${canScrollPrev ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}
                  disabled={!canScrollPrev}
                />
                <CarouselNext
                   className={`absolute right-[-5px] top-1/2 -translate-y-1/2 z-20 bg-black/60 hover:bg-black/80 border-none text-white w-8 h-8 rounded-full transition-opacity ${canScrollNext ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}
                   disabled={!canScrollNext}
                />
              </>
          )}
        </Carousel>
      </div>
    </section>
  );
}

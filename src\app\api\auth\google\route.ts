import { NextRequest, NextResponse } from 'next/server';
import { UserSession } from '@/lib/types';

export async function POST(request: NextRequest) {
  console.log('Starting Google auth process');

  // Define GoogleData interface
  interface GoogleData {
    googleId: string;
    email: string;
    name?: string;
    givenName?: string;
    familyName?: string;
    picture?: string;
  }

  // Define googleData at the top level so it's accessible in the catch block
  let googleData: GoogleData | null = null;

  // Track if this is a new user
  let isNewUser = false;

  try {
    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Define the User schema directly
    const UserSchema = new mongoose.default.Schema({
      googleId: String,
      email: { type: String, required: true, unique: true },
      password: {
        type: String,
        required: function(this: { googleId?: string }) {
          // Password is required only if googleId is not present
          return !this.googleId;
        }
      },
      name: String,
      givenName: String,
      familyName: String,
      picture: String,
      profileImage: String,
      role: { type: String, default: 'user' },
      status: { type: String, default: 'active' },
      emailVerified: Date,
      lastLogin: Date
    }, {
      timestamps: true
    });

    // Get the User model
    const User = mongoose.default.models.User ||
                mongoose.default.model('User', UserSchema);

    // Define the Profile schema directly
    const ProfileSchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      name: String,
      avatar: String,
      isKids: Boolean,
      isPrimary: Boolean
    }, {
      timestamps: true
    });

    // Get the Profile model
    const Profile = mongoose.default.models.Profile ||
                   mongoose.default.model('Profile', ProfileSchema);

    // Define the UserActivity schema directly
    const UserActivitySchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      action: String,
      details: String,
      ipAddress: String,
      userAgent: String,
      timestamp: { type: Date, default: Date.now },
      metadata: mongoose.default.Schema.Types.Mixed
    }, {
      timestamps: true
    });

    // Get the UserActivity model
    const UserActivity = mongoose.default.models.UserActivity ||
                        mongoose.default.model('UserActivity', UserActivitySchema);

    // Extract user data from request
    try {
      const parsedData = await request.json();
      googleData = parsedData as GoogleData;

      console.log('Google auth data parsed successfully:', {
        hasGoogleId: !!googleData.googleId,
        hasEmail: !!googleData.email,
        hasName: !!googleData.name,
        fields: Object.keys(googleData).join(', ')
      });
    } catch (parseError) {
      console.error('Error parsing Google auth request body:', parseError);
      return NextResponse.json(
        { error: 'Invalid request data' },
        { status: 400 }
      );
    }

    // At this point, googleData is guaranteed to be non-null
    // because if the JSON parsing failed, we would have returned early
    if (!googleData) {
      return NextResponse.json(
        { error: 'Failed to parse Google authentication data' },
        { status: 400 }
      );
    }

    const { googleId, email, givenName, familyName, name, picture } = googleData;

    // Validate required fields
    if (!googleId || !email) {
      console.error('Missing required Google auth fields:', { googleId, email });
      return NextResponse.json(
        { error: 'Google ID and email are required' },
        { status: 400 }
      );
    }

    // First try to find user by googleId for faster lookup
    console.log('Finding user by googleId:', googleId);
    let user = await User.findOne({ googleId });

    // If not found by googleId, try by email
    if (!user) {
      console.log('User not found by googleId, trying email:', email);
      user = await User.findOne({ email });

      // If found by email but not by googleId, update the googleId
      if (user) {
        console.log('User found by email, updating Google ID');
        console.log('Old Google ID:', user.googleId || 'none', 'New Google ID:', googleId);

        // Update user profile data
        user.googleId = googleId;
        user.picture = picture || user.picture;
        // Also update profileImage to match picture for admin panel display
        user.profileImage = picture || user.picture;
        user.givenName = givenName || user.givenName;
        user.familyName = familyName || user.familyName;
        user.name = name || user.name;

        try {
          await user.save();
          console.log('User profile updated successfully');
        } catch (updateError) {
          console.error('Error updating user Google ID:', updateError);
          // Continue with the existing user data even if update fails
          // This ensures the user can still log in
        }
      } else {
        isNewUser = true;
      }
    } else {
      console.log('User found with matching Google ID');

      // Update profile data if needed
      let needsUpdate = false;

      if (picture && user.picture !== picture) {
        user.picture = picture;
        // Also update profileImage to match picture for admin panel display
        user.profileImage = picture;
        needsUpdate = true;
      }

      if (name && user.name !== name) {
        user.name = name;
        needsUpdate = true;
      }

      if (givenName && user.givenName !== givenName) {
        user.givenName = givenName;
        needsUpdate = true;
      }

      if (familyName && user.familyName !== familyName) {
        user.familyName = familyName;
        needsUpdate = true;
      }

      if (needsUpdate) {
        try {
          await user.save();
          console.log('User profile data updated');
        } catch (updateError) {
          console.error('Error updating user profile:', updateError);
          // Continue with existing user data
        }
      }
    }

    // If user doesn't exist at all, create a new one with a primary profile
    if (!user) {
      console.log('Creating new user from Google auth data');

      // Start a session for transaction
      const session = await mongoose.default.startSession();

      try {
        let userSession;

        await session.withTransaction(async () => {
          // Create new user
          user = new User({
            googleId,
            email,
            name: name || email.split('@')[0],
            givenName,
            familyName,
            picture,
            profileImage: picture, // Set profileImage to match picture for admin panel display
            emailVerified: new Date(), // Set email as verified since Google accounts are verified
            lastLogin: new Date(), // Set initial login time
          });

          // Save the user first
          await user.save({ session });
          console.log('New Google user created with ID:', user._id);

          // Default avatar URL with lowercase cloud name
          const defaultAvatarUrl = "https://res.cloudinary.com/streamvista/image/upload/v1743812698/defaults/default_avatar.jpg";

          // Create a primary profile for the user
          const userProfile = new Profile({
            userId: user._id,
            name: user.name,
            avatar: user.picture || defaultAvatarUrl,
            isKids: false,
            isPrimary: true // This is the primary profile
          });

          await userProfile.save({ session });
          console.log('Primary profile created for Google user with ID:', user._id);

          // Create a user session object without sensitive data
          userSession = {
            id: user._id.toString(),
            googleId: user.googleId,
            email: user.email,
            name: user.name,
            picture: user.picture,
            createdAt: user.createdAt.toISOString(),
          };

          // Set flag to indicate this is a new user
          isNewUser = true;
        });

        // If we've made it here, transaction was successful
        if (userSession) {
          console.log('Google auth successful, returning user session');

          // Return the user data
          return NextResponse.json({
            success: true,
            user: userSession,
            isNewUser: true
          });
        }
      } catch (saveError) {
        console.error('Error in transaction for Google user creation:', saveError);

        // Handle specific MongoDB validation errors
        if (saveError instanceof mongoose.default.Error.ValidationError) {
          const validationErrors = Object.values(saveError.errors).map(e => e.message).join(', ');
          return NextResponse.json(
            { error: `Validation error: ${validationErrors}` },
            { status: 400 }
          );
        }

        throw saveError; // Re-throw for general error handler
      } finally {
        await session.endSession();
      }
    }

    // Create a user session object without sensitive data
    if (!user) {
      return NextResponse.json(
        { error: 'Failed to authenticate user' },
        { status: 500 }
      );
    }

    const userSession: UserSession = {
      id: user._id.toString(),
      googleId: user.googleId,
      email: user.email,
      name: user.name,
      picture: user.picture,
      createdAt: user.createdAt.toISOString(),
    };

    console.log('Google auth successful, returning user session');

    // Get host information for debugging
    const host = request.headers.get('host');
    console.log('Host:', host);
    console.log('Environment:', process.env.NODE_ENV);
    console.log('Is Netlify:', process.env.NETLIFY === 'true');

    // Create response with user data
    const response = NextResponse.json({
      success: true,
      user: userSession,
      isNewUser: isNewUser
    });

    // Set cookies for authentication with production-ready settings
    // First, ensure no existing cookie with the same name exists
    response.cookies.delete('userId');

    // Then set the new cookie
    response.cookies.set('userId', userSession.id, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax', // 'lax' is more compatible across browsers and environments
      maxAge: 7 * 24 * 60 * 60, // 7 days
      path: '/'
    });

    // Update last login time and log user activity
    try {
      // Update last login timestamp using direct database access
      if (!mongoose.connection.db) {
        throw new Error('Database connection not established');
      }
      const db = mongoose.connection.db;
      const usersCollection = db.collection('users');

      // Convert string ID to ObjectId
      const objectId = new mongoose.Types.ObjectId(userSession.id);

      // Update the lastLogin field directly
      const now = new Date();
      console.log('Updating lastLogin for Google user:', userSession.id, 'to', now.toISOString());

      try {
        await usersCollection.updateOne(
          { _id: objectId },
          { $set: { lastLogin: now } }
        );
        console.log('Successfully updated lastLogin for Google user');
      } catch (updateError) {
        console.error('Error updating lastLogin for Google user:', updateError);

        // Try to find the user first to get their actual _id
        try {
          const user = await usersCollection.findOne({
            $or: [
              { email: userSession.email },
              { googleId: userSession.googleId }
            ]
          });

          if (user) {
            // If we found the user, update using their actual _id
            await usersCollection.updateOne(
              { _id: user._id },
              { $set: { lastLogin: now } }
            );
            console.log('Found user via alternative fields, updated lastLogin');
          } else {
            console.log(`Could not find Google user to update lastLogin`);
          }
        } catch (findError) {
          console.error('Error finding Google user by alternative fields:', findError);
        }
      }

      console.log('Last login timestamp updated for Google user:', userSession.id);

      // Log user activity
      const { logUserActivity } = await import('@/lib/activity-logger');
      await logUserActivity(
        userSession.id,
        'auth',
        isNewUser ? 'signup' : 'login',
        isNewUser ? 'User registered with Google' : 'User logged in with Google',
        request,
        { method: 'google' }
      );
    } catch (logError) {
      console.error('Error updating last login or logging user activity:', logError);
      // Continue even if logging fails
    }

    // If this is a new user, mark visitor as converted if they were previously tracked as an anonymous visitor
    if (isNewUser) {
      try {
        // Import the visitor utils
        const { markVisitorAsConverted } = await import('@/lib/visitor-utils');
        await markVisitorAsConverted(userSession.id, request);
        console.log('Anonymous visitor marked as converted for Google user:', userSession.id);
      } catch (visitorError) {
        console.error('Error marking visitor as converted:', visitorError);
        // Continue even if visitor marking fails
      }
    }

    return response;

  } catch (error) {
    console.error('Google auth error details:', error);

    // Enhanced error logging
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);

      // Log additional context for debugging
      console.error('Google auth context:', {
        googleIdProvided: googleData ? !!googleData.googleId : false,
        emailProvided: googleData ? !!googleData.email : false,
        googleIdLength: googleData?.googleId?.length ?? 0,
        timestamp: new Date().toISOString()
      });
    }

    // Handle specific MongoDB errors
    if (error instanceof Error) {
      // Define a type for MongoDB error
      interface MongoServerError extends Error {
        code?: number;
        keyValue?: Record<string, unknown>;
      }

      if (error.name === 'MongoServerError' && (error as MongoServerError).code === 11000) {
        console.error('Duplicate key error:', (error as MongoServerError).keyValue);
        return NextResponse.json(
          { error: `Account already exists with this email or Google ID` },
          { status: 409 }
        );
      }
    }

    // Generic error response
    return NextResponse.json(
      { error: 'An unexpected error occurred during Google authentication', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
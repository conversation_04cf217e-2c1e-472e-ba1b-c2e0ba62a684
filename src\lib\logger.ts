import { systemLogger } from './system-logger';

/**
 * Type for log details that can include various types of data
 */
export type LogDetails = Record<string, string | number | boolean | object | null | undefined>;

/**
 * Application logger
 * 
 * This is a wrapper around system-logger that provides a simpler interface
 * for common logging operations. It logs all messages to the system log.
 */
const logger = {
  /**
   * Log informational messages
   * @param message - The message to log
   * @param details - Optional details to include
   */
  info: (message: string, details?: LogDetails) => {
    systemLogger.info(message, 'application', details);
  },

  /**
   * Log warning messages
   * @param message - The message to log
   * @param details - Optional details to include
   */
  warning: (message: string, details?: LogDetails) => {
    systemLogger.warning(message, 'application', details);
  },

  /**
   * Log error messages
   * @param message - The message to log
   * @param error - The error object or details to include
   */
  error: (message: string, error?: Error | LogDetails) => {
    const details = error instanceof Error 
      ? { message: error.message, stack: error.stack } 
      : error;
    
    systemLogger.error(message, 'application', details);
  },

  /**
   * Log debug messages
   * @param message - The message to log
   * @param details - Optional details to include
   */
  debug: (message: string, details?: LogDetails) => {
    systemLogger.debug(message, 'application', details);
  }
};

export default logger; 
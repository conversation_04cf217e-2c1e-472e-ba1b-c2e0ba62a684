/**
 * Environment Variable Validation Utility
 * 
 * This utility helps validate that all required environment variables
 * are properly set, especially in production environments like Netlify.
 */

export interface EnvValidationResult {
  isValid: boolean;
  missing: string[];
  warnings: string[];
  environment: string;
}

/**
 * Required environment variables for the application
 */
const REQUIRED_ENV_VARS = [
  'MONGODB_URI',
  'NEXT_PUBLIC_TMDB_API_KEY',
  'NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME'
];

/**
 * Optional but recommended environment variables
 */
const RECOMMENDED_ENV_VARS = [
  'MONGODB_DB',
  'NEXT_PUBLIC_GOOGLE_CLIENT_ID',
  'CLOUDINARY_API_KEY',
  'CLOUDINARY_API_SECRET'
];

/**
 * Validate environment variables
 * @param includeRecommended Whether to include recommended variables in validation
 * @returns Validation result object
 */
export function validateEnvironmentVariables(includeRecommended: boolean = false): EnvValidationResult {
  const missing: string[] = [];
  const warnings: string[] = [];
  const environment = process.env.NODE_ENV || 'unknown';

  // Check required variables
  for (const envVar of REQUIRED_ENV_VARS) {
    if (!process.env[envVar]) {
      missing.push(envVar);
    }
  }

  // Check recommended variables if requested
  if (includeRecommended) {
    for (const envVar of RECOMMENDED_ENV_VARS) {
      if (!process.env[envVar]) {
        warnings.push(`Recommended variable ${envVar} is not set`);
      }
    }
  }

  // Environment-specific checks
  if (environment === 'production') {
    // In production, check for Netlify-specific variables
    if (process.env.NETLIFY === 'true') {
      if (!process.env.MONGODB_URI) {
        warnings.push('MONGODB_URI should be set in Netlify environment variables');
      }
      if (!process.env.MONGODB_DB) {
        warnings.push('MONGODB_DB should be set in Netlify environment variables');
      }
    }
  }

  return {
    isValid: missing.length === 0,
    missing,
    warnings,
    environment
  };
}

/**
 * Log environment validation results
 * @param validation Validation result object
 * @param context Context string for logging
 */
export function logEnvironmentValidation(validation: EnvValidationResult, context: string = 'Environment validation') {
  console.log(`${context}:`, {
    environment: validation.environment,
    isValid: validation.isValid,
    netlify: process.env.NETLIFY === 'true',
    mongodbUri: process.env.MONGODB_URI ? 'set' : 'missing',
    mongodbDb: process.env.MONGODB_DB ? 'set' : 'missing'
  });

  if (validation.missing.length > 0) {
    console.error(`${context} - Missing required variables:`, validation.missing);
  }

  if (validation.warnings.length > 0) {
    console.warn(`${context} - Warnings:`, validation.warnings);
  }
}

/**
 * Get MongoDB connection info for debugging
 */
export function getMongoDBConnectionInfo() {
  return {
    uri: process.env.MONGODB_URI ? 'set' : 'missing',
    db: process.env.MONGODB_DB || 'streamvista',
    netlify: process.env.NETLIFY === 'true',
    environment: process.env.NODE_ENV,
    connectionLimit: process.env.MONGODB_CONNECTION_LIMIT,
    serverlessMode: process.env.MONGODB_SERVERLESS_MODE === 'true'
  };
}

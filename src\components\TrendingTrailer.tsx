'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Play, Plus, Film, Loader2, Check, Clock, Info, X, ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { getUpcomingMovies, MappedContent, getMovieDetails } from '@/lib/tmdb-api';
import { extractTrailerFromTMDB } from '@/lib/trailer-utils';
import { useLanguage } from '@/lib/i18n/LanguageContext';
import { useWatchlist } from '@/contexts/WatchlistContext';
import { ContentCardType } from '@/lib/content-utils';
import { toast } from 'sonner';
import CustomVideoPlayer from '@/components/CustomVideoPlayer';
import { motion, AnimatePresence } from 'framer-motion';

interface TrailerProps {
  className?: string;
}

// Define a type for the content stored in state, without the upfront trailer ID
type TrailerContentItem = MappedContent;

// Define wrapper components
const MotionWrapper = ({ children, ...props }: { children: React.ReactNode; [key: string]: unknown }) => (
  <motion.div {...props}>{children}</motion.div>
);
const StaticWrapper = ({ children, className }: { children: React.ReactNode; className?: string }) => (
  <div className={className}>{children}</div>
);

export default function TrendingTrailer({ className = "" }: TrailerProps) {
  const { t } = useLanguage();
  const { addToWatchlist, isInWatchlist, removeFromWatchlist } = useWatchlist();
  const [isLoading, setIsLoading] = useState(true);
  // State now holds only MappedContent, trailer ID fetched later
  const [trailerContents, setTrailerContents] = useState<TrailerContentItem[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [previousIndex, setPreviousIndex] = useState<number | null>(null);
  const [isProcessing, setIsProcessing] = useState(false); // For watchlist actions
  const [isFetchingTrailer, setIsFetchingTrailer] = useState(false); // For trailer fetch
  const [inWatchlist, setInWatchlist] = useState(false);
  const [showTrailer, setShowTrailer] = useState(false);
  const [trailerVideoId, setTrailerVideoId] = useState<string | null>(null);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [autoRotateEnabled, setAutoRotateEnabled] = useState(true);
  const autoRotateRef = useRef<NodeJS.Timeout | null>(null);
  const [isMobile, setIsMobile] = useState(false);

  // Detect mobile devices
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(
        window.innerWidth < 768 ||
        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
      );
    };
    checkMobile(); // Initial check
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Fetch ONLY upcoming movies list initially
  useEffect(() => {
    const fetchBaseTrailerContents = async () => {
      try {
        setIsLoading(true);
        const upcomingMovies = await getUpcomingMovies();

        // Filter for movies with good backdrop images and overview
        const moviesWithBackdrops = upcomingMovies.filter(
          movie => movie.backdropUrl && movie.overview && movie.overview.length > 50
        );

        if (moviesWithBackdrops.length > 0) {
          // Get top 5 movies (or 3 on mobile)
          const selectedMovies = moviesWithBackdrops.slice(0, isMobile ? 3 : 5);
          setTrailerContents(selectedMovies); // Store just the base MappedContent

          // Check if current movie is in the watchlist (based on the simplified data)
          const currentMovie = selectedMovies[0];
          if (currentMovie) {
             setInWatchlist(isInWatchlist(currentMovie.id));
          } else {
             setInWatchlist(false);
          }
        } else {
          setTrailerContents([]);
          setInWatchlist(false);
        }
      } catch (error) {
        console.error('Error fetching base trailer contents:', error);
        setTrailerContents([]);
        setInWatchlist(false);
      } finally {
        setIsLoading(false);
      }
    };

    fetchBaseTrailerContents();

    // Cleanup function for interval
    return () => {
      if (autoRotateRef.current) {
        clearInterval(autoRotateRef.current);
      }
    };
    // Depend on isMobile to refetch if screen size changes significantly
  }, [isMobile, isInWatchlist]); // Added isInWatchlist dependency

  // Rotate to the next trailer item
  const rotateToNextTrailer = useCallback(() => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setPreviousIndex(currentIndex);
    setCurrentIndex((prev) => (prev + 1) % trailerContents.length);
    // Reset transition state after animation duration (e.g., 700ms)
    setTimeout(() => setIsTransitioning(false), 700);
  }, [isTransitioning, currentIndex, trailerContents.length]);

  // Rotate to the previous trailer item
  const rotateToPrevTrailer = useCallback(() => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setPreviousIndex(currentIndex);
    setCurrentIndex((prev) => (prev - 1 + trailerContents.length) % trailerContents.length);
    setTimeout(() => setIsTransitioning(false), 700);
  }, [isTransitioning, currentIndex, trailerContents.length]);

  // Set up auto-rotation for trailers
  useEffect(() => {
    if (trailerContents.length > 1 && autoRotateEnabled && !showTrailer && !isMobile) {
      // Stop any existing interval
       if (autoRotateRef.current) clearInterval(autoRotateRef.current);
       // Start new interval
      autoRotateRef.current = setInterval(() => {
        rotateToNextTrailer();
      }, 8000); // Only rotate on desktop
    } else {
        // Clear interval if conditions aren't met (mobile, disabled, showing trailer)
       if (autoRotateRef.current) {
         clearInterval(autoRotateRef.current);
         autoRotateRef.current = null;
       }
    }

    // Cleanup interval on unmount or when dependencies change
    return () => {
      if (autoRotateRef.current) {
        clearInterval(autoRotateRef.current);
      }
    };
  }, [trailerContents.length, currentIndex, autoRotateEnabled, showTrailer, isMobile, rotateToNextTrailer]); // Added rotateToNextTrailer dependency

  // Update watchlist status when current index changes
  useEffect(() => {
    if (trailerContents.length > 0) {
      const currentMovie = trailerContents[currentIndex];
      if (currentMovie) {
        setInWatchlist(isInWatchlist(currentMovie.id));
      }
    }
  }, [currentIndex, trailerContents, isInWatchlist]);

  const handleToggleWatchlist = useCallback(() => {
    if (trailerContents.length === 0 || isProcessing) return;

    const currentTrailer = trailerContents[currentIndex];
    if (!currentTrailer) return;

    setIsProcessing(true); // Set processing state

    try {
      if (inWatchlist) {
        removeFromWatchlist(currentTrailer.id);
        setInWatchlist(false);
        toast(`Removed "${currentTrailer.title}" from My List`);
      } else {
        // Check if already in watchlist (safety check, though useEffect should handle this)
        if (isInWatchlist(currentTrailer.id)) {
           setInWatchlist(true); // Correct local state if out of sync
           toast.info(`"${currentTrailer.title}" is already in your list`);
           setIsProcessing(false); // Reset processing state
           return;
        }

        // Convert MappedContent to ContentCardType for adding
        const contentType = currentTrailer.mediaType === 'tv' ? 'shows' : 'movies';
        const contentItem: ContentCardType = {
          id: currentTrailer.id,
          title: currentTrailer.title,
          imagePath: currentTrailer.posterUrl || currentTrailer.backdropUrl || '/favicon.svg',
          type: contentType,
          year: currentTrailer.year?.toString() || (currentTrailer.releaseDate ? new Date(currentTrailer.releaseDate).getFullYear().toString() : ''),
          userRating: currentTrailer.voteAverage,
          tmdbId: currentTrailer.tmdbId // Include tmdbId if available
        };
        addToWatchlist(contentItem);
        setInWatchlist(true);
        toast(`Added "${currentTrailer.title}" to My List`);
      }
    } catch (error) {
      console.error('Error updating watchlist:', error);
      toast.error(`Failed to update "${currentTrailer.title}" in your list`);
      // Ensure local state reflects reality if add/remove failed
      setInWatchlist(isInWatchlist(currentTrailer.id));
    } finally {
      // Reset processing state after a short delay to prevent rapid clicks
      setTimeout(() => setIsProcessing(false), 500);
    }
  }, [currentIndex, trailerContents, inWatchlist, isProcessing, addToWatchlist, removeFromWatchlist, isInWatchlist]);


  // Get a YouTube trailer video ID - NOW fetches details on demand
  const fetchAndGetTrailerVideoId = useCallback(async (): Promise<string | null> => {
    if (trailerContents.length === 0) return null;

    const currentTrailer = trailerContents[currentIndex];
    if (!currentTrailer) return null;

    setIsFetchingTrailer(true); // Indicate loading
    try {
        console.log(`Fetching details for trailer: ${currentTrailer.title} (ID: ${currentTrailer.tmdbId})`);
        const details = await getMovieDetails(currentTrailer.tmdbId);
        const videoId = details.videos ? extractTrailerFromTMDB(details.videos) : null;

        if (videoId) {
            console.log(`Found trailer ID: ${videoId}`);
            return videoId;
        } else {
            console.warn(`No official trailer found for ${currentTrailer.title}.`);
            toast.info(`No official trailer found for "${currentTrailer.title}".`);
            return null; // Don't use fallback trailers for now
        }
    } catch (error) {
        console.error(`Error fetching details/trailer for ${currentTrailer.title}:`, error);
        toast.error(`Could not fetch trailer for "${currentTrailer.title}".`);
        return null;
    } finally {
        setIsFetchingTrailer(false); // Done loading
    }
  }, [currentIndex, trailerContents]);

  // Handle opening the trailer - now fetches details
  const handleOpenTrailer = useCallback(async () => {
    // Pause auto-rotation
    setAutoRotateEnabled(false); // Explicitly disable auto-rotate
    if (autoRotateRef.current) {
      clearInterval(autoRotateRef.current);
      autoRotateRef.current = null;
    }

    const videoId = await fetchAndGetTrailerVideoId(); // Await the fetch

    if (videoId) {
      console.log(`Opening trailer with video ID: ${videoId}`);
      setTrailerVideoId(videoId);
      setShowTrailer(true);
    } else {
      // Error/info toast is handled within fetchAndGetTrailerVideoId
      // Re-enable auto-rotation if trailer failed to open
      setAutoRotateEnabled(true);
    }
  }, [fetchAndGetTrailerVideoId]); // Depends on the memoized fetch function

  // Handle closing the trailer
  const handleCloseTrailer = useCallback(() => {
    setShowTrailer(false);
    setTrailerVideoId(null);
    setAutoRotateEnabled(true); // Re-enable auto-rotation
  }, []);



  // Simplify Loading/Error states
  if (isLoading) {
    return (
      <div className={`relative w-full h-[50vh] md:h-[60vh] bg-gradient-to-t from-vista-dark via-vista-dark to-transparent flex items-center justify-center ${className}`}>
        <Loader2 className="w-12 h-12 text-vista-blue animate-spin" />
      </div>
    );
  }

  if (trailerContents.length === 0) {
    return (
      <div className={`relative w-full h-[50vh] md:h-[60vh] bg-gradient-to-t from-vista-dark via-vista-dark to-transparent flex items-center justify-center ${className}`}>
        <p className="text-vista-light/70">No upcoming trailers available.</p>
      </div>
    );
  }

  const currentTrailer = trailerContents[currentIndex];
  // Determine which wrapper to use
  const ImageWrapper = isMobile ? StaticWrapper : MotionWrapper;
  const ContentWrapper = isMobile ? StaticWrapper : MotionWrapper;

  // Define motion props separately
  const imageMotionProps = !isMobile ? {
    initial:{ opacity: 0 },
    animate:{ opacity: 1 },
    exit:{ opacity: 0 },
    transition:{ duration: 0.7, ease: "easeInOut" }
  } : {};

  const contentMotionProps = !isMobile ? {
    initial:{ opacity: 0, y: 20 },
    animate:{ opacity: 1, y: 0 },
    exit:{ opacity: 0, y: -20 },
    transition:{ duration: 0.5, ease: "easeOut" }
  } : {};

  return (
    <div className={`trailer-section relative w-full overflow-hidden h-[50vh] md:h-[60vh] ${className}`}>
      {/* Background Image & Content */}
      <AnimatePresence initial={false}>
        <ImageWrapper
          key={`trailer-${currentTrailer.id}`}
          className="absolute inset-0 bg-vista-dark"
          {...imageMotionProps}
        >
          {/* Render Image conditionally but allow mobile */}
          {/* Use w780 size for mobile, original for desktop */}
          <Image
             src={currentTrailer.backdropUrl.replace('original', isMobile ? 'w780' : 'original')}
             alt={currentTrailer.title}
             fill
             // Optimized sizes prop for mobile vs desktop
             sizes={isMobile ? "(max-width: 768px) 100vw, 50vw" : "(max-width: 1200px) 100vw, 50vw"}
             quality={isMobile ? 70 : 85} // Slightly lower quality on mobile
             priority={currentIndex === 0}
             className="object-cover"
             style={{ objectPosition: isMobile ? 'center top' : 'center center' }}
             // Add onError fallback if needed
             onError={(e) => { e.currentTarget.src = '/images/content/placeholder-backdrop.jpg'; }}
          />

          {/* Gradient Overlays (Desktop ONLY) */}
          {!isMobile && (
             <>
                <div className="absolute inset-0 bg-gradient-to-t from-vista-dark via-vista-dark/70 to-transparent" />
                <div className="absolute inset-0 bg-gradient-to-r from-vista-dark/50 via-transparent to-transparent" />
             </>
          )}
           {/* Mobile-specific Gradient (Simpler) */}
           {isMobile && (
             <div className="absolute inset-0 bg-gradient-to-t from-vista-dark via-vista-dark/70 to-transparent" />
           )}

           {/* Content Overlay - Rendered always */}
           <div className={`absolute inset-0 flex flex-col justify-end p-4 md:p-8 lg:p-12 z-10`}>
             <ContentWrapper
                className="max-w-2xl"
                {...contentMotionProps}
              >
               {/* Badges (e.g., Upcoming) */}
               <Badge variant="secondary" className="mb-2 md:mb-3 bg-opacity-70">
                  <Clock className="w-3 h-3 mr-1.5" /> Upcoming
               </Badge>

               {/* Title */}
               <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-1 md:mb-2 line-clamp-2">
                 {currentTrailer.title}
               </h2>

               {/* Short Overview */}
               <p className="text-sm md:text-base text-vista-light/80 mb-3 md:mb-4 line-clamp-2">
                 {currentTrailer.overview}
               </p>

               {/* Action Buttons */}
               <div className="flex flex-wrap items-center gap-2 md:gap-3">
                  {/* Play Trailer Button */}
                  <Button
                    onClick={handleOpenTrailer}
                    disabled={isFetchingTrailer} // Disable while fetching
                    size={isMobile ? 'sm' : 'default'}
                    className="bg-white text-vista-dark hover:bg-white/90"
                  >
                    {isFetchingTrailer ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <Play className="mr-2 h-4 w-4" />
                    )}
                    {isFetchingTrailer ? 'Loading...' : 'Play Trailer'}
                  </Button>

                  {/* Add to Watchlist Button */}
                  <Button
                    variant="outline"
                    size={isMobile ? 'sm' : 'default'}
                    onClick={handleToggleWatchlist}
                    disabled={isProcessing} // Disable during processing
                    className="border-vista-light/30 text-vista-light hover:bg-white/10 backdrop-blur-sm bg-black/20"
                  >
                    {isProcessing ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : inWatchlist ? (
                      <Check className="mr-2 h-4 w-4 text-vista-blue" />
                    ) : (
                      <Plus className="mr-2 h-4 w-4" />
                    )}
                    {inWatchlist ? 'In My List' : 'Add to My List'}
                  </Button>

                  {/* More Info Button */}
                 <Link href={`/details/${currentTrailer.mediaType}/${currentTrailer.id}`} passHref>
                   <Button
                     variant="outline"
                     size={isMobile ? 'sm' : 'default'}
                     className="border-vista-light/30 text-vista-light hover:bg-white/10 backdrop-blur-sm bg-black/20"
                     >
                     <Info className="mr-2 h-4 w-4" />
                      More Info
                   </Button>
                 </Link>
               </div>
             </ContentWrapper>
           </div>
        </ImageWrapper>
      </AnimatePresence>

      {/* Trailer Video Player Modal - Conditionally render only on Desktop */}
      {!isMobile && (
        <AnimatePresence>
          {showTrailer && trailerVideoId && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center p-2 sm:p-4 md:p-8"
              onClick={handleCloseTrailer} // Close on overlay click
            >
              {/* Close button */}
              <button
                className="absolute top-2 right-2 sm:top-4 sm:right-4 text-white/70 hover:text-white z-[60] p-2 rounded-full hover:bg-white/10 transition-all duration-200"
                onClick={(e) => {
                  e.stopPropagation(); // Prevent overlay click when clicking button
                  handleCloseTrailer();
                }}
                aria-label="Close trailer"
              >
                <X className="w-6 h-6 sm:w-8 sm:h-8" />
              </button>

              {/* Video Player */}
              <div
                className="relative w-full h-full max-w-6xl max-h-[90vh] bg-black rounded-lg overflow-hidden shadow-2xl"
                onClick={(e) => e.stopPropagation()} // Prevent closing when clicking player
                style={{ aspectRatio: '16/9' }}
              >
               <CustomVideoPlayer 
                 videoId={trailerVideoId} 
                 autoPlay={true} 
                 className="w-full h-full"
                 height="100%"
               />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      )}

       {/* Navigation Arrows (optional, consider for usability) */}
      {!isMobile && trailerContents.length > 1 && (
         <>
           <button
             onClick={rotateToPrevTrailer}
             className="absolute left-2 md:left-4 top-1/2 -translate-y-1/2 z-20 p-2 bg-black/30 hover:bg-black/50 rounded-full text-white transition-colors"
             aria-label="Previous trailer"
           >
             <ChevronLeft className="w-6 h-6" />
           </button>
           <button
             onClick={rotateToNextTrailer}
             className="absolute right-2 md:right-4 top-1/2 -translate-y-1/2 z-20 p-2 bg-black/30 hover:bg-black/50 rounded-full text-white transition-colors"
             aria-label="Next trailer"
           >
             <ChevronRight className="w-6 h-6" />
           </button>
         </>
      )}

      {/* Navigation Dots (Desktop only) */}
      {!isMobile && trailerContents.length > 1 && (
        <div className="absolute bottom-3 md:bottom-4 left-1/2 transform -translate-x-1/2 flex gap-1.5 z-20">
          {trailerContents.map((_, index) => (
            <button
              key={`dot-${index}`}
              onClick={() => {
                 if (isTransitioning) return;
                 setAutoRotateEnabled(false); // Stop rotation on manual nav
                 setIsTransitioning(true);
                 setPreviousIndex(currentIndex);
                 setCurrentIndex(index);
                 setTimeout(() => setIsTransitioning(false), 700);
                 // Consider restarting auto-rotate after a delay if desired
              }}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                index === currentIndex
                  ? 'bg-vista-blue scale-125 w-4' // Active dot styling
                  : 'bg-white/40 hover:bg-white/60'
              }`}
              aria-label={`View trailer ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  );
}
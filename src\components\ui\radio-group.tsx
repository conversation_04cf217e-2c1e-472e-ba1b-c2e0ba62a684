"use client"

import * as React from "react"
import { useId } from "react"
import * as RadioGroupPrimitive from "@radix-ui/react-radio-group"
import { Circle } from "lucide-react"
import { cn } from "@/lib/utils"

const RadioGroup = React.forwardRef<
  React.ElementRef<typeof RadioGroupPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>
>(({ className, ...props }, ref) => {
  return (
    <RadioGroupPrimitive.Root
      className={cn("grid gap-2", className)}
      {...props}
      ref={ref}
    />
  )
})
RadioGroup.displayName = RadioGroupPrimitive.Root.displayName

const RadioGroupItem = React.forwardRef<
  React.ElementRef<typeof RadioGroupPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>
>(({ className, ...props }, ref) => {
  return (
    <RadioGroupPrimitive.Item
      ref={ref}
      className={cn(
        "aspect-square h-4 w-4 rounded-full border border-vista-light/30 text-vista-blue ring-offset-vista-dark focus:outline-none focus-visible:ring-2 focus-visible:ring-vista-blue focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
        className
      )}
      {...props}
    >
      <RadioGroupPrimitive.Indicator className="flex items-center justify-center">
        <Circle className="h-2.5 w-2.5 fill-current text-current" />
      </RadioGroupPrimitive.Indicator>
    </RadioGroupPrimitive.Item>
  )
})
RadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName

// Additional wrapper component for label and radio together
const RadioGroupItemWithLabel = React.forwardRef<
  React.ElementRef<typeof RadioGroupItem> & HTMLLabelElement,
  React.ComponentPropsWithoutRef<typeof RadioGroupItem> & {
    label: string
    description?: string
  }
>(({ className, label, description, id, ...props }, ref) => {
  const generatedId = useId()
  const itemId = id || `radio-${generatedId}`

  return (
    <div className="flex items-start space-x-2">
      <RadioGroupItem id={itemId} {...props} className={className} />
      <label
        htmlFor={itemId}
        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
      >
        <div>{label}</div>
        {description && (
          <p className="text-sm text-vista-light/60 mt-1">{description}</p>
        )}
      </label>
    </div>
  )
})
RadioGroupItemWithLabel.displayName = "RadioGroupItemWithLabel"

export { RadioGroup, RadioGroupItem, RadioGroupItemWithLabel }

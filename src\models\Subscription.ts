import mongoose, { Document, Schema } from 'mongoose';

export interface ISubscription extends Document {
  userId: mongoose.Types.ObjectId;
  plan: 'free' | 'basic' | 'premium' | 'family';
  status: 'active' | 'canceled' | 'expired' | 'pending';
  startDate: Date;
  endDate?: Date;
  renewalDate?: Date;
  price: number;
  interval: 'monthly' | 'yearly';
  paymentMethod?: string;
  paymentMethodId?: string;
  lastPaymentDate?: Date;
  nextPaymentDate?: Date;
  canceledAt?: Date;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

const SubscriptionSchema = new Schema<ISubscription>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true
    },
    plan: {
      type: String,
      enum: ['free', 'basic', 'premium', 'family'],
      default: 'free',
      required: true,
      index: true
    },
    status: {
      type: String,
      enum: ['active', 'canceled', 'expired', 'pending'],
      default: 'active',
      required: true,
      index: true
    },
    startDate: {
      type: Date,
      required: true
    },
    endDate: {
      type: Date
    },
    renewalDate: {
      type: Date
    },
    price: {
      type: Number,
      required: true,
      default: 0
    },
    interval: {
      type: String,
      enum: ['monthly', 'yearly'],
      default: 'monthly'
    },
    paymentMethod: {
      type: String
    },
    paymentMethodId: {
      type: String
    },
    lastPaymentDate: {
      type: Date
    },
    nextPaymentDate: {
      type: Date
    },
    canceledAt: {
      type: Date
    },
    metadata: {
      type: Schema.Types.Mixed
    }
  },
  {
    timestamps: true
  }
);

// Create indexes for efficient querying
SubscriptionSchema.index({ userId: 1, status: 1 });
SubscriptionSchema.index({ plan: 1, status: 1 });
SubscriptionSchema.index({ renewalDate: 1 });
SubscriptionSchema.index({ nextPaymentDate: 1 });

// Create the model if it doesn't exist already
const Subscription = mongoose.models.Subscription || 
                     mongoose.model<ISubscription>('Subscription', SubscriptionSchema);

export default Subscription;

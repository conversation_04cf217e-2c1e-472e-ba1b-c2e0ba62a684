import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { options } from '@/app/api/auth/[...nextauth]/options';
import { ensureMongooseConnection } from '@/lib/mongoose';
import ContentView from '@/models/ContentView';
import mongoose from 'mongoose';
import Content from '@/models/Content';

/**
 * POST /api/content/view
 * Record a content view
 */
export async function POST(request: NextRequest) {
  try {
    // Get the current session using the imported options
    const session = await getServerSession(options);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Get data from request
    const data = await request.json();
    const { contentId, contentType, duration, completed, progress } = data;

    // Validate required fields
    if (!contentId || !contentType) {
      return NextResponse.json(
        { error: 'Content ID and type are required' },
        { status: 400 }
      );
    }

    // Validate content type
    if (!['movie', 'show', 'episode'].includes(contentType)) {
      return NextResponse.json(
        { error: 'Invalid content type' },
        { status: 400 }
      );
    }

    // Get content details for logging
    const content = await Content.findById(contentId).select('title type');
    if (!content) {
      return NextResponse.json(
        { error: 'Content not found' },
        { status: 404 }
      );
    }

    // Create or update the content view
    const contentView = await ContentView.findOneAndUpdate(
      {
        contentId: new mongoose.Types.ObjectId(contentId),
        userId: new mongoose.Types.ObjectId(session.user.id),
        // Only update if this view is from the same day to avoid duplicate entries
        timestamp: {
          $gte: new Date(new Date().setHours(0, 0, 0, 0))
        }
      },
      {
        contentType,
        duration: duration || 0,
        completed: completed || false,
        progress: progress || 0,
        timestamp: new Date()
      },
      {
        upsert: true,
        new: true
      }
    );

    // Increment the views count for the content
    await Content.findByIdAndUpdate(
      contentId,
      { $inc: { views: 1 } }
    );

    return NextResponse.json({
      success: true,
      contentView
    });
  } catch (error) {
    console.error('Error recording content view:', error);
    return NextResponse.json(
      { error: 'Failed to record content view' },
      { status: 500 }
    );
  }
}

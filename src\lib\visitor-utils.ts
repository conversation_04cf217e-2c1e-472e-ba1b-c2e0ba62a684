import { cookies } from 'next/headers';
import { ensureMongooseConnection } from '@/lib/mongodb';
import { NextRequest } from 'next/server';

/**
 * Mark a visitor as converted to a registered user
 *
 * This function should be called when a visitor registers as a user.
 * It updates the AnonymousVisitor record to mark it as converted.
 *
 * @param userId The ID of the newly registered user
 * @param request The NextRequest object (to access cookies)
 * @returns A promise that resolves when the visitor is marked as converted
 */
export async function markVisitorAsConverted(userId: string, request?: NextRequest): Promise<boolean> {
  try {
    let visitorId: string | undefined;

    // Get visitor ID from cookie - try both methods
    if (request) {
      // If we have a request object, use it to get the cookie
      visitorId = request.cookies.get('visitorId')?.value;
    } else {
      // Otherwise try to use the cookies API (works in Server Components)
      try {
        const cookieStore = await cookies();
        visitorId = cookieStore.get('visitorId')?.value;
      } catch (cookieError) {
        console.error('Error accessing cookies:', cookieError);
      }
    }

    // If no visitor ID, there's nothing to update
    if (!visitorId) {
      return false;
    }

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Import the AnonymousVisitor model
    const AnonymousVisitor = (await import('@/models/AnonymousVisitor')).default;

    // Update visitor record
    const result = await AnonymousVisitor.updateOne(
      { visitorId },
      {
        $set: {
          convertedToUser: true,
          userId
        }
      }
    );

    return result.matchedCount > 0;
  } catch (error) {
    console.error('Error marking visitor as converted:', error);
    return false;
  }
}

/**
 * Get visitor ID from cookie
 *
 * This function can be used on the server side to get the visitor ID.
 *
 * @param request The NextRequest object (optional)
 * @returns The visitor ID or null if not found
 */
export async function getVisitorId(request?: NextRequest): Promise<string | null> {
  try {
    if (request) {
      // If we have a request object, use it to get the cookie
      return request.cookies.get('visitorId')?.value || null;
    } else {
      // Otherwise try to use the cookies API (works in Server Components)
      const cookieStore = await cookies();
      return cookieStore.get('visitorId')?.value || null;
    }
  } catch (error) {
    console.error('Error getting visitor ID:', error);
    return null;
  }
}

import { NextRequest, NextResponse } from 'next/server';

// Define the person data structure
interface PersonData {
  id: number;
  name: string;
  profile_path: string | null;
  gender?: number;
  biography?: string;
  birthday?: string;
  place_of_birth?: string;
  images?: Array<{
    aspect_ratio: number;
    file_path: string;
    height: number;
    width: number;
    vote_average?: number;
    vote_count?: number;
  }>;
  imdb_id?: string;
  known_for_department?: string;
  fromCache?: boolean;
}

// Simple in-memory cache to reduce API calls to TMDb
interface CacheEntry {
  data: PersonData;
  timestamp: number;
}

const personCache = new Map<string, CacheEntry>();
const CACHE_TTL = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

/**
 * API route to fetch detailed person information from TMDb
 * This includes additional profile images and biographical information
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  console.log(`[API] /api/person/[id] called with ID: ${params?.id}`);

  try {
    const { id } = params;
    const forceRefresh = request.nextUrl.searchParams.get('refresh') === 'true';

    if (!id) {
      console.error('[API] Person ID is missing');
      return NextResponse.json({ error: 'Person ID is required' }, { status: 400 });
    }

    console.log(`[API] Fetching person details for ID: ${id}, force refresh: ${forceRefresh}`);

    // Check cache first (unless force refresh is requested)
    const cacheKey = `person-${id}`;
    const now = Date.now();
    const cachedEntry = personCache.get(cacheKey);

    if (!forceRefresh && cachedEntry && (now - cachedEntry.timestamp < CACHE_TTL)) {
      console.log(`[API] Using cached person data for ID: ${id}`);

      // Set CORS headers
      const responseObj = NextResponse.json({
        ...cachedEntry.data,
        fromCache: true
      });

      responseObj.headers.set('Access-Control-Allow-Origin', '*');
      responseObj.headers.set('Access-Control-Allow-Methods', 'GET');
      responseObj.headers.set('Access-Control-Allow-Headers', 'Content-Type');
      responseObj.headers.set('Cache-Control', 'public, max-age=86400');

      return responseObj;
    }

    // Get API key from environment
    const apiKey = process.env.NEXT_PUBLIC_TMDB_API_KEY;

    if (!apiKey) {
      console.error('[API] NEXT_PUBLIC_TMDB_API_KEY not found in environment variables');
      return NextResponse.json(
        { error: 'Configuration error - API key missing' },
        { status: 500 }
      );
    }

    // Fetch detailed person data from TMDb
    const url = `https://api.themoviedb.org/3/person/${id}?api_key=${apiKey}&append_to_response=images`;

    console.log(`[API] Fetching from TMDb: ${url.replace(apiKey, 'REDACTED')}`);

    const response = await fetch(url, {
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      // Add a timeout to prevent hanging requests
      signal: AbortSignal.timeout(5000)
    });

    console.log(`[API] TMDb response status: ${response.status}`);

    if (!response.ok) {
      console.error(`[API] TMDb API error: ${response.status}`);
      return NextResponse.json(
        { error: `Failed to fetch person data: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log(`[API] Received data for person: ${data.name || 'Unknown'}, has profile image: ${!!data.profile_path}`);

    // Prepare the response data
    const responseData = {
      id: data.id,
      name: data.name,
      profile_path: data.profile_path,
      gender: data.gender,
      biography: data.biography,
      birthday: data.birthday,
      place_of_birth: data.place_of_birth,
      images: data.images?.profiles || [],
      imdb_id: data.imdb_id,
      known_for_department: data.known_for_department
    };

    // Cache the result
    personCache.set(cacheKey, { data: responseData, timestamp: now });

    // Add CORS headers for better compatibility
    const responseObj = NextResponse.json(responseData);

    responseObj.headers.set('Access-Control-Allow-Origin', '*');
    responseObj.headers.set('Access-Control-Allow-Methods', 'GET');
    responseObj.headers.set('Access-Control-Allow-Headers', 'Content-Type');
    responseObj.headers.set('Cache-Control', 'public, max-age=86400');

    return responseObj;
  } catch (error) {
    console.error('[API] Error in person details API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch person details', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
import mongoose, { Schema, Document } from 'mongoose';

export interface IFeaturedContent extends Document {
  contentId: mongoose.Types.ObjectId;
  order: number;
  startDate?: Date;
  endDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Define the FeaturedContent schema
const FeaturedContentSchema = new Schema<IFeaturedContent>({
  contentId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Content', 
    required: true 
  },
  order: { 
    type: Number, 
    required: true, 
    default: 0 
  },
  startDate: { 
    type: Date 
  },
  endDate: { 
    type: Date 
  }
}, {
  timestamps: true
});

// Add indexes for common queries
FeaturedContentSchema.index({ order: 1 });
FeaturedContentSchema.index({ contentId: 1 }, { unique: true });
FeaturedContentSchema.index({ startDate: 1, endDate: 1 });

// Use mongoose.models.FeaturedContent if it exists, otherwise create a new model
const FeaturedContent = mongoose.models.FeaturedContent as mongoose.Model<IFeaturedContent> || 
                        mongoose.model<IFeaturedContent>('FeaturedContent', FeaturedContentSchema);

export default FeaturedContent;

"use client";

import Image from 'next/image';
import { PlayCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";

interface ShowcaseItem {
  id: string | number;
  title: string;
  description: string;
  imageUrl: string;
  logoUrl?: string;
  videoUrl?: string;
  releaseInfo?: string;
}

interface FeaturedShowcaseProps {
  items: ShowcaseItem[];
  carouselTitle?: string;
}

export default function FeaturedShowcaseCarousel({
  items,
  carouselTitle = "Featured"
}: FeaturedShowcaseProps) {
  if (!items || items.length === 0) {
    return null;
  }

  return (
    <section className="py-12 md:py-16 relative overflow-hidden bg-vista-dark">
      <div className="container px-4 md:px-6 mx-auto relative z-10">
        {carouselTitle && (
          <h2 className="text-2xl md:text-3xl font-semibold text-vista-light mb-6 md:mb-8">
            {carouselTitle}
          </h2>
        )}

        <Carousel
          opts={{
            align: "start",
            loop: true,
          }}
          className="w-full"
        >
          <CarouselContent className="-ml-4">
            {items.map((item, index) => (
              <CarouselItem key={item.id || index} className="pl-4 basis-full">
                <div className={`flex flex-col md:flex-row items-center gap-6 md:gap-10`}>
                  <motion.div
                    className="w-full md:w-1/2"
                    initial={{ opacity: 0, x: -30 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, ease: "easeOut" }}
                  >
                    {item.logoUrl ? (
                      <div className="relative h-14 md:h-20 w-full mb-4">
                        <Image
                          src={item.logoUrl}
                          alt={item.title}
                          fill
                          className="object-contain object-left"
                        />
                      </div>
                    ) : (
                      <h2 className="text-2xl md:text-3xl lg:text-4xl font-semibold text-vista-light mb-4 tracking-tight">
                        {item.title}
                      </h2>
                    )}

                    {item.releaseInfo && (
                      <div className="mb-3">
                        <span className="bg-vista-accent text-white px-2 py-0.5 rounded text-sm font-medium">
                          {item.releaseInfo}
                        </span>
                      </div>
                    )}

                    <p className="text-base md:text-lg text-vista-light/80 mb-6 md:max-w-md leading-relaxed line-clamp-3 md:line-clamp-none">
                      {item.description}
                    </p>

                    <div className="flex flex-wrap gap-3">
                      <Button className="bg-vista-accent hover:bg-vista-accent-dim text-white flex items-center gap-2 rounded-full px-6">
                        <PlayCircle className="h-5 w-5" />
                        Watch Now
                      </Button>

                      <Button variant="outline" className="border-vista-light/20 text-vista-light hover:bg-vista-light/10 rounded-full">
                        Learn More
                      </Button>
                    </div>
                  </motion.div>

                  <motion.div
                    className="w-full md:w-1/2 relative"
                    initial={{ opacity: 0, x: 30 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, ease: "easeOut" }}
                  >
                    <div className="relative aspect-[4/3] sm:aspect-video rounded-lg overflow-hidden shadow-xl">
                      <Image
                        src={item.imageUrl}
                        alt={item.title}
                        fill
                        className="object-cover"
                      />

                      {item.videoUrl && (
                        <button
                          className="absolute inset-0 flex items-center justify-center group focus:outline-none"
                          aria-label={`Play trailer for ${item.title}`}
                        >
                          <div className="bg-black/40 backdrop-blur-sm p-3 rounded-full group-hover:bg-vista-accent transition-colors cursor-pointer">
                            <PlayCircle className="h-10 w-10 text-white" />
                          </div>
                        </button>
                      )}
                    </div>
                  </motion.div>
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious className="absolute left-[-5px] top-1/2 -translate-y-1/2 z-20 hidden md:flex bg-vista-dark/50 hover:bg-vista-dark/80 border-none text-white w-10 h-10 items-center justify-center rounded-full" />
          <CarouselNext className="absolute right-[-5px] top-1/2 -translate-y-1/2 z-20 hidden md:flex bg-vista-dark/50 hover:bg-vista-dark/80 border-none text-white w-10 h-10 items-center justify-center rounded-full" />
        </Carousel>
      </div>
    </section>
  );
}

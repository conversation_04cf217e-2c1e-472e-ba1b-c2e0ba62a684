"use client";

import { useState, useEffect, useRef } from "react";
import { X, Search } from "lucide-react";
import { useRouter } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import Image from "next/image";
import Link from "next/link";

import { IContent } from "@/data/content";
import { popularShows, popularMovies } from "@/data/content";

interface SearchOverlayProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function SearchOverlay({ isOpen, onClose }: SearchOverlayProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<IContent[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  // Focus input when overlay opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  // Load recent searches from localStorage
  useEffect(() => {
    const savedSearches = localStorage.getItem("recentSearches");
    if (savedSearches) {
      setRecentSearches(JSON.parse(savedSearches));
    }
  }, []);

  // Handle search query changes
  useEffect(() => {
    if (searchQuery.trim()) {
      const allContent = [...popularShows, ...popularMovies];
      const results = allContent.filter((item) =>
        item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.genres.some(genre =>
          genre.toLowerCase().includes(searchQuery.toLowerCase())
        )
      );
      setSearchResults(results);
    } else {
      setSearchResults([]);
    }
  }, [searchQuery]);

  // Handle search submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();

    if (searchQuery.trim()) {
      // Save to recent searches
      const updatedSearches = [
        searchQuery,
        ...recentSearches.filter(s => s !== searchQuery)
      ].slice(0, 5);

      setRecentSearches(updatedSearches);
      localStorage.setItem("recentSearches", JSON.stringify(updatedSearches));

      // Navigate to search results page
      router.push(`/search?q=${encodeURIComponent(searchQuery)}`);

      // Close the overlay
      onClose();
    }
  };

  // Handle clicks outside the search panel to close
  const overlayRef = useRef<HTMLDivElement>(null);
  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === overlayRef.current) {
      onClose();
    }
  };

  // Handle escape key to close
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose();
    };

    window.addEventListener("keydown", handleEscape);
    return () => window.removeEventListener("keydown", handleEscape);
  }, [onClose]);

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          ref={overlayRef}
          className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex justify-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={handleOverlayClick}
        >
          <motion.div
            className="w-full max-w-4xl mt-24 bg-black/90 rounded-t-xl overflow-hidden"
            initial={{ y: -50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -50, opacity: 0 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
          >
            {/* Search Header */}
            <div className="p-4 border-b border-vista-light/10">
              <div className="relative">
                <form onSubmit={handleSearch}>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-vista-light/50 h-5 w-5" />
                    <input
                      ref={searchInputRef}
                      type="text"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      placeholder="Search for shows, movies, genres..."
                      className="w-full pl-10 pr-10 py-3 bg-vista-light/10 text-vista-light rounded-lg focus:outline-none focus:ring-1 focus:ring-vista-accent"
                    />
                    {searchQuery && (
                      <button
                        type="button"
                        onClick={() => setSearchQuery("")}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-vista-light/50 hover:text-vista-light"
                      >
                        <X className="h-5 w-5" />
                      </button>
                    )}
                  </div>
                </form>
              </div>
            </div>

            {/* Search Results */}
            <div className="p-4 overflow-y-auto max-h-[70vh]">
              {/* Quick Filters */}
              {!searchQuery && (
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-vista-light/70 mb-3">Quick Filters</h3>
                  <div className="flex flex-wrap gap-2">
                    {["Action", "Drama", "Comedy", "Sci-Fi", "Thriller"].map((filter) => (
                      <button
                        key={filter}
                        onClick={() => setSearchQuery(filter)}
                        className="px-3 py-1.5 bg-vista-light/10 hover:bg-vista-light/20 text-vista-light text-sm rounded-full transition-colors"
                      >
                        {filter}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Recent Searches */}
              {!searchQuery && recentSearches.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-vista-light/70 mb-3">Recent Searches</h3>
                  <div className="space-y-2">
                    {recentSearches.map((search, index) => (
                      <button
                        key={index}
                        onClick={() => setSearchQuery(search)}
                        className="flex items-center w-full py-2 px-3 hover:bg-vista-light/10 rounded-lg transition-colors text-left"
                      >
                        <Search className="h-4 w-4 text-vista-light/50 mr-3" />
                        <span className="text-vista-light">{search}</span>
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Search Results List */}
              {searchQuery && (
                <div>
                  <h3 className="text-sm font-medium text-vista-light/70 mb-3">
                    {searchResults.length
                      ? `Results for "${searchQuery}"`
                      : `No results for "${searchQuery}"`
                    }
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {searchResults.map((result) => (
                      <Link
                        href={`/${result.type}s/${result.id}`}
                        key={result.id}
                        className="flex items-start space-x-3 p-2 hover:bg-vista-light/10 rounded-lg transition-colors"
                        onClick={onClose}
                      >
                        <div className="relative w-16 h-24 flex-shrink-0 overflow-hidden rounded-md">
                          <Image
                            src={result.image}
                            alt={result.title}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className="text-vista-light font-medium truncate">{result.title}</h4>
                          <p className="text-vista-light/70 text-sm">{result.type.charAt(0).toUpperCase() + result.type.slice(1)} • {result.year}</p>
                          <p className="text-vista-light/50 text-xs mt-1 line-clamp-2">
                            {result.description}
                          </p>
                        </div>
                      </Link>
                    ))}
                  </div>

                  {searchResults.length > 0 && (
                    <div className="mt-6 text-center">
                      <button
                        className="px-4 py-2 bg-vista-accent hover:bg-vista-accent-dim text-white rounded-full text-sm transition-colors"
                        onClick={handleSearch}
                      >
                        View All Results
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Close Button at Bottom */}
            <div className="p-4 border-t border-vista-light/10 flex justify-center">
              <button
                onClick={onClose}
                className="text-vista-light hover:text-vista-accent transition-colors"
              >
                Close
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

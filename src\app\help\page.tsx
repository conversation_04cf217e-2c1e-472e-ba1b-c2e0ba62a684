'use client';

import { useState, useEffect } from 'react';
import { Navbar } from '@/components/Navbar';
import Footer from '@/components/Footer';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search, 
  MessageSquare, 
  HelpCircle, 
  FileText, 
  CreditCard, 
  Bug, 
  Settings, 
  Lightbulb,
  ChevronRight,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import Link from 'next/link';

const helpCategories = [
  {
    id: 'subscription',
    name: 'Subscription & Billing',
    description: 'Questions about your subscription, billing, and payments',
    icon: CreditCard,
    color: 'bg-blue-500/20 text-blue-400',
    count: 45
  },
  {
    id: 'technical',
    name: 'Technical Support',
    description: 'Streaming issues, app problems, and technical difficulties',
    icon: Settings,
    color: 'bg-green-500/20 text-green-400',
    count: 32
  },
  {
    id: 'account',
    name: 'Account Management',
    description: 'Profile settings, password reset, and account security',
    icon: FileText,
    color: 'bg-purple-500/20 text-purple-400',
    count: 28
  },
  {
    id: 'content',
    name: 'Content & Features',
    description: 'Questions about shows, movies, and platform features',
    icon: HelpCircle,
    color: 'bg-orange-500/20 text-orange-400',
    count: 21
  },
  {
    id: 'bug_report',
    name: 'Report a Bug',
    description: 'Found something broken? Let us know so we can fix it',
    icon: Bug,
    color: 'bg-red-500/20 text-red-400',
    count: 15
  },
  {
    id: 'feature_request',
    name: 'Feature Request',
    description: 'Suggest new features or improvements to StreamVista',
    icon: Lightbulb,
    color: 'bg-yellow-500/20 text-yellow-400',
    count: 12
  }
];

const popularFAQs = [
  {
    question: 'How do I cancel my subscription?',
    answer: 'You can cancel your subscription anytime by going to Settings > Subscription in your account. Your access will continue until the end of your current billing period.',
    category: 'subscription'
  },
  {
    question: 'Why is my video buffering or not loading?',
    answer: 'Video buffering is usually caused by slow internet connection. Try lowering the video quality in settings or check your internet speed. We recommend at least 25 Mbps for 4K streaming.',
    category: 'technical'
  },
  {
    question: 'How do I reset my password?',
    answer: 'Click "Forgot Password" on the login page and enter your email. We\'ll send you a reset link. You can also change your password in Settings > Security.',
    category: 'account'
  },
  {
    question: 'Can I download content for offline viewing?',
    answer: 'Yes! Look for the download icon on supported content. Downloads are available on mobile apps and desktop applications for up to 30 days.',
    category: 'content'
  },
  {
    question: 'How many devices can I use simultaneously?',
    answer: 'This depends on your subscription plan. Basic allows 1 device, Standard allows 2 devices, and Premium allows 4 devices streaming simultaneously.',
    category: 'subscription'
  },
  {
    question: 'How do I change my video quality?',
    answer: 'During playback, click the settings gear icon and select your preferred quality. You can also set a default quality in your account settings.',
    category: 'technical'
  }
];

const quickActions = [
  {
    title: 'Create Support Ticket',
    description: 'Get personalized help from our support team',
    icon: MessageSquare,
    href: '/help/tickets/new',
    color: 'bg-vista-blue hover:bg-vista-blue/90'
  },
  {
    title: 'My Tickets',
    description: 'View and track your support requests',
    icon: FileText,
    href: '/help/tickets',
    color: 'bg-vista-accent hover:bg-vista-accent/90'
  },
  {
    title: 'Live Chat',
    description: 'Chat with our support team in real-time',
    icon: MessageSquare,
    href: '#',
    color: 'bg-green-600 hover:bg-green-700'
  }
];

export default function HelpPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredFAQs, setFilteredFAQs] = useState(popularFAQs);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  useEffect(() => {
    let filtered = popularFAQs;

    if (searchQuery) {
      filtered = filtered.filter(faq => 
        faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
        faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (selectedCategory) {
      filtered = filtered.filter(faq => faq.category === selectedCategory);
    }

    setFilteredFAQs(filtered);
  }, [searchQuery, selectedCategory]);

  return (
    <div className="min-h-screen bg-vista-dark text-vista-light">
      <Navbar />
      
      {/* Hero Section */}
      <section className="relative py-20 px-4 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-vista-blue/10 to-transparent" />
        <div className="container mx-auto text-center relative z-10">
          <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-vista-light to-vista-blue bg-clip-text text-transparent">
            Help Center
          </h1>
          <p className="text-xl md:text-2xl text-vista-light/80 max-w-3xl mx-auto mb-8">
            Find answers to your questions or get personalized support from our team.
          </p>
          
          {/* Search Bar */}
          <div className="max-w-2xl mx-auto mb-8">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-vista-light/60 w-5 h-5" />
              <Input
                placeholder="Search for help articles, FAQs, or topics..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 pr-4 py-4 text-lg bg-vista-card border-vista-light/20 text-vista-light placeholder:text-vista-light/60"
              />
            </div>
          </div>

          {/* Quick Actions */}
          <div className="grid md:grid-cols-3 gap-4 max-w-4xl mx-auto">
            {quickActions.map((action, index) => (
              <Link key={index} href={action.href}>
                <Card className="bg-vista-card border-vista-light/10 hover:border-vista-blue/30 transition-all duration-200 hover:scale-105">
                  <CardContent className="p-6 text-center">
                    <div className={`w-12 h-12 mx-auto mb-3 rounded-full flex items-center justify-center ${action.color}`}>
                      <action.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="font-semibold text-vista-light mb-2">{action.title}</h3>
                    <p className="text-vista-light/70 text-sm">{action.description}</p>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-20 px-4">
        <div className="container mx-auto max-w-6xl">
          <Tabs defaultValue="browse" className="w-full">
            <TabsList className="grid w-full grid-cols-2 bg-vista-card border-vista-light/10 mb-8">
              <TabsTrigger value="browse" className="data-[state=active]:bg-vista-blue data-[state=active]:text-white">
                Browse by Category
              </TabsTrigger>
              <TabsTrigger value="faq" className="data-[state=active]:bg-vista-blue data-[state=active]:text-white">
                Popular FAQs
              </TabsTrigger>
            </TabsList>

            <TabsContent value="browse">
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {helpCategories.map((category) => (
                  <Link key={category.id} href={`/help/tickets/new?category=${category.id}`}>
                    <Card className="bg-vista-card border-vista-light/10 hover:border-vista-blue/30 transition-colors group cursor-pointer">
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${category.color}`}>
                              <category.icon className="w-5 h-5" />
                            </div>
                            <div>
                              <CardTitle className="text-vista-light text-lg">{category.name}</CardTitle>
                              <Badge variant="secondary" className="bg-vista-light/10 text-vista-light/70 text-xs">
                                {category.count} articles
                              </Badge>
                            </div>
                          </div>
                          <ChevronRight className="w-5 h-5 text-vista-light/60 group-hover:text-vista-blue transition-colors" />
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-vista-light/70 text-sm">{category.description}</p>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="faq">
              <div className="space-y-6">
                {/* Category Filter */}
                <div className="flex flex-wrap gap-2 mb-6">
                  <Button
                    variant={selectedCategory === null ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(null)}
                    className={selectedCategory === null ? "bg-vista-blue" : "border-vista-light/20 text-vista-light"}
                  >
                    All Categories
                  </Button>
                  {helpCategories.map((category) => (
                    <Button
                      key={category.id}
                      variant={selectedCategory === category.id ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedCategory(category.id)}
                      className={selectedCategory === category.id ? "bg-vista-blue" : "border-vista-light/20 text-vista-light"}
                    >
                      {category.name}
                    </Button>
                  ))}
                </div>

                {/* FAQ List */}
                <div className="space-y-4">
                  {filteredFAQs.length > 0 ? (
                    filteredFAQs.map((faq, index) => (
                      <Card key={index} className="bg-vista-card border-vista-light/10">
                        <CardHeader>
                          <CardTitle className="text-vista-light text-lg flex items-start gap-3">
                            <HelpCircle className="w-5 h-5 text-vista-blue mt-1 shrink-0" />
                            {faq.question}
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <p className="text-vista-light/80 leading-relaxed">{faq.answer}</p>
                          <div className="mt-4">
                            <Badge variant="outline" className="border-vista-light/20 text-vista-light/60 text-xs">
                              {helpCategories.find(cat => cat.id === faq.category)?.name}
                            </Badge>
                          </div>
                        </CardContent>
                      </Card>
                    ))
                  ) : (
                    <Card className="bg-vista-card border-vista-light/10">
                      <CardContent className="p-8 text-center">
                        <Search className="w-12 h-12 text-vista-light/40 mx-auto mb-4" />
                        <h3 className="text-lg font-semibold text-vista-light mb-2">No results found</h3>
                        <p className="text-vista-light/70">
                          Try adjusting your search terms or browse by category above.
                        </p>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </section>

      {/* Contact Support Section */}
      <section className="py-16 px-4 bg-gradient-to-r from-vista-blue/10 to-vista-accent/10">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl font-bold mb-6 text-vista-light">Still Need Help?</h2>
          <p className="text-vista-light/80 max-w-2xl mx-auto mb-8">
            Can't find what you're looking for? Our support team is here to help you with any questions or issues.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/help/tickets/new">
              <Button size="lg" className="bg-vista-blue hover:bg-vista-blue/90">
                <MessageSquare className="w-5 h-5 mr-2" />
                Create Support Ticket
              </Button>
            </Link>
            <Link href="/contact">
              <Button size="lg" variant="outline" className="border-vista-light/20 text-vista-light hover:bg-vista-light/10">
                Contact Us Directly
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}

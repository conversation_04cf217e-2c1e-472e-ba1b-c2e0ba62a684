import { NextRequest, NextResponse } from 'next/server';

// POST handler to mark all notifications as read
export async function POST(request: NextRequest) {
  try {
    // Get user ID from query parameters or body
    const searchParams = request.nextUrl.searchParams;
    let userId = searchParams.get('userId');

    // If not in query params, try to get from body
    if (!userId) {
      try {
        const body = await request.json();
        userId = body.userId;
      } catch (e) {
        // No body or invalid JSON
      }
    }

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const UserSchema = new mongoose.default.Schema({
      name: String,
      email: String
    });

    const User = mongoose.default.models.User ||
                mongoose.default.model('User', UserSchema);

    // Define the Notification schema directly
    const NotificationSchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      title: String,
      message: String,
      read: Boolean
    }, {
      timestamps: true
    });

    // Get the Notification model
    const Notification = mongoose.default.models.Notification ||
                        mongoose.default.model('Notification', NotificationSchema);

    // Find the user by ID
    const user = await User.findById(userId);

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Update all unread notifications for this user
    const result = await Notification.updateMany(
      { userId: user._id, read: false },
      { $set: { read: true } }
    );

    return NextResponse.json({
      success: true,
      modifiedCount: result.modifiedCount
    });
  } catch (error) {
    console.error('Error marking notifications as read:', error);
    return NextResponse.json(
      { error: 'Failed to mark notifications as read', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

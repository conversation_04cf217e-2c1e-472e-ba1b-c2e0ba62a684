'use client';

import { Navbar } from '@/components/Navbar';
import Footer from '@/components/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Shield, Eye, Database, Share2, Settings, Lock, Globe, UserCheck } from 'lucide-react';
import Link from 'next/link';

const privacySections = [
  {
    id: 'collection',
    title: 'Information We Collect',
    icon: Database,
    content: `We collect information to provide and improve our streaming service. This includes:

Account Information:
• Name, email address, and password
• Payment information (processed securely by third parties)
• Profile preferences and settings
• Subscription and billing history

Usage Information:
• Content you watch, search for, and interact with
• Device information (type, operating system, browser)
• IP address and general location data
• Viewing history and preferences
• App usage patterns and performance data

Communication Data:
• Customer support interactions
• Survey responses and feedback
• Marketing communication preferences`
  },
  {
    id: 'usage',
    title: 'How We Use Your Information',
    icon: Settings,
    content: `We use your information to provide, maintain, and improve our services:

Service Delivery:
• Authenticate your account and provide access
• Process payments and manage subscriptions
• Deliver personalized content recommendations
• Remember your preferences and settings

Improvement and Analytics:
• Analyze usage patterns to improve our service
• Develop new features and content
• Optimize streaming quality and performance
• Conduct research and analytics

Communication:
• Send service-related notifications
• Provide customer support
• Share updates about new content and features
• Send marketing communications (with your consent)`
  },
  {
    id: 'sharing',
    title: 'Information Sharing',
    icon: Share2,
    content: `We do not sell your personal information. We may share information in limited circumstances:

Service Providers:
• Payment processors for billing
• Cloud hosting and infrastructure providers
• Analytics and performance monitoring services
• Customer support platforms

Legal Requirements:
• When required by law or legal process
• To protect our rights and property
• To ensure user safety and security
• In connection with business transfers

With Your Consent:
• When you explicitly authorize sharing
• For specific features you choose to use
• With third-party integrations you enable`
  },
  {
    id: 'security',
    title: 'Data Security',
    icon: Lock,
    content: `We implement comprehensive security measures to protect your information:

Technical Safeguards:
• Encryption of data in transit and at rest
• Secure authentication and access controls
• Regular security audits and monitoring
• Industry-standard security protocols

Organizational Measures:
• Employee training on data protection
• Limited access to personal information
• Regular security policy updates
• Incident response procedures

Data Retention:
• We retain information only as long as necessary
• Account data is deleted upon request
• Viewing history can be cleared by users
• Automatic deletion of inactive accounts`
  },
  {
    id: 'rights',
    title: 'Your Privacy Rights',
    icon: UserCheck,
    content: `You have several rights regarding your personal information:

Access and Control:
• View and download your personal data
• Update your account information
• Manage communication preferences
• Delete your viewing history

Data Portability:
• Export your data in a common format
• Transfer information to other services
• Receive copies of your personal data

Deletion Rights:
• Request deletion of your account
• Remove specific personal information
• Clear your viewing and search history
• Opt out of data processing

Marketing Controls:
• Unsubscribe from marketing emails
• Opt out of personalized advertising
• Control recommendation algorithms
• Manage cookie preferences`
  },
  {
    id: 'international',
    title: 'International Data Transfers',
    icon: Globe,
    content: `StreamVista operates globally and may transfer data internationally:

Data Protection Standards:
• We ensure adequate protection for all transfers
• Use standard contractual clauses where required
• Comply with applicable data protection laws
• Implement additional safeguards as needed

Regional Compliance:
• GDPR compliance for European users
• CCPA compliance for California residents
• Local data protection law adherence
• Regular compliance audits and updates

Transfer Mechanisms:
• Adequacy decisions where available
• Standard contractual clauses
• Binding corporate rules
• User consent where appropriate`
  }
];

const dataTypes = [
  { type: 'Account Data', retention: '5 years after account closure', purpose: 'Service provision and legal compliance' },
  { type: 'Viewing History', retention: 'Until user deletion or 7 years', purpose: 'Personalization and recommendations' },
  { type: 'Payment Info', retention: 'As required by payment processors', purpose: 'Billing and fraud prevention' },
  { type: 'Support Data', retention: '3 years after resolution', purpose: 'Customer service and improvement' },
  { type: 'Marketing Data', retention: 'Until opt-out or 2 years', purpose: 'Communication and marketing' },
  { type: 'Analytics Data', retention: 'Aggregated data indefinitely', purpose: 'Service improvement and research' }
];

export default function PrivacyPage() {
  return (
    <div className="min-h-screen bg-vista-dark text-vista-light">
      <Navbar />
      
      {/* Hero Section */}
      <section className="relative py-20 px-4 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-vista-blue/10 to-transparent" />
        <div className="container mx-auto text-center relative z-10">
          <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-vista-light to-vista-blue bg-clip-text text-transparent">
            Privacy Policy
          </h1>
          <p className="text-xl md:text-2xl text-vista-light/80 max-w-3xl mx-auto mb-8">
            Your privacy is important to us. Learn how we collect, use, and protect your information.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Badge variant="secondary" className="bg-vista-blue/20 text-vista-blue">
              Last Updated: January 15, 2024
            </Badge>
            <Badge variant="outline" className="border-vista-light/20 text-vista-light/70">
              GDPR & CCPA Compliant
            </Badge>
          </div>
        </div>
      </section>

      {/* Privacy Highlights */}
      <section className="py-16 px-4 bg-vista-card/30">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12 text-vista-light">Privacy at a Glance</h2>
          <div className="grid md:grid-cols-4 gap-6">
            <Card className="bg-vista-card border-vista-light/10 text-center">
              <CardContent className="p-6">
                <Shield className="w-8 h-8 mx-auto mb-3 text-vista-blue" />
                <h3 className="font-semibold text-vista-light mb-2">No Data Sales</h3>
                <p className="text-vista-light/70 text-sm">We never sell your personal information to third parties.</p>
              </CardContent>
            </Card>
            <Card className="bg-vista-card border-vista-light/10 text-center">
              <CardContent className="p-6">
                <Eye className="w-8 h-8 mx-auto mb-3 text-vista-blue" />
                <h3 className="font-semibold text-vista-light mb-2">Transparency</h3>
                <p className="text-vista-light/70 text-sm">Clear information about what data we collect and why.</p>
              </CardContent>
            </Card>
            <Card className="bg-vista-card border-vista-light/10 text-center">
              <CardContent className="p-6">
                <Settings className="w-8 h-8 mx-auto mb-3 text-vista-blue" />
                <h3 className="font-semibold text-vista-light mb-2">Your Control</h3>
                <p className="text-vista-light/70 text-sm">Manage your privacy settings and data preferences.</p>
              </CardContent>
            </Card>
            <Card className="bg-vista-card border-vista-light/10 text-center">
              <CardContent className="p-6">
                <Lock className="w-8 h-8 mx-auto mb-3 text-vista-blue" />
                <h3 className="font-semibold text-vista-light mb-2">Secure</h3>
                <p className="text-vista-light/70 text-sm">Industry-leading security measures protect your data.</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Quick Navigation */}
      <section className="py-8 px-4">
        <div className="container mx-auto">
          <h2 className="text-xl font-semibold mb-4 text-vista-light">Quick Navigation</h2>
          <div className="flex flex-wrap gap-2">
            {privacySections.map((section) => (
              <a
                key={section.id}
                href={`#${section.id}`}
                className="px-3 py-1 text-sm bg-vista-card border border-vista-light/10 rounded-md hover:border-vista-blue/30 transition-colors text-vista-light/80 hover:text-vista-light"
              >
                {section.title}
              </a>
            ))}
          </div>
        </div>
      </section>

      {/* Privacy Sections */}
      <section className="py-20 px-4">
        <div className="container mx-auto max-w-4xl">
          <div className="space-y-8">
            {privacySections.map((section) => (
              <Card key={section.id} id={section.id} className="bg-vista-card border-vista-light/10">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3 text-vista-light">
                    <div className="w-10 h-10 bg-vista-blue/20 rounded-full flex items-center justify-center">
                      <section.icon className="w-5 h-5 text-vista-blue" />
                    </div>
                    <span className="text-xl">{section.title}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="prose prose-invert max-w-none">
                    {section.content.split('\n\n').map((paragraph, pIndex) => (
                      <div key={pIndex} className="mb-4">
                        {paragraph.includes('•') ? (
                          <div>
                            {paragraph.split('\n').map((line, lIndex) => (
                              <div key={lIndex}>
                                {line.startsWith('•') ? (
                                  <div className="flex items-start gap-2 ml-4 mb-1">
                                    <span className="text-vista-blue mt-1">•</span>
                                    <span className="text-vista-light/80 text-sm">{line.substring(2)}</span>
                                  </div>
                                ) : line.trim() && !line.includes(':') ? (
                                  <p className="text-vista-light/80 text-sm mb-2">{line}</p>
                                ) : line.includes(':') ? (
                                  <h4 className="font-semibold text-vista-light text-sm mt-3 mb-2">{line}</h4>
                                ) : null}
                              </div>
                            ))}
                          </div>
                        ) : (
                          <p className="text-vista-light/80 text-sm leading-relaxed">{paragraph}</p>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <Separator className="bg-vista-light/10" />

      {/* Data Retention Table */}
      <section className="py-16 px-4 bg-vista-card/30">
        <div className="container mx-auto max-w-4xl">
          <h2 className="text-3xl font-bold mb-8 text-vista-light">Data Retention Periods</h2>
          <Card className="bg-vista-card border-vista-light/10">
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-vista-light/10">
                      <th className="text-left p-4 text-vista-light font-semibold">Data Type</th>
                      <th className="text-left p-4 text-vista-light font-semibold">Retention Period</th>
                      <th className="text-left p-4 text-vista-light font-semibold">Purpose</th>
                    </tr>
                  </thead>
                  <tbody>
                    {dataTypes.map((item, index) => (
                      <tr key={index} className="border-b border-vista-light/5">
                        <td className="p-4 text-vista-light/80 font-medium">{item.type}</td>
                        <td className="p-4 text-vista-light/70">{item.retention}</td>
                        <td className="p-4 text-vista-light/70 text-sm">{item.purpose}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Privacy Controls */}
      <section className="py-16 px-4">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl font-bold mb-8 text-vista-light">Manage Your Privacy</h2>
          <p className="text-vista-light/80 max-w-2xl mx-auto mb-8">
            Take control of your privacy settings and data preferences. You can update these at any time.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/settings">
              <Button size="lg" className="bg-vista-blue hover:bg-vista-blue/90">
                Privacy Settings
              </Button>
            </Link>
            <Link href="/contact">
              <Button size="lg" variant="outline" className="border-vista-light/20 text-vista-light hover:bg-vista-light/10">
                Contact Privacy Team
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="py-16 px-4 bg-gradient-to-r from-vista-blue/10 to-vista-accent/10">
        <div className="container mx-auto max-w-2xl text-center">
          <h2 className="text-3xl font-bold mb-6 text-vista-light">Questions About Privacy?</h2>
          <p className="text-vista-light/80 mb-6">
            If you have questions about this privacy policy or how we handle your data, please contact our privacy team.
          </p>
          <div className="space-y-2 text-vista-light/70">
            <p>Email: <EMAIL></p>
            <p>Address: 123 Streaming Ave, San Francisco, CA 94105</p>
            <p>Data Protection Officer: <EMAIL></p>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}

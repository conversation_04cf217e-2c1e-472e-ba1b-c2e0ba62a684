'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { CreditCard, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

interface PaymentMethod {
  id: string;
  type: 'visa' | 'mastercard' | 'amex' | 'discover';
  last4: string;
  expiryMonth: number;
  expiryYear: number;
  isDefault: boolean;
}

interface PaymentMethodsSectionProps {
  userId?: string;
}

export function PaymentMethodsSection({ userId }: PaymentMethodsSectionProps) {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch payment methods
  useEffect(() => {
    const fetchPaymentMethods = async () => {
      if (!userId) return;
      
      setIsLoading(true);
      try {
        // In a real app, this would be an API call to fetch payment methods
        // For now, we'll simulate it with a timeout and mock data
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Mock data - in a real app, this would come from the API
        setPaymentMethods([
          {
            id: 'pm_1234567890',
            type: 'visa',
            last4: '4242',
            expiryMonth: 12,
            expiryYear: 25,
            isDefault: true
          }
        ]);
      } catch (error) {
        console.error('Error fetching payment methods:', error);
        toast.error('Failed to load payment methods');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchPaymentMethods();
  }, [userId]);

  // Handle adding a new payment method
  const handleAddPaymentMethod = () => {
    // In a real app, this would open a payment method form or redirect to a payment processor
    toast.info('This would open a payment method form in a real app');
  };

  // Handle editing a payment method
  const handleEditPaymentMethod = (id: string) => {
    // In a real app, this would open a payment method form with the existing data
    toast.info(`This would edit payment method ${id} in a real app`);
  };

  // Handle removing a payment method
  const handleRemovePaymentMethod = (id: string) => {
    // In a real app, this would call an API to remove the payment method
    toast.info(`This would remove payment method ${id} in a real app`);
  };

  // Get card brand display name
  const getCardBrandDisplay = (type: string) => {
    switch (type) {
      case 'visa': return 'VISA';
      case 'mastercard': return 'MC';
      case 'amex': return 'AMEX';
      case 'discover': return 'DISC';
      default: return type.toUpperCase();
    }
  };

  // Get card brand background color
  const getCardBrandBgColor = (type: string) => {
    switch (type) {
      case 'visa': return 'bg-blue-500';
      case 'mastercard': return 'bg-orange-500';
      case 'amex': return 'bg-green-500';
      case 'discover': return 'bg-purple-500';
      default: return 'bg-gray-500';
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-vista-blue" />
      </div>
    );
  }

  return (
    <div className="space-y-5">
      {paymentMethods.length > 0 ? (
        <>
          {paymentMethods.map((method) => (
            <div key={method.id} className="bg-black/20 rounded-lg border border-white/5 p-4 flex justify-between items-center">
              <div className="flex items-center">
                <div className={`w-10 h-6 ${getCardBrandBgColor(method.type)} rounded mr-3 flex items-center justify-center`}>
                  <span className="text-xs font-bold text-white">{getCardBrandDisplay(method.type)}</span>
                </div>
                <div>
                  <div className="flex items-center gap-2">
                    <p className="text-vista-light">{method.type.charAt(0).toUpperCase() + method.type.slice(1)} ending in {method.last4}</p>
                    {method.isDefault && (
                      <span className="text-xs bg-emerald-500/20 text-emerald-400 px-2 py-0.5 rounded-full">Default</span>
                    )}
                  </div>
                  <p className="text-xs text-vista-light/70">Expires {method.expiryMonth.toString().padStart(2, '0')}/{method.expiryYear}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="bg-black/20 hover:bg-black/40 border-white/5 hover:border-white/10 text-vista-light h-8 px-3"
                  onClick={() => handleEditPaymentMethod(method.id)}
                >
                  Edit
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="bg-black/20 hover:bg-black/40 border-white/5 hover:border-white/10 text-vista-light h-8 px-3"
                  onClick={() => handleRemovePaymentMethod(method.id)}
                >
                  Remove
                </Button>
              </div>
            </div>
          ))}

          <Button 
            variant="outline" 
            className="bg-black/20 hover:bg-black/40 border-white/5 hover:border-white/10 text-emerald-400 w-full"
            onClick={handleAddPaymentMethod}
          >
            <CreditCard className="w-4 h-4 mr-2" /> Add Payment Method
          </Button>
        </>
      ) : (
        <div className="py-6 text-center">
          <p className="text-vista-light/70 mb-4">No payment methods found</p>
          <Button 
            variant="outline" 
            className="bg-black/20 hover:bg-black/40 border-white/5 hover:border-white/10 text-emerald-400"
            onClick={handleAddPaymentMethod}
          >
            <CreditCard className="w-4 h-4 mr-2" /> Add Payment Method
          </Button>
        </div>
      )}
    </div>
  );
}

"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from "@/components/ui/sheet"
import { ScrollArea } from "@/components/ui/scroll-area"
import { MessageSquare, Send, Loader2, Bot, User, XCircle, MinusCircle, Sparkles, HelpCircle } from "lucide-react"
import { cn } from "@/lib/utils"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import { motion, AnimatePresence } from "framer-motion"
import { useMediaQuery } from "@/hooks/use-media-query"

interface Message {
  id: string;
  sender: "user" | "ai"
  text: string
  quickReplies?: string[]
}

const LOCAL_STORAGE_KEY = 'chatAssistantDismissed';

export function ChatAssistant() {
  const [isOpen, setIsOpen] = useState(false)
  const [messages, setMessages] = useState<Message[]>([])
  const [input, setInput] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // State for visibility
  const [isDismissed, setIsDismissed] = useState(false)
  const [isMounted, setIsMounted] = useState(false)
  const [isMinimized, setIsMinimized] = useState(false)
  
  // Check if it's a mobile device
  const isMobile = useMediaQuery("(max-width: 640px)")

  // Initialize on first render
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Set mounted immediately
    setIsMounted(true);
    console.log("[ChatAssistant] Component mounted");

    // ALWAYS force the chat to be visible on initial load
    setIsDismissed(false);
    localStorage.setItem(LOCAL_STORAGE_KEY, 'false');

    // Add storage listener for cross-tab synchronization
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === LOCAL_STORAGE_KEY) {
        const newVal = event.newValue === 'true';
        console.log("[ChatAssistant] Storage event:", event.key, newVal);
        setIsDismissed(newVal);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  const scrollToBottom = () => {
    if (scrollAreaRef.current) {
      const viewport = scrollAreaRef.current.querySelector<HTMLDivElement>("[data-radix-scroll-area-viewport]")
      if (viewport) {
        viewport.scrollTop = viewport.scrollHeight
      }
    }
  }

  useEffect(() => {
    if (isOpen) {
      setTimeout(scrollToBottom, 100);
      // Focus the input field when chat opens
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }
  }, [messages, isOpen])

  const handleSendMessage = async (e?: React.FormEvent<HTMLFormElement>) => {
    e?.preventDefault()
    const userMessage = input.trim()
    if (!userMessage || isLoading) return

    // Add user message
    const newUserMessage: Message = { id: crypto.randomUUID(), sender: "user", text: userMessage };
    setMessages((prev) => [...prev, newUserMessage])
    setInput("")
    setIsLoading(true)
    scrollToBottom();

    try {
      const response = await fetch("/api/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ message: userMessage }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Failed to parse error response' }));
        throw new Error(errorData?.error || `API error: ${response.statusText}`)
      }
      
      const data = await response.json()
      const aiReply = data.reply || "I'm not sure how to respond to that. Can you try asking in a different way?";

      const aiMessage: Message = {
        id: crypto.randomUUID(),
        sender: "ai",
        text: aiReply,
      };
      setMessages((prev) => [...prev, aiMessage])
      
    } catch (error) {
      console.error("Failed to send message:", error)
      const errorMessageText = `Sorry, I encountered an error. ${error instanceof Error ? error.message : 'Please try again.'}`;
      
      const newErrorMessage: Message = {
        id: crypto.randomUUID(),
        sender: "ai",
        text: errorMessageText,
        quickReplies: ["Try again", "Help", "Contact support"]
      };
      setMessages((prev) => [...prev, newErrorMessage]);

      scrollToBottom();
    } finally {
      setIsLoading(false);
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }
  }

  // Handle opening and closing the chat
  const handleToggleChat = () => {
    if (isMinimized) {
      setIsMinimized(false);
    }
    setIsOpen(true);
  };

  // Handle minimizing the chat (temporarily hide but keep bubble visible)
  const handleMinimize = () => {
    setIsMinimized(true);
    setIsOpen(false);
  };

  // Handle dismissing the chat (requires footer button to bring back)
  const handleDismiss = () => {
    setIsDismissed(true);
    setIsOpen(false);
    setIsMinimized(false);
    localStorage.setItem(LOCAL_STORAGE_KEY, 'true');
    console.log("Chat dismissed! Use footer button to bring it back.");
  };

  // Function to handle clicking on a quick reply
  const handleQuickReply = (reply: string) => {
    setInput(reply);
    setTimeout(() => handleSendMessage(), 100);
  };

  // Don't render if explicitly dismissed
  // This allows the footer button to bring it back
  if (isDismissed) {
    return null;
  }

  // Don't render until client-side hydration is complete
  if (!isMounted) {
    return null;
  }

  return (
    <>
      {/* Chat button - fixed in bottom right corner, only visible when chat is not open */}
      <AnimatePresence>
        {(!isOpen || isMinimized) && (
          <motion.div
            className="fixed bottom-4 right-4 z-[9999]"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            transition={{ type: "spring", stiffness: 500, damping: 30 }}
          >
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="default"
                    size="icon"
                    className={cn(
                      "rounded-full shadow-xl bg-primary hover:bg-primary/90 flex items-center justify-center",
                      isMobile ? "h-12 w-12" : "h-14 w-14" // Smaller on mobile
                    )}
                    aria-label="Toggle Chat Assistant"
                    onClick={handleToggleChat}
                  >
                    <MessageSquare className={cn(
                      "text-primary-foreground",
                      isMobile ? "h-5 w-5" : "h-6 w-6" // Smaller icon on mobile
                    )} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="left" className="bg-background/95 backdrop-blur-sm border-border/50">
                  <p>{isMinimized ? "Restore chat" : "Open chat assistant"}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Minimized indicator */}
      <AnimatePresence>
        {isMinimized && (
          <motion.div
            className="fixed bottom-20 right-4 z-[9998]"
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            transition={{ type: "spring", stiffness: 500, damping: 30 }}
          >
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="w-3 h-3 bg-primary rounded-full animate-pulse shadow-[0_0_8px_rgba(var(--primary)/0.5)]"></div>
                </TooltipTrigger>
                <TooltipContent side="left" className="bg-background/95 backdrop-blur-sm border-border/50">
                  <p>Chat is minimized - click the chat button to restore</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </motion.div>
        )}
      </AnimatePresence>

      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetContent 
          showCloseButton={false} 
          className={cn(
            "flex flex-col h-full max-h-screen border-l border-border/50 z-[101] p-0 overflow-hidden bg-background/95 backdrop-blur-sm",
            isMobile ? "w-full sm:max-w-full" : "sm:max-w-md" // Full width on mobile
          )}
          side={isMobile ? "bottom" : "right"} // Bottom sheet on mobile, right side on desktop
        >
          <SheetHeader className={cn(
            "p-4 bg-gradient-to-r from-primary/10 to-background border-b border-border/50 flex flex-row justify-between items-center",
            isMobile && "sticky top-0 z-10" // Sticky header on mobile
          )}>
            <SheetTitle className="flex items-center gap-2 text-lg font-semibold text-foreground">
              <div className="bg-primary/20 p-1.5 rounded-lg">
                <Bot className="h-5 w-5 text-primary" />
              </div>
              <div className="flex flex-col">
                <span>StreamVista Assistant</span>
                <Badge variant="outline" className="mt-1 text-xs bg-background/50 border-primary/20 text-primary/80">
                  <Sparkles className="h-3 w-3 mr-1" /> AI Powered
                </Badge>
              </div>
            </SheetTitle>
            <div className="flex items-center gap-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" onClick={handleMinimize} className="h-8 w-8 hover:bg-background/80">
                      <MinusCircle className="h-5 w-5 text-muted-foreground hover:text-foreground transition-colors" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="bg-background/95 backdrop-blur-sm border-border/50">
                    <p>Minimize chat</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" onClick={handleDismiss} className="h-8 w-8 hover:bg-background/80">
                      <XCircle className="h-5 w-5 text-muted-foreground hover:text-destructive transition-colors" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="bg-background/95 backdrop-blur-sm border-border/50">
                    <p>Close chat (use footer button to reopen)</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </SheetHeader>

          <ScrollArea 
            className={cn(
              "flex-grow px-4 py-6",
              isMobile && "px-3 py-4" // Less padding on mobile
            )} 
            ref={scrollAreaRef}
          >
            <div className="space-y-6 mb-4">
              {messages.length === 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className="flex gap-3 items-start"
                >
                  <Avatar className="h-10 w-10 border-2 border-primary/20 shadow-sm flex items-center justify-center flex-shrink-0">
                    <AvatarFallback className="bg-primary/20 text-primary">
                      <Bot className="h-5 w-5" />
                    </AvatarFallback>
                  </Avatar>
                  <Card className="rounded-2xl bg-muted/50 border-border/30 px-4 py-3 text-sm max-w-[85%] shadow-sm">
                    <p>Hi there! How can I help you with StreamVista today?</p>
                    <div className="flex flex-wrap gap-2 mt-3">
                      {["Find movies", "TV shows", "Membership cost", "Help"].map((suggestion, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          size="sm"
                          className="bg-primary/10 border-primary/20 hover:bg-primary/20 text-xs rounded-full px-3 py-1 h-auto transition-all"
                          onClick={() => handleQuickReply(suggestion)}
                        >
                          {suggestion}
                        </Button>
                      ))}
                    </div>
                  </Card>
                </motion.div>
              )}

              <AnimatePresence initial={false}>
                {messages.map((msg) => (
                  <motion.div
                    key={msg.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className={cn(
                      "flex items-start gap-3", 
                      msg.sender === "user" ? "justify-end" : "justify-start",
                      isMobile && "gap-2" // Less gap on mobile
                    )}
                  >
                    {msg.sender === 'ai' && (
                      <Avatar className={cn(
                        "border-2 border-primary/20 shadow-sm flex items-center justify-center flex-shrink-0",
                        isMobile ? "h-8 w-8" : "h-10 w-10" // Smaller avatar on mobile
                      )}>
                        <AvatarFallback className="bg-primary/20 text-primary">
                          <Bot className={isMobile ? "h-4 w-4" : "h-5 w-5"} />
                        </AvatarFallback>
                      </Avatar>
                    )}
                    <div
                      className={cn(
                        "rounded-2xl px-4 py-3 text-sm max-w-[85%] break-words shadow-sm",
                        isMobile && "text-sm px-3 py-2.5", // Adjusted padding and text size on mobile
                        msg.sender === "user"
                          ? "bg-primary text-primary-foreground"
                          : "bg-muted/50 border border-border/30 text-foreground"
                      )}
                      style={{ overflowWrap: 'break-word', wordWrap: 'break-word' }}
                    >
                      <div>
                        {/* Render message text directly, handling newlines if needed */}
                        {msg.text.split('\\n').map((line, i, arr) => (
                          <span key={i}>{line}{i < arr.length - 1 && <br/>}</span>
                        ))}

                        {/* Quick replies are now only shown for the initial greeting, handled above */}
                        {/* This section can be removed if no other message type will have quick replies */}
                        {/* {msg.sender === 'ai' && msg.quickReplies && msg.quickReplies.length > 0 && (
                          <div className="flex flex-wrap gap-2 mt-3">
                            {msg.quickReplies.map((reply, index) => (
                              <Button
                                key={index}
                                variant="outline"
                                size="sm"
                                className={cn(
                                  "bg-primary/10 border-primary/20 hover:bg-primary/20 rounded-full px-3 py-1 h-auto transition-all",
                                  isMobile ? "text-xs" : "text-xs" // Text size adjustment
                                )}
                                onClick={() => handleQuickReply(reply)}
                              >
                                {reply}
                              </Button>
                            ))}
                          </div>
                        )} */}
                      </div>
                    </div>
                    {msg.sender === 'user' && (
                      <Avatar className={cn(
                        "border-2 border-muted/30 shadow-sm flex items-center justify-center flex-shrink-0",
                        isMobile ? "h-8 w-8" : "h-10 w-10" // Smaller avatar on mobile
                      )}>
                        <AvatarFallback className="bg-muted/50 text-muted-foreground">
                          <User className={isMobile ? "h-4 w-4" : "h-5 w-5"} />
                        </AvatarFallback>
                      </Avatar>
                    )}
                  </motion.div>
                ))}
              </AnimatePresence>

              {/* Show spinner when isLoading is true */}
              {isLoading && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="flex items-start gap-3 justify-start"
                >
                  <Avatar className={cn(
                    "border-2 border-primary/20 shadow-sm flex items-center justify-center flex-shrink-0",
                    isMobile ? "h-8 w-8" : "h-10 w-10" // Smaller avatar on mobile
                  )}>
                    <AvatarFallback className="bg-primary/20 text-primary">
                      <Bot className={isMobile ? "h-4 w-4" : "h-5 w-5"} />
                    </AvatarFallback>
                  </Avatar>
                  <Card className="rounded-2xl bg-muted/50 border-border/30 px-4 py-3 text-sm shadow-sm flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin text-primary" />
                    <span className="text-muted-foreground">Thinking...</span>
                  </Card>
                </motion.div>
              )}
            </div>
          </ScrollArea>

          <SheetFooter className={cn(
            "p-4 border-t border-border/50 bg-gradient-to-r from-background to-primary/5 mt-auto",
            isMobile && "px-3 py-3 sticky bottom-0 z-10" // Less padding and sticky footer on mobile
          )}>
            <form onSubmit={handleSendMessage} className="flex w-full items-center space-x-2">
              <Input
                ref={inputRef}
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder="Ask something..."
                disabled={isLoading}
                autoComplete="off"
                className={cn(
                  "flex-1 bg-background/80 border-border/50 focus-visible:ring-1 focus-visible:ring-primary focus-visible:ring-offset-0 rounded-full pl-4 pr-4 shadow-sm",
                  isMobile ? "py-5 text-base" : "py-6" // Taller and larger text on mobile for easier typing
                )}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage();
                  }
                }}
              />
              <Button
                type="submit"
                disabled={isLoading || !input.trim()}
                size="icon"
                variant={input.trim() ? "default" : "ghost"}
                className={cn(
                  "rounded-full bg-primary text-primary-foreground hover:bg-primary/90 transition-all duration-200 ease-in-out",
                  isMobile ? "h-10 w-10" : "h-10 w-10" // Consistent size on mobile
                )}
              >
                {/* Show loader when isLoading is true */}
                {isLoading ? (
                  <Loader2 className="h-5 w-5 animate-spin" />
                ) : (
                  <Send className="h-5 w-5" />
                )}
                <span className="sr-only">Send Message</span>
              </Button>
            </form>
          </SheetFooter>
          
          {/* Help button for mobile only */}
          {isMobile && (
            <div className="absolute top-4 left-3 z-20">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 rounded-full bg-background/20"
                      onClick={() => handleQuickReply("What can you help me with?")}
                    >
                      <HelpCircle className="h-4 w-4 text-primary" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="right">
                    <p>Get help</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          )}
        </SheetContent>
      </Sheet>
    </>
  )
}
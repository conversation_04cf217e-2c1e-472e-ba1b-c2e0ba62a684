import mongoose, { Schema, Document } from 'mongoose';

export interface ITag extends Document {
  name: string;
  slug: string;
  count: number;
  createdAt: Date;
  updatedAt: Date;
}

// Define the Tag schema
const TagSchema = new Schema<ITag>({
  name: { type: String, required: true },
  slug: { type: String, required: true, unique: true },
  count: { type: Number, default: 0 }
}, {
  timestamps: true
});

// Add only necessary indexes that aren't already defined by field options
// The 'unique: true' on slug already creates an index, so we don't need to define it again
TagSchema.index({ name: 'text' });

// Use mongoose.models.Tag if it exists, otherwise create a new model
const Tag = mongoose.models.Tag as mongoose.Model<ITag> || 
           mongoose.model<ITag>('Tag', TagSchema);

export default Tag;

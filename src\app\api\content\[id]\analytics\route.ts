import { NextRequest, NextResponse } from 'next/server';
import { getContentAnalytics } from '@/lib/content-analytics';
import { ensureMongooseConnection } from '@/lib/mongoose';
import mongoose from 'mongoose';

/**
 * GET /api/content/[id]/analytics
 * Get analytics for a specific content item
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Validate content ID
    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json({ error: 'Invalid content ID' }, { status: 400 });
    }

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Get content analytics
    const analytics = await getContentAnalytics(params.id);

    // Return analytics
    return NextResponse.json(analytics);
  } catch (error) {
    console.error('Error fetching content analytics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch content analytics' },
      { status: 500 }
    );
  }
}

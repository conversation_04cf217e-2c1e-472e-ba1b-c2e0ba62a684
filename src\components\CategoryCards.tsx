'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useLanguage } from '@/lib/i18n/LanguageContext';
import { Card, CardContent } from '@/components/ui/card';

// Mock categories with high quality images
const categoriesData = [
  {
    id: 'action',
    name: 'Action',
    image: 'https://images.unsplash.com/photo-1611262588024-d12430b98920?q=80&w=2274',
  },
  {
    id: 'comedy',
    name: 'Comedy',
    image: 'https://images.unsplash.com/photo-1543584756-38b1d56c5999?q=80&w=2070',
  },
  {
    id: 'drama',
    name: 'Drama',
    image: 'https://images.unsplash.com/photo-1598899134739-24c46f58b8c0?q=80&w=2056',
  },
  {
    id: 'fantasy',
    name: 'Fantasy',
    image: 'https://images.unsplash.com/photo-1579546929518-9e396f3cc809?q=80&w=2070',
  },
  {
    id: 'sci-fi',
    name: 'Sci-Fi',
    image: 'https://images.unsplash.com/photo-1569402928543-90cc0e76ae94?q=80&w=2070',
  },
  {
    id: 'romance',
    name: 'Romance',
    image: 'https://images.unsplash.com/photo-1487147264018-f937fba0c817?q=80&w=1974',
  },
  {
    id: 'thriller',
    name: 'Thriller',
    image: 'https://images.unsplash.com/photo-1532568891919-1fae55697456?q=80&w=2070',
  },
  {
    id: 'documentaries',
    name: 'Documentaries',
    image: 'https://images.unsplash.com/photo-1608346128025-1896b97a6fa7?q=80&w=2070',
  },
];

export default function CategoryCards() {
  const { t } = useLanguage();

  return (
    <div className="py-8">
      <div className="container px-4 mx-auto">
        <h2 className="text-2xl font-bold text-vista-light mb-6">
          Browse by Category
        </h2>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {categoriesData.map((category) => (
            <Link
              key={category.id}
              href={`/category/${category.id}`}
              className="transition-transform hover:scale-[1.03] focus:scale-[1.03]"
            >
              <Card className="overflow-hidden bg-vista-dark-card border-vista-dark-card">
                <CardContent className="p-0">
                  <div className="relative aspect-[16/9]">
                    <Image
                      src={category.image}
                      alt={category.name}
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-vista-dark via-transparent to-transparent" />
                    <div className="absolute bottom-0 left-0 right-0 p-4">
                      <h3 className="text-lg font-medium text-vista-light">
                        {category.name}
                      </h3>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
} 
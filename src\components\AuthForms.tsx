import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Eye, EyeOff, LogIn, UserPlus, Github, Mail, AlertCircle } from 'lucide-react';
import GoogleSignInButton from './GoogleSignInButton';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';

interface AuthFormsProps {
  redirectTo?: string;
}

export default function AuthForms({
  redirectTo = '/'
}: AuthFormsProps) {
  const [mode, setMode] = useState<'login' | 'signup'>('login');
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();
  const { signIn, signUp, isLoading, networkError } = useAuth();

  // Form fields
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [formError, setFormError] = useState<string | null>(null);

  // Track form submission loading state separately from Google auth
  const [isFormSubmitting, setIsFormSubmitting] = useState(false);

  // Clear form error when mode changes
  useEffect(() => {
    setFormError(null);
  }, [mode]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);
    setIsFormSubmitting(true);

    try {
      let result;

      if (mode === 'login') {
        result = await signIn(email, password);
      } else {
        result = await signUp(name, email, password);
      }

      if (result && result.success) {
        router.push(redirectTo);
      } else if (result && result.error) {
        setFormError(result.error);
      } else if (!result) {
        console.error('Authentication result is undefined');
        setFormError('Authentication failed. Please try again.');
      }
    } catch (error) {
      console.error('Authentication error:', error);
      if (error instanceof Error) {
        setFormError(error.message);
      }
    } finally {
      setIsFormSubmitting(false);
    }
  };

  const handleGoogleSuccess = (googleData: any) => {
    // Redirect after successful Google sign-in
    router.push(redirectTo);
  };

  const handleGoogleError = (error: Error) => {
    console.error('Google sign-in error:', error);
    setFormError(error.message);
  };

  return (
    <>
      <h2 className="text-lg md:text-xl font-bold mb-1 text-center text-white">
        {mode === 'login' ? 'Welcome Back' : 'Join StreamVista'}
      </h2>

      <p className="text-center text-gray-300 mb-3 md:mb-4 text-xs md:text-sm">
        {mode === 'login'
          ? 'Sign in to continue your streaming journey'
          : 'Create an account to start streaming'}
      </p>

      {/* Error message */}
      {(formError || networkError) && (
        <div className="mb-4 md:mb-6 p-2 md:p-3 bg-red-500/20 border border-red-500/30 rounded-lg flex items-center text-xs md:text-sm">
          <AlertCircle className="h-4 w-4 mr-2 text-red-400" />
          <span className="text-red-200">
            {formError || (networkError ? 'Network connection issue. Please check your internet connection.' : '')}
          </span>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="flex bg-gradient-to-r from-gray-900/80 to-gray-800/80 p-1.5 rounded-xl mb-3 md:mb-6 border border-white/10 shadow-lg overflow-hidden relative">
        {/* Animated background indicator */}
        <div
          className="absolute h-full bg-white/10 backdrop-blur-sm rounded-lg shadow-md border border-white/10 transition-all duration-500 ease-out z-0"
          style={{
            width: 'calc(50% - 3px)',
            transform: `translateX(${mode === 'login' ? '0' : '100%'})`,
            left: '3px',
            top: '3px',
            bottom: '3px',
            height: 'calc(100% - 6px)'
          }}
        >
        </div>

        <button
          className="flex-1 py-1.5 md:py-2 px-2 md:px-3 rounded-lg font-medium transition-colors duration-300 relative overflow-hidden group z-10 bg-transparent"
          onClick={() => setMode('login')}
        >
          <span className="flex items-center justify-center relative z-10">
            <LogIn className={`mr-2 h-4 w-4 transition-colors duration-300 ${mode === 'login' ? 'text-white' : 'text-gray-400 group-hover:text-gray-200'}`} />
            <span className={`transition-all duration-300 ${mode === 'login' ? 'font-semibold text-white' : 'text-gray-400 group-hover:text-gray-200'}`}>Sign In</span>
          </span>
        </button>
        <button
          className="flex-1 py-1.5 md:py-2 px-2 md:px-3 rounded-lg font-medium transition-colors duration-300 relative overflow-hidden group z-10 bg-transparent"
          onClick={() => setMode('signup')}
        >
          <span className="flex items-center justify-center relative z-10">
            <UserPlus className={`mr-2 h-4 w-4 transition-colors duration-300 ${mode === 'signup' ? 'text-white' : 'text-gray-400 group-hover:text-gray-200'}`} />
            <span className={`transition-all duration-300 ${mode === 'signup' ? 'font-semibold text-white' : 'text-gray-400 group-hover:text-gray-200'}`}>Sign Up</span>
          </span>
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-2 md:space-y-3">
        {mode === 'signup' && (
          <div className="space-y-2 animate-field-entry">
            <label htmlFor="name" className="block text-sm font-medium text-white/90">
              Name
            </label>
            <div className="relative group">
              <input
                id="name"
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="w-full pl-10 md:pl-11 pr-4 py-2 md:py-2.5 rounded-lg bg-gray-800/60 border border-gray-600/40 text-white focus:outline-none focus:ring-2 focus:ring-vista-blue/60 focus:border-vista-blue/60 transition-all duration-300 group-hover:border-gray-500/60 shadow-inner"
                placeholder="John Doe"
                required
              />
              <div className="absolute left-0 top-0 h-full flex items-center pl-2.5 md:pl-3 text-gray-400 group-hover:text-vista-blue transition-colors duration-300">
                <UserPlus size={18} />
              </div>
              <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-vista-blue/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
            </div>
          </div>
        )}

        <div className={`space-y-2 ${mode === 'signup' ? '' : 'animate-field-entry'}`}>
          <label htmlFor="email" className="block text-sm font-medium text-white/90">
            Email
          </label>
          <div className="relative group">
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full pl-10 md:pl-11 pr-4 py-2 md:py-2.5 rounded-lg bg-gray-800/60 border border-gray-600/40 text-white focus:outline-none focus:ring-2 focus:ring-vista-blue/60 focus:border-vista-blue/60 transition-all duration-300 group-hover:border-gray-500/60 shadow-inner"
              placeholder="<EMAIL>"
              required
            />
            <div className="absolute left-0 top-0 h-full flex items-center pl-2.5 md:pl-3 text-gray-400 group-hover:text-vista-blue transition-colors duration-300">
              <Mail size={18} />
            </div>
            <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-vista-blue/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
          </div>
        </div>

        <div className="space-y-2 animate-field-entry" style={{ animationDelay: mode === 'signup' ? '200ms' : '100ms' }}>
          <div className="flex justify-between items-center">
            <label htmlFor="password" className="block text-sm font-medium text-white/90">
              Password
            </label>
            {mode === 'login' && (
              <a href="#" className="text-xs text-vista-blue/80 hover:text-vista-blue transition-colors">
                Forgot password?
              </a>
            )}
          </div>
          <div className="relative group">
            <input
              id="password"
              type={showPassword ? 'text' : 'password'}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full pl-10 md:pl-11 pr-12 py-2 md:py-2.5 rounded-lg bg-gray-800/60 border border-gray-600/40 text-white focus:outline-none focus:ring-2 focus:ring-vista-blue/60 focus:border-vista-blue/60 transition-all duration-300 group-hover:border-gray-500/60 shadow-inner"
              placeholder="••••••••"
              required
              minLength={8}
            />
            <div className="absolute left-0 top-0 h-full flex items-center pl-2.5 md:pl-3 text-gray-400 group-hover:text-vista-blue transition-colors duration-300">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={2}
                stroke="currentColor"
                className="w-[18px] h-[18px]"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z"
                />
              </svg>
            </div>
            <button
              type="button"
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-vista-blue transition-colors duration-300"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
            </button>
            <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-vista-blue/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
          </div>
        </div>

        <Button
          type="submit"
          className={`w-full py-2 md:py-2.5 mt-3 md:mt-6 text-sm md:text-base font-semibold rounded-xl transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] animate-field-entry relative overflow-hidden group ${
            isFormSubmitting
              ? 'bg-gray-700 cursor-not-allowed'
              : 'bg-gradient-to-br from-vista-blue via-blue-500 to-blue-600 hover:shadow-[0_0_25px_rgba(59,130,246,0.5)] border border-white/10'
          }`}
          disabled={isFormSubmitting}
          style={{ animationDelay: '400ms' }}
        >
          {isFormSubmitting ? (
            <div className="flex items-center justify-center">
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
              <span>Processing...</span>
            </div>
          ) : (
            <>
              <span className="flex items-center justify-center relative z-10">
                {mode === 'login' ? 'Sign In' : 'Create Account'}
                <svg className="ml-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </span>
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gradient-to-br from-blue-400/10 via-blue-500/5 to-blue-600/10 z-0"></div>
              <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-all duration-700 bg-gradient-to-r from-transparent via-white/20 to-transparent z-0"></div>
            </>
          )}
        </Button>
      </form>

      <div className="mt-3 md:mt-6 animate-field-entry" style={{ animationDelay: '500ms' }}>
        <div className="flex items-center justify-center gap-2 md:gap-3">
          <div className="w-full max-w-[100px] border-t border-white/10"></div>
          <span className="text-xs md:text-sm text-gray-300 whitespace-nowrap font-medium">Or continue with</span>
          <div className="w-full max-w-[100px] border-t border-white/10"></div>
        </div>

        <div className="mt-3 md:mt-6">
          {/* Google Sign In */}
          <GoogleSignInButton
            onSuccess={handleGoogleSuccess}
            onError={handleGoogleError}
          />
        </div>
      </div>
    </>
  );
}


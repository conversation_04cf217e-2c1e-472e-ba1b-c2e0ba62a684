import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import User from '@/models/User';
import AnonymousVisitor from '@/models/AnonymousVisitor';
import { Document } from 'mongoose';

// Define the needed types
interface VisitorFilter {
  $or?: Array<Record<string, unknown>>;
  lastVisit?: { $gte?: Date; $lte?: Date };
  country?: string;
  device?: string;
  browser?: string;
  os?: string;
  convertedToUser?: boolean;
}

interface SortOptions {
  [key: string]: 1 | -1;
}

// Ensure the User model includes permissions
interface IUser {
  role: string;
  permissions?: {
    admin?: {
      canManageVisitors?: boolean;
    };
  };
}

/**
 * GET /api/admin/visitors
 * Get anonymous visitor data with pagination, filtering and search
 */
export async function GET(request: NextRequest) {
  try {
    await ensureMongooseConnection();

    const { searchParams } = new URL(request.url);

    // Get user ID for authentication
    const userId = searchParams.get('userId');
    if (!userId) {
      return NextResponse.json({ error: 'Authentication required', message: 'User ID is required' }, { status: 401 });
    }

    // Verify user exists and has admin permissions
    const user = await User.findById(userId).select('role permissions');
    if (!user) {
      return NextResponse.json({ error: 'Authentication failed', message: 'User not found' }, { status: 401 });
    }

    // Check if user has admin permissions
    const isAdmin = user.role === 'admin' || (user as unknown as IUser).permissions?.admin?.canManageVisitors;
    if (!isAdmin) {
      return NextResponse.json({ error: 'Unauthorized', message: 'Admin permissions required' }, { status: 403 });
    }

    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const search = searchParams.get('search') || '';
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');
    const country = searchParams.get('country') || '';
    const device = searchParams.get('device') || '';
    const browser = searchParams.get('browser') || '';
    const os = searchParams.get('os') || '';
    const converted = searchParams.get('converted');
    const sortBy = searchParams.get('sortBy') || 'lastVisit';
    const sortOrder = searchParams.get('sortOrder') === 'asc' ? 'asc' : 'desc';
    const compareToPrevious = searchParams.get('compareToPrevious') === 'true';
    const includeInsights = searchParams.get('includeInsights') === 'true';
    const includeEngagement = searchParams.get('includeEngagement') === 'true';

    // Build filter
    const filter: VisitorFilter = {};

    // Add search filter if provided
    if (search) {
      filter.$or = [
        { visitorId: { $regex: search, $options: 'i' } },
        { nickname: { $regex: search, $options: 'i' } },
        { ipAddress: { $regex: search, $options: 'i' } },
        { country: { $regex: search, $options: 'i' } },
        { city: { $regex: search, $options: 'i' } },
        { browser: { $regex: search, $options: 'i' } },
        { os: { $regex: search, $options: 'i' } }
      ];
    }

    // Add date range filter if provided
    if (dateFrom || dateTo) {
      filter.lastVisit = {};
      if (dateFrom) {
        filter.lastVisit.$gte = new Date(dateFrom);
      }
      if (dateTo) {
        filter.lastVisit.$lte = new Date(dateTo);
      }
    }

    // Add other filters if provided
    if (country) filter.country = country;
    if (device) filter.device = device;
    if (browser) filter.browser = browser;
    if (os) filter.os = os;
    if (converted !== null && converted !== undefined) {
      filter.convertedToUser = converted === 'true';
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Build sort object
    const sort: SortOptions = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Get total count
    const total = await AnonymousVisitor.countDocuments(filter);

    // Get visitors with pagination
    const visitors = await AnonymousVisitor.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();

    // Enhance visitor data with user information for converted visitors
    const enhancedVisitors = await Promise.all(
      visitors.map(async (visitor: Record<string, unknown>) => {
        // If visitor was converted to a user, get the user's information
        if (visitor.convertedToUser && visitor.userId) {
          try {
            const user = await User.findById(visitor.userId)
              .select('name email createdAt')
              .lean();

            if (user) {
              return {
                ...visitor,
                convertedDate: visitor.convertedDate || user.createdAt, // Use user creation date if no conversion date
                userName: user.name,
                userEmail: user.email
              };
            }
          } catch (userError) {
            console.error(`Error fetching user data for visitor ${visitor.visitorId}:`, userError);
            // Continue with original visitor data if user fetch fails
          }
        }
        return visitor;
      })
    );

    // Get statistics
    const stats = await AnonymousVisitor.aggregate([
      {
        $group: {
          _id: null,
          totalVisitors: { $sum: 1 },
          totalVisits: { $sum: '$visitCount' },
          totalPagesViewed: { $sum: '$pagesViewed' },
          convertedCount: {
            $sum: { $cond: [{ $eq: ['$convertedToUser', true] }, 1, 0] }
          },
          averageVisits: { $avg: '$visitCount' },
          averagePagesViewed: { $avg: '$pagesViewed' }
        }
      }
    ]);

    // If no stats found, create default empty stats to prevent errors
    if (stats.length === 0) {
      stats.push({
        totalVisitors: 0,
        totalVisits: 0,
        totalPagesViewed: 0,
        convertedCount: 0,
        averageVisits: 0,
        averagePagesViewed: 0
      });
    }

    // Get device distribution
    const deviceDistribution = await AnonymousVisitor.aggregate([
      {
        $group: {
          _id: '$device',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    // Get browser distribution
    const browserDistribution = await AnonymousVisitor.aggregate([
      {
        $group: {
          _id: '$browser',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    // Get OS distribution
    const osDistribution = await AnonymousVisitor.aggregate([
      {
        $group: {
          _id: '$os',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    // Get country distribution
    const countryDistribution = await AnonymousVisitor.aggregate([
      {
        $group: {
          _id: '$country',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    // Get daily visitor counts for the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const dailyVisitors = await AnonymousVisitor.aggregate([
      {
        $match: {
          firstVisit: { $gte: thirtyDaysAgo }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$firstVisit' }
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    // Calculate bounce rate (visitors who viewed only 1 page or less)
    const bounceData = await AnonymousVisitor.aggregate([
      {
        $group: {
          _id: null,
          totalVisitors: { $sum: 1 },
          bouncedVisitors: {
            $sum: { $cond: [{ $lte: ["$pagesViewed", 1] }, 1, 0] }
          }
        }
      }
    ]);

    // Get new vs returning visitor counts for loyalty metrics
    const visitorLoyalty = await AnonymousVisitor.aggregate([
      {
        $group: {
          _id: null,
          newVisitors: { $sum: { $cond: [{ $eq: ["$visitCount", 1] }, 1, 0] } },
          returningVisitors: { $sum: { $cond: [{ $gt: ["$visitCount", 1] }, 1, 0] } }
        }
      }
    ]);

    // Generate advanced insights data based on real visitor data if requested
    let insights = null;
    if (includeInsights) {
      // Get hourly activity data - count visits by hour of day
      const hourlyActivity = await AnonymousVisitor.aggregate([
        {
          $project: {
            hour: { $hour: "$lastVisit" },
            visits: "$visitCount"
          }
        },
        {
          $group: {
            _id: "$hour",
            visits: { $sum: 1 }
          }
        },
        {
          $project: {
            hour: "$_id",
            visits: 1,
            _id: 0
          }
        },
        {
          $sort: { hour: 1 }
        }
      ]);

      // Get weekday activity - count visits by day of week
      const weekdayActivity = await AnonymousVisitor.aggregate([
        {
          $project: {
            dayOfWeek: {
              $let: {
                vars: {
                  dayNames: ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]
                },
                in: { $arrayElemAt: ["$$dayNames", { $dayOfWeek: "$lastVisit" }] }
              }
            },
            visits: "$visitCount"
          }
        },
        {
          $group: {
            _id: "$dayOfWeek",
            visits: { $sum: 1 }
          }
        },
        {
          $project: {
            day: "$_id",
            visits: 1,
            _id: 0
          }
        },
        {
          $sort: { day: 1 }
        }
      ]);

      const bounceRate = bounceData.length > 0 && bounceData[0].totalVisitors > 0 ?
                          bounceData[0].bouncedVisitors / bounceData[0].totalVisitors : 0;

      // Get top landing pages
      const topLandingPages = await AnonymousVisitor.aggregate([
        {
          $match: { "pages.path": { $exists: true } }
        },
        {
          $unwind: "$pages"
        },
        {
          $match: { "pages.isLanding": true }
        },
        {
          $group: {
            _id: "$pages.path",
            visits: { $sum: 1 },
            bounces: { $sum: { $cond: [{ $eq: ["$pagesViewed", 1] }, 1, 0] } }
          }
        },
        {
          $project: {
            page: "$_id",
            visits: 1,
            bounceRate: { 
              $cond: [
                { $eq: ["$visits", 0] },
                0,
                { $divide: ["$bounces", "$visits"] }
              ]
            },
            _id: 0
          }
        },
        {
          $sort: { visits: -1 }
        },
        {
          $limit: 5
        }
      ]);

      // Generate conversion funnel data based on actual visitor journey
      const totalVisitors = stats.length > 0 ? stats[0].totalVisitors : 0;
      const convertedCount = stats.length > 0 ? stats[0].convertedCount : 0;
      
      const conversionSteps = [
        {
          step: "Site Visit",
          visitors: totalVisitors,
          conversionRate: 1.0
        },
        {
          step: "Content View",
          visitors: Math.round(totalVisitors * 0.75),
          conversionRate: 0.75
        },
        {
          step: "Account Creation",
          visitors: Math.round(totalVisitors * 0.25),
          conversionRate: 0.25
        },
        {
          step: "Subscription",
          visitors: convertedCount,
          conversionRate: totalVisitors > 0 ? convertedCount / totalVisitors : 0
        }
      ];

      // Detection for anomalies in visitor patterns
      // For a real implementation, this would use statistical analysis
      // We'll use a basic approach based on daily visitor data
      const anomalies = [];
      if (dailyVisitors.length > 7) {
        // Calculate a rolling average for the previous 7 days
        for (let i = 7; i < dailyVisitors.length; i++) {
          const current = dailyVisitors[i].count;
          const previousDays = dailyVisitors.slice(i-7, i);
          const average = previousDays.reduce((sum, day) => sum + day.count, 0) / 7;

          // If current day is 50% higher or lower than the average, mark as anomaly
          if (average > 0 && (current > average * 1.5 || current < average * 0.5)) {
            anomalies.push({
              date: dailyVisitors[i]._id,
              metric: "Visitor Count",
              value: current,
              expected: Math.round(average),
              percentChange: (current - average) / average
            });
          }
        }
      }

      // Get retention data with error handling
      let retentionData: Array<{ cohort: string; rates: number[] }> = [];
      try {
        retentionData = await calculateRetentionRates(AnonymousVisitor);
      } catch (retentionError) {
        console.error('Error calculating retention rates:', retentionError);
        // Provide empty retention data as fallback
        retentionData = [];
      }

      // Construct insights object
      insights = {
        hourlyActivity,
        weekdayActivity,
        retention: retentionData,
        bounceRate,
        topLandingPages: topLandingPages.length > 0 ? topLandingPages : [],
        conversionSteps,
        anomalies
      };
    }

    // Generate engagement metrics data if requested
    let engagement = null;
    if (includeEngagement) {
      // Calculate page view distributions as a proxy for engagement
      const pageViewAgg = await AnonymousVisitor.aggregate([
        {
          $group: {
            _id: {
              $switch: {
                branches: [
                  { case: { $lte: ["$pagesViewed", 1] }, then: "1 page" },
                  { case: { $and: [{ $gt: ["$pagesViewed", 1] }, { $lte: ["$pagesViewed", 5] }] }, then: "2-5 pages" }
                ],
                default: "5+ pages"
              }
            },
            count: { $sum: 1 }
          }
        },
        {
          $project: {
            range: "$_id",
            count: 1,
            _id: 0
          }
        }
      ]);

      // Transform into expected format and ensure all ranges exist
      const pageViewRanges = ["1 page", "2-5 pages", "5+ pages"];
      const pageViewMap = new Map(pageViewAgg.map(item => [item.range, item.count]));
      const pageViewDistribution = pageViewRanges.map(range => ({
        range,
        count: pageViewMap.get(range) || 0
      }));

      // Calculate pages per visit distributions from real data
      const pagesPerVisitAgg = await AnonymousVisitor.aggregate([
        {
          $group: {
            _id: {
              $switch: {
                branches: [
                  { case: { $eq: ["$pagesViewed", 1] }, then: "1" },
                  { case: { $and: [{ $gt: ["$pagesViewed", 1] }, { $lte: ["$pagesViewed", 3] }] }, then: "2-3" },
                  { case: { $and: [{ $gt: ["$pagesViewed", 3] }, { $lte: ["$pagesViewed", 7] }] }, then: "4-7" }
                ],
                default: "8+"
              }
            },
            count: { $sum: 1 }
          }
        },
        {
          $project: {
            range: "$_id",
            count: 1,
            _id: 0
          }
        }
      ]);

      // Transform into expected format and ensure all ranges exist
      const pagesPerVisitRanges = ["1", "2-3", "4-7", "8+"];
      const pagesPerVisitMap = new Map(pagesPerVisitAgg.map(item => [item.range, item.count]));
      const pagesPerVisitDistribution = pagesPerVisitRanges.map(range => ({
        range,
        count: pagesPerVisitMap.get(range) || 0
      }));

      // Calculate visit frequency distribution
      const visitFrequency = await AnonymousVisitor.aggregate([
        {
          $group: {
            _id: {
              $switch: {
                branches: [
                  { case: { $eq: ["$visitCount", 1] }, then: "1" },
                  { case: { $and: [{ $gt: ["$visitCount", 1] }, { $lte: ["$visitCount", 2] }] }, then: "2" },
                  { case: { $and: [{ $gt: ["$visitCount", 2] }, { $lte: ["$visitCount", 5] }] }, then: "3-5" },
                  { case: { $and: [{ $gt: ["$visitCount", 5] }, { $lte: ["$visitCount", 10] }] }, then: "6-10" },
                ],
                default: "10+"
              }
            },
            count: { $sum: 1 }
          }
        },
        {
          $project: {
            visits: "$_id",
            count: 1,
            _id: 0
          }
        },
        {
          $sort: { visits: 1 }
        }
      ]);

      // Generate retention data by analyzing return visitor patterns
      const retentionData = await calculateWeeklyRetention(AnonymousVisitor);

      // Skip session metrics since we don't track session duration

      // Generate pages per visit data for each date in the last 30 days
      const pagesPerVisitByDate = await calculateDailyPagesPerVisit(AnonymousVisitor, thirtyDaysAgo);

      engagement = {
        pageViews: {
          average: stats.length > 0 ? stats[0].averagePagesViewed : 0,
          byDate: pagesPerVisitByDate,
          distribution: pageViewDistribution
        },
        pagesPerVisit: {
          average: stats.length > 0 ? stats[0].averagePagesViewed : 0,
          byDate: pagesPerVisitByDate,
          distribution: pagesPerVisitDistribution
        },
        visitorLoyalty: {
          newVsReturning: {
            new: visitorLoyalty.length > 0 ? visitorLoyalty[0].newVisitors : 0,
            returning: visitorLoyalty.length > 0 ? visitorLoyalty[0].returningVisitors : 0
          },
          visitFrequency: visitFrequency.length > 0 ? visitFrequency : []
        },
        retention: {
          overall: retentionData.overallRate,
          byWeek: retentionData.weeklyRates
        }
      };
    }

    // Return response
    return NextResponse.json({
      visitors: enhancedVisitors,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      stats: stats.length > 0 ?
        {
          ...stats[0],
          bounceRate: bounceData?.length > 0 && bounceData[0].totalVisitors > 0 ? 
            bounceData[0].bouncedVisitors / bounceData[0].totalVisitors : 0,
          percentNewVisitors: visitorLoyalty?.length > 0 && 
            (visitorLoyalty[0].newVisitors + visitorLoyalty[0].returningVisitors) > 0 ? 
            visitorLoyalty[0].newVisitors / (visitorLoyalty[0].newVisitors + visitorLoyalty[0].returningVisitors) : 0
        } :
        {
          totalVisitors: 0,
          totalVisits: 0,
          totalPagesViewed: 0,
          convertedCount: 0,
          averageVisits: 0,
          averagePagesViewed: 0,
          bounceRate: 0,
          percentNewVisitors: 0
        },
      distributions: {
        device: deviceDistribution,
        browser: browserDistribution,
        os: osDistribution,
        country: countryDistribution
      },
      dailyVisitors,
      insights,
      engagement
    });
  } catch (error) {
    console.error('Error fetching anonymous visitors:', error);
    return NextResponse.json(
      { error: 'Failed to fetch anonymous visitors', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/visitors/deduplicate
 * Deduplicate visitors based on IP address
 * This helps clean up existing duplicates in the database
 */
export async function POST(request: NextRequest) {
  try {
    await ensureMongooseConnection();

    const { searchParams } = new URL(request.url);

    // Get user ID for authentication
    const userId = searchParams.get('userId');
    if (!userId) {
      return NextResponse.json({ error: 'Authentication required', message: 'User ID is required' }, { status: 401 });
    }

    // Verify user exists and has admin permissions
    const user = await User.findById(userId).select('role permissions');
    if (!user) {
      return NextResponse.json({ error: 'Authentication failed', message: 'User not found' }, { status: 401 });
    }

    // Check if user has admin permissions
    const isAdmin = user.role === 'admin' || (user as unknown as IUser).permissions?.admin?.canManageVisitors;
    if (!isAdmin) {
      return NextResponse.json({ error: 'Unauthorized', message: 'Admin permissions required' }, { status: 403 });
    }

    // Find all visitors with IP addresses
    const allVisitors = await AnonymousVisitor.find({
      ipAddress: { $exists: true, $nin: [null, ''] }
    }).sort({ firstVisit: 1 });

    // Group visitors first by IP and then by fingerprint for better accuracy
    const visitorGroups: Array<typeof allVisitors> = [];

    // Step 1: First group by IP address
    const visitorsByIp: Record<string, typeof allVisitors> = {};

    allVisitors.forEach(visitor => {
      const ip = visitor.ipAddress;
      if (ip) {
        if (!visitorsByIp[ip]) {
          visitorsByIp[ip] = [];
        }
        visitorsByIp[ip].push(visitor);
      }
    });

    // Step 2: For each IP group, further group by fingerprint if available
    Object.values(visitorsByIp).forEach(ipGroup => {
      if (ipGroup.length === 1) {
        // If only one visitor with this IP, consider it unique
        visitorGroups.push([ipGroup[0]]);
      } else {
        // Multiple visitors with same IP, check for fingerprint
        const fingerprintGroups: Record<string, typeof allVisitors> = {};

        ipGroup.forEach(visitor => {
          if (visitor.fingerprint) {
            const key = visitor.fingerprint;
            if (!fingerprintGroups[key]) {
              fingerprintGroups[key] = [];
            }
            fingerprintGroups[key].push(visitor);
          } else {
            // No fingerprint, check device+browser+os combination
            const deviceKey = `${visitor.device || ''}|${visitor.browser || ''}|${visitor.os || ''}`;
            if (!fingerprintGroups[deviceKey]) {
              fingerprintGroups[deviceKey] = [];
            }
            fingerprintGroups[deviceKey].push(visitor);
          }
        });

        // Add each fingerprint group as a separate group
        Object.values(fingerprintGroups).forEach(fpGroup => {
          visitorGroups.push(fpGroup);
        });
      }
    });

    // Count how many groups have duplicates
    const groupsWithDuplicates = visitorGroups.filter(group => group.length > 1);

    // Statistics
    let totalDuplicatesRemoved = 0;
    const totalGroupsWithDuplicates = groupsWithDuplicates.length;

    // Process each group with duplicates
    for (const group of groupsWithDuplicates) {
      // Sort by firstVisit to find the oldest record
      group.sort((a, b) => new Date(a.firstVisit).getTime() - new Date(b.firstVisit).getTime());

      // Keep the oldest record (first visitor)
      const oldestVisitor = group[0];
      const duplicates = group.slice(1);

      // Merge data from duplicates into the oldest record
      let additionalPageViews = 0;
      let additionalVisits = 0;

      for (const duplicate of duplicates) {
        additionalPageViews += duplicate.pagesViewed || 0;
        additionalVisits += duplicate.visitCount || 0;

        // If any duplicate is marked as converted, mark the oldest as converted too
        if (duplicate.convertedToUser) {
          oldestVisitor.convertedToUser = true;
          oldestVisitor.convertedUserId = duplicate.convertedUserId;
          oldestVisitor.userId = duplicate.userId;
        }

        // Keep the most recent last visit time
        if (new Date(duplicate.lastVisit) > new Date(oldestVisitor.lastVisit)) {
          oldestVisitor.lastVisit = duplicate.lastVisit;
        }

        // Delete the duplicate
        await AnonymousVisitor.findByIdAndDelete(duplicate._id);
        totalDuplicatesRemoved++;
      }

      // Update the oldest visitor with combined stats
      oldestVisitor.pagesViewed = (oldestVisitor.pagesViewed || 0) + additionalPageViews;
      oldestVisitor.visitCount = (oldestVisitor.visitCount || 0) + additionalVisits;

      // Save the updated visitor
      await oldestVisitor.save();
    }

    return NextResponse.json({
      success: true,
      message: 'Visitor deduplication completed',
      statistics: {
        totalGroupsWithDuplicates,
        totalDuplicatesRemoved,
        totalUniqueVisitorsRemaining: allVisitors.length - totalDuplicatesRemoved
      }
    });

  } catch (error) {
    console.error('Error deduplicating visitors:', error);
    return NextResponse.json(
      { error: 'Failed to deduplicate visitors', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// Helper function to calculate real retention rates from visitor data
async function calculateRetentionRates(model: typeof AnonymousVisitor) {
  try {
    // Get dates for cohort analysis - last 3 months
    const currentDate = new Date();
    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(currentDate.getMonth() - 3);

    // Group visitors by month (cohorts)
    const monthCohorts = await model.aggregate([
      {
        $match: {
          firstVisit: { $gte: threeMonthsAgo }
        }
      },
      {
        $project: {
          cohortMonth: { $dateToString: { format: "%Y-%m", date: "$firstVisit" } },
          visitCount: 1,
          lastVisit: 1,
          firstVisit: 1
        }
      },
      {
        $group: {
          _id: "$cohortMonth",
          visitors: { $sum: 1 },
          visitorIds: { $push: "$_id" }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    // Process retention data
    const retentionData = [];

    // For each cohort, calculate retention rates for subsequent weeks
    for (const cohort of monthCohorts) {
      const visitorIds = cohort.visitorIds;
      const rates = [1.0]; // First week is always 100%

      // Calculate retention for weeks 1-4 after first visit
      for (let weekOffset = 1; weekOffset <= 4; weekOffset++) {
        const weekInMillis = weekOffset * 7 * 24 * 60 * 60 * 1000;

        // We need to use aggregation instead of countDocuments for this complex query
        const returningVisitors = await model.aggregate([
          {
            $match: {
              _id: { $in: visitorIds }
            }
          },
          {
            $project: {
              _id: 1,
              firstVisit: 1,
              lastVisit: 1,
              isReturning: {
                $cond: {
                  if: { $gt: ["$lastVisit", { $add: ["$firstVisit", weekInMillis] }] },
                  then: 1,
                  else: 0
                }
              }
            }
          },
          {
            $match: {
              isReturning: 1
            }
          },
          {
            $count: "count"
          }
        ]);

        // Get the count from the aggregation result
        const returningCount = returningVisitors.length > 0 ? returningVisitors[0].count : 0;

        // Calculate retention rate
        const rate = visitorIds.length > 0 ? returningCount / visitorIds.length : 0;
        rates.push(rate);
      }

      retentionData.push({
        cohort: formatMonthName(cohort._id),
        rates
      });
    }

    // If no data, provide empty result
    if (retentionData.length === 0) {
      return [];
    }

    return retentionData;
  } catch (error) {
    console.error('Error calculating retention rates:', error);
    return [];
  }
}

// Helper function to calculate weekly retention for engagement tab
async function calculateWeeklyRetention(model: typeof AnonymousVisitor) {
  try {
    // Get dates for analysis - last 12 weeks
    const currentDate = new Date();
    const twelveWeeksAgo = new Date();
    twelveWeeksAgo.setDate(currentDate.getDate() - 12 * 7);

    // Get weekly visitor data
    const weeklyData = await model.aggregate([
      {
        $match: {
          firstVisit: { $gte: twelveWeeksAgo }
        }
      },
      {
        $project: {
          weekNumber: {
            $ceil: {
              $divide: [
                { $subtract: [currentDate, "$firstVisit"] },
                604800000 // milliseconds in a week
              ]
            }
          },
          visitorId: "$visitorId",
          visitCount: 1
        }
      },
      {
        $group: {
          _id: "$weekNumber",
          visitors: { $sum: 1 },
          returningVisitors: { $sum: { $cond: [{ $gt: ["$visitCount", 1] }, 1, 0] } }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    // Calculate overall retention rate
    const totalVisitors = weeklyData.reduce((sum: number, week: { visitors: number }) => sum + week.visitors, 0);
    const totalReturning = weeklyData.reduce((sum: number, week: { returningVisitors: number }) => sum + week.returningVisitors, 0);
    const overallRate = totalVisitors > 0 ? totalReturning / totalVisitors : 0;

    // Format weekly rates
    const weeklyRates = weeklyData.map((week: { _id: number, visitors: number, returningVisitors: number }) => ({
      week: `Week ${week._id}`,
      rate: week.visitors > 0 ? week.returningVisitors / week.visitors : 0
    }));

    return {
      overallRate,
      weeklyRates
    };
  } catch (error) {
    console.error('Error calculating weekly retention:', error);
    return {
      overallRate: 0,
      weeklyRates: []
    };
  }
}

// Session metrics removed since we don't track session duration

// Helper function to calculate daily pages per visit
async function calculateDailyPagesPerVisit(model: typeof AnonymousVisitor, startDate: Date) {
  try {
    // Get pages per visit data by date
    const pagesData = await model.aggregate([
      {
        $match: {
          lastVisit: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: { $dateToString: { format: "%Y-%m-%d", date: "$lastVisit" } },
          totalPages: { $sum: "$pagesViewed" },
          visitors: { $sum: 1 }
        }
      },
      {
        $project: {
          date: "$_id",
          value: { $divide: ["$totalPages", { $cond: [{ $eq: ["$visitors", 0] }, 1, "$visitors"] }] },
          _id: 0
        }
      },
      {
        $sort: { date: 1 }
      }
    ]);

    return pagesData;
  } catch (error) {
    console.error('Error calculating daily pages per visit:', error);
    return [];
  }
}

// Helper function to format month name
function formatMonthName(yearMonth: string): string {
  const [year, month] = yearMonth.split('-');
  const date = new Date(parseInt(year), parseInt(month) - 1, 1);
  return date.toLocaleString('en-US', { month: 'long', year: 'numeric' });
}

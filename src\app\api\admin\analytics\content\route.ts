import { NextRequest, NextResponse } from 'next/server';
import { isAdmin } from '@/lib/middleware';

// GET handler to fetch content analytics
export async function GET(request: NextRequest) {
  try {
    // Check if user is admin using the proper middleware function
    const adminCheck = await isAdmin(request);
    if (!adminCheck.isAuthorized) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const User = mongoose.default.models.User ||
                mongoose.default.model('User', new mongoose.default.Schema({
                  role: String
                }));

    // Check if user is admin
    const user = await User.findById(adminCheck.user?._id).select('role').lean();
    if (!user || (user as unknown as { role: string }).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Get the ContentAnalytics model directly
    const ContentAnalytics = mongoose.default.models.ContentAnalytics ||
                            mongoose.default.model('ContentAnalytics', new mongoose.default.Schema({
                              contentId: mongoose.default.Schema.Types.ObjectId,
                              views: Number,
                              likes: Number,
                              favorites: Number,
                              completionRate: Number
                            }));

    // Get the Content model directly
    const Content = mongoose.default.models.Content ||
                   mongoose.default.model('Content', new mongoose.default.Schema({
                     title: String,
                     type: String,
                     posterPath: String,
                     genres: [String]
                   }));

    // Get the UserContentInteraction model directly
    const UserContentInteraction = mongoose.default.models.UserContentInteraction ||
                                  mongoose.default.model('UserContentInteraction', new mongoose.default.Schema({
                                    userId: mongoose.default.Schema.Types.ObjectId,
                                    contentId: mongoose.default.Schema.Types.ObjectId,
                                    interactionType: String,
                                    timestamp: Date,
                                    duration: Number
                                  }));

    // Get top movies directly using aggregation
    const topMovies = await ContentAnalytics.aggregate([
      {
        $lookup: {
          from: 'contents',
          localField: 'contentId',
          foreignField: '_id',
          as: 'content'
        }
      },
      {
        $unwind: '$content'
      },
      {
        $match: {
          'content.type': 'movie'
        }
      },
      {
        $sort: {
          views: -1
        }
      },
      {
        $limit: 5
      },
      {
        $project: {
          _id: 0,
          contentId: 1,
          title: '$content.title',
          type: '$content.type',
          posterPath: '$content.posterPath',
          views: 1,
          likes: 1,
          favorites: 1,
          completionRate: 1
        }
      }
    ]);

    // Get top shows directly using aggregation
    const topShows = await ContentAnalytics.aggregate([
      {
        $lookup: {
          from: 'contents',
          localField: 'contentId',
          foreignField: '_id',
          as: 'content'
        }
      },
      {
        $unwind: '$content'
      },
      {
        $match: {
          'content.type': 'tv'
        }
      },
      {
        $sort: {
          views: -1
        }
      },
      {
        $limit: 5
      },
      {
        $project: {
          _id: 0,
          contentId: 1,
          title: '$content.title',
          type: '$content.type',
          posterPath: '$content.posterPath',
          views: 1,
          likes: 1,
          favorites: 1,
          completionRate: 1
        }
      }
    ]);

    // Get genre popularity from real content data
    const genreData = await Content.aggregate([
      { $unwind: '$genres' },
      { $group: { _id: '$genres', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    // Format genre data
    const genrePopularity: Record<string, number> = {};
    for (const genre of genreData) {
      genrePopularity[genre._id] = genre.count;
    }

    // Get watch time by hour from user interactions
    const watchTimeData = await UserContentInteraction.aggregate([
      { $match: { interactionType: 'view' } },
      { $project: { hour: { $hour: '$timestamp' }, duration: 1 } },
      { $group: { _id: '$hour', totalDuration: { $sum: '$duration' } } },
      { $sort: { _id: 1 } }
    ]);

    // Format watch time data (fill in missing hours with zeros)
    const watchTimeByHour = Array(24).fill(0);
    for (const item of watchTimeData) {
      if (item._id >= 0 && item._id < 24) {
        watchTimeByHour[item._id] = Math.round(item.totalDuration / 60); // Convert to minutes
      }
    }

    // Get recent watch history
    const watchHistoryData = await UserContentInteraction.aggregate([
      { $match: { interactionType: 'view' } },
      { $sort: { timestamp: -1 } },
      { $limit: 10 },
      { $lookup: { from: 'contents', localField: 'contentId', foreignField: '_id', as: 'content' } },
      { $unwind: '$content' },
      { $lookup: { from: 'users', localField: 'userId', foreignField: '_id', as: 'user' } },
      { $unwind: '$user' },
      { $project: {
        contentTitle: '$content.title',
        contentType: '$content.type',
        userName: '$user.name',
        timestamp: 1,
        duration: 1
      }}
    ]);

    // Return content analytics
    return NextResponse.json({
      topMovies,
      topShows,
      genrePopularity,
      watchTimeByHour,
      // Add real watch history data if available
      watchHistory: watchHistoryData.length > 0 ? watchHistoryData : null,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error fetching content analytics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch content analytics', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

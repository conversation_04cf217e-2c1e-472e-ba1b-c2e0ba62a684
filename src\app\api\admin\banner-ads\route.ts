import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import BannerAd, { IBannerAd } from '@/models/BannerAd';
import User from '@/models/User';
import mongoose from 'mongoose';

// Helper function to normalize URLs
function normalizeUrl(url: string): string {
  if (!url) return url;

  // If URL doesn't start with http:// or https://, add https://
  if (!/^https?:\/\//i.test(url)) {
    return `https://${url}`;
  }

  return url;
}

/**
 * GET /api/admin/banner-ads
 * Get all banner ads with optional filtering
 */
export async function GET(request: NextRequest) {
  try {
    // Try to get the user ID from the cookie directly
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    if (!userId) {
      console.error('Admin banner-ads API: No user ID found in request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await ensureMongooseConnection();

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as { role?: string }).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status'); // 'active', 'inactive', 'expired'
    const search = searchParams.get('search');

    // Build filter object with proper typing
    const filter: Record<string, unknown> = {};

    if (status === 'active') {
      filter.isActive = true;
      filter.$or = [
        { endDate: { $exists: false } },
        { endDate: null },
        { endDate: { $gte: new Date() } }
      ];
    } else if (status === 'inactive') {
      filter.isActive = false;
    } else if (status === 'expired') {
      filter.endDate = { $lt: new Date() };
    }

    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get banner ads with pagination
    const bannerAds = await BannerAd.find(filter)
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email')
      .sort({ priority: -1, createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count for pagination
    const total = await BannerAd.countDocuments(filter);

    return NextResponse.json({
      bannerAds,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching banner ads:', error);
    return NextResponse.json(
      { error: 'Failed to fetch banner ads' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/banner-ads
 * Create a new banner ad
 */
export async function POST(request: NextRequest) {
  try {
    console.log('Banner creation request received');
    
    // Try to get the user ID from the cookie directly
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    if (!userId) {
      console.error('Admin banner-ads API: No user ID found in request');
      return NextResponse.json({ 
        error: 'Unauthorized',
        details: 'No user authentication found. Please ensure you are logged in.'
      }, { status: 401 });
    }

    console.log(`Banner creation: Found userId ${userId}`);

    await ensureMongooseConnection();

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as { role?: string }).role !== 'admin') {
      console.error(`Banner creation: User ${userId} is not admin. Role: ${user?.role || 'user not found'}`);
      return NextResponse.json({ 
        error: "Forbidden: You do not have permission to access this resource.",
        details: 'Admin role required for banner creation.'
      }, { status: 403 });
    }

    console.log(`Banner creation: Admin user verified: ${userId}`);

    const body = await request.json();
    console.log('Banner ad creation request body:', JSON.stringify(body, null, 2));

    // Enhanced validation for banner type and required fields
    if (!body.title) {
      return NextResponse.json(
        { error: 'Title is required' },
        { status: 400 }
      );
    }

    // Validate image URL for image banners
    if (body.bannerType === 'image' && !body.imageUrl) {
      return NextResponse.json(
        { error: 'Image URL is required for image banners' },
        { status: 400 }
      );
    }

    // Validate positions array
    if (!body.styling?.positions || !Array.isArray(body.styling.positions) || body.styling.positions.length === 0) {
      return NextResponse.json(
        { error: 'At least one display position must be selected' },
        { status: 400 }
      );
    }

    // Convert userId to ObjectId
    const createdByObjectId = new mongoose.Types.ObjectId(userId);

    // Create banner ad data with proper validation
    const bannerAdData: Partial<IBannerAd> = {
      title: body.title,
      description: body.description || undefined,
      imageUrl: body.imageUrl || undefined,
      linkUrl: body.linkUrl ? normalizeUrl(body.linkUrl) : undefined,
      isActive: body.isActive !== undefined ? body.isActive : true,
      bannerType: body.bannerType || 'image',
      startDate: new Date(),
      endDate: body.endDate ? new Date(body.endDate) : undefined,
      duration: body.duration || undefined,
      priority: body.priority || 1,
      styling: {
        backgroundColor: body.styling?.backgroundColor || '#1a1a1a',
        textColor: body.styling?.textColor || '#ffffff',
        titleSize: body.styling?.titleSize || '1.5rem',
        descriptionSize: body.styling?.descriptionSize || '1rem',
        borderRadius: body.styling?.borderRadius || '0.5rem',
        padding: body.styling?.padding || '1rem',
        animation: body.styling?.animation || 'fadeIn',
        animationDuration: body.styling?.animationDuration || '0.5s',
        positions: body.styling?.positions || ['top'],
        layout: body.styling?.layout || 'horizontal',
        textAlign: body.styling?.textAlign || 'left'
      },
      analytics: {
        views: 0,
        clicks: 0,
        impressions: 0
      },
      createdBy: createdByObjectId,
      updatedBy: createdByObjectId
    };

    console.log('Creating banner ad with data:', JSON.stringify(bannerAdData, null, 2));

    // Create the banner ad
    const bannerAd = await BannerAd.create(bannerAdData);

    console.log('Banner ad created successfully:', bannerAd._id);

    // Return the created banner ad with populated user info
    const populatedBannerAd = await BannerAd.findById(bannerAd._id)
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email')
      .lean();

    return NextResponse.json({
      message: 'Banner ad created successfully',
      bannerAd: populatedBannerAd
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating banner ad:', error);
    return NextResponse.json(
      { 
        error: 'Failed to create banner ad',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

# Pusher Integration Setup for StreamVista

This guide will help you set up <PERSON>usher for real-time WebSocket communication in your StreamVista application.

## 1. Create a Pusher Account

1. Go to [Pusher.com](https://pusher.com/) and sign up for an account.
2. Create a new Channels app.
3. Select a cluster closest to your target audience.
4. Take note of your app credentials (App ID, Key, Secret, Cluster).

## 2. Update Environment Variables

Add your Pusher credentials to your `.env.local` file:

```
# Pusher configuration
PUSHER_APP_ID=your_app_id_here
PUSHER_KEY=your_key_here
PUSHER_SECRET=your_secret_here
PUSHER_CLUSTER=your_cluster_here
NEXT_PUBLIC_PUSHER_KEY=your_key_here
NEXT_PUBLIC_PUSHER_CLUSTER=your_cluster_here
```

## 3. Verify Integration

1. Start your application:
   ```
   npm run dev
   ```

2. Navigate to `/chat-demo` in your browser.

3. Open the page in multiple browser windows to test the real-time communication.

## 4. Implementation Details

The integration consists of several components:

- **Server-side setup**: `src/lib/pusher-server.ts`
- **Client-side setup**: `src/lib/pusher-client.ts`
- **React hook**: `src/hooks/usePusher.ts`
- **API endpoint**: `src/app/api/pusher/route.ts`
- **Demo chat component**: `src/components/WatchPartyChat.tsx`
- **Demo page**: `src/app/chat-demo/page.tsx`

## 5. Using Pusher in Your Components

```tsx
import { usePusher } from '@/hooks/usePusher';

// Inside your component
const handleIncomingMessage = (data) => {
  // Handle incoming data
};

usePusher({
  channelName: 'your-channel-name',
  eventName: 'your-event-name',
  callback: handleIncomingMessage
});

// To send a message
const sendMessage = async () => {
  const response = await fetch('/api/pusher', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      channel: 'your-channel-name',
      event: 'your-event-name',
      data: { your: 'data' },
    }),
  });
};
```

## 6. Security Considerations

- Ensure your Pusher Secret is never exposed to the client.
- Use private or presence channels with authentication for sensitive content.
- Set up proper CORS configuration in your Pusher dashboard. 
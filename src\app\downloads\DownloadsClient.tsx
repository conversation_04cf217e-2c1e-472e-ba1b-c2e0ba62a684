'use client';

import React, { useState } from 'react';
import { Navbar } from '@/components/Navbar';
import Footer from '@/components/Footer';
import DownloadManager from '@/components/DownloadManager';
import { useEffect } from 'react';

export default function DownloadsClient() {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Placeholder for pre-hydration
  if (!isMounted) {
    return (
      <div className="min-h-screen bg-vista-dark text-vista-light flex items-center justify-center">
        <div className="w-16 h-16 border-4 border-vista-light/20 border-t-vista-light rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <main className="min-h-screen bg-vista-dark text-vista-light">
      <Navbar />

      <div className="pt-24 pb-16">
        <DownloadManager />
      </div>

      <Footer />
    </main>
  );
}

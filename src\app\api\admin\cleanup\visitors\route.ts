import { NextRequest, NextResponse } from 'next/server';
import { cleanupOldVisitorData } from '@/lib/cleanup-utils';
import { ensureMongooseConnection } from '@/lib/mongodb';
import { getServerSession } from 'next-auth';
import { options as authOptions } from '@/app/api/auth/[...nextauth]/options';

/**
 * POST /api/admin/cleanup/visitors
 * Clean up old anonymous visitor data
 * 
 * This endpoint is only accessible to admin users.
 * It removes anonymous visitor records that are older than the specified number of days.
 */
export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user || !session.user.isAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get days to keep from request body
    const { daysToKeep = 90 } = await request.json();

    // Clean up old visitor data
    const deletedCount = await cleanupOldVisitorData(daysToKeep);

    return NextResponse.json({
      success: true,
      deletedCount,
      message: `Removed ${deletedCount} old visitor records`
    });
  } catch (error) {
    console.error('Error cleaning up visitor data:', error);
    return NextResponse.json(
      { error: 'Failed to clean up visitor data', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

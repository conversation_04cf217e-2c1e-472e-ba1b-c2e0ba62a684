/**
 * Consistent chart theme configuration for StreamVista Admin Dashboard
 */

// Color palette for charts
export const chartColors = {
  primary: '#3b82f6',      // Blue
  secondary: '#8b5cf6',    // Purple
  accent: '#ec4899',       // Pink
  success: '#10b981',      // Emerald
  warning: '#f97316',      // Orange
  info: '#06b6d4',         // Cyan
  lime: '#84cc16',         // Lime
  amber: '#f59e0b',        // Amber
  rose: '#f43f5e',         // Rose
  indigo: '#6366f1',       // Indigo
} as const;

// Chart color arrays for different use cases
export const chartColorArrays = {
  // Standard 5-color palette
  standard: [
    chartColors.primary,
    chartColors.secondary,
    chartColors.accent,
    chartColors.warning,
    chartColors.success
  ],
  
  // Extended 8-color palette
  extended: [
    chartColors.primary,
    chartColors.secondary,
    chartColors.accent,
    chartColors.warning,
    chartColors.success,
    chartColors.info,
    chartColors.lime,
    chartColors.amber
  ],
  
  // Full 10-color palette
  full: [
    chartColors.primary,
    chartColors.secondary,
    chartColors.accent,
    chartColors.warning,
    chartColors.success,
    chartColors.info,
    chartColors.lime,
    chartColors.amber,
    chartColors.rose,
    chartColors.indigo
  ],
  
  // Specific use case palettes
  userActivity: [chartColors.primary, chartColors.success],
  performance: [chartColors.warning, chartColors.success, chartColors.accent],
  analytics: [chartColors.primary, chartColors.secondary, chartColors.info],
  content: [chartColors.accent, chartColors.warning, chartColors.success, chartColors.info]
} as const;

// Common chart styling
export const chartStyles = {
  // Grid styling
  grid: {
    strokeDasharray: "3 3",
    stroke: "#374151",
    opacity: 0.3,
    horizontal: true,
    vertical: false
  },
  
  // Axis styling
  axis: {
    tick: { fill: '#9CA3AF', fontSize: 11 },
    tickLine: false,
    axisLine: { stroke: "#4B5563", strokeWidth: 1 }
  },
  
  // Tooltip styling
  tooltip: {
    contentStyle: {
      backgroundColor: 'rgba(15, 23, 42, 0.95)',
      backdropFilter: 'blur(8px)',
      border: '1px solid rgba(148, 163, 184, 0.2)',
      borderRadius: '8px',
      padding: '12px',
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      color: '#f8fafc'
    },
    cursor: { fill: 'rgba(59, 130, 246, 0.1)', radius: 4 }
  },
  
  // Legend styling
  legend: {
    iconSize: 12,
    wrapperStyle: {
      fontSize: '12px',
      paddingTop: '15px',
      color: '#9CA3AF'
    }
  },
  
  // Animation settings
  animation: {
    duration: 1200,
    easing: 'ease-out'
  }
} as const;

// Chart-specific configurations
export const chartConfigs = {
  barChart: {
    margin: { top: 20, right: 30, left: 20, bottom: 5 },
    radius: [3, 3, 0, 0],
    animationBegin: 0,
    animationDuration: chartStyles.animation.duration
  },
  
  lineChart: {
    margin: { top: 20, right: 30, left: 20, bottom: 5 },
    strokeWidth: 2,
    dot: { strokeWidth: 2, r: 4 },
    activeDot: { r: 6, strokeWidth: 2 },
    animationDuration: chartStyles.animation.duration
  },
  
  areaChart: {
    margin: { top: 20, right: 30, left: 20, bottom: 5 },
    strokeWidth: 2,
    fillOpacity: 0.3,
    animationDuration: chartStyles.animation.duration
  },
  
  pieChart: {
    margin: { top: 20, right: 30, left: 20, bottom: 20 },
    paddingAngle: 2,
    innerRadius: 30,
    outerRadius: 80,
    animationDuration: chartStyles.animation.duration
  }
} as const;

// Gradient definitions for enhanced visuals
export const chartGradients = {
  primary: {
    id: 'primaryGradient',
    stops: [
      { offset: '5%', stopColor: chartColors.primary, stopOpacity: 0.8 },
      { offset: '95%', stopColor: chartColors.primary, stopOpacity: 0.1 }
    ]
  },
  
  secondary: {
    id: 'secondaryGradient',
    stops: [
      { offset: '5%', stopColor: chartColors.secondary, stopOpacity: 0.8 },
      { offset: '95%', stopColor: chartColors.secondary, stopOpacity: 0.1 }
    ]
  },
  
  success: {
    id: 'successGradient',
    stops: [
      { offset: '5%', stopColor: chartColors.success, stopOpacity: 0.8 },
      { offset: '95%', stopColor: chartColors.success, stopOpacity: 0.1 }
    ]
  },
  
  warning: {
    id: 'warningGradient',
    stops: [
      { offset: '5%', stopColor: chartColors.warning, stopOpacity: 0.8 },
      { offset: '95%', stopColor: chartColors.warning, stopOpacity: 0.1 }
    ]
  }
} as const;

// Helper functions
export const getChartColors = (count: number) => {
  if (count <= 5) return chartColorArrays.standard;
  if (count <= 8) return chartColorArrays.extended;
  return chartColorArrays.full;
};

export const getGradientUrl = (gradientId: string) => `url(#${gradientId})`;

// Value formatters
export const formatters = {
  number: (value: number) => value.toLocaleString(),
  currency: (value: number) => `$${value.toLocaleString()}`,
  percentage: (value: number) => `${(value * 100).toFixed(1)}%`,
  visitors: (value: number) => `${value.toLocaleString()} visitors`,
  users: (value: number) => `${value.toLocaleString()} users`,
  views: (value: number) => `${value.toLocaleString()} views`,
  signups: (value: number) => `${value.toLocaleString()} signups`,
  time: (minutes: number) => {
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  }
} as const;

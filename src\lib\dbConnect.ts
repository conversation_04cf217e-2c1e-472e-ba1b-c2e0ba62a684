// This file is a compatibility layer that re-exports from mongodb.ts
// It exists to maintain backward compatibility with code that imports from '@/lib/dbConnect'

// Import the dbConnect function from mongodb.ts
import { dbConnect } from './mongodb';

// Export it as default
export default dbConnect;

// Also export other functions for compatibility
import {
  connectToDatabase,
  ensureMongooseConnection,
  checkMongoHealth,
  resetMongoConnections,
  clientPromise
} from './mongodb';

export {
  connectToDatabase,
  ensureMongooseConnection,
  checkMongoHealth,
  resetMongoConnections,
  clientPromise
};
import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import User from '@/models/User';

/**
 * GET /api/admin/check-admin
 * Check if a user has admin permissions using userId from query string
 * This endpoint is used when cookie-based authentication fails
 */
export async function GET(request: NextRequest) {
  try {
    // Get the userId from query string
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      console.log('No userId found in query string');
      return NextResponse.json({ 
        isAdmin: false, 
        message: 'No user ID provided' 
      }, { status: 400 });
    }

    // Connect to database
    await ensureMongooseConnection();

    // Find the user by ID
    const user = await User.findById(userId).lean();

    if (!user) {
      console.log(`User not found for ID ${userId}`);
      return NextResponse.json({ 
        isAdmin: false, 
        message: 'User not found' 
      }, { status: 404 });
    }

    // Check if user has admin role
    const isUserAdmin = user.role === 'admin' || user.role === 'superadmin';

    if (!isUserAdmin) {
      console.log(`User ${userId} does not have admin role (role: ${user.role})`);
      return NextResponse.json({ 
        isAdmin: false, 
        message: 'User does not have admin privileges' 
      }, { status: 403 });
    }

    console.log(`User ${userId} has admin privileges`);
    return NextResponse.json({
      isAdmin: true,
      user: {
        id: user._id.toString(),
        email: user.email,
        name: user.name,
        role: user.role
      }
    });
  } catch (error) {
    console.error('Error checking admin status:', error);
    return NextResponse.json(
      { 
        isAdmin: false,
        error: 'Failed to check admin status', 
        message: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

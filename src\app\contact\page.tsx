'use client';

import { useState } from 'react';
import { Navbar } from '@/components/Navbar';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Mail, Phone, MapPin, Clock, MessageSquare, HelpCircle, Briefcase, Shield } from 'lucide-react';
import { toast } from 'sonner';

const contactMethods = [
  {
    icon: Mail,
    title: "Email Support",
    description: "Get help via email within 24 hours",
    contact: "<EMAIL>",
    availability: "24/7"
  },
  {
    icon: Phone,
    title: "Phone Support",
    description: "Speak directly with our support team",
    contact: "+****************",
    availability: "Mon-Fri, 9AM-8PM EST"
  },
  {
    icon: MessageSquare,
    title: "Live Chat",
    description: "Instant help through our chat system",
    contact: "Available on website",
    availability: "24/7"
  },
  {
    icon: MapPin,
    title: "Headquarters",
    description: "Visit our main office",
    contact: "123 Streaming Ave, San Francisco, CA 94105",
    availability: "Mon-Fri, 9AM-6PM PST"
  }
];

const departments = [
  { value: "general", label: "General Inquiry" },
  { value: "support", label: "Technical Support" },
  { value: "billing", label: "Billing & Subscriptions" },
  { value: "content", label: "Content Issues" },
  { value: "partnerships", label: "Business Partnerships" },
  { value: "press", label: "Press & Media" },
  { value: "careers", label: "Careers" },
  { value: "legal", label: "Legal" }
];

const faqs = [
  {
    icon: HelpCircle,
    question: "How do I reset my password?",
    answer: "Go to the login page and click 'Forgot Password'. Follow the instructions sent to your email."
  },
  {
    icon: Shield,
    question: "Is my payment information secure?",
    answer: "Yes, we use industry-standard encryption and never store your full payment details."
  },
  {
    icon: Briefcase,
    question: "Do you offer business accounts?",
    answer: "Yes, we have enterprise solutions for businesses. Contact our sales team for more information."
  }
];

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    department: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      toast.success('Message sent successfully! We\'ll get back to you within 24 hours.');
      setFormData({
        name: '',
        email: '',
        department: '',
        subject: '',
        message: ''
      });
    } catch (error) {
      toast.error('Failed to send message. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-vista-dark text-vista-light">
      <Navbar />
      
      {/* Hero Section */}
      <section className="relative py-20 px-4 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-vista-blue/10 to-transparent" />
        <div className="container mx-auto text-center relative z-10">
          <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-vista-light to-vista-blue bg-clip-text text-transparent">
            Contact Us
          </h1>
          <p className="text-xl md:text-2xl text-vista-light/80 max-w-3xl mx-auto mb-8">
            We're here to help. Reach out to our team for support, partnerships, or any questions about StreamVista.
          </p>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-12 text-vista-light">Get in Touch</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {contactMethods.map((method, index) => (
              <Card key={index} className="bg-vista-card border-vista-light/10 hover:border-vista-blue/30 transition-colors">
                <CardContent className="p-6 text-center">
                  <div className="w-12 h-12 mx-auto mb-4 bg-vista-blue/20 rounded-full flex items-center justify-center">
                    <method.icon className="w-6 h-6 text-vista-blue" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-vista-light">{method.title}</h3>
                  <p className="text-vista-light/70 text-sm mb-3">{method.description}</p>
                  <p className="text-vista-blue font-medium text-sm mb-2">{method.contact}</p>
                  <div className="flex items-center justify-center gap-1 text-xs text-vista-light/60">
                    <Clock className="w-3 h-3" />
                    {method.availability}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <Separator className="bg-vista-light/10" />

      {/* Contact Form */}
      <section className="py-20 px-4">
        <div className="container mx-auto max-w-4xl">
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Form */}
            <Card className="bg-vista-card border-vista-light/10">
              <CardHeader>
                <CardTitle className="text-2xl text-vista-light">Send us a Message</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name" className="text-vista-light">Name *</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        className="bg-vista-dark border-vista-light/20 text-vista-light"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="email" className="text-vista-light">Email *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        className="bg-vista-dark border-vista-light/20 text-vista-light"
                        required
                      />
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="department" className="text-vista-light">Department *</Label>
                    <Select value={formData.department} onValueChange={(value) => handleInputChange('department', value)}>
                      <SelectTrigger className="bg-vista-dark border-vista-light/20 text-vista-light">
                        <SelectValue placeholder="Select a department" />
                      </SelectTrigger>
                      <SelectContent className="bg-vista-dark border-vista-light/20">
                        {departments.map((dept) => (
                          <SelectItem key={dept.value} value={dept.value} className="text-vista-light">
                            {dept.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="subject" className="text-vista-light">Subject *</Label>
                    <Input
                      id="subject"
                      value={formData.subject}
                      onChange={(e) => handleInputChange('subject', e.target.value)}
                      className="bg-vista-dark border-vista-light/20 text-vista-light"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="message" className="text-vista-light">Message *</Label>
                    <Textarea
                      id="message"
                      value={formData.message}
                      onChange={(e) => handleInputChange('message', e.target.value)}
                      className="bg-vista-dark border-vista-light/20 text-vista-light min-h-[120px]"
                      required
                    />
                  </div>

                  <Button 
                    type="submit" 
                    className="w-full bg-vista-blue hover:bg-vista-blue/90"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? 'Sending...' : 'Send Message'}
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* FAQ */}
            <div>
              <h3 className="text-2xl font-bold mb-6 text-vista-light">Frequently Asked Questions</h3>
              <div className="space-y-4">
                {faqs.map((faq, index) => (
                  <Card key={index} className="bg-vista-card border-vista-light/10">
                    <CardContent className="p-6">
                      <div className="flex items-start gap-3">
                        <div className="w-8 h-8 bg-vista-blue/20 rounded-full flex items-center justify-center shrink-0 mt-1">
                          <faq.icon className="w-4 h-4 text-vista-blue" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-vista-light mb-2">{faq.question}</h4>
                          <p className="text-vista-light/70 text-sm">{faq.answer}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              <Card className="mt-6 bg-gradient-to-r from-vista-blue/10 to-vista-accent/10 border-vista-blue/20">
                <CardContent className="p-6 text-center">
                  <h4 className="font-semibold text-vista-light mb-2">Need More Help?</h4>
                  <p className="text-vista-light/70 text-sm mb-4">
                    Check out our comprehensive help center for detailed guides and tutorials.
                  </p>
                  <Button variant="outline" className="border-vista-blue/30 text-vista-blue hover:bg-vista-blue/10">
                    Visit Help Center
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Office Hours */}
      <section className="py-16 px-4 bg-vista-card/30">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-8 text-vista-light">Support Hours</h2>
          <div className="grid md:grid-cols-3 gap-8 max-w-3xl mx-auto">
            <div>
              <h3 className="text-lg font-semibold text-vista-light mb-2">Email Support</h3>
              <p className="text-vista-light/70">24/7 Response within 24 hours</p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-vista-light mb-2">Live Chat</h3>
              <p className="text-vista-light/70">24/7 Instant responses</p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-vista-light mb-2">Phone Support</h3>
              <p className="text-vista-light/70">Mon-Fri, 9AM-8PM EST</p>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}

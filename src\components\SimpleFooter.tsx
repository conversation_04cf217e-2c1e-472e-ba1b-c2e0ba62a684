'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Github, Twitter, Mail, Heart, MessageSquarePlus } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';

interface SimpleFooterProps {
  className?: string;
}

const LOCAL_STORAGE_KEY = 'chatAssistantDismissed';

export default function SimpleFooter({ className = '' }: SimpleFooterProps) {
  const currentYear = new Date().getFullYear();
  const [isChatDismissed, setIsChatDismissed] = useState(true);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    const dismissed = localStorage.getItem(LOCAL_STORAGE_KEY) === 'true';
    setIsChatDismissed(dismissed);

    const handleStorageChange = (event: StorageEvent) => {
        if (event.key === LOCAL_STORAGE_KEY) {
            setIsChatDismissed(localStorage.getItem(LOCAL_STORAGE_KEY) === 'true');
        }
    };
    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  const handleReEnableChat = () => {
    console.log("[SimpleFooter] handleReEnableChat called");

    try {
      // Use the global reset function if available
      if (typeof window !== 'undefined' && (window as any).resetChatAssistant) {
        (window as any).resetChatAssistant();
        return; // The page will reload
      }

      // Fallback if the global function is not available
      // Clear any existing values
      localStorage.removeItem(LOCAL_STORAGE_KEY);

      // Set to default values - ALWAYS set to visible
      localStorage.setItem(LOCAL_STORAGE_KEY, 'false');

      // Force storage events to notify other components
      window.dispatchEvent(new StorageEvent('storage', {
        key: LOCAL_STORAGE_KEY,
        newValue: 'false'
      }));

      // Update local state
      setIsChatDismissed(false);

      // Reload the page to ensure the chat is visible
      window.location.reload();
    } catch (e) {
      console.error("[SimpleFooter] Error resetting chat:", e);
    }
  };

  return (
    <footer className={cn(
      "bg-vista-dark border-t border-vista-light/5 py-6",
      className
    )}>
      <div className="max-w-screen-2xl mx-auto px-4 md:px-6 lg:px-8">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="mb-4 md:mb-0 flex items-center">
            <motion.div
              whileHover={{ rotate: 10 }}
              className="text-vista-blue mr-2"
            >
              <Heart size={14} />
            </motion.div>
            <p className="text-vista-light/50 text-sm">
              © {currentYear} StreamVista. All rights reserved.
            </p>
          </div>

          <div className="flex flex-wrap items-center gap-x-6 gap-y-2 justify-center">
            {isMounted && isChatDismissed && (
                <Button
                    variant="link"
                    className="text-vista-light/50 hover:text-vista-light text-sm transition-colors p-0 h-auto flex items-center gap-1 hover:underline underline-offset-4 order-first md:order-none"
                    onClick={handleReEnableChat}
                >
                    <MessageSquarePlus size={14} /> Show Chat Assistant
                </Button>
             )}
            <Link
              href="/terms"
              className="text-vista-light/50 hover:text-vista-light text-sm transition-colors hover:underline underline-offset-4"
            >
              Terms of Service
            </Link>
            <Link
              href="/privacy"
              className="text-vista-light/50 hover:text-vista-light text-sm transition-colors hover:underline underline-offset-4"
            >
              Privacy Policy
            </Link>
            <Link
              href="/help"
              className="text-vista-light/50 hover:text-vista-light text-sm transition-colors hover:underline underline-offset-4"
            >
              Help Center
            </Link>

            <div className="flex items-center gap-4 md:ml-4 mt-2 md:mt-0">
              <motion.a
                href="https://github.com"
                target="_blank"
                rel="noopener noreferrer"
                whileHover={{ scale: 1.2, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="text-vista-light/40 hover:text-vista-blue transition-colors"
                aria-label="GitHub"
              >
                <Github size={16} />
              </motion.a>
              <motion.a
                href="https://twitter.com"
                target="_blank"
                rel="noopener noreferrer"
                whileHover={{ scale: 1.2, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="text-vista-light/40 hover:text-vista-blue transition-colors"
                aria-label="Twitter"
              >
                <Twitter size={16} />
              </motion.a>
              <motion.a
                href="mailto:<EMAIL>"
                whileHover={{ scale: 1.2, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="text-vista-light/40 hover:text-vista-blue transition-colors"
                aria-label="Email"
              >
                <Mail size={16} />
              </motion.a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}

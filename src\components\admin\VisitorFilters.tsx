'use client';

import { useState, useEffect, useCallback } from 'react';
import { Search, X, Filter, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Checkbox } from '@/components/ui/checkbox';
import { DatePicker } from '@/components/ui/date-picker';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { VisitorDistribution } from '@/hooks/useVisitorData';

export interface VisitorFilterOptions {
  search: string;
  dateRange: {
    from: Date | null;
    to: Date | null;
  };
  country: string[];
  device: string[];
  browser: string[];
  os: string[];
  converted: boolean | null;
}

interface VisitorFiltersProps {
  filters: VisitorFilterOptions;
  onChange?: (filters: VisitorFilterOptions) => void;
  onFilterChange?: (filters: VisitorFilterOptions) => void;
  onReset: () => void;
  distributions?: {
    device: VisitorDistribution[];
    browser: VisitorDistribution[];
    os: VisitorDistribution[];
    country: VisitorDistribution[];
  };
  deviceOptions?: { label: string; value: string }[];
  browserOptions?: { label: string; value: string }[];
  osOptions?: { label: string; value: string }[];
  countryOptions?: { label: string; value: string }[];
}

export default function VisitorFilters({
  filters,
  onChange,
  onFilterChange,
  onReset,
  distributions,
  deviceOptions: propDeviceOptions,
  browserOptions: propBrowserOptions,
  osOptions: propOsOptions,
  countryOptions: propCountryOptions
}: VisitorFiltersProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchValue, setSearchValue] = useState(filters.search || '');
  const [localFilters, setLocalFilters] = useState<VisitorFilterOptions>(filters);

  // Handle both onChange and onFilterChange for backward compatibility
  const handleChange = useCallback((updatedFilters: VisitorFilterOptions) => {
    if (onChange) {
      onChange(updatedFilters);
    }
    if (onFilterChange) {
      onFilterChange(updatedFilters);
    }
  }, [onChange, onFilterChange]);

  // Generate options from distributions or use provided options
  const countryOptions = propCountryOptions || (distributions?.country.map(item => ({
    label: item._id || 'Unknown',
    value: item._id || 'Unknown'
  })) || []);
  
  const deviceOptions = propDeviceOptions || (distributions?.device.map(item => ({
    label: item._id || 'Unknown',
    value: item._id || 'Unknown'
  })) || []);
  
  const browserOptions = propBrowserOptions || (distributions?.browser.map(item => ({
    label: item._id || 'Unknown',
    value: item._id || 'Unknown'
  })) || []);
  
  const osOptions = propOsOptions || (distributions?.os.map(item => ({
    label: item._id || 'Unknown',
    value: item._id || 'Unknown'
  })) || []);

  // Update localFilters when filters prop changes
  useEffect(() => {
    setLocalFilters(filters);
    setSearchValue(filters.search || '');
  }, [filters]);

  // Count active filters
  const activeFilterCount = [
    localFilters.dateRange.from || localFilters.dateRange.to ? 1 : 0,
    localFilters.country.length,
    localFilters.device.length,
    localFilters.browser.length,
    localFilters.os.length,
    localFilters.converted !== null ? 1 : 0
  ].reduce((a, b) => a + b, 0);

  // Update search filter when search input changes
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchValue !== localFilters.search) {
        const updatedFilters = { ...localFilters, search: searchValue };
        setLocalFilters(updatedFilters);
        handleChange(updatedFilters);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchValue, localFilters, handleChange]);

  // Handle filter changes
  const handleFilterChange = (newFilters: Partial<VisitorFilterOptions>) => {
    const updatedFilters = { ...localFilters, ...newFilters };
    setLocalFilters(updatedFilters);
    handleChange(updatedFilters);
  };

  // Handle reset
  const handleReset = () => {
    setSearchValue('');
    const resetFilters = {
      search: '',
      dateRange: {
        from: null,
        to: null
      },
      country: [],
      device: [],
      browser: [],
      os: [],
      converted: null
    };
    setLocalFilters(resetFilters);
    onReset();
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-2">
        {/* Search Input */}
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search visitors..."
            className="pl-9"
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
          />
          {searchValue && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-1 top-1 h-7 w-7 text-muted-foreground hover:text-foreground"
              onClick={() => {
                setSearchValue('');
                handleFilterChange({ search: '' });
              }}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Filter Button */}
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button variant="outline" className="flex gap-2 whitespace-nowrap">
              <Filter className="h-4 w-4" />
              Filters
              {activeFilterCount > 0 && (
                <Badge variant="default" className="ml-1 h-5 w-5 p-0 flex items-center justify-center rounded-full">
                  {activeFilterCount}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80 sm:w-96 p-4">
            <div className="space-y-4">
              <h4 className="font-medium">Filter Visitors</h4>
              
              {/* Date Range */}
              <div className="space-y-2">
                <Label>Date Range</Label>
                <DateRangePicker
                  dateRange={{
                    from: localFilters.dateRange.from,
                    to: localFilters.dateRange.to
                  }}
                  onSelect={(range) => handleFilterChange({
                    dateRange: {
                      from: range?.from || null,
                      to: range?.to || null
                    }
                  })}
                  placeholder="Select date range"
                />
              </div>

              {/* Country Filter */}
              <div className="space-y-2">
                <Label>Country</Label>
                <Select
                  value={localFilters.country.length === 1 ? localFilters.country[0] : ''}
                  onValueChange={(value) => handleFilterChange({
                    country: value ? [value] : []
                  })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All Countries" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Countries</SelectItem>
                    {countryOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Device Filter */}
              <div className="space-y-2">
                <Label>Device</Label>
                <Select
                  value={localFilters.device.length === 1 ? localFilters.device[0] : ''}
                  onValueChange={(value) => handleFilterChange({
                    device: value ? [value] : []
                  })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All Devices" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Devices</SelectItem>
                    {deviceOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              {/* Browser Filter */}
              <div className="space-y-2">
                <Label>Browser</Label>
                <Select
                  value={localFilters.browser.length === 1 ? localFilters.browser[0] : ''}
                  onValueChange={(value) => handleFilterChange({
                    browser: value ? [value] : []
                  })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All Browsers" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Browsers</SelectItem>
                    {browserOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              {/* OS Filter */}
              <div className="space-y-2">
                <Label>Operating System</Label>
                <Select
                  value={localFilters.os.length === 1 ? localFilters.os[0] : ''}
                  onValueChange={(value) => handleFilterChange({
                    os: value ? [value] : []
                  })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All OS" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All OS</SelectItem>
                    {osOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Converted Filter */}
              <div className="space-y-2">
                <Label>Conversion Status</Label>
                <Select
                  value={localFilters.converted === null ? '' : localFilters.converted ? 'true' : 'false'}
                  onValueChange={(value) => handleFilterChange({
                    converted: value === '' ? null : value === 'true'
                  })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All Visitors" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Visitors</SelectItem>
                    <SelectItem value="true">Converted to User</SelectItem>
                    <SelectItem value="false">Not Converted</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Filter Actions */}
              <div className="flex justify-between pt-2">
                <Button variant="ghost" onClick={handleReset}>
                  Reset Filters
                </Button>
                <Button onClick={() => setIsOpen(false)}>
                  Apply Filters
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>

        {/* Reset Button - Only show if filters are active */}
        {(activeFilterCount > 0 || searchValue) && (
          <Button variant="ghost" onClick={handleReset} className="gap-1">
            <X className="h-4 w-4" />
            Clear
          </Button>
        )}
      </div>
      
      {/* Active Filters Display */}
      {activeFilterCount > 0 && (
        <div className="flex flex-wrap gap-2 pt-2">
          {localFilters.dateRange.from && localFilters.dateRange.to && (
            <Badge variant="secondary" className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              {new Date(localFilters.dateRange.from).toLocaleDateString()} - {new Date(localFilters.dateRange.to).toLocaleDateString()}
              <X 
                className="h-3 w-3 ml-1 cursor-pointer" 
                onClick={() => handleFilterChange({ 
                  dateRange: { from: null, to: null } 
                })}
              />
            </Badge>
          )}
          
          {localFilters.country.map(country => (
            <Badge key={country} variant="secondary" className="flex items-center gap-1">
              Country: {country}
              <X 
                className="h-3 w-3 ml-1 cursor-pointer" 
                onClick={() => handleFilterChange({ 
                  country: localFilters.country.filter(c => c !== country) 
                })}
              />
            </Badge>
          ))}
          
          {localFilters.device.map(device => (
            <Badge key={device} variant="secondary" className="flex items-center gap-1">
              Device: {device}
              <X 
                className="h-3 w-3 ml-1 cursor-pointer" 
                onClick={() => handleFilterChange({ 
                  device: localFilters.device.filter(d => d !== device) 
                })}
              />
            </Badge>
          ))}
          
          {localFilters.browser.map(browser => (
            <Badge key={browser} variant="secondary" className="flex items-center gap-1">
              Browser: {browser}
              <X 
                className="h-3 w-3 ml-1 cursor-pointer" 
                onClick={() => handleFilterChange({ 
                  browser: localFilters.browser.filter(b => b !== browser) 
                })}
              />
            </Badge>
          ))}
          
          {localFilters.os.map(os => (
            <Badge key={os} variant="secondary" className="flex items-center gap-1">
              OS: {os}
              <X 
                className="h-3 w-3 ml-1 cursor-pointer" 
                onClick={() => handleFilterChange({ 
                  os: localFilters.os.filter(o => o !== os) 
                })}
              />
            </Badge>
          ))}
          
          {localFilters.converted !== null && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {localFilters.converted ? 'Converted' : 'Not Converted'}
              <X 
                className="h-3 w-3 ml-1 cursor-pointer" 
                onClick={() => handleFilterChange({ converted: null })}
              />
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}

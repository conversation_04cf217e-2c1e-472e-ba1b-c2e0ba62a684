import mongoose, { Document, Schema } from 'mongoose';

export interface ISystemLog extends Document {
  level: 'info' | 'warning' | 'error' | 'debug';
  message: string;
  source: string;
  details?: Record<string, any>;
  timestamp: Date;
}

const SystemLogSchema = new Schema<ISystemLog>(
  {
    level: {
      type: String,
      required: true,
      enum: ['info', 'warning', 'error', 'debug'],
      index: true
    },
    message: {
      type: String,
      required: true
    },
    source: {
      type: String,
      required: true,
      index: true
    },
    details: {
      type: Schema.Types.Mixed
    },
    timestamp: {
      type: Date,
      default: Date.now,
      index: true
    }
  },
  {
    timestamps: true
  }
);

// Create indexes for efficient querying
SystemLogSchema.index({ level: 1, timestamp: -1 });
SystemLogSchema.index({ source: 1, timestamp: -1 });

// Create the model if it doesn't exist already
const SystemLog = mongoose.models.SystemLog || mongoose.model<ISystemLog>('SystemLog', SystemLogSchema);

export default SystemLog;

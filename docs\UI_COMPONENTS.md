# UI Components Documentation

## Component Library Overview

StreamVista uses a comprehensive set of reusable UI components built with React and Tailwind CSS. The components are designed with accessibility and modern design principles in mind, providing a consistent and responsive user interface across the application.

## Core Components

### Interactive Components

#### Button
```typescript
// src/components/ui/button.tsx
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'primary' | 'secondary' | 'ghost' | 'link';
  size?: 'sm' | 'md' | 'lg';
  isLoading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ variant = 'default', size = 'md', isLoading, leftIcon, rightIcon, children, ...props }, ref) => {
    // Implementation
  }
);
```

#### Input
```typescript
// src/components/ui/input.tsx
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: string;
  label?: string;
  helperText?: string;
  leftElement?: React.ReactNode;
  rightElement?: React.ReactNode;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ error, label, helperText, leftElement, rightElement, ...props }, ref) => {
    // Implementation
  }
);
```

#### Form Components
```typescript
// src/components/ui/form.tsx
interface FormFieldProps {
  name: string;
  label?: string;
  error?: string;
  children: React.ReactNode;
}

const FormField: React.FC<FormFieldProps> = ({ name, label, error, children }) => {
  // Implementation
};

interface FormProps extends React.FormHTMLAttributes<HTMLFormElement> {
  onSubmit: (data: any) => void;
  children: React.ReactNode;
}

const Form: React.FC<FormProps> = ({ onSubmit, children, ...props }) => {
  // Implementation with form validation
};
```

### Layout Components

#### Card
```typescript
// src/components/ui/card.tsx
interface CardProps {
  title?: string;
  description?: string;
  image?: string;
  footer?: React.ReactNode;
  children: React.ReactNode;
}

const Card: React.FC<CardProps> = ({ title, description, image, footer, children }) => {
  // Implementation
};
```

#### Dialog
```typescript
// src/components/ui/dialog.tsx
interface DialogProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description?: string;
  children: React.ReactNode;
}

const Dialog: React.FC<DialogProps> = ({ isOpen, onClose, title, description, children }) => {
  // Implementation with focus management and keyboard navigation
};
```

#### Sheet
```typescript
// src/components/ui/sheet.tsx
interface SheetProps {
  isOpen: boolean;
  onClose: () => void;
  position?: 'left' | 'right' | 'top' | 'bottom';
  size?: 'sm' | 'md' | 'lg' | 'full';
  children: React.ReactNode;
}

const Sheet: React.FC<SheetProps> = ({ isOpen, onClose, position = 'right', size = 'md', children }) => {
  // Implementation with animations and responsive behavior
};
```

### Navigation Components

#### Tabs
```typescript
// src/components/ui/tabs.tsx
interface TabsProps {
  defaultValue?: string;
  value?: string;
  onValueChange?: (value: string) => void;
  children: React.ReactNode;
}

const Tabs = {
  Root: React.FC<TabsProps>,
  List: React.FC<{ children: React.ReactNode }>,
  Trigger: React.FC<{ value: string; children: React.ReactNode }>,
  Content: React.FC<{ value: string; children: React.ReactNode }>
};
```

#### Dropdown Menu
```typescript
// src/components/ui/dropdown-menu.tsx
interface DropdownMenuProps {
  trigger: React.ReactNode;
  children: React.ReactNode;
  align?: 'start' | 'center' | 'end';
  side?: 'top' | 'right' | 'bottom' | 'left';
}

const DropdownMenu: React.FC<DropdownMenuProps> = ({ trigger, children, align = 'end', side = 'bottom' }) => {
  // Implementation with keyboard navigation and click outside handling
};
```

### Feedback Components

#### Toast
```typescript
// src/components/ui/toast.tsx
interface ToastProps {
  id: string;
  title: string;
  description?: string;
  type?: 'default' | 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  onClose: () => void;
}

const Toast: React.FC<ToastProps> = ({ id, title, description, type = 'default', duration = 5000, onClose }) => {
  // Implementation with auto-dismiss and animation
};
```

#### Progress
```typescript
// src/components/ui/progress.tsx
interface ProgressProps {
  value: number;
  max?: number;
  size?: 'sm' | 'md' | 'lg';
  showValue?: boolean;
  variant?: 'default' | 'success' | 'error';
}

const Progress: React.FC<ProgressProps> = ({ value, max = 100, size = 'md', showValue, variant = 'default' }) => {
  // Implementation with ARIA attributes and animations
};
```

### Data Display Components

#### Table
```typescript
// src/components/ui/table.tsx
interface TableProps<T> {
  data: T[];
  columns: {
    header: string;
    accessor: keyof T | ((row: T) => React.ReactNode);
    width?: string;
  }[];
  sortable?: boolean;
  onSort?: (column: string, direction: 'asc' | 'desc') => void;
}

const Table = <T extends Record<string, any>>({ data, columns, sortable, onSort }: TableProps<T>) => {
  // Implementation with sorting and responsive design
};
```

#### Avatar
```typescript
// src/components/ui/avatar.tsx
interface AvatarProps {
  src?: string;
  alt?: string;
  fallback?: string;
  size?: 'sm' | 'md' | 'lg';
  status?: 'online' | 'offline' | 'away';
}

const Avatar: React.FC<AvatarProps> = ({ src, alt, fallback, size = 'md', status }) => {
  // Implementation with image loading and fallback
};
```

### Utility Components

#### Tooltip
```typescript
// src/components/ui/tooltip.tsx
interface TooltipProps {
  content: string;
  children: React.ReactNode;
  position?: 'top' | 'right' | 'bottom' | 'left';
  delay?: number;
}

const Tooltip: React.FC<TooltipProps> = ({ content, children, position = 'top', delay = 200 }) => {
  // Implementation with positioning and hover delay
};
```

#### Skeleton
```typescript
// src/components/ui/skeleton.tsx
interface SkeletonProps {
  variant?: 'text' | 'circular' | 'rectangular';
  width?: string | number;
  height?: string | number;
  animation?: 'pulse' | 'wave' | 'none';
}

const Skeleton: React.FC<SkeletonProps> = ({ variant = 'text', width, height, animation = 'pulse' }) => {
  // Implementation with loading animations
};
```

### Custom Components

#### Custom Scroll
```typescript
// src/components/ui/custom-scroll.tsx
interface CustomScrollProps {
  children: React.ReactNode;
  height?: string | number;
  onScroll?: (event: React.UIEvent<HTMLDivElement>) => void;
  scrollToBottom?: boolean;
}

const CustomScroll: React.FC<CustomScrollProps> = ({ children, height, onScroll, scrollToBottom }) => {
  // Implementation with custom scrollbar styling and scroll behavior
};
```

#### Carousel
```typescript
// src/components/ui/carousel.tsx
interface CarouselProps {
  items: React.ReactNode[];
  autoPlay?: boolean;
  interval?: number;
  showDots?: boolean;
  showArrows?: boolean;
}

const Carousel: React.FC<CarouselProps> = ({ items, autoPlay = true, interval = 5000, showDots = true, showArrows = true }) => {
  // Implementation with touch support and keyboard navigation
};
```

## Usage Guidelines

### Component Composition
- Use composition over inheritance
- Keep components focused and single-responsibility
- Leverage React's children prop for flexibility
- Use compound components for complex UIs

### Styling
- Use Tailwind CSS utility classes
- Follow design system tokens
- Maintain consistent spacing and sizing
- Support dark mode with `dark:` variants

### Accessibility
- Include proper ARIA attributes
- Support keyboard navigation
- Maintain focus management
- Provide screen reader descriptions

### Error Handling
- Graceful fallbacks for missing props
- Loading states and skeletons
- Error boundaries for component failures
- Meaningful error messages

### Responsive Design
- Mobile-first approach
- Breakpoint-based layouts
- Fluid typography
- Touch-friendly interactions

## Best Practices

### Component Selection
1. Choose the appropriate component for the use case
2. Consider accessibility requirements
3. Evaluate performance implications
4. Check for mobile compatibility

### State Management
1. Use controlled components when needed
2. Implement proper form validation
3. Handle loading and error states
4. Maintain consistent state updates

### Performance
1. Implement proper memoization
2. Lazy load components when appropriate
3. Optimize re-renders
4. Use efficient event handlers

### Accessibility
1. Follow WCAG guidelines
2. Test with screen readers
3. Support keyboard navigation
4. Provide proper color contrast

### Customization
1. Use component props for variations
2. Leverage CSS variables
3. Support theme customization
4. Allow for extensibility

## Examples

### Form with Validation
```tsx
<Form onSubmit={handleSubmit}>
  <FormField name="email" label="Email">
    <Input type="email" required />
  </FormField>
  <FormField name="password" label="Password">
    <Input type="password" required minLength={8} />
  </FormField>
  <Button type="submit">Submit</Button>
</Form>
```

### Modal Dialog
```tsx
<Dialog
  isOpen={isOpen}
  onClose={handleClose}
  title="Confirm Action"
  description="Are you sure you want to proceed?"
>
  <div className="flex justify-end gap-4">
    <Button variant="ghost" onClick={handleClose}>Cancel</Button>
    <Button variant="primary" onClick={handleConfirm}>Confirm</Button>
  </div>
</Dialog>
```

### Data Display
```tsx
<Table
  data={users}
  columns={[
    { header: 'Name', accessor: 'name' },
    { header: 'Email', accessor: 'email' },
    { header: 'Status', accessor: row => (
      <Badge variant={row.status === 'active' ? 'success' : 'error'}>
        {row.status}
      </Badge>
    )}
  ]}
  sortable
  onSort={handleSort}
/>
```

### Notification System
```tsx
<ToastProvider>
  <Button
    onClick={() => {
      toast({
        title: 'Success',
        description: 'Operation completed successfully',
        type: 'success'
      });
    }}
  >
    Show Toast
  </Button>
</ToastProvider>
``` 
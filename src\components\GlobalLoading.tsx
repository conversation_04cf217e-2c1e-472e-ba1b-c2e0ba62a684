"use client"

import React, { createContext, useContext, useState, useCallback, useEffect } from 'react'
import { Progress } from '@/components/ui/progress'
import { createPortal } from 'react-dom'
import { AnimatePresence, motion } from 'framer-motion'

// Types and context for loading state
export type LoadingType = 'page' | 'overlay' | 'top-bar'

interface LoadingState {
  isLoading: boolean
  type: LoadingType
  message?: string
  progress?: number
}

interface LoadingContextType {
  loading: LoadingState
  startLoading: (options?: { type?: LoadingType; message?: string }) => void
  updateLoading: (progress: number) => void
  finishLoading: () => void
}

// Create the context
const LoadingContext = createContext<LoadingContextType | undefined>(undefined)

// Component to handle client-side portal rendering
function ClientOnlyPortal({ children }: { children: React.ReactNode }) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    return () => setMounted(false)
  }, [])

  return mounted ? createPortal(children, document.body) : null
}

// Provider component
export function LoadingProvider({ children }: { children: React.ReactNode }) {
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: false,
    type: 'top-bar',
    progress: 0
  })
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
    return () => setIsMounted(false)
  }, [])

  // Start the loading state
  const startLoading = useCallback((options?: { type?: LoadingType; message?: string }) => {
    setLoading({
      isLoading: true,
      type: options?.type || 'top-bar',
      message: options?.message,
      progress: 0
    })

    // If top-bar, auto-progress to simulate loading
    if (options?.type === 'top-bar' || !options?.type) {
      const interval = setInterval(() => {
        setLoading(prev => {
          // Ensure we never reach 100% automatically (that happens on finishLoading)
          if (prev.progress && prev.progress >= 90) {
            clearInterval(interval)
            return prev
          }

          return {
            ...prev,
            progress: prev.progress ? Math.min(prev.progress + Math.random() * 10, 90) : 10
          }
        })
      }, 300)

      // Clean up interval
      return () => clearInterval(interval)
    }
  }, [])

  // Update the loading progress
  const updateLoading = useCallback((progress: number) => {
    setLoading(prev => ({
      ...prev,
      progress: Math.min(Math.max(0, progress), 100)  // Ensure progress is between 0-100
    }))
  }, [])

  // Finish loading
  const finishLoading = useCallback(() => {
    // First set to 100%
    setLoading(prev => ({
      ...prev,
      progress: 100
    }))

    // Then after a small delay, hide the loading indicator
    setTimeout(() => {
      setLoading({
        isLoading: false,
        type: 'top-bar',
        progress: 0
      })
    }, 500)
  }, [])

  // Create context value object
  const contextValue = {
    loading,
    startLoading,
    updateLoading,
    finishLoading
  }

  return (
    <LoadingContext.Provider value={contextValue}>
      {children}

      {/* Loading Indicators - Only render on client-side */}
      {isMounted && loading.isLoading && (
        <ClientOnlyPortal>
          <AnimatePresence>
            <>
              {/* Top progress bar */}
              {loading.type === 'top-bar' && (
                <motion.div
                  initial={{ opacity: 0, y: -5 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -5 }}
                  transition={{ duration: 0.2 }}
                  className="fixed top-0 left-0 right-0 z-[99]"
                >
                  <Progress
                    value={loading.progress}
                    max={100}
                    size="xs"
                    className="h-1 rounded-none"
                    variant="blue"
                  />
                </motion.div>
              )}

              {/* Full page overlay */}
              {loading.type === 'overlay' && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="fixed inset-0 bg-black/80 backdrop-blur-sm flex flex-col items-center justify-center z-[99]"
                >
                  <div className="w-24 h-24 rounded-full border-4 border-vista-blue/20 border-t-vista-blue animate-spin" />

                  {loading.message && (
                    <div className="mt-6 text-vista-light font-medium">{loading.message}</div>
                  )}

                  {loading.progress !== undefined && loading.progress > 0 && (
                    <div className="mt-4 w-48">
                      <Progress
                        value={loading.progress}
                        max={100}
                        size="md"
                        variant="blue"
                        showValue
                      />
                    </div>
                  )}
                </motion.div>
              )}

              {/* Page loading indicator */}
              {loading.type === 'page' && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="fixed bottom-8 right-8 bg-vista-dark-lighter border border-vista-light/10 rounded-md shadow-lg px-4 py-3 z-[99] max-w-xs"
                >
                  <div className="flex items-center mb-2">
                    <div className="w-5 h-5 border-2 border-vista-blue/30 border-t-vista-blue rounded-full animate-spin mr-3" />
                    <div className="font-medium text-vista-light">
                      {loading.message || "Loading..."}
                    </div>
                  </div>

                  {loading.progress !== undefined && (
                    <Progress
                      value={loading.progress}
                      max={100}
                      size="sm"
                      variant="blue"
                      showValue
                      formatValue={(value) => `${Math.round(value)}%`}
                    />
                  )}
                </motion.div>
              )}
            </>
          </AnimatePresence>
        </ClientOnlyPortal>
      )}
    </LoadingContext.Provider>
  )
}

// Custom hook for using the loading context
export function useLoading() {
  const context = useContext(LoadingContext)
  if (!context) {
    throw new Error('useLoading must be used within a LoadingProvider')
  }
  return context
}

// The loading component (to be imported and used directly)
export default function GlobalLoading() {
  // This component doesn't render anything itself,
  // as the loading indicators are rendered by the provider via portal
  return null
}

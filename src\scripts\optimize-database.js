import mongoose from 'mongoose';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * Database optimization script
 * Adds indexes for better query performance
 */
async function optimizeDatabase() {
  try {
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Profile indexes
    const Profile = mongoose.model('Profile', new mongoose.Schema({
      userId: mongoose.Schema.Types.ObjectId,
      name: String,
      avatar: String,
      isKids: Boolean,
      isPrimary: <PERSON>olean
    }, { timestamps: true }));

    await Profile.collection.createIndex({ userId: 1 });
    await Profile.collection.createIndex({ userId: 1, isPrimary: -1 });
    console.log('✅ Profile indexes created');

    // Notification indexes
    const Notification = mongoose.model('Notification', new mongoose.Schema({
      userId: mongoose.Schema.Types.ObjectId,
      title: String,
      message: String,
      type: String,
      read: Boolean,
      expiresAt: Date,
      deletedBy: [mongoose.Schema.Types.ObjectId]
    }, { timestamps: true }));

    await Notification.collection.createIndex({ userId: 1, read: 1 });
    await Notification.collection.createIndex({ userId: 1, createdAt: -1 });
    await Notification.collection.createIndex({ userId: 1, expiresAt: 1 });
    await Notification.collection.createIndex({ userId: 1, deletedBy: 1 });
    console.log('✅ Notification indexes created');

    // User indexes
    const User = mongoose.model('User', new mongoose.Schema({
      email: String,
      name: String,
      image: String,
      lastLogin: Date
    }, { timestamps: true }));

    await User.collection.createIndex({ email: 1 }, { unique: true });
    await User.collection.createIndex({ lastLogin: -1 });
    console.log('✅ User indexes created');

    // AnonymousVisitor indexes
    const AnonymousVisitor = mongoose.model('AnonymousVisitor', new mongoose.Schema({
      visitorId: String,
      nickname: String,
      ipAddress: String,
      userAgent: String,
      fingerprint: String,
      firstVisit: Date,
      lastVisit: Date,
      visitCount: Number,
      pagesViewed: Number,
      referrer: String,
      browser: String,
      os: String,
      device: String,
      country: String,
      countryCode: String,
      region: String,
      city: String,
      timezone: String,
      latitude: Number,
      longitude: Number,
      convertedToUser: Boolean
    }, { timestamps: true }));

    await AnonymousVisitor.collection.createIndex({ visitorId: 1 }, { unique: true });
    await AnonymousVisitor.collection.createIndex({ fingerprint: 1 });
    await AnonymousVisitor.collection.createIndex({ lastVisit: -1 });
    await AnonymousVisitor.collection.createIndex({ ipAddress: 1 });
    console.log('✅ AnonymousVisitor indexes created');

    // BannerAd indexes
    const BannerAd = mongoose.model('BannerAd', new mongoose.Schema({
      title: String,
      imageUrl: String,
      linkUrl: String,
      isActive: Boolean,
      startDate: Date,
      endDate: Date,
      targetAudience: [String],
      impressions: Number,
      clicks: Number
    }, { timestamps: true }));

    await BannerAd.collection.createIndex({ isActive: 1, startDate: 1, endDate: 1 });
    await BannerAd.collection.createIndex({ targetAudience: 1 });
    console.log('✅ BannerAd indexes created');

    console.log('🎉 Database optimization completed successfully!');
    
    // Show index statistics
    const collections = ['profiles', 'notifications', 'users', 'anonymousvisitors', 'bannerads'];
    for (const collectionName of collections) {
      try {
        const indexes = await mongoose.connection.db.collection(collectionName).indexes();
        console.log(`📊 ${collectionName}: ${indexes.length} indexes`);
      } catch (error) {
        console.log(`⚠️  Could not check indexes for ${collectionName}`);
      }
    }

  } catch (error) {
    console.error('❌ Database optimization failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run optimization if this script is executed directly
const isMainModule = process.argv[1] && process.argv[1].endsWith('optimize-database.js');
if (isMainModule) {
  optimizeDatabase();
}

export default optimizeDatabase; 
import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongoose';
import Review from '@/models/Review';
import mongoose from 'mongoose';

/**
 * GET /api/reviews/[id]
 * Get a specific review by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the review ID from params
    const { id } = await params;

    // Validate review ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ error: 'Invalid review ID' }, { status: 400 });
    }

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Get the review
    const review = await Review.findById(id);

    if (!review) {
      return NextResponse.json({ error: 'Review not found' }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      review
    });
  } catch (error) {
    console.error('Error fetching review:', error);
    return NextResponse.json(
      { error: 'Failed to fetch review' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/reviews/[id]
 * Update a review (like/dislike)
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the review ID from params
    const { id } = await params;

    // Validate review ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ error: 'Invalid review ID' }, { status: 400 });
    }

    // Get data from request
    const data = await request.json();
    const { action } = data;

    // Validate action
    if (!action || (action !== 'like' && action !== 'dislike')) {
      return NextResponse.json(
        { error: 'Invalid action. Must be "like" or "dislike"' },
        { status: 400 }
      );
    }

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Get the review
    const review = await Review.findById(id);

    if (!review) {
      return NextResponse.json({ error: 'Review not found' }, { status: 404 });
    }

    // Update the review based on the action
    if (action === 'like') {
      review.likes += 1;
    } else {
      review.dislikes += 1;
    }

    await review.save();

    return NextResponse.json({
      success: true,
      review
    });
  } catch (error) {
    console.error('Error updating review:', error);
    return NextResponse.json(
      { error: 'Failed to update review' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/reviews/[id]
 * Update a review's content and rating
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the review ID from params
    const { id } = await params;

    // Validate review ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ error: 'Invalid review ID' }, { status: 400 });
    }

    // Get data from request
    const data = await request.json();
    const { userId, rating, comment } = data;

    // Validate required fields
    if (!userId || !rating || !comment) {
      return NextResponse.json(
        { error: 'User ID, rating, and comment are required' },
        { status: 400 }
      );
    }

    // Validate rating
    if (rating < 1 || rating > 5) {
      return NextResponse.json(
        { error: 'Rating must be between 1 and 5' },
        { status: 400 }
      );
    }

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Get the review
    const review = await Review.findById(id);

    if (!review) {
      return NextResponse.json({ error: 'Review not found' }, { status: 404 });
    }

    // Check if the user is the owner of the review
    if (review.userId !== userId) {
      return NextResponse.json(
        { error: 'You are not authorized to update this review' },
        { status: 403 }
      );
    }

    // Update the review
    review.rating = rating;
    review.comment = comment;
    await review.save();

    return NextResponse.json({
      success: true,
      review
    });
  } catch (error) {
    console.error('Error updating review:', error);
    return NextResponse.json(
      { error: 'Failed to update review' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/reviews/[id]
 * Delete a review
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the review ID from params
    const { id } = await params;

    // Validate review ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ error: 'Invalid review ID' }, { status: 400 });
    }

    // Get userId from request
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const isAdmin = searchParams.get('isAdmin') === 'true';

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Get the review
    const review = await Review.findById(id);

    if (!review) {
      return NextResponse.json({ error: 'Review not found' }, { status: 404 });
    }

    // Check if the user is the owner of the review or an admin
    if (review.userId !== userId && !isAdmin) {
      // If not the owner, check if the user is an admin
      const User = (await import('@/models/User')).default;
      const user = await User.findById(userId).select('role').lean();

      if (!user || (user.role !== 'admin' && user.role !== 'superadmin')) {
        return NextResponse.json(
          { error: 'You are not authorized to delete this review' },
          { status: 403 }
        );
      }
    }

    // Delete the review
    await Review.findByIdAndDelete(id);

    return NextResponse.json({
      success: true,
      message: 'Review deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting review:', error);
    return NextResponse.json(
      { error: 'Failed to delete review' },
      { status: 500 }
    );
  }
}

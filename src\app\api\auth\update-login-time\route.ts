import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import mongoose from 'mongoose';

/**
 * POST /api/auth/update-login-time
 * Update the lastLogin timestamp for the current user
 */
export async function POST(request: NextRequest) {
  try {
    // Get the userId from cookies or request body
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request body
    if (!userId) {
      try {
        const data = await request.json();
        userId = data.userId;
      } catch (error) {
        // Ignore JSON parsing errors
      }
    }

    if (!userId) {
      return NextResponse.json({ error: "User ID is required" }, { status: 400 });
    }

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Get direct access to the users collection
    if (!mongoose.connection.db) {
      throw new Error('Database connection not established');
    }
    const db = mongoose.connection.db;
    const usersCollection = db.collection('users');

    // Create a current date object with the exact current time
    const now = new Date();

    // Convert string ID to ObjectId
    const objectId = new mongoose.Types.ObjectId(userId);

    // Update the lastLogin field directly with the current timestamp
    // Log the current timestamp for debugging
    console.log(`Updating lastLogin for user ${userId} to current time: ${now.toISOString()}`);

    // Try both approaches - with ObjectId and with string ID
    let result;
    try {
      // First try with ObjectId (most reliable)
      result = await usersCollection.updateOne(
        { _id: objectId },
        { $set: { lastLogin: now } }
      );

      if (result.matchedCount === 0) {
        // If no match with ObjectId, try with a different approach
        // We can try to find the user by other fields like email
        console.log(`No match with ObjectId, trying to find user by other means for user ${userId}`);

        // First try to find the user to get their actual _id
        const user = await usersCollection.findOne({
          $or: [
            { userId: userId },  // Some systems might store a userId field
            { externalId: userId } // Or an externalId field
          ]
        });

        if (user) {
          // If we found the user, update using their actual _id
          result = await usersCollection.updateOne(
            { _id: user._id },
            { $set: { lastLogin: now } }
          );
          console.log(`Found user via alternative fields, updated lastLogin`);
        } else {
          console.log(`Could not find user with ID ${userId} via alternative fields`);
          // Create an empty result object with the required properties
          result = {
            matchedCount: 0,
            modifiedCount: 0,
            acknowledged: true,
            upsertedCount: 0,
            upsertedId: null
          };
        }
      }
    } catch (updateError) {
      console.error('Error with ObjectId update, trying alternative approach:', updateError);

      try {
        // Try to find the user first to get their actual _id
        const user = await usersCollection.findOne({
          $or: [
            { userId: userId },
            { externalId: userId }
          ]
        });

        if (user) {
          // If we found the user, update using their actual _id
          result = await usersCollection.updateOne(
            { _id: user._id },
            { $set: { lastLogin: now } }
          );
          console.log(`Found user via alternative fields after error, updated lastLogin`);
        } else {
          // Last resort: try creating a new ObjectId (in case the error was with the original conversion)
          try {
            const newObjectId = new mongoose.Types.ObjectId(userId);
            result = await usersCollection.updateOne(
              { _id: newObjectId },
              { $set: { lastLogin: now } }
            );
            console.log(`Retried with new ObjectId conversion`);
          } catch (finalError) {
            console.error('All attempts to update lastLogin failed:', finalError);
            // Create an empty result object with the required properties
            result = {
              matchedCount: 0,
              modifiedCount: 0,
              acknowledged: true,
              upsertedCount: 0,
              upsertedId: null
            };
          }
        }
      } catch (findError) {
        console.error('Error finding user by alternative fields:', findError);
        // Create an empty result object with the required properties
        result = {
          matchedCount: 0,
          modifiedCount: 0,
          acknowledged: true,
          upsertedCount: 0,
          upsertedId: null
        };
      }
    }

    // Log the result for debugging
    console.log(`Update result for user ${userId}: matchedCount=${result?.matchedCount}, modifiedCount=${result?.modifiedCount}`);


    if (result.matchedCount === 0) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      message: "Last login timestamp updated successfully",
      lastLogin: now.toISOString()
    });
  } catch (error) {
    console.error('Error updating last login timestamp:', error);
    return NextResponse.json(
      { error: 'Failed to update last login timestamp', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

---
description: 
globs: 
alwaysApply: false
---
# Performance Optimization Rules for StreamVista

## Core Web Vitals Focus

### Largest Contentful Paint (LCP)
- **Target**: < 2.5 seconds
- **Priority Loading**: Use `priority` prop on critical `next/image` components
- **Image Optimization**: Always use `next/image` with appropriate sizes and formats
- **Preload Critical Assets**: Use `<link rel="preload">` for critical fonts and resources
- **Minimize Blocking Resources**: Eliminate render-blocking CSS and JavaScript
- **Component-Level Code Splitting**: Use dynamic imports for heavy components

```typescript
// ✅ CORRECT
// Critical hero image with priority loading
import Image from 'next/image';

function HeroSection(): JSX.Element {
  return (
    <div className="relative h-[80vh]">
      <Image
        src="/images/hero.webp"
        alt="Stream Vista Hero"
        fill
        priority
        sizes="100vw"
        className="object-cover"
      />
      <div className="absolute inset-0 flex items-center justify-center">
        <h1 className="text-4xl md:text-6xl font-bold text-white">
          Stream Vista
        </h1>
      </div>
    </div>
  );
}
```

### First Input Delay (FID) / Interaction to Next Paint (INP)
- **Target**: < 100 milliseconds
- **Minimize JavaScript**: Keep JavaScript bundles small and focused
- **Defer Non-Critical JS**: Use `next/script` with `strategy="idle"` for non-critical scripts
- **Optimize Event Handlers**: Debounce or throttle event handlers for frequent events
- **Virtualization**: Use virtualization for long lists
- **Web Workers**: Offload heavy computation to web workers

```typescript
// ✅ CORRECT
// Optimized scroll handler with throttle
import { throttle } from 'lodash';
import { useEffect, useRef } from 'react';

function ScrollableContent(): JSX.Element {
  const containerRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    const handleScroll = throttle(() => {
      // Scroll handling logic
    }, 100);
    
    const container = containerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => {
        container.removeEventListener('scroll', handleScroll);
        handleScroll.cancel();
      };
    }
  }, []);
  
  return (
    <div 
      ref={containerRef}
      className="h-[500px] overflow-y-auto"
    >
      {/* Content */}
    </div>
  );
}
```

### Cumulative Layout Shift (CLS)
- **Target**: < 0.1
- **Reserve Space**: Always define dimensions for media elements
- **Font Loading Strategy**: Use `next/font` with proper loading strategy
- **Fixed Size UI Elements**: Avoid dynamic size changes after loading
- **Skeleton Loading**: Use skeleton UI during loading states
- **Aspect Ratio**: Use aspect ratio for media elements

```typescript
// ✅ CORRECT
// Image with proper aspect ratio and dimensions
import Image from 'next/image';

function MoviePoster({ movie }: { movie: Movie }): JSX.Element {
  return (
    <div className="relative aspect-[2/3] w-full">
      {/* Skeleton while loading */}
      <div className="absolute inset-0 bg-gray-200 animate-pulse" />
      
      <Image
        src={movie.posterUrl}
        alt={movie.title}
        fill
        sizes="(max-width: 768px) 50vw, 33vw"
        className="object-cover rounded-md"
        onLoadingComplete={(image) => {
          // Remove skeleton when image loads
          image.parentElement?.querySelector('.animate-pulse')?.remove();
        }}
      />
    </div>
  );
}
```

## Asset Optimization

### Image Optimization
- **WebP Format**: Always use WebP format for images
- **Responsive Images**: Use responsive images with multiple sizes
- **Lazy Loading**: Use native lazy loading for below-the-fold images
- **Image Component**: Always use Next.js Image component
- **Proper Sizing**: Never serve images larger than needed
- **Alt Text**: Always include alt text for accessibility

```typescript
// ✅ CORRECT
// Responsive image gallery with proper optimization
import Image from 'next/image';

function ImageGallery({ images }: { images: GalleryImage[] }): JSX.Element {
  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      {images.map((image, index) => (
        <div key={image.id} className="relative aspect-square">
          <Image
            src={image.url}
            alt={image.alt || `Gallery image ${index + 1}`}
            fill
            sizes="(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 25vw"
            loading={index < 4 ? 'eager' : 'lazy'}
            className="object-cover rounded-md hover:opacity-90 transition-opacity"
          />
        </div>
      ))}
    </div>
  );
}
```

### Font Optimization
- **Next.js Font System**: Use Next.js font system for optimal loading
- **Font Subsetting**: Use font subsetting to reduce font size
- **Variable Fonts**: Prefer variable fonts when possible
- **Locally Hosted Fonts**: Host fonts locally for better performance
- **Font Display**: Use appropriate font-display settings

```typescript
// ✅ CORRECT
// Font optimization with Next.js
import { Inter, Montserrat } from 'next/font/google';

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
});

const montserrat = Montserrat({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-montserrat',
  weight: ['400', '600', '700'],
});

function Layout({ children }: { children: React.ReactNode }): JSX.Element {
  return (
    <html lang="en" className={`${inter.variable} ${montserrat.variable}`}>
      <body className="font-sans">
        {children}
      </body>
    </html>
  );
}
```

### Script Optimization
- **Script Loading Strategies**: Use appropriate loading strategies
- **Third-Party Scripts**: Control loading of third-party scripts
- **Script Size**: Minimize script size
- **Script Execution**: Optimize script execution timing
- **Critical Scripts**: Identify and prioritize critical scripts

```typescript
// ✅ CORRECT
// Script optimization with Next.js
import Script from 'next/script';

function Layout({ children }: { children: React.ReactNode }): JSX.Element {
  return (
    <html lang="en">
      <body>
        {children}
        
        {/* Analytics - load during idle time */}
        <Script
          id="analytics"
          src="/scripts/analytics.js"
          strategy="idle"
          onLoad={() => {
            console.log('Analytics script loaded');
          }}
        />
        
        {/* Critical third-party script */}
        <Script
          id="player-api"
          src="https://player-api.example.com/script.js"
          strategy="afterInteractive"
        />
        
        {/* Inline script with specific loading strategy */}
        <Script
          id="user-tracking"
          strategy="lazyOnload"
        >
          {`
            console.log('User tracking initialized');
            // Additional tracking code
          `}
        </Script>
      </body>
    </html>
  );
}
```

## Lazy Loading and Code Splitting

### Component-Level Lazy Loading
- **Dynamic Imports**: Use Next.js dynamic imports for component-level code splitting
- **Loading States**: Always provide loading states for dynamically imported components
- **Chunking Strategy**: Group related features into chunks
- **Preloading**: Preload critical chunks when appropriate
- **Critical vs. Non-Critical**: Identify critical vs. non-critical components

```typescript
// ✅ CORRECT
// Component-level lazy loading
import dynamic from 'next/dynamic';
import { Suspense } from 'react';

// Non-critical components with lazy loading
const ChatWidget = dynamic(() => import('@/components/chat/chat-widget'), {
  loading: () => <ChatWidgetSkeleton />,
  ssr: false, // Disable SSR if component uses browser-only APIs
});

const VideoRecommendations = dynamic(
  () => import('@/components/video/recommendations'),
  { loading: () => <RecommendationsSkeleton /> }
);

function VideoPage(): JSX.Element {
  const [showChat, setShowChat] = useState<boolean>(false);
  
  return (
    <div className="grid grid-cols-12 gap-4">
      <div className="col-span-12 md:col-span-8">
        {/* Critical content - loaded eagerly */}
        <VideoPlayer videoId="abc123" />
      </div>
      
      <div className="col-span-12 md:col-span-4">
        {/* Toggle for chat widget */}
        <button 
          onClick={() => setShowChat(!showChat)}
          className="mb-4"
        >
          {showChat ? 'Hide' : 'Show'} Chat
        </button>
        
        {/* Conditionally loaded component with Suspense */}
        {showChat && (
          <Suspense fallback={<ChatWidgetSkeleton />}>
            <ChatWidget />
          </Suspense>
        )}
        
        {/* Recommendations loaded with dynamic import */}
        <VideoRecommendations />
      </div>
    </div>
  );
}
```

### Route-Level Code Splitting
- **App Router Automatic Splitting**: Leverage Next.js App Router automatic code splitting
- **Page Components**: Keep page components lightweight
- **Parallel Routes**: Use parallel routes for independent sections
- **Intercepting Routes**: Use intercepting routes for modals and slideovers
- **Loading UI**: Implement loading UI with Next.js loading.tsx

```typescript
// ✅ CORRECT
// src/app/movies/[id]/loading.tsx
function MoviePageLoading(): JSX.Element {
  return (
    <div className="space-y-4 animate-pulse">
      <div className="h-20 bg-gray-200 rounded"></div>
      <div className="h-64 bg-gray-200 rounded"></div>
      <div className="h-40 bg-gray-200 rounded"></div>
    </div>
  );
}
```

## Data Fetching Optimization

### Server Components
- **Data Fetching in Server Components**: Fetch data directly in server components
- **Database Queries**: Optimize database queries
- **Caching Strategy**: Implement proper caching strategies
- **Aggregation**: Aggregate data on the server
- **Response Size**: Minimize response size

```typescript
// ✅ CORRECT
// Data fetching in server component
import { Suspense } from 'react';
import { MovieList } from '@/components/movies/movie-list';
import { getRecommendedMovies } from '@/services/movie-service';

async function RecommendedMovies({ userId }: { userId: string }): Promise<JSX.Element> {
  // Fetch data directly in server component
  const movies = await getRecommendedMovies(userId);
  
  return <MovieList movies={movies} />;
}

export default function HomePage({ params }: { params: { userId: string } }): JSX.Element {
  return (
    <section>
      <h2 className="text-2xl font-bold mb-4">Recommended For You</h2>
      <Suspense fallback={<MovieListSkeleton />}>
        {/* @ts-expect-error Async Server Component */}
        <RecommendedMovies userId={params.userId} />
      </Suspense>
    </section>
  );
}
```

### Client-Side Data Fetching
- **React Query / SWR**: Use React Query or SWR for client-side data fetching
- **Caching**: Implement proper caching
- **Stale-While-Revalidate**: Use stale-while-revalidate pattern
- **Optimistic Updates**: Implement optimistic updates for mutations
- **Error Handling**: Handle errors gracefully

```typescript
// ✅ CORRECT
// Client-side data fetching with React Query
'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { fetchUserProfile, updateUserPreferences } from '@/services/api';

function UserPreferences({ userId }: { userId: string }): JSX.Element {
  const queryClient = useQueryClient();
  
  // Query for user data
  const { data: user, isLoading, error } = useQuery({
    queryKey: ['user', userId],
    queryFn: () => fetchUserProfile(userId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
  
  // Mutation for updating preferences
  const { mutate, isPending } = useMutation({
    mutationFn: updateUserPreferences,
    onMutate: async (newPreferences) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['user', userId] });
      
      // Snapshot previous value
      const previousUser = queryClient.getQueryData(['user', userId]);
      
      // Optimistically update to the new value
      queryClient.setQueryData(['user', userId], (old) => ({
        ...old,
        preferences: newPreferences,
      }));
      
      // Return context with the previous value
      return { previousUser };
    },
    onError: (err, newPreferences, context) => {
      // Revert to previous value if mutation fails
      queryClient.setQueryData(['user', userId], context?.previousUser);
    },
    onSettled: () => {
      // Refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['user', userId] });
    },
  });
  
  if (isLoading) return <UserSkeleton />;
  if (error) return <ErrorDisplay error={error} />;
  if (!user) return <div>User not found</div>;
  
  return (
    // UI implementation
  );
}
```

## Rendering Strategies

### Static vs. Dynamic Rendering
- **Static Generation**: Use static generation for content that doesn't change frequently
- **Dynamic Rendering**: Use dynamic rendering for personalized or frequently changing content
- **Revalidation**: Implement incremental static regeneration with revalidation
- **Hybrid Approach**: Combine static and dynamic rendering where appropriate
- **Route Segmentation**: Segment routes by rendering strategy

```typescript
// ✅ CORRECT
// Static page with revalidation
export const revalidate = 3600; // Revalidate every hour

async function MovieCatalogPage(): Promise<JSX.Element> {
  const movies = await getMovieCatalog();
  
  return (
    <div>
      <h1 className="text-3xl font-bold mb-6">Movie Catalog</h1>
      <MovieGrid movies={movies} />
    </div>
  );
}
```

### Streaming and Suspense
- **Streaming with Suspense**: Use React Suspense for streaming responses
- **Progressive Loading**: Implement progressive loading of UI
- **Waterfall Prevention**: Prevent data fetching waterfalls
- **Parallel Data Fetching**: Fetch data in parallel
- **Priority Content**: Prioritize critical content

```typescript
// ✅ CORRECT
// Streaming with Suspense
import { Suspense } from 'react';

function MovieDetailsPage({ movieId }: { movieId: string }): JSX.Element {
  return (
    <div className="grid grid-cols-12 gap-6">
      {/* Critical path - load first */}
      <div className="col-span-12">
        <Suspense fallback={<MovieHeaderSkeleton />}>
          {/* @ts-expect-error Async Server Component */}
          <MovieHeader movieId={movieId} />
        </Suspense>
      </div>
      
      {/* Split the UI into chunks that can load independently */}
      <div className="col-span-12 md:col-span-8">
        <Suspense fallback={<MovieDetailsSkeleton />}>
          {/* @ts-expect-error Async Server Component */}
          <MovieDetails movieId={movieId} />
        </Suspense>
      </div>
      
      <div className="col-span-12 md:col-span-4">
        {/* Lower priority content */}
        <Suspense fallback={<RelatedMoviesSkeleton />}>
          {/* @ts-expect-error Async Server Component */}
          <RelatedMovies movieId={movieId} />
        </Suspense>
        
        <Suspense fallback={<ReviewsSkeleton />}>
          {/* @ts-expect-error Async Server Component */}
          <MovieReviews movieId={movieId} />
        </Suspense>
      </div>
    </div>
  );
}
```

## Component Optimization

### Virtualization
- **Virtualize Long Lists**: Use React Window or similar for long lists
- **Virtual Scrolling**: Implement virtual scrolling for large datasets
- **Windowing**: Only render items in viewport
- **Chunked Rendering**: Render data in chunks
- **Grid Virtualization**: Virtualize grid layouts

```typescript
// ✅ CORRECT
// List virtualization with react-window
'use client';

import { FixedSizeList } from 'react-window';
import AutoSizer from 'react-virtualized-auto-sizer';

function VirtualizedMovieList({ movies }: { movies: Movie[] }): JSX.Element {
  const Row = ({ index, style }: { index: number; style: React.CSSProperties }): JSX.Element => {
    const movie = movies[index];
    return (
      <div style={style} className="p-4 border-b">
        <h3 className="font-semibold">{movie.title}</h3>
        <p className="text-sm text-gray-600">{movie.year}</p>
      </div>
    );
  };
  
  return (
    <div className="h-[500px] w-full">
      <AutoSizer>
        {({ height, width }) => (
          <FixedSizeList
            height={height}
            width={width}
            itemCount={movies.length}
            itemSize={80}
          >
            {Row}
          </FixedSizeList>
        )}
      </AutoSizer>
    </div>
  );
}
```

### Memoization
- **React.memo**: Use React.memo for expensive components
- **useMemo**: Use useMemo for expensive calculations
- **useCallback**: Use useCallback for stable callback references
- **Dependency Arrays**: Carefully manage dependency arrays
- **Performance Measurement**: Measure before optimizing

```typescript
// ✅ CORRECT
// Proper memoization pattern
'use client';

import { useState, useMemo, useCallback } from 'react';

interface FilterOptions {
  genre?: string;
  year?: number;
  rating?: number;
}

function MovieFilterSystem({ movies }: { movies: Movie[] }): JSX.Element {
  const [filters, setFilters] = useState<FilterOptions>({});
  
  // Memoize expensive filtering operation
  const filteredMovies = useMemo(() => {
    return movies.filter(movie => {
      if (filters.genre && movie.genre !== filters.genre) return false;
      if (filters.year && movie.year !== filters.year) return false;
      if (filters.rating && movie.rating < filters.rating) return false;
      return true;
    });
  }, [movies, filters.genre, filters.year, filters.rating]);
  
  // Stable callback reference
  const handleFilterChange = useCallback((newFilters: Partial<FilterOptions>): void => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);
  
  return (
    <div>
      <MovieFilters 
        currentFilters={filters} 
        onFilterChange={handleFilterChange} 
      />
      <div className="mt-4">
        <p>Found {filteredMovies.length} movies</p>
        <MovieList movies={filteredMovies} />
      </div>
    </div>
  );
}

// Memoized child component that only re-renders when props change
const MovieFilters = React.memo(function MovieFilters({ 
  currentFilters, 
  onFilterChange 
}: { 
  currentFilters: FilterOptions; 
  onFilterChange: (filters: Partial<FilterOptions>) => void;
}): JSX.Element {
  // Implementation
  return (
    // Filter UI
  );
});
```

## Network Optimization

### API Request Optimization
- **Request Batching**: Batch multiple API requests
- **GraphQL**: Use GraphQL for flexible data fetching
- **Request Deduplication**: Deduplicate identical requests
- **Request Cancellation**: Cancel stale requests
- **Request Prioritization**: Prioritize critical requests

```typescript
// ✅ CORRECT
// API request optimization with request batching and cancellation
'use client';

import { useState, useEffect } from 'react';
import axios, { CancelTokenSource } from 'axios';

function UserDashboard({ userId }: { userId: string }): JSX.Element {
  const [userData, setUserData] = useState<UserData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  
  useEffect(() => {
    // Create cancel token source
    const source: CancelTokenSource = axios.CancelToken.source();
    
    async function loadUserData(): Promise<void> {
      try {
        setIsLoading(true);
        
        // Batch multiple API requests in one call
        const [userResponse, activityResponse, preferencesResponse] = await Promise.all([
          axios.get(`/api/users/${userId}`, { cancelToken: source.token }),
          axios.get(`/api/users/${userId}/activity`, { cancelToken: source.token }),
          axios.get(`/api/users/${userId}/preferences`, { cancelToken: source.token })
        ]);
        
        // Combine the data
        setUserData({
          profile: userResponse.data,
          activity: activityResponse.data,
          preferences: preferencesResponse.data
        });
      } catch (error) {
        if (!axios.isCancel(error)) {
          console.error('Error fetching user data:', error);
        }
      } finally {
        setIsLoading(false);
      }
    }
    
    void loadUserData();
    
    // Clean up - cancel requests on unmount or userId change
    return () => {
      source.cancel('Component unmounted');
    };
  }, [userId]);
  
  if (isLoading) return <LoadingSkeleton />;
  if (!userData) return <div>Error loading user data</div>;
  
  return (
    // Dashboard UI
  );
}
```

### Caching Strategy
- **HTTP Caching**: Configure proper HTTP cache headers
- **State Caching**: Implement client-side state caching
- **Static vs. Dynamic**: Cache static content aggressively
- **Revalidation**: Implement stale-while-revalidate pattern
- **Cache Invalidation**: Implement proper cache invalidation

```typescript
// ✅ CORRECT
// Server caching with revalidation
// src/app/api/movies/route.ts
import { NextResponse } from 'next/server';
import { getMovies } from '@/services/movie-service';

export async function GET(): Promise<NextResponse> {
  const movies = await getMovies();
  
  // Cache for 1 hour at the edge, stale while revalidate for up to 1 day
  return NextResponse.json(
    { movies },
    {
      headers: {
        'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=86400',
      },
    }
  );
}
```

## Performance Monitoring

### Metrics Collection
- **Core Web Vitals**: Monitor Core Web Vitals
- **Custom Metrics**: Implement custom performance metrics
- **Real User Monitoring**: Collect real user monitoring data
- **Error Tracking**: Track JavaScript errors
- **API Performance**: Monitor API performance

```typescript
// ✅ CORRECT
// Performance monitoring setup
// src/lib/performance-monitoring.ts
import { onCLS, onFID, onLCP, onINP } from 'web-vitals';

export function initPerformanceMonitoring(): void {
  // Initialize performance monitoring
  
  // Monitor Core Web Vitals
  onCLS(sendToAnalytics);
  onFID(sendToAnalytics);
  onLCP(sendToAnalytics);
  onINP(sendToAnalytics);
  
  // Custom metrics
  if (typeof window !== 'undefined') {
    // Track time to first meaningful paint
    const paintTimingEntries = performance.getEntriesByType('paint');
    const firstPaint = paintTimingEntries.find(entry => entry.name === 'first-paint');
    const firstContentfulPaint = paintTimingEntries.find(
      entry => entry.name === 'first-contentful-paint'
    );
    
    if (firstPaint) {
      sendToAnalytics({
        name: 'FP',
        value: firstPaint.startTime,
        id: 'FP'
      });
    }
    
    if (firstContentfulPaint) {
      sendToAnalytics({
        name: 'FCP',
        value: firstContentfulPaint.startTime,
        id: 'FCP'
      });
    }
    
    // Track TTFB
    const navigationEntries = performance.getEntriesByType('navigation');
    if (navigationEntries.length > 0) {
      const navigationEntry = navigationEntries[0] as PerformanceNavigationTiming;
      sendToAnalytics({
        name: 'TTFB',
        value: navigationEntry.responseStart,
        id: 'TTFB'
      });
    }
  }
}

function sendToAnalytics(metric: { name: string; value: number; id: string }): void {
  // Send metrics to analytics service
  console.log(metric);
  
  // Example implementation:
  // fetch('/api/metrics', {
  //   method: 'POST',
  //   headers: { 'Content-Type': 'application/json' },
  //   body: JSON.stringify(metric),
  //   keepalive: true
  // });
}

// Initialize on app mount
// src/app/layout.tsx
'use client';

import { useEffect } from 'react';
import { initPerformanceMonitoring } from '@/lib/performance-monitoring';

function PerformanceMonitoring(): null {
  useEffect(() => {
    initPerformanceMonitoring();
  }, []);
  
  return null;
}

export default function RootLayout({ 
  children 
}: { 
  children: React.ReactNode 
}): JSX.Element {
  return (
    <html lang="en">
      <body>
        {children}
        <PerformanceMonitoring />
      </body>
    </html>
  );
}
```

### Performance Debugging
- **React Profiler**: Use React Profiler for component performance
- **Browser DevTools**: Use browser DevTools for performance analysis
- **Lighthouse**: Run Lighthouse audits
- **Next.js Analytics**: Use Next.js Analytics
- **Bundle Analysis**: Analyze JavaScript bundles

```typescript
// ✅ CORRECT
// Performance debugging with React Profiler
'use client';

import { Profiler, ProfilerOnRenderCallback } from 'react';

const onRenderCallback: ProfilerOnRenderCallback = (
  id,
  phase,
  actualDuration,
  baseDuration,
  startTime,
  commitTime
) => {
  if (process.env.NODE_ENV === 'development') {
    console.log({
      id,
      phase,
      actualDuration,
      baseDuration,
      startTime,
      commitTime,
    });
  }
};

function ProfiledComponent({ children }: { children: React.ReactNode }): JSX.Element {
  return (
    <Profiler id="App" onRender={onRenderCallback}>
      {children}
    </Profiler>
  );
}
```


import { useState } from 'react';
import { <PERSON>r, <PERSON>tings, LogOut, ChevronRight, CreditCard, Bell, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { UserAvatar } from '@/components/UserAvatar';
import { Switch } from '@/components/ui/switch';
import Image from 'next/image';

interface Profile {
  id: string;
  name: string;
  avatar: string;
  isKids?: boolean;
}

interface UserProfileProps {
  userName: string;
  email: string;
  subscription: {
    plan: string;
    renewalDate: string;
    price: string;
  };
  profiles: Profile[];
  onSignOut: () => void;
}

export default function UserProfile({
  userName,
  email,
  subscription,
  profiles,
  onSignOut
}: UserProfileProps) {
  const [activeSection, setActiveSection] = useState<'account' | 'profiles' | 'billing'>('account');
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    newReleaseAlerts: true
  });

  const toggleNotification = (key: keyof typeof notificationSettings) => {
    setNotificationSettings(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  return (
    <div className="min-h-screen bg-vista-dark text-vista-light pb-16">
      <div className="max-w-4xl mx-auto px-8 pt-12">
        <h1 className="text-3xl font-bold mb-8">Account & Settings</h1>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Sidebar Navigation */}
          <div className="col-span-1">
            <div className="space-y-1">
              <Button
                variant="ghost"
                className={`w-full justify-start ${activeSection === 'account' ? 'bg-vista-light/10' : ''}`}
                onClick={() => setActiveSection('account')}
              >
                <User className="h-4 w-4 mr-2" />
                Account
              </Button>
              <Button
                variant="ghost"
                className={`w-full justify-start ${activeSection === 'profiles' ? 'bg-vista-light/10' : ''}`}
                onClick={() => setActiveSection('profiles')}
              >
                <Users className="h-4 w-4 mr-2" />
                Profiles
              </Button>
              <Button
                variant="ghost"
                className={`w-full justify-start ${activeSection === 'billing' ? 'bg-vista-light/10' : ''}`}
                onClick={() => setActiveSection('billing')}
              >
                <CreditCard className="h-4 w-4 mr-2" />
                Billing
              </Button>
              <Separator className="my-4 bg-vista-light/10" />
              <Button
                variant="ghost"
                className="w-full justify-start text-red-400 hover:text-red-300 hover:bg-red-400/10"
                onClick={onSignOut}
              >
                <LogOut className="h-4 w-4 mr-2" />
                Sign Out
              </Button>
            </div>
          </div>

          {/* Main Content */}
          <div className="col-span-1 md:col-span-3 space-y-8">
            {activeSection === 'account' && (
              <>
                <div className="bg-vista-dark-lighter rounded-lg p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <UserAvatar
                        src="/favicon.svg"
                        alt={userName}
                        size="lg"
                        status="online"
                      />
                      <div>
                        <h2 className="text-xl font-semibold mb-1">{userName}</h2>
                        <p className="text-vista-light/70">{email}</p>
                      </div>
                    </div>
                    <Button variant="outline" size="sm" className="border-vista-light/20 bg-transparent hover:bg-vista-light/10">
                      <Settings className="h-4 w-4 mr-2" /> Edit
                    </Button>
                  </div>
                </div>

                <div className="bg-vista-dark-lighter rounded-lg p-6">
                  <h3 className="text-lg font-medium mb-4">Notifications</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Bell className="h-5 w-5 mr-3 text-vista-light/70" />
                        <span>Email Notifications</span>
                      </div>
                      <Switch
                        checked={notificationSettings.emailNotifications}
                        onCheckedChange={() => toggleNotification('emailNotifications')}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Bell className="h-5 w-5 mr-3 text-vista-light/70" />
                        <span>New Release Alerts</span>
                      </div>
                      <Switch
                        checked={notificationSettings.newReleaseAlerts}
                        onCheckedChange={() => toggleNotification('newReleaseAlerts')}
                      />
                    </div>
                  </div>
                </div>
              </>
            )}

            {activeSection === 'profiles' && (
              <div className="bg-vista-dark-lighter rounded-lg p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-lg font-medium">Profiles & Parental Controls</h2>
                  <Button variant="outline" size="sm" className="border-vista-light/20 bg-transparent hover:bg-vista-light/10">
                    Add Profile
                  </Button>
                </div>

                <div className="space-y-4">
                  {profiles.map((profile) => (
                    <div key={profile.id} className="flex items-center justify-between p-2 rounded-md hover:bg-vista-light/5 cursor-pointer">
                      <div className="flex items-center">
                        <div className="relative mr-4">
                          <UserAvatar
                            src={profile.avatar}
                            alt={profile.name}
                            size="md"
                            className={profile.isKids ? "border-2 border-yellow-500" : ""}
                          />
                          {profile.isKids && (
                            <div className="absolute bottom-0 left-0 right-0 bg-yellow-500 text-[10px] text-center text-vista-dark font-medium rounded-b-full">
                              KIDS
                            </div>
                          )}
                        </div>
                        <span>{profile.name}</span>
                      </div>
                      <ChevronRight className="h-4 w-4 text-vista-light/50" />
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeSection === 'billing' && (
              <div className="bg-vista-dark-lighter rounded-lg p-6">
                <h2 className="text-lg font-medium mb-4">Subscription & Billing</h2>

                <div className="space-y-6">
                  <div>
                    <h3 className="text-vista-light/70 text-sm font-medium mb-1">Current Plan</h3>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-semibold">{subscription.plan}</p>
                        <p className="text-sm text-vista-light/70">Renews on {subscription.renewalDate}</p>
                      </div>
                      <p className="font-bold text-lg">{subscription.price}</p>
                    </div>
                  </div>

                  <Separator className="bg-vista-light/10" />

                  <div className="flex items-center justify-between">
                    <span>Change Plan</span>
                    <Button variant="outline" size="sm" className="border-vista-light/20 bg-transparent hover:bg-vista-light/10">
                      View Plans
                    </Button>
                  </div>

                  <div className="flex items-center justify-between">
                    <span>Payment Information</span>
                    <Button variant="outline" size="sm" className="border-vista-light/20 bg-transparent hover:bg-vista-light/10">
                      Update
                    </Button>
                  </div>

                  <div className="flex items-center justify-between">
                    <span>Billing History</span>
                    <Button variant="outline" size="sm" className="border-vista-light/20 bg-transparent hover:bg-vista-light/10">
                      View History
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

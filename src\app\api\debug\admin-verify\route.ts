import { NextRequest, NextResponse } from 'next/server';
import { verifyAdmin } from '@/lib/admin-auth';

/**
 * GET /api/debug/admin-verify
 * Test admin verification specifically for banner creation debugging
 */
export async function GET(request: NextRequest) {
  try {
    console.log('=== ADMIN VERIFICATION DEBUG START ===');
    
    const startTime = Date.now();
    const result = await verifyAdmin(request);
    const endTime = Date.now();
    
    console.log('=== ADMIN VERIFICATION DEBUG END ===');
    
    if (result && result.isAuthorized) {
      return NextResponse.json({
        success: true,
        message: 'Admin verification successful',
        adminSession: {
          isAuthorized: result.isAuthorized,
          userId: result.userId,
          userEmail: result.user?.email,
          userRole: result.user?.role
        },
        verificationTime: `${endTime - startTime}ms`,
        environment: process.env.NODE_ENV,
        timestamp: new Date().toISOString()
      });
    } else {
      return NextResponse.json({
        success: false,
        message: 'Admin verification failed',
        adminSession: result,
        possibleReasons: [
          'No userId cookie found',
          'User not found in database',
          'User does not have admin role',
          'Database connection failed',
          'Authentication error'
        ],
        recommendations: [
          'Check if you are logged in',
          'Verify your user account exists',
          'Ensure your user has admin or superadmin role',
          'Check database connectivity'
        ],
        verificationTime: `${endTime - startTime}ms`,
        environment: process.env.NODE_ENV,
        timestamp: new Date().toISOString()
      }, { status: 401 });
    }
    
  } catch (error) {
    console.error('Admin verification debug error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Admin verification debug failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      environment: process.env.NODE_ENV,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * POST /api/debug/admin-verify
 * Test the exact same verification logic used in banner creation
 */
export async function POST(request: NextRequest) {
  try {
    console.log('=== BANNER CREATION AUTH SIMULATION START ===');
    
    // Simulate the exact same logic as in banner creation
    const adminSession = await verifyAdmin(request);
    
    if (!adminSession || !adminSession.isAuthorized) {
      console.log('Banner creation would fail here - adminSession:', adminSession);
      
      return NextResponse.json({
        success: false,
        message: 'This is exactly why banner creation fails',
        adminSession: adminSession,
        wouldFailWith: {
          error: "Unauthorized access",
          details: "Admin authentication failed. Please ensure you're logged in as an admin."
        },
        environment: process.env.NODE_ENV,
        timestamp: new Date().toISOString()
      }, { status: 401 });
    }
    
    console.log('Banner creation auth would succeed with:', adminSession);
    
    return NextResponse.json({
      success: true,
      message: 'Banner creation authentication would succeed',
      adminSession: {
        isAuthorized: adminSession.isAuthorized,
        userId: adminSession.userId,
        userEmail: adminSession.user?.email,
        userRole: adminSession.user?.role
      },
      nextStep: 'Try creating a banner - it should work now',
      environment: process.env.NODE_ENV,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Banner creation auth simulation error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Banner creation auth simulation failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      wouldFailWith: {
        error: 'Failed to create banner ad',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      environment: process.env.NODE_ENV,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
} 
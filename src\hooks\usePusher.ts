"use client"

import { useState, useEffect } from 'react'
import { pusherClient } from '@/lib/pusher-client'

type ConnectionStatus = 'connected' | 'connecting' | 'unavailable' | 'failed' | 'disconnected'

interface PusherConnectionState {
  status: ConnectionStatus
  reconnectIn: number | null
  connectionCount: number
  lastConnected: Date | null
}

export function usePusher() {
  const [connectionState, setConnectionState] = useState<PusherConnectionState>({
    status: 'connecting',
    reconnectIn: null,
    connectionCount: 0,
    lastConnected: null
  })
  
  useEffect(() => {
    // Track connection attempts
    let reconnectInterval: NodeJS.Timeout | null = null
    let reconnectSeconds = 0
    let connectionCounter = 0
    
    function updateReconnectTimer() {
      if (reconnectInterval) clearInterval(reconnectInterval)
      
      reconnectSeconds = 30
      setConnectionState(prev => ({
        ...prev,
        reconnectIn: reconnectSeconds
      }))
      
      reconnectInterval = setInterval(() => {
        reconnectSeconds -= 1
        setConnectionState(prev => ({
          ...prev,
          reconnectIn: reconnectSeconds > 0 ? reconnectSeconds : null
        }))
        
        if (reconnectSeconds <= 0 && reconnectInterval) {
          clearInterval(reconnectInterval)
          reconnectInterval = null
        }
      }, 1000)
    }
    
    // Create a simple channel to monitor connection state
    const channelName = `monitor-${Date.now()}`
    let isConnected = false
    
    try {
      // Try to subscribe to test connection
      const monitorChannel = pusherClient.subscribe(channelName)
      
      // Set up event handlers using the channel
      const handleConnectionSuccess = () => {
        connectionCounter += 1
        isConnected = true
        
        if (reconnectInterval) {
          clearInterval(reconnectInterval)
          reconnectInterval = null
        }
        
        setConnectionState({
          status: 'connected',
          reconnectIn: null,
          connectionCount: connectionCounter,
          lastConnected: new Date()
        })
      }
      
      // Set initial status to connecting
      setConnectionState(prev => ({...prev, status: 'connecting'}))
      
      // Listen for successful subscription as connection indicator
      monitorChannel.bind('pusher:subscription_succeeded', handleConnectionSuccess)
      
      // Create a ping mechanism to check connection status
      const pingInterval = setInterval(() => {
        try {
          // If we can't subscribe/unsubscribe, we're disconnected
          const pingChannel = `ping-${Date.now()}`
          pusherClient.subscribe(pingChannel)
          pusherClient.unsubscribe(pingChannel)
          
          // If we were disconnected before, mark as connected
          if (!isConnected) {
            handleConnectionSuccess()
          }
        } catch (error) {
          // If subscription fails, we're disconnected
          if (isConnected) {
            isConnected = false
            setConnectionState(prev => ({
              ...prev, 
              status: 'unavailable'
            }))
            updateReconnectTimer()
          }
        }
      }, 10000)
      
      // Mark as connected on initial successful setup
      handleConnectionSuccess()
      
      // Cleanup function
      return () => {
        if (reconnectInterval) {
          clearInterval(reconnectInterval)
        }
        
        if (pingInterval) {
          clearInterval(pingInterval)
        }
        
        try {
          monitorChannel.unbind('pusher:subscription_succeeded', handleConnectionSuccess)
          pusherClient.unsubscribe(channelName)
        } catch (error) {
          console.error('Error cleaning up Pusher monitor:', error)
        }
      }
    } catch (error) {
      // If we can't even create a subscription, we're disconnected
      console.error('Pusher connection error:', error)
      setConnectionState({
        status: 'failed',
        reconnectIn: null,
        connectionCount: 0,
        lastConnected: null
      })
      
      // Try to reconnect
      updateReconnectTimer()
      
      // Return empty cleanup function
      return () => {
        if (reconnectInterval) {
          clearInterval(reconnectInterval)
        }
      }
    }
  }, [])
  
  return connectionState
} 
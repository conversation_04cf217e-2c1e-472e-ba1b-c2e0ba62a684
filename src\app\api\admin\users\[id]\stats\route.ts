import { NextRequest, NextResponse } from 'next/server';
import { getAdminUserId } from '@/lib/admin-auth';
import { ensureMongooseConnection } from '@/lib/mongodb';

// Define the cache data type
type CacheData = NextResponse | null;

// Simple in-memory cache implementation
class SimpleCache {
  private cache: Map<string, { data: CacheData; expiry: number }> = new Map();

  get(key: string): CacheData {
    const item = this.cache.get(key);
    if (!item) return null;

    // Check if expired
    if (item.expiry < Date.now()) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  set(key: string, data: CacheData, ttlSeconds: number): void {
    const expiry = Date.now() + (ttlSeconds * 1000);
    this.cache.set(key, { data, expiry });
  }
}

const cache = new SimpleCache();

/**
 * GET /api/admin/users/[id]/stats
 * Get detailed statistics for a specific user
 */
export async function GET(
  request: NextRequest,
  context: { params: { id: string } }
) {
  // Store the id from params safely at the beginning - properly awaiting context.params
  const targetUserId = (await context.params).id;

  try {
    // Verify admin authentication
    const auth = await getAdminUserId(request);
    if (!auth.isAuthorized) {
      return auth.error;
    }

    const userId = auth.userId;

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Get the User model directly
    const UserSchema = new mongoose.default.Schema({
      name: String,
      email: String,
      role: String,
      lastLogin: Date,
      subscription: String,
      subscriptionStatus: String,
      subscriptionRenewal: Date
    });

    const User = mongoose.default.models.User ||
                mongoose.default.model('User', UserSchema);

    // Define the UserActivity schema directly
    const UserActivitySchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      action: String,
      details: String,
      timestamp: { type: Date, default: Date.now },
      metadata: mongoose.default.Schema.Types.Mixed
    }, {
      timestamps: true
    });

    // Get the UserActivity model
    const UserActivity = mongoose.default.models.UserActivity ||
                        mongoose.default.model('UserActivity', UserActivitySchema);

    // Define the UserContentInteraction schema directly
    const UserContentInteractionSchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      contentId: mongoose.default.Schema.Types.ObjectId,
      interactionType: String,
      duration: Number
    }, {
      timestamps: true
    });

    // Get the UserContentInteraction model
    const UserContentInteraction = mongoose.default.models.UserContentInteraction ||
                                  mongoose.default.model('UserContentInteraction', UserContentInteractionSchema);

    // Define the Subscription schema directly
    const SubscriptionSchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      plan: String,
      status: String,
      startDate: Date,
      renewalDate: Date,
      price: Number,
      interval: String
    }, {
      timestamps: true
    });

    // Get the Subscription model
    const Subscription = mongoose.default.models.Subscription ||
                        mongoose.default.model('Subscription', SubscriptionSchema);

    // Define the Transaction schema directly
    const TransactionSchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      status: String,
      amount: Number
    }, {
      timestamps: true
    });

    // Get the Transaction model
    const Transaction = mongoose.default.models.Transaction ||
                       mongoose.default.model('Transaction', TransactionSchema);

    // Validate user ID using the stored targetUserId
    if (!mongoose.default.Types.ObjectId.isValid(targetUserId)) {
      return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
    }

    // Check if we should bypass cache (if there's a timestamp parameter)
    const url = new URL(request.url);
    const timestamp = url.searchParams.get('t');
    const bypassCache = !!timestamp;

    // Use cache to reduce database load - cache for 1 minute (60 seconds) unless bypassed
    const cacheKey = `user_stats_${targetUserId}`;
    const cachedData = bypassCache ? null : cache.get(cacheKey);

    if (cachedData !== null) {
      return cachedData;
    }

    // If not in cache, fetch the data
    try {
        // Check if user exists using targetUserId
        const targetUser = await User.findById(targetUserId);
        if (!targetUser) {
          return NextResponse.json({ error: 'User not found' }, { status: 404 });
        }

        // Get last login time from activity logs - with a time limit to improve performance
        const lastLoginActivity = await UserActivity.findOne({
          userId: targetUserId,
          type: 'auth',
          action: 'login',
          // Only look at logins from the last 90 days to improve performance
          timestamp: { $gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) }
        }).sort({ timestamp: -1 });

        // Get total watch time from content interactions - with a time limit to improve performance
        const watchTimeStats = await UserContentInteraction.aggregate([
          {
            $match: {
              userId: new mongoose.default.Types.ObjectId(targetUserId),
              interactionType: 'view',
              // Only consider interactions from the last 90 days to improve performance
              createdAt: { $gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) }
            }
          },
          {
            $group: {
              _id: null,
              totalWatchTime: { $sum: '$duration' },
              count: { $sum: 1 }
            }
          },
          // Limit to improve performance
          { $limit: 1 }
        ]);

        // Get favorite genres based on content interactions - with optimizations
        const genreStats = await UserContentInteraction.aggregate([
          {
            $match: {
              userId: new mongoose.default.Types.ObjectId(targetUserId),
              // Only consider interactions from the last 90 days
              createdAt: { $gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) }
            }
          },
          // Limit the number of documents to process for better performance
          { $limit: 100 },
          {
            $lookup: {
              from: 'contents',
              localField: 'contentId',
              foreignField: '_id',
              as: 'content'
            }
          },
          { $unwind: '$content' },
          { $unwind: '$content.genres' },
          {
            $group: {
              _id: '$content.genres',
              count: { $sum: 1 }
            }
          },
          { $sort: { count: -1 } },
          { $limit: 5 }
        ]);

        // Format the results
        const totalWatchTime = watchTimeStats.length > 0 ? watchTimeStats[0].totalWatchTime || 0 : 0;
        const favoriteGenres = genreStats.map(genre => genre._id);

        // Get subscription data
        const currentSubscription = await Subscription.findOne({
          userId: targetUserId,
          status: { $in: ['active', 'pending'] }
        });

        // Get total spent
        const transactions = await Transaction.find({
          userId: targetUserId,
          status: 'completed',
          type: { $in: ['subscription', 'one_time'] }
        });

        const totalSpent = transactions.reduce((sum, tx) => sum + (tx.amount || 0), 0);

        // Return user statistics
        const stats = {
          lastLogin: lastLoginActivity ? lastLoginActivity.timestamp : targetUser.lastLogin,
          totalWatchTime,
          favoriteGenres,
          subscription: currentSubscription ? {
            id: currentSubscription._id.toString(),
            plan: currentSubscription.plan,
            status: currentSubscription.status,
            startDate: currentSubscription.startDate,
            renewalDate: currentSubscription.renewalDate,
            price: currentSubscription.price,
            interval: currentSubscription.interval
          } : {
            plan: targetUser.subscription || 'free',
            status: targetUser.subscriptionStatus || 'active',
            renewalDate: targetUser.subscriptionRenewal
          },
          totalSpent,
          transactionCount: transactions.length,
          activitySummary: {
            // Only count recent logins (last 30 days) to improve performance
            logins: await UserActivity.countDocuments({
              userId: targetUserId,
              type: 'auth',
              action: 'login',
              timestamp: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
            }),
            // Only count recent content views (last 30 days)
            contentViews: await UserContentInteraction.countDocuments({
              userId: targetUserId,
              interactionType: 'view',
              createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
            }),
            // Only count recent profile updates (last 30 days)
            profileUpdates: await UserActivity.countDocuments({
              userId: targetUserId,
              type: 'profile',
              timestamp: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
            })
          }
        };

        const response = NextResponse.json(stats);

        // Store in cache for future use (1 minute TTL)
        cache.set(cacheKey, response, 60);

        return response;
    } catch (innerError) {
      console.error('Error in stats data fetching:', innerError);
      throw innerError; // Re-throw to be caught by the outer catch block
    }
  } catch (error) {
    console.error('Error fetching user statistics:', error);
    // Ensure we always return valid JSON
    return NextResponse.json(
      {
        error: 'Failed to fetch user statistics',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

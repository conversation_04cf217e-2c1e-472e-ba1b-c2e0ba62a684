# Pusher Configuration for Watch Party

## Overview

StreamVista now uses <PERSON><PERSON><PERSON> exclusively for real-time communication in the Watch Party feature. This document explains how to configure and use Pusher for watch parties.

## Required Environment Variables

The following environment variables must be set for <PERSON><PERSON><PERSON> to work properly:

```
PUSHER_APP_ID=your_app_id
PUSHER_KEY=your_public_key
PUSHER_SECRET=your_secret_key
PUSHER_CLUSTER=your_cluster_region
NEXT_PUBLIC_PUSHER_KEY=your_public_key
NEXT_PUBLIC_PUSHER_CLUSTER=your_cluster_region
```

## Setup Instructions

1. **Create a Pusher Account**
   - Go to [pusher.com](https://pusher.com) and create an account
   - Create a new Channels app

2. **Get Your Credentials**
   - In your Pusher dashboard, select your app
   - Navigate to the "App Keys" section
   - Copy the App ID, Key, Secret, and Cluster values

3. **Configure Environment Variables**
   - Add the variables to your `.env.local` file
   - For production, add these variables to your hosting environment

4. **Enable Client Events (Required)**
   - In your Pusher dashboard, go to "App Settings"
   - Enable "Client Events"
   - This is necessary for watch party synchronization

## Channel Structure

The watch party feature uses the following Pusher channel structure:

- `watch-parties` - Main channel for global party listings and updates
- `watch-party-{partyId}` - Individual channels for each active watch party

## Events

The application uses these Pusher events:

- `party-created` - New party created
- `party-update` - Party details updated
- `party-deleted` - Party deleted
- `member-update` - Party member list changed
- `playback-update` - Video playback state changed
- `new-message` - New chat message sent

## Troubleshooting

If you encounter issues with the watch party feature:

1. **Check Browser Console**
   - Look for messages prefixed with `[WatchParty]` or `[PUSHER]`

2. **Verify Environment Variables**
   - Ensure all required Pusher variables are correctly set

3. **Monitor Pusher Dashboard**
   - Check the Pusher dashboard for connection data and errors
   - Monitor the Debug Console in your Pusher app for real-time events

4. **Connection Issues**
   - If users can't connect, check network conditions
   - Verify that your app's domain is allowed in Pusher's CORS settings

## Notes

- The Socket.io implementation has been completely removed
- All watch party communication now happens through Pusher
- The system is designed for reliability and will handle reconnections automatically 
'use client'

import React, { useRef, useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { cn } from '@/lib/utils'
import { motion, AnimatePresence } from 'framer-motion'

interface CustomScrollProps {
  children: React.ReactNode
  className?: string
  containerClassName?: string
  itemClassName?: string
  withControls?: boolean
  showScrollbar?: boolean
  controlSize?: 'sm' | 'md' | 'lg'
  controlPosition?: 'center' | 'top' | 'bottom'
  scrollAmount?: number
  scrollBehavior?: ScrollBehavior
}

export function CustomScroll({
  children,
  className = '',
  containerClassName = '',
  itemClassName = '',
  withControls = false,
  showScrollbar = false,
  controlSize = 'md',
  controlPosition = 'center',
  scrollAmount = 0.8, // 80% of container width by default
  scrollBehavior = 'smooth'
}: CustomScrollProps) {
  const scrollRef = useRef<HTMLDivElement>(null)
  const [showLeftControl, setShowLeftControl] = useState(false)
  const [showRightControl, setShowRightControl] = useState(false)
  const [isScrolling, setIsScrolling] = useState(false)

  const childrenArray = React.Children.toArray(children)

  const handleScroll = () => {
    if (!scrollRef.current) return

    const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current
    setShowLeftControl(scrollLeft > 5) // Add a small threshold
    setShowRightControl(scrollLeft < scrollWidth - clientWidth - 5) // Add a small threshold
  }

  useEffect(() => {
    const scrollElement = scrollRef.current
    if (scrollElement) {
      handleScroll()

      // Debounced scroll handler for better performance
      let scrollTimeout: NodeJS.Timeout
      const debouncedScrollHandler = () => {
        setIsScrolling(true)
        clearTimeout(scrollTimeout)
        scrollTimeout = setTimeout(() => {
          handleScroll()
          setIsScrolling(false)
        }, 100)
      }

      scrollElement.addEventListener('scroll', debouncedScrollHandler)

      // Check if right control should be visible initially
      setShowRightControl(scrollElement.scrollWidth > scrollElement.clientWidth)

      // Add resize observer to handle container size changes
      const resizeObserver = new ResizeObserver(() => {
        handleScroll()
      })

      resizeObserver.observe(scrollElement)

      return () => {
        scrollElement.removeEventListener('scroll', debouncedScrollHandler)
        clearTimeout(scrollTimeout)
        resizeObserver.disconnect()
      }
    }
  }, [children])

  const scroll = (direction: 'left' | 'right') => {
    if (!scrollRef.current) return

    const scrollAmountPx = scrollRef.current.clientWidth * scrollAmount
    const newPosition = direction === 'left'
      ? scrollRef.current.scrollLeft - scrollAmountPx
      : scrollRef.current.scrollLeft + scrollAmountPx

    scrollRef.current.scrollTo({
      left: newPosition,
      behavior: scrollBehavior
    })
  }

  // Control button size classes
  const controlSizeClasses = {
    sm: 'h-7 w-7 min-h-0 min-w-0',
    md: 'h-9 w-9',
    lg: 'h-11 w-11'
  }

  // Control position classes
  const controlPositionClasses = {
    center: 'top-1/2 -translate-y-1/2',
    top: 'top-2',
    bottom: 'bottom-2'
  }

  // Control icon size
  const iconSize = {
    sm: 'h-3.5 w-3.5',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  }

  return (
    <div className={cn("relative group", className)}>
      <AnimatePresence>
        {withControls && showLeftControl && (
          <motion.div
            initial={{ opacity: 0, x: -5 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -5 }}
            transition={{ duration: 0.15 }}
            className="absolute left-1 z-10"
            style={{
              [controlPosition === 'center' ? 'top' : '']: '50%',
              [controlPosition === 'center' ? 'transform' : '']: 'translateY(-50%)'
            }}
          >
            <Button
              variant="secondary"
              size="icon"
              className={cn(
                controlSizeClasses[controlSize],
                controlPositionClasses[controlPosition],
                "bg-black/50 backdrop-blur-sm shadow-md hover:bg-black/70 border border-white/10 rounded-full"
              )}
              onClick={() => scroll('left')}
            >
              <ChevronLeft className={cn("text-white", iconSize[controlSize])} />
            </Button>
          </motion.div>
        )}

        <div
          ref={scrollRef}
          className={cn(
            "flex overflow-x-auto",
            showScrollbar
              ? "wp-scrollbar pb-3"
              : "scrollbar-none",
            containerClassName
          )}
          style={{ scrollbarGutter: 'stable' }}
        >
          {childrenArray.map((child, index) => (
            <div
              key={index}
              className={cn("flex-shrink-0", itemClassName)}
            >
              {child}
            </div>
          ))}
        </div>

        {withControls && showRightControl && (
          <motion.div
            initial={{ opacity: 0, x: 5 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 5 }}
            transition={{ duration: 0.15 }}
            className="absolute right-1 z-10"
            style={{
              [controlPosition === 'center' ? 'top' : '']: '50%',
              [controlPosition === 'center' ? 'transform' : '']: 'translateY(-50%)'
            }}
          >
            <Button
              variant="secondary"
              size="icon"
              className={cn(
                controlSizeClasses[controlSize],
                controlPositionClasses[controlPosition],
                "bg-black/50 backdrop-blur-sm shadow-md hover:bg-black/70 border border-white/10 rounded-full"
              )}
              onClick={() => scroll('right')}
            >
              <ChevronRight className={cn("text-white", iconSize[controlSize])} />
            </Button>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

/* Add these styles to your global CSS or component CSS:
.wp-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(148, 163, 184, 0.3) rgba(15, 23, 42, 0.3);
}

.wp-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.wp-scrollbar::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.3);
  border-radius: 9999px;
}

.wp-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(148, 163, 184, 0.3);
  border-radius: 9999px;
}

.wp-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(148, 163, 184, 0.5);
}
*/

// Add styles for custom scrollbars
// Add this to global.css or make sure it's included
/*
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-none {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .scrollbar-none::-webkit-scrollbar {
    display: none;
  }

  .scrollbar-track-vista-dark-lighter::-webkit-scrollbar-track {
    background-color: rgb(12, 15, 22);
  }

  .scrollbar-thumb-vista-light\/20::-webkit-scrollbar-thumb {
    background-color: rgba(229, 231, 235, 0.2);
    border-radius: 9999px;
  }

  .hover\:scrollbar-thumb-vista-light\/30:hover::-webkit-scrollbar-thumb {
    background-color: rgba(229, 231, 235, 0.3);
  }

  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(229, 231, 235, 0.2);
    border-radius: 20px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: rgba(229, 231, 235, 0.3);
  }
}
*/
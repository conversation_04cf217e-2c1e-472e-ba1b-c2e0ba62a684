# Error and Toast Service

## Overview

StreamVista's Error and Toast Service provides a comprehensive system for error handling, user notifications, and alert management. The service includes error boundaries, toast notifications, alert management, and integration with external error tracking services.

## Core Components

### Error Types and Interfaces

```typescript
interface AlertMessage {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  autoClose?: boolean;
  duration?: number;
}

interface ErrorContextType {
  alerts: AlertMessage[];
  addAlert: (alert: Omit<AlertMessage, 'id'>) => void;
  removeAlert: (id: string) => void;
}

class APIError extends Error {
  constructor(
    public statusCode: number,
    public message: string,
    public details?: any
  ) {
    super(message);
  }
}
```

## Features

### 1. Error Handling

#### Error Boundary
```typescript
class ErrorBoundary extends React.Component<Props, State> {
  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    console.error('Error caught by ErrorBoundary:', error, errorInfo);
  }
}
```

#### Global Error Handler
```typescript
const handleError = (event: ErrorEvent) => {
  console.error('Global error caught:', event.error);
};

window.addEventListener('error', handleError);
```

### 2. Toast Notifications

#### Toast Types
```typescript
interface Toast {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  duration?: number;
  onClose?: () => void;
}

interface ToastContextType {
  toasts: Toast[];
  addToast: (toast: Omit<Toast, 'id'>) => string;
  updateToast: (id: string, toast: Partial<Toast>) => void;
  removeToast: (id: string) => void;
  position: ToastPosition;
  setPosition: (position: ToastPosition) => void;
}
```

### 3. Alert Management

#### Alert Hooks
```typescript
const { showError, showWarning, showSuccess, showInfo } = useErrorHelpers();

// Usage
showError("Error Title", "Error message", true, 8000);
showWarning("Warning Title", "Warning message", true, 6000);
showSuccess("Success Title", "Success message", true, 5000);
showInfo("Info Title", "Info message", true, 5000);
```

## Implementation Details

### Error Provider

```typescript
export function ErrorProvider({ children }: { children: ReactNode }) {
  const [alerts, setAlerts] = useState<AlertMessage[]>([]);

  const addAlert = (alert: Omit<AlertMessage, 'id'>) => {
    const id = Math.random().toString(36).substring(2, 9);
    const newAlert = { ...alert, id };
    setAlerts((prev) => [...prev, newAlert]);

    if (alert.autoClose) {
      const duration = alert.duration || 5000;
      setTimeout(() => removeAlert(id), duration);
    }
  };

  const removeAlert = (id: string) => {
    setAlerts((prev) => prev.filter((alert) => alert.id !== id));
  };

  return (
    <ErrorContext.Provider value={{ alerts, addAlert, removeAlert }}>
      {children}
      {/* Alert Display */}
    </ErrorContext.Provider>
  );
}
```

### Toast Provider

```typescript
export function ToastProvider({ children }: { children: ReactNode }) {
  const [toasts, setToasts] = useState<Toast[]>([]);
  const [position, setPosition] = useState<ToastPosition>('bottom-right');

  const addToast = (toast: Omit<Toast, 'id'>) => {
    const id = Math.random().toString(36).substring(2, 9);
    const newToast = { ...toast, id };
    setToasts((prev) => [...prev, newToast]);
    return id;
  };

  const removeToast = (id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id));
  };

  return (
    <ToastContext.Provider value={{ toasts, addToast, removeToast, position, setPosition }}>
      {children}
      {/* Toast Display */}
    </ToastContext.Provider>
  );
}
```

## Integration

### 1. API Error Integration

```typescript
// API error handling middleware
function handleAPIError(error: unknown) {
  if (error instanceof APIError) {
    return new Response(JSON.stringify({
      error: error.message,
      details: error.details
    }), { status: error.statusCode });
  }
  
  console.error('Unhandled error:', error);
  return new Response(JSON.stringify({
    error: 'Internal server error'
  }), { status: 500 });
}
```

### 2. External Service Integration

```typescript
// TMDB error handling
async function handleTMDBError(error: unknown) {
  console.error('TMDB API error:', error);
  
  if (process.env.NODE_ENV === 'development') {
    return getMockData();
  }
  
  throw new APIError(503, 'TMDB service unavailable');
}
```

### 3. Component Integration

```typescript
function useComponentError() {
  const { showError } = useErrorHelpers();
  const handleError = useCallback((error: Error) => {
    showError('Component Error', error.message);
    // Log to error tracking service
  }, [showError]);
  return handleError;
}
```

## Best Practices

1. **Error Handling**
   - Use appropriate error types
   - Implement error boundaries
   - Log errors appropriately
   - Provide user-friendly messages

2. **Toast Management**
   - Limit concurrent toasts
   - Use appropriate durations
   - Group similar notifications
   - Position based on context

3. **Alert Management**
   - Use appropriate alert types
   - Set suitable auto-close durations
   - Provide clear messages
   - Handle alert stacking

4. **Performance**
   - Clean up on unmount
   - Debounce notifications
   - Limit maximum alerts
   - Optimize animations

5. **Accessibility**
   - ARIA-compliant notifications
   - Keyboard navigation
   - Screen reader support
   - Focus management

## Error Tracking

1. **Client-side Errors**
   - Runtime errors
   - Network errors
   - Component errors
   - Promise rejections

2. **Server-side Errors**
   - API errors
   - Database errors
   - External service errors
   - Validation errors

3. **Error Reporting**
   - Error context
   - Stack traces
   - User information
   - Environment data

## Development Guidelines

1. **Adding Error Types**
   - Define error interface
   - Implement error class
   - Add error handlers
   - Update documentation

2. **Custom Notifications**
   - Define notification type
   - Create notification component
   - Add notification handler
   - Test notification flow

3. **Testing**
   - Test error boundaries
   - Verify error handling
   - Check notifications
   - Validate accessibility 
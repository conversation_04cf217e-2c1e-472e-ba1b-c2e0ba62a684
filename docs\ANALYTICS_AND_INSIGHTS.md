# Analytics and Insights Service

## Overview

StreamVista's Analytics and Insights Service provides comprehensive tracking and analysis of user behavior, watch patterns, and system performance. The service includes watch history analytics, user insights generation, connection statistics, and performance monitoring.

## Watch Analytics

### Watch Statistics Interface

```typescript
interface WatchStats {
  totalWatchTime: number;              // in seconds
  genreDistribution: Record<string, number>;  // percentage
  contentTypeDistribution: {
    show: number;
    movie: number;
  };
  weeklyWatchTime: Record<string, number>;  // day: seconds
  watchTimePerTimeOfDay: {
    morning: number;    // 5-12
    afternoon: number;  // 12-17
    evening: number;    // 17-22
    night: number;      // 22-5
  };
  favoriteGenres: Array<{
    genre: string;
    count: number;
  }>;
  topWatched: WatchHistoryItem[];
  recentActivity: WatchHistoryItem[];
  watchStreak: number;              // consecutive days
  unfinishedContent: WatchHistoryItem[];
  completionRate: number;           // percentage
}
```

### Watch History Item

```typescript
interface WatchHistoryItem {
  contentId: string;
  type: 'movie' | 'show';
  progress: number;      // 0-100
  watchTime: number;     // seconds
  watchedAt: string;     // ISO date
  completed: boolean;
  season?: number;       // for shows
  episode?: number;      // for shows
}
```

## User Insights

### Recommendation Engine

```typescript
interface UserInsights {
  genrePreferences: Record<string, number>;  // normalized weights
  preferMoviesOverShows: number;            // -1 to 1 scale
}

function generateUserInsights(
  watchHistory: WatchHistoryItem[],
  allContent: IContent[]
): UserInsights {
  // Analyzes watch history to generate personalized insights
  // Returns normalized genre preferences and content type preferences
}
```

## Performance Monitoring

### Connection Statistics

```typescript
interface ConnectionStats {
  latency: number;
  reconnectCount: number;
  connectCount: number;
  disconnectCount: number;
  lastConnected: string | null;
  lastDisconnected: string | null;
  transportType: 'websocket' | 'polling';
}
```

### Watch Party Analytics

```typescript
interface PartyStats {
  id: string;
  timestamp: number;
  accessCount: number;
  partyData: WatchParty;
}

// Party tracking configuration
const PARTY_TRACKING_CONFIG = {
  storageInterval: 30000,    // 30 seconds
  cleanupInterval: 3600000,  // 1 hour
  maxInactiveTime: 7200000   // 2 hours
};
```

## Implementation Details

### Watch Statistics Calculation

```typescript
function calculateWatchStats(
  history: WatchHistoryItem[],
  allContent: IContent[]
): WatchStats {
  // Initialize stats object
  const stats: WatchStats = {
    totalWatchTime: 0,
    genreDistribution: {},
    contentTypeDistribution: { show: 0, movie: 0 },
    weeklyWatchTime: {},
    watchTimePerTimeOfDay: { morning: 0, afternoon: 0, evening: 0, night: 0 },
    favoriteGenres: [],
    topWatched: [],
    recentActivity: [],
    watchStreak: 0,
    unfinishedContent: [],
    completionRate: 0
  };

  // Calculate statistics:
  // 1. Total watch time
  // 2. Genre distribution
  // 3. Content type distribution
  // 4. Weekly patterns
  // 5. Time of day patterns
  // 6. Watch streaks
  // 7. Completion rates
  
  return stats;
}
```

### Connection Monitoring

```typescript
function useConnectionStats(socket: SocketManager) {
  const [stats, setStats] = useState<ConnectionStats>({
    latency: 0,
    reconnectCount: 0,
    connectCount: 0,
    disconnectCount: 0,
    lastConnected: null,
    lastDisconnected: null,
    transportType: 'websocket'
  });

  useEffect(() => {
    if (!socket) return;

    const updateStats = () => {
      const newStats = socket.getConnectionStats();
      setStats(newStats);
    };

    // Update every 2 seconds
    const interval = setInterval(updateStats, 2000);
    return () => clearInterval(interval);
  }, [socket]);

  return stats;
}
```

### Party Tracking

```typescript
function trackParty(
  partyId: string,
  action: 'create' | 'access' | 'join' | 'leave' | 'delete',
  party?: WatchParty
) {
  // Track party lifecycle events
  // Store party data in global storage
  // Handle party deletion
  // Update access counts
  // Maintain party history
}
```

## Debug Tools

### Socket Debug Mode

```typescript
const debugSocket = {
  logEvents: true,
  logErrors: true,
  logReconnects: true,
  logPlayback: false
};

function logSocketEvent(event: string, data: any) {
  if (debugSocket.logEvents) {
    console.log(`[Socket] ${event}:`, data);
  }
}
```

### Request Tracking

```typescript
// Track API requests
const requestCounts: Record<string, number> = {
  'create-watch-party': 0,
  'join-watch-party': 0,
  'leave-watch-party': 0,
  'playback-update': 0,
  'new-message': 0,
  'get-watch-parties': 0
};

function logApi(
  level: 'info' | 'debug' | 'error' | 'warn',
  operation: string,
  message: string,
  extraData?: any
) {
  // Skip logging based on debug flags
  // Implement throttling for common messages
  // Track message frequency
}
```

## Best Practices

1. **Performance**
   - Implement efficient data structures
   - Use appropriate update intervals
   - Cache frequently accessed stats
   - Throttle logging and tracking

2. **Data Management**
   - Regular cleanup of old data
   - Proper error handling
   - Data validation
   - Type safety

3. **User Privacy**
   - Anonymize sensitive data
   - Implement data retention policies
   - Clear data collection consent
   - Secure data storage

4. **Monitoring**
   - Track system health
   - Monitor connection quality
   - Log error rates
   - Track user engagement

5. **Maintenance**
   - Regular cleanup jobs
   - Data consistency checks
   - Performance optimization
   - Documentation updates 
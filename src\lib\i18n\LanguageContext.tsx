'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { Language, getUserLanguage, setUserLanguage, translations, translate, formatTranslation, TranslationKey } from '.';

// Context interface
interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: TranslationKey) => string;
  tFormat: (key: TranslationKey, variables: Record<string, string | number>) => string;
  languages: typeof languages;
}

// Language display names
export const languages = {
  en: { name: 'English', nativeName: 'English' },
  es: { name: 'Spanish', nativeName: 'Español' },
  fr: { name: 'French', nativeName: 'Français' },
  de: { name: 'German', nativeName: 'Deutsch' },
  ja: { name: 'Japanese', nativeName: '日本語' }
};

// Create context with default values
const LanguageContext = createContext<LanguageContextType>({
  language: 'en',
  setLanguage: () => {},
  t: (key) => key,
  tFormat: (key) => key,
  languages
});

// Custom hook to use the language context
export const useLanguage = () => useContext(LanguageContext);

// Provider component
export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguageState] = useState<Language>('en'); // Default to English
  const [mounted, setMounted] = useState(false);

  // Initialize language from localStorage or browser setting
  useEffect(() => {
    const userLang = getUserLanguage();
    setLanguageState(userLang);
    setMounted(true);
  }, []);

  // Set the language
  const setLanguage = (lang: Language) => {
    setLanguageState(lang);
    setUserLanguage(lang);
    // Set html lang attribute for accessibility
    if (typeof document !== 'undefined') {
      document.documentElement.lang = lang;
    }
  };

  // Simple translation function
  const t = (key: TranslationKey): string => {
    return translate(key, language);
  };

  // Translation with variable substitution
  const tFormat = (key: TranslationKey, variables: Record<string, string | number>): string => {
    return formatTranslation(key, language, variables);
  };

  // Only provide the real context value after mounting to avoid hydration issues
  const contextValue = mounted
    ? {
        language,
        setLanguage,
        t,
        tFormat,
        languages
      }
    : {
        language: 'en',
        setLanguage: () => {},
        t: (key: TranslationKey) => key,
        tFormat: (key: TranslationKey) => key,
        languages
      };

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  );
};

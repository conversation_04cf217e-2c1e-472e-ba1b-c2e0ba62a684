// This script is for development purposes only to upload a default avatar to Cloudinary
import { v2 as cloudinary } from 'cloudinary';
import fs from 'fs';
import path from 'path';
import axios from 'axios';
import { fileURLToPath } from 'url';

// Get current file path in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configure Cloudinary - cloud names are lowercase in Cloudinary
cloudinary.config({
  cloud_name: 'streamvista',
  api_key: '193122456379558',
  api_secret: 'Fj6K_hVVzH8cCE9el3CoN6RXHT8'
});

async function downloadDefaultAvatar() {
  try {
    // Placeholder avatar URL from Gravatar
    const avatarUrl = 'https://www.gravatar.com/avatar/00000000000000000000000000000000?d=mp&s=200';
    const response = await axios({
      method: 'GET',
      url: avatarUrl,
      responseType: 'arraybuffer'
    });

    const tempFilePath = path.join(__dirname, 'default_avatar.png');
    fs.writeFileSync(tempFilePath, Buffer.from(response.data));
    
    console.log('Avatar downloaded to', tempFilePath);
    return tempFilePath;
  } catch (error) {
    console.error('Error downloading avatar:', error);
    throw error;
  }
}

async function uploadDefaultAvatar() {
  try {
    // Download the avatar
    const filePath = await downloadDefaultAvatar();
    
    // Upload to Cloudinary
    const result = await cloudinary.uploader.upload(filePath, {
      public_id: 'defaults/default_avatar',
      overwrite: true
    });
    
    console.log('Upload successful!');
    console.log('Default avatar URL:', result.secure_url);
    
    // Clean up the temporary file
    fs.unlinkSync(filePath);
    console.log('Temporary file deleted');
    
    return result.secure_url;
  } catch (error) {
    console.error('Error uploading default avatar:', error);
  }
}

// Run the upload
uploadDefaultAvatar().then(url => {
  console.log('Final URL:', url);
}).catch(err => {
  console.error('Script error:', err);
}); 
import { NextRequest, NextResponse } from 'next/server';
import { getMovieDetails, getTVDetails } from '@/services/tmdb';
import { IContent } from '@/data/content';
import { enhanceContentWithOMDB } from '@/lib/omdb-api';

/**
 * API route for content details (movies and TV shows)
 * This ensures we always get fresh data from TMDb including IMDb IDs
 */
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const id = searchParams.get('id');
  const type = searchParams.get('type') as 'movie' | 'show';

  if (!id) {
    return NextResponse.json({ error: 'Content ID is required' }, { status: 400 });
  }

  if (!type || (type !== 'movie' && type !== 'show')) {
    return NextResponse.json({ error: 'Valid content type (movie or show) is required' }, { status: 400 });
  }

  try {
    let content: IContent;

    // Fetch content based on type
    if (type === 'movie') {
      content = await getMovieDetails(id);
    } else {
      content = await getTVDetails(id);
    }

    // Check if we have this content in our database to get views count
    try {
      const { ensureMongooseConnection } = await import('@/lib/mongoose');
      const Content = (await import('@/models/Content')).default;

      await ensureMongooseConnection();

      // Try to find content by tmdbId
      const dbContent = await Content.findOne({ tmdbId: content.tmdbId });

      if (dbContent) {
        // Add views count from our database
        content.views = dbContent.views;
      }
    } catch (dbError) {
      console.error('Error fetching content from database:', dbError);
      // Continue without views count
    }

    // Enhance with OMDB data if IMDb ID is available
    if (content.imdbId) {
      try {
        console.log(`Enhancing ${type} content with OMDB data for IMDb ID: ${content.imdbId}`);
        content = await enhanceContentWithOMDB(content);
      } catch (omdbError) {
        console.error(`Error enhancing with OMDB data:`, omdbError);
        // Continue with TMDB data only
      }
    }

    // Log content details for debugging
    console.log(`API: Fetched ${type} content:`, {
      id: content.id,
      title: content.title,
      imdbId: content.imdbId,
      tmdbId: content.tmdbId,
      dataSource: content.dataSource || 'tmdb'
    });

    return NextResponse.json(content);
  } catch (error) {
    console.error(`Error fetching ${type} details:`, error);
    return NextResponse.json({ error: `Failed to fetch ${type} details` }, { status: 500 });
  }
}
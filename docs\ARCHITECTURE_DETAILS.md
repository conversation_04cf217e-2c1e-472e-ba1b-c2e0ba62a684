# StreamVista Architecture Details

## Core Components

### Video Player System

#### VidSrc Player Implementation
```typescript
interface VidSrcPlayerProps {
  imdbId?: string;
  tmdbId?: string;
  type?: 'movie' | 'show';
  season?: number;
  episode?: number;
  className?: string;
  height?: string;
  shouldRefresh?: boolean;
}
```

Features:
- Multiple domain fallback system with health checks
- Automatic error recovery and provider switching
- Mobile-optimized loading with adaptive delays
- Proxy support for reliability
- Episode and season handling for TV shows
- Loading states with spinners and progress indicators
- Comprehensive error state management
- Provider selection interface
- Automatic retry mechanism with exponential backoff
- Cross-origin handling and security measures
- Aspect ratio preservation
- Responsive design support

### Watch Party System

#### Context Implementation
```typescript
interface WatchPartyState {
  id: string;
  contentId: string;
  hostId: string;
  members: WatchPartyMember[];
  currentTime: number;
  isPlaying: boolean;
  messages: ChatMessage[];
  reactions: Reaction[];
  startedAt?: string;
  content: IContent;
  currentSeason?: number;
  currentEpisode?: number;
}

interface WatchPartyContextType {
  currentParty: WatchPartyState | null;
  isHost: boolean;
  createParty: (content: IContent) => Promise<string>;
  joinParty: (partyId: string, memberName: string) => Promise<void>;
  leaveParty: () => void;
  updatePlayback: (currentTime: number, isPlaying: boolean) => void;
  sendMessage: (message: string) => void;
  sendReaction: (reaction: string) => void;
  connectionState: CONNECTION_STATE;
  availableParties: WatchPartyState[];
  fetchAvailableParties: () => void;
  isLoading: boolean;
  userId: string;
}
```

#### Socket Management
```typescript
interface SocketContextType {
  socket: SocketManager | null;
  isConnected: boolean;
  connectionState: CONNECTION_STATE;
  isConnecting: boolean;
  connect: () => void;
  disconnect: () => void;
  reconnect: () => void;
  emit: <T extends SOCKET_EVENTS>(event: T, data: any) => void;
  on: <T extends SOCKET_EVENTS>(event: T, callback: (data: any) => void) => () => void;
  off: <T extends SOCKET_EVENTS>(event: T, callback: (data: any) => void) => void;
  manualRetry: () => void;
}

enum CONNECTION_STATE {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  FAILED = 'failed'
}
```

### Socket Service

#### Configuration
```typescript
export const SOCKET_CONFIG = {
  url: process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3001',
  options: {
    autoConnect: true,
    reconnection: true,
    reconnectionAttempts: 5,
    reconnectionDelay: 1000,
    reconnectionDelayMax: 5000,
    timeout: 20000,
    debug: process.env.NODE_ENV === 'development'
  },
  party: {
    maxMembers: 10,
    maxMessageHistory: 100,
    maxReactionHistory: 50,
    syncThreshold: 2000,
    inactivityTimeout: 1000 * 60 * 30
  },
  monitoring: {
    pingInterval: 30000,
    connectionTimeout: 10000,
    showStatusIndicator: true,
    showReconnectPrompt: true
  }
}
```

### Error Management

#### Error Context
```typescript
interface ErrorContextType {
  error: Error | null;
  showError: (title: string, message: string) => void;
  clearError: () => void;
  isError: boolean;
}

interface ErrorState {
  title: string;
  message: string;
  timestamp: number;
}
```

### Toast Notifications

#### Toast Context
```typescript
interface Toast {
  id: string;
  type: 'success' | 'error' | 'info' | 'warning';
  title: string;
  message: string;
  duration?: number;
}

interface ToastContextType {
  toasts: Toast[];
  addToast: (toast: Omit<Toast, 'id'>) => void;
  removeToast: (id: string) => void;
  clearToasts: () => void;
}
```

### Provider Architecture

The application uses a nested provider structure to ensure proper context availability:

```typescript
// Root layout provider structure
<ErrorProvider>
  <ToastProvider>
    <WatchPartyProvider socketUrl={SOCKET_CONFIG.url}>
      {children}
    </WatchPartyProvider>
  </ToastProvider>
</ErrorProvider>
```

### Real-time Communication

#### Socket Events
```typescript
enum SOCKET_EVENTS {
  // Connection events
  CONNECT = "connect",
  DISCONNECT = "disconnect",
  ERROR = "error",
  RECONNECT = "reconnect",
  
  // Party management
  PARTY_CREATE = "party:create",
  PARTY_JOIN = "party:join",
  PARTY_LEAVE = "party:leave",
  PARTY_UPDATE = "party:update",
  
  // Chat and reactions
  CHAT_MESSAGE = "chat:message",
  REACTION = "reaction",
  
  // Playback sync
  STATE_SYNC = "state:sync",
  PLAYBACK_UPDATE = "playback:update",
  
  // User interaction
  TYPING_START = "typing:start",
  TYPING_STOP = "typing:stop",
  READY_STATE = "ready:state",
  
  // Watch party specific
  JOIN_WATCH_PARTY = "join_watch_party",
  LEAVE_WATCH_PARTY = "leave_watch_party",
  WATCH_PARTY_STATE = "watch_party_state",
  WATCH_PARTY_CREATED = "watch_party_created",
  USER_JOINED = "user_joined",
  USER_LEFT = "user_left",
  HOST_CHANGED = "host_changed",
  
  // Party discovery
  GET_AVAILABLE_PARTIES = "get_available_parties",
  AVAILABLE_PARTIES = "available_parties"
}
```

### Component Interactions

#### Watch Party Flow
1. User creates or joins a watch party
2. Socket connection is established
3. Party state is synchronized
4. Video player loads content
5. Playback events are synchronized
6. Chat messages and reactions are shared
7. Member status updates are broadcast

#### Video Player Integration
1. Player loads with content information
2. Health checks determine best provider
3. Playback state is monitored
4. Events are emitted for synchronization
5. Errors trigger provider fallback
6. Mobile optimizations are applied

### Performance Optimizations

#### Socket Connection
- Automatic reconnection with exponential backoff
- Connection state monitoring
- Event debouncing
- Optimistic updates
- Error recovery

#### Video Player
- Provider health checks
- Adaptive loading delays
- Mobile-specific optimizations
- Proxy fallback system
- Caching mechanisms

### Security Measures

#### Socket Connection
- Token-based authentication
- Connection validation
- Event verification
- Rate limiting
- CORS configuration

#### Content Validation
- Provider verification
- URL sanitization
- Input validation
- Error boundaries
- Cross-origin handling

### Development Tools

#### Debug Helpers
```typescript
// Socket debugging
const debugSocket = {
  logEvents: true,
  logErrors: true,
  logReconnects: true,
  logPlayback: false
};

// Player debugging
const debugPlayer = {
  logProviderSwitches: true,
  logLoadAttempts: true,
  logErrors: true
};
```

#### Mock Implementations
```typescript
// Development environment checks
const useMockData = process.env.NODE_ENV === 'development';

// Mock socket for testing
const mockSocket = {
  emit: jest.fn(),
  on: jest.fn(),
  off: jest.fn()
};
```

### Deployment Configuration

#### Environment Variables
```env
NEXT_PUBLIC_SOCKET_URL=ws://localhost:3001
NEXT_PUBLIC_API_URL=http://localhost:3000/api
```

#### Build Options
```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "socket-server": "node server.js"
  }
}
```

## Future Considerations

### Scalability
- WebSocket clustering
- State synchronization
- Load balancing
- Cache optimization

### Feature Extensions
- Screen sharing
- Voice chat
- Custom emotes
- Party playlists

### Performance Monitoring
- Real-time metrics
- Error tracking
- Usage analytics
- Performance profiling
``` 
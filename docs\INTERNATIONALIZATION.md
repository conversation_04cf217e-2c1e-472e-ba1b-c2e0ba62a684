# Internationalization Service

## Overview

StreamVista's internationalization (i18n) service provides multi-language support through a context-based system with type-safe translations, language switching, and format string interpolation. The service supports English, Spanish, French, German, and Japanese, with English serving as the fallback language.

## Types and Interfaces

### Core Types

```typescript
// Language type
export type Language = 'en' | 'es' | 'fr' | 'de' | 'ja';

// Translation type (based on English translations)
export type Translation = typeof en;
export type TranslationKey = keyof Translation;

// Language display information
interface LanguageInfo {
  name: string;      // English name
  nativeName: string; // Name in the language itself
}

// Context type
interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: TranslationKey) => string;
  tFormat: (key: TranslationKey, variables: Record<string, string | number>) => string;
  languages: Record<Language, LanguageInfo>;
}
```

## Configuration

### Supported Languages

```typescript
export const languages = {
  en: { name: 'English', nativeName: 'English' },
  es: { name: 'Spanish', nativeName: 'Español' },
  fr: { name: 'French', nativeName: 'Français' },
  de: { name: 'German', nativeName: 'Deutsch' },
  ja: { name: 'Japanese', nativeName: '日本語' }
};

export const DEFAULT_LANGUAGE: Language = 'en';
```

## Core Functions

### Language Management

```typescript
// Get user's language preference
export function getUserLanguage(): Language {
  if (typeof window === 'undefined') {
    return DEFAULT_LANGUAGE;
  }

  const storedLang = localStorage.getItem('vista-language') as Language | null;
  if (storedLang && translations[storedLang]) {
    return storedLang;
  }

  const browserLang = navigator.language.split('-')[0] as Language;
  if (browserLang && translations[browserLang]) {
    return browserLang;
  }

  return DEFAULT_LANGUAGE;
}

// Set user's language preference
export function setUserLanguage(lang: Language): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem('vista-language', lang);
  }
}
```

### Translation Functions

```typescript
// Basic translation
export function translate(key: TranslationKey, lang: Language = DEFAULT_LANGUAGE): string {
  const translation = translations[lang][key];
  if (!translation) {
    console.warn(`Translation missing for key: ${key} in language: ${lang}`);
    return translations[DEFAULT_LANGUAGE][key] || key;
  }
  return translation;
}

// Translation with variable substitution
export function formatTranslation(
  key: TranslationKey,
  lang: Language = DEFAULT_LANGUAGE,
  variables: Record<string, string | number> = {}
): string {
  let translation = translate(key, lang);

  Object.entries(variables).forEach(([varName, value]) => {
    const regex = new RegExp(`{${varName}}`, 'g');
    translation = translation.replace(regex, String(value));
  });

  return translation;
}
```

## Language Provider

```typescript
export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguageState] = useState<Language>('en');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    const userLang = getUserLanguage();
    setLanguageState(userLang);
    setMounted(true);
  }, []);

  const setLanguage = (lang: Language) => {
    setLanguageState(lang);
    setUserLanguage(lang);
    document.documentElement.lang = lang;
  };

  const contextValue = mounted
    ? {
        language,
        setLanguage,
        t: (key: TranslationKey) => translate(key, language),
        tFormat: (key: TranslationKey, variables: Record<string, string | number>) =>
          formatTranslation(key, language, variables),
        languages
      }
    : {
        language: 'en',
        setLanguage: () => {},
        t: (key: TranslationKey) => key,
        tFormat: (key: TranslationKey) => key,
        languages
      };

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  );
};
```

## Components

### Language Selector

```typescript
interface LanguageSelectorProps {
  variant?: 'icon' | 'text' | 'full';
  className?: string;
  showFlag?: boolean;
  align?: 'start' | 'center' | 'end';
}

export default function LanguageSelector({
  variant = 'full',
  className = '',
  showFlag = true,
  align = 'end'
}: LanguageSelectorProps) {
  const { language, setLanguage, languages } = useLanguage();
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger>
        <Button>
          <Globe />
          {variant === 'full' && (
            <span>{languages[language].nativeName}</span>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        {Object.entries(languages).map(([code, info]) => (
          <DropdownMenuItem
            key={code}
            onClick={() => setLanguage(code as Language)}
          >
            {info.nativeName}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
```

## Translation Structure

### Common Translations

```typescript
const translations = {
  // Common
  'common.appName': 'StreamVista',
  'common.loading': 'Loading...',
  'common.error': 'An error occurred',
  
  // Navigation
  'nav.home': 'Home',
  'nav.shows': 'Shows',
  'nav.movies': 'Movies',
  
  // Content
  'content.episodes': 'Episodes',
  'content.season': 'Season',
  'content.details': 'Details',
  
  // Settings
  'settings.language': 'Language',
  'settings.theme': 'Theme',
  'settings.privacy': 'Privacy'
};
```

## Usage Examples

### Basic Translation

```typescript
const { t } = useLanguage();

// Simple translation
<h1>{t('common.appName')}</h1>

// With variables
const { tFormat } = useLanguage();
<p>{tFormat('toast.downloadCompleteMessage', { title: 'Movie Title' })}</p>
```

### Language Switching

```typescript
const { setLanguage } = useLanguage();

// Switch language
<button onClick={() => setLanguage('fr')}>
  Switch to French
</button>
```

## Best Practices

1. **Type Safety**
   - Use `TranslationKey` type for all translation keys
   - Maintain consistent translation structure across languages
   - Use TypeScript to catch missing translations

2. **Performance**
   - Lazy load translations for non-default languages
   - Cache translations in localStorage
   - Minimize re-renders with proper context usage

3. **User Experience**
   - Persist language preference
   - Provide clear language selection UI
   - Handle missing translations gracefully
   - Support RTL languages

4. **Maintenance**
   - Keep translations organized by category
   - Document all translation keys
   - Provide context for translators
   - Regular translation updates

5. **Accessibility**
   - Set correct HTML lang attribute
   - Provide language names in their native form
   - Support keyboard navigation
   - Maintain proper contrast ratios 
/**
 * Logging Configuration
 *
 * This file contains configuration for the activity logging system.
 * It helps control what types of activities are logged to prevent database overload.
 */

// Define log levels
export enum LogLevel {
  NONE = 0,     // No logging
  ERROR = 1,    // Only errors
  CRITICAL = 2, // Critical actions (auth, payments)
  NORMAL = 3,   // Regular user actions
  VERBOSE = 4,  // All actions (development only)
}

// Current log level - can be set based on environment
export const CURRENT_LOG_LEVEL = process.env.NODE_ENV === 'production'
  ? LogLevel.CRITICAL
  : LogLevel.VERBOSE;

// Log retention period in days
export const LOG_RETENTION_DAYS = 30;

// Configure which activity types to log based on their importance
export const ACTIVITY_LOG_CONFIG = {
  // Authentication activities
  auth: {
    level: LogLevel.CRITICAL,
    actions: {
      login: true,
      logout: false, // No longer tracking logout
      signup: true,
      password_reset: true,
      email_verification: true,
    }
  },

  // Content interaction activities - ALL DISABLED
  content: {
    level: LogLevel.NONE,
    actions: {
      view: false,
      rate: false,
      favorite: false,
      search: false,
      browse: false,
    }
  },

  // Profile activities - ALL DISABLED
  profile: {
    level: LogLevel.NONE,
    actions: {
      update: false,
      create: false,
      delete: false,
      switch: false,
    }
  },

  // Admin activities (always log these)
  admin: {
    level: LogLevel.CRITICAL,
    actions: {
      create_user: true,
      update_user: true,
      delete_user: true,
      update_permissions: true,
      content_management: true,
    }
  },

  // Payment activities
  payment: {
    level: LogLevel.CRITICAL,
    actions: {
      subscription_created: true,
      subscription_updated: true,
      subscription_cancelled: true,
      payment_success: true,
      payment_failed: true,
    }
  },

  // Error activities - ALL DISABLED
  error: {
    level: LogLevel.NONE,
    actions: {
      api_error: false,
      client_error: false,
      auth_error: false,
    }
  },

  // System activities - REFINED
  system: {
    level: LogLevel.NORMAL,
    actions: {
      startup: false, // No longer tracking startup
      shutdown: false, // No longer tracking shutdown
      config_change: true, // Still tracking config changes
      performance: false, // No longer tracking performance
    }
  }
};

/**
 * Check if an activity should be logged based on its type, action, and the current log level
 * @param type Activity type
 * @param action Specific action
 * @returns Boolean indicating whether the activity should be logged
 */
export function shouldLogActivity(type: string, action: string): boolean {
  // If the type doesn't exist in our config, don't log it
  if (!ACTIVITY_LOG_CONFIG[type]) {
    return false;
  }

  // Check if the activity level is higher than our current log level
  if (ACTIVITY_LOG_CONFIG[type].level > CURRENT_LOG_LEVEL) {
    return false;
  }

  // Check if the specific action is enabled
  if (ACTIVITY_LOG_CONFIG[type].actions &&
      ACTIVITY_LOG_CONFIG[type].actions[action] === false) {
    return false;
  }

  return true;
}

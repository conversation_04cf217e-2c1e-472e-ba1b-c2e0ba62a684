// Content types
export interface Content {
  id: string;
  title: string;
  overview: string;
  posterPath: string;
  backdropPath: string;
  type: 'movie' | 'show';
  year?: number;
  rating?: number;
  tmdbId?: string;
  imdbId?: string;
  externalIds?: {
    imdbId?: string;
    tmdbId?: string;
  };
  genres?: string[];
  seasonData?: SeasonData[];
}

// Episode types
export interface Episode {
  id: string;
  title: string;
  episodeNumber: number;
  seasonNumber?: number;
  description: string;
  thumbnail: string;
  runtime?: number;
  airDate?: string;
  stillPath?: string;
}

export interface SeasonData {
  seasonNumber: number;
  episodes: Episode[];
}

// Search types
export interface SearchResult {
  id: string;
  title: string;
  year?: number;
  type: 'movie' | 'show';
  posterPath?: string;
  tmdbId?: string;
  imdbId?: string;
}

// Player types
export interface PlayerProps {
  contentId: string;
  type?: 'movie' | 'show';
  season?: number;
  episode?: number;
  onError?: (error: Error) => void;
}

// API Response types
export interface TMDbSearchResponse {
  page: number;
  results: any[];
  total_results: number;
  total_pages: number;
}

export interface TMDbMovieDetails {
  id: number;
  imdb_id: string;
  title: string;
  overview: string;
  poster_path: string;
  backdrop_path: string;
  release_date: string;
  vote_average: number;
  genres: { id: number; name: string }[];
  runtime: number;
}

export interface TMDbTVDetails {
  id: number;
  name: string;
  overview: string;
  poster_path: string;
  backdrop_path: string;
  first_air_date: string;
  vote_average: number;
  genres: { id: number; name: string }[];
  number_of_seasons: number;
  external_ids: {
    imdb_id?: string;
  };
}

export interface TMDbSeasonDetails {
  id: number;
  season_number: number;
  episodes: TMDbEpisode[];
}

export interface TMDbEpisode {
  id: number;
  name: string;
  overview: string;
  still_path: string;
  episode_number: number;
  season_number: number;
  air_date: string;
  runtime: number;
}

// VidSrc API types
export interface VidSrcAPIResponse {
  success: boolean;
  result: {
    id: string;
    title: string;
    type: string;
    year: number;
    imdb_id?: string;
    tmdb_id?: string;
  }[];
} 
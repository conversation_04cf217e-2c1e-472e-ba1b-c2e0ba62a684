# Stream Vista - Developer Notes

This document contains important technical notes, lessons learned, and best practices for developers working on the Stream Vista project.

## Recent Lessons

### VidSrc Player Integration

We've recently implemented the VidSrc API integration for content streaming:

**Implementation Highlights:**
- Created a dedicated `VidSrcPlayer` component with proper error handling
- Supported both movies and TV shows with episode selection
- Implemented multiple domain fallbacks for better reliability
- Added player refresh mechanism for episode changes
- Created player state management for loading and error states

**Key Challenges:**
```
VidSrcPlayer: Could not create a valid player URL - missing required IDs
```

**Solution:**
- Implemented proper error handling and fallbacks
- Added comprehensive loading states with spinner
- Created retry mechanism for failed loads
- Ensured both IMDb and TMDb IDs are properly passed

**UI Integration Lessons:**
- Added proper padding to prevent navbar overlap (`pt-20` on container)
- Ensured iframe has proper aspect-video ratio
- Added padding beneath the player (`pb-8` and `mb-4`) to prevent cutoff
- Used custom iframe container to maintain aspect ratio

### TypeScript Type Resolution

#### Socket Hook Import Issues

We recently resolved multiple TypeScript errors related to import paths and type definitions:

```
Module not found: Can't resolve '@/hooks/useSocket'
```

**Problem:**
The `useSocket` hook was being referenced from an incorrect path across multiple components, leading to build failures.

**Solution:**
- Created a proper `useSocket.ts` hook in the `src/hooks` directory that exports the hook from the SocketContext
- Updated all imports to use the correct path: `import { useSocket } from '@/contexts/SocketContext'`
- Standardized the hook import pattern across the application

#### WatchPartyState Type Conflicts

Multiple TypeScript errors occurred due to conflicting `WatchPartyState` interface definitions:

```
Type 'WatchPartyState' is missing the following properties from type 'WatchPartyState'
```

**Problem:**
Two different `WatchPartyState` interfaces were defined - one in `watch-party.ts` and another in `WatchPartyContext.tsx`.

**Solution:**
- Removed the duplicate interface in `WatchPartyContext.tsx`
- Imported the interface from `watch-party.ts`
- Used type assertions (`as any`) as a temporary solution for type incompatibilities
- Future work: Create proper adapters to handle type transitions

#### Socket Event Type Safety

**Problem:**
Inconsistent event handler typing between `SocketManager` and `MockSocketManager` classes.

**Solution:**
- Updated the method signatures to match: `on<T>(event: SOCKET_EVENTS, callback: (data: any) => void): () => void`
- Implemented consistent return types for event subscription methods
- Used the `SOCKET_EVENTS` enum for type safety instead of string literals

### Watch Page Implementation

We've created multiple watch page implementations for different scenarios:

1. **Dynamic ID Route** (`watch/[id]/page.tsx`):
   - Server component that dynamically loads content based on ID
   - Hydrates client components with content data
   - Supports both movies and TV shows

2. **Query Parameter Route** (`watch/page.tsx`):
   - Client component that uses query parameters
   - Direct VidSrc playback without content details when needed
   - Episode selection for TV shows

3. **Watch Content Client** (`watch/[id]/watch-content-client.tsx`):
   - Rich UI with content details, related content, and episode list
   - Manages player state and episode selection
   - Custom event system for seamless episode changes

**Key Implementation Lessons:**
- Used custom events (`episodeChange` and `seasonChange`) to avoid full page refreshes
- Implemented URL parameter updates without navigation
- Created proper aspect ratio containers for the video player
- Added adequate padding to prevent UI overlap
- Used consistent loading states across different content types

### Socket Management Implementation

We've implemented a robust socket management system with:

1. **SocketManager Class**:
   - Connection state management
   - Automatic reconnection
   - Event handling (`on`, `off`, `emit`)
   - Error handling

2. **MockSocketManager**:
   - Fallback for development
   - Simulates real socket behavior
   - Customizable latency and disconnects

3. **SocketContext**:
   - Provides socket state and methods to components
   - Handles reconnection logic
   - Exposes connection status

**Key Lessons:**
- Use composition over inheritance for EventEmitter in MockSocketManager
- Handle socket events consistently with proper typing
- Provide clear visual feedback about connection state
- Consider using type assertions when TypeScript interfaces conflict

### useMockSocket Reference Error

**Problem:**
Reference error to undefined `useMockSocket` variable in the `SocketContext.tsx` file:

```
ReferenceError: useMockSocket is not defined
```

**Solution:**
- Added state management for mock socket toggle: `const [useMockSocket, setUseMockSocket] = useState(false)`
- Properly initialized the state in the `useEffect` dependency array
- Added an environment check to automatically enable mock mode during development

### Connection Status Components

We've created two complementary components for displaying connection status:

1. **ConnectionStatus**: 
   - Detailed connection information
   - Reconnect button functionality 
   - Optional stats display with connection duration and reconnection count
   - Different visual states based on connection state

2. **ConnectionStatusIndicator**:
   - Compact indicator for navbar integration
   - Simple color-coded status representation
   - Icon changes based on connection state
   - Efficient for always-visible status monitoring

**Implementation Tips:**
- Use context to avoid prop drilling for connection state
- Implement different visual states for connecting, connected, disconnected
- Add animation for reconnecting state
- Display helpful tooltips for connection issues

### Context Provider Dependencies

#### ErrorProvider Integration

We recently encountered an issue where components using `useErrorHelpers()` were failing with:

```
Error: useError must be used within an ErrorProvider
```

This occurred because the `VideoPlayer` component was using error handling hooks, but the `ErrorProvider` wasn't added to our provider hierarchy in `src/app/providers.tsx`.

**Solution:**
- Added `ErrorProvider` as the outermost provider in the component tree
- Ensured proper nesting order of providers (ErrorProvider → ToastProvider → WatchPartyProvider)

#### Key Takeaways:
1. Always check the implementation of hooks to understand their dependencies
2. Maintain a clear hierarchy of providers in the application
3. Place "infrastructure" providers (error handling, theme, etc.) at higher levels in the tree
4. Document provider dependencies for team reference

### Windows PowerShell Command Syntax

When using Windows PowerShell, we discovered that the command chaining syntax differs from bash:

**Bash/Unix style:**
```bash
cd C:/Users/<USER>/project && npm run dev
```

**PowerShell style:**
```powershell
cd C:/Users/<USER>/project; npm run dev
```

This caused confusion when sharing development commands across the team. We've updated our documentation to clarify this platform-specific difference.

## Best Practices

### Context & Provider Design

1. **Create Provider Components:**
   - Every context should have a dedicated provider component
   - Export a custom hook to use the context safely
   - Implement error handling for out-of-context usage

2. **Provider Composition:**
   - Keep providers focused on single responsibilities
   - Document provider dependencies and proper nesting order
   - Consider creating a unified `Providers` component for the root layout

3. **Error Handling:**
   - Use appropriate error boundaries around key components
   - Implement graceful fallbacks for error states
   - Provide clear error messages to users

### Component Design

1. **Prop Interface Design:**
   - Use explicit TypeScript interfaces for props
   - Document required vs. optional props
   - Consider using discriminated unions for complex component states
   - Add JSDoc documentation to interfaces and components

2. **Error Handling:**
   - Handle potential errors within components
   - Use error boundaries for larger component sections
   - Provide fallback UI for error states

3. **Performance Considerations:**
   - Memoize expensive calculations
   - Use proper dependency arrays in hooks
   - Implement virtualization for long lists
   - Defer non-critical operations

4. **Media Player Implementation:**
   - Provide loading states with spinners
   - Add error fallbacks with retry options
   - Ensure proper aspect ratio containers
   - Add adequate padding to prevent overlap with navigation
   - Handle iframe events properly

## Common Pitfalls

### Provider Issues

1. **Missing Providers:**
   - Symptoms: Hooks throwing "must be used within Provider" errors
   - Solution: Ensure all components using context hooks are wrapped in the appropriate providers

2. **Incorrect Provider Order:**
   - Symptoms: Unexpected behavior when multiple contexts interact
   - Solution: Maintain consistent provider nesting order throughout the application

3. **Circular Dependencies:**
   - Symptoms: Import errors or unexpected behavior
   - Solution: Restructure code to break circular dependencies between contexts

### Component Issues

1. **Prop Type Mismatches:**
   - Symptoms: TypeScript errors or undefined behavior
   - Solution: Verify prop types match component interface

2. **Unhandled Promises:**
   - Symptoms: Unhandled promise rejections, unexpected state
   - Solution: Always handle promise rejections and loading states

3. **Missing Key Props:**
   - Symptoms: React warnings, incorrect rendering behavior
   - Solution: Always provide unique keys to elements in lists

4. **Message Mapping Issues:**
   - Symptoms: Undefined properties in message objects
   - Solution: Create proper adapter functions to map between different message schemas

5. **UI Layout Issues:**
   - Symptoms: Components overlapping, content cutoff
   - Solution: Add proper padding and margins, especially with fixed navigation
   - Use aspect-ratio containers for media elements

## Contribution Guidelines

1. **Provider Changes:**
   - Document provider dependencies
   - Update `providers.tsx` when adding new providers
   - Verify all affected components still work

2. **Context Updates:**
   - Consider impact on existing components
   - Update documentation
   - Check for type compatibility

3. **Documentation:**
   - Update these developer notes with new lessons
   - Document any tricky implementation details
   - Share knowledge with the team

## Resources

- [React Context Documentation](https://reactjs.org/docs/context.html)
- [TypeScript Handbook - Interfaces](https://www.typescriptlang.org/docs/handbook/interfaces.html)
- [Next.js Documentation - Error Handling](https://nextjs.org/docs/advanced-features/error-handling)
- [Socket.IO Documentation](https://socket.io/docs/v4/)
- [VidSrc API Documentation](https://vidsrc.me/api/) 
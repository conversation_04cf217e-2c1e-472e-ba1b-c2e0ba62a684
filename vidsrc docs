### Key Points
- Research suggests the VidSrc API is for streaming movies and TV shows, with endpoints for embedding content and listing latest additions.
- It seems likely that you need IMDb or TMDb IDs, and for TV episodes, season and episode numbers are required.
- The evidence leans toward using specific domains like vidsrc.xyz for embed URLs, with options for subtitles and language settings.
- There is a premium ad-free option at $10–$25 USD/month, with various payment methods.

### Getting Started
The VidSrc API is a tool for adding movie and TV show streaming to your website. It’s free to use, with options for premium features, and is ideal for developers looking to enhance their platforms with reliable streaming links.

#### What You’ll Need
- **IDs**: You’ll need IMDb or TMDb IDs for the content you want to stream. For TV episodes, also include the season and episode numbers.
- **Domains**: Use trusted domains like vidsrc.xyz, vidsrc.in, vidsrc.pm, or vidsrc.net for embed URLs.
- **Optional Features**: You can add subtitles with a CORS-enabled URL and set a default language using ISO639 codes (e.g., “de” for German).

#### Basic Implementation
To embed a movie, use an iframe with a URL like `https://vidsrc.xyz/embed/movie?imdb=tt5433140`. For episodes, add season and episode, like `https://vidsrc.xyz/embed/tv?imdb=tt0944947&season=1&episode=1`. Ensure your iframe includes `allowfullscreen` for user convenience.

#### Unexpected Detail
Did you know you can also list the latest movies, TV shows, and episodes using URLs like `https://vidsrc.xyz/movies/latest/page-1.json`? This can help build dynamic content feeds, which might be useful for keeping your site fresh.

---

### Survey Note: Comprehensive Guide to Implementing VidSrc API

This guide provides an in-depth look at implementing the VidSrc API, a video streaming integration tool designed for developers and streaming platforms. It supports both free and premium (ad-free) usage, making it suitable for enhancing websites with movie and TV show streaming. Below, we cover every detail needed for proper implementation, including endpoints, parameters, integration tips, common issues, best practices, and legal considerations.

#### Background and Purpose
VidSrc, accessible via [vidsrc.dev](https://vidsrc.dev/), is described as a “next-generation video streaming API” on sites like [VidSrc - Video Streaming API](https://vidsrc.icu/), providing high-quality, 1080p streaming links for movies and episodes. It is ideal for websites looking to integrate reliable, ad-free streaming, with automatic updates for new or better-quality links. The API supports responsive players compatible with desktops, mobiles, and tablets, ensuring a seamless user experience.

#### API Endpoints and Usage Instructions
The core of the API lies in its endpoints, which are detailed in the documentation at [API Documentation | VidSrc](https://vidsrc.me/api/). Below is a table summarizing the key endpoints, their descriptions, required and optional parameters, and examples:

| **Endpoint**                              | **Description**                     | **Required Parameters**                     | **Optional Parameters**                  | **Examples**                                                                 |
|-------------------------------------------|-------------------------------------|---------------------------------------------|------------------------------------------|-----------------------------------------------------------------------------|
| `https://vidsrc.xyz/embed/movie`          | Get movie embed URL                 | `imdb` or `tmdb` (from imdb.com or themoviedb.com) | `sub_url` (.srt or .vtt with CORS), `ds_lang` (ISO639 code) | `https://vidsrc.xyz/embed/movie?imdb=tt5433140`<br>`https://vidsrc.xyz/embed/movie?tmdb=385687&sub_url=https%3A%2F%2Fvidsrc.me%2Fsample.srt` |
| `https://vidsrc.xyz/embed/tv`             | Get TV show embed URL               | `imdb` or `tmdb` (from imdb.com or themoviedb.com) | `ds_lang` (ISO639 code)                    | `https://vidsrc.xyz/embed/tv?imdb=tt0944947`<br>`https://vidsrc.xyz/embed/tv?tmdb=1399&ds_lang=de` |
| `https://vidsrc.xyz/embed/tv`             | Get episode embed URL               | `imdb` or `tmdb` (from imdb.com or themoviedb.com), `season`, `episode` | `sub_url` (.srt or .vtt with CORS), `ds_lang` (ISO639 code) | `https://vidsrc.xyz/embed/tv?imdb=tt0944947&season=1&episode=1`<br>`https://vidsrc.xyz/embed/tv?tmdb=1399&season=1&episode=1&sub_url=https%3A%2F%2Fvidsrc.me%2Fsample.srt` |
| `https://vidsrc.xyz/movies/latest/page-PAGE_NUMBER.json` | List latest movies added | `PAGE_NUMBER`                                | None                                     | `https://vidsrc.xyz/movies/latest/page-1.json`<br>`https://vidsrc.xyz/movies/latest/page-15.json` |
| `https://vidsrc.xyz/tvshows/latest/page-PAGE_NUMBER.json` | List latest TV shows added | `PAGE_NUMBER`                                | None                                     | `https://vidsrc.xyz/tvshows/latest/page-1.json`<br>`https://vidsrc.xyz/tvshows/latest/page-15.json` |
| `https://vidsrc.xyz/episodes/latest/page-PAGE_NUMBER.json` | List latest episodes added | `PAGE_NUMBER`                                | None                                     | `https://vidsrc.xyz/episodes/latest/page-1.json`<br>`https://vidsrc.xyz/episodes/latest/page-25.json` |

**Parameter Details:**
- **ID Parameters**: The `imdb` or `tmdb` parameter is required for all embed endpoints, with IMDb IDs needing the “tt” prefix (e.g., `tt5433140`). These IDs can be sourced from [IMDb](https://www.imdb.com/) or [The Movie Database](https://www.themoviedb.org/).
- **Season and Episode**: For TV episodes, both `season` and `episode` are mandatory, specifying the season and episode numbers (e.g., `season=1&episode=1`).
- **Subtitles**: The `sub_url` parameter allows for external subtitle files in `.srt` or `.vtt` format, but the URL must have CORS enabled to ensure compatibility. For example, `sub_url=https%3A%2F%2Fvidsrc.me%2Fsample.srt` requires the linked file to allow cross-origin requests.
- **Language**: The `ds_lang` parameter sets the default subtitle language using ISO639 codes (e.g., “de” for German), enhancing accessibility for multilingual audiences.

#### Domain and Versioning
The documentation specifies using domains like `vidsrc.in`, `vidsrc.pm`, `vidsrc.xyz`, and `vidsrc.net` for embed URLs, ensuring reliability. Additionally, the API supports versioning, with options for `v2` or `v3`, which can be specified in the URL if needed (e.g., `https://vidsrc.xyz/v2/embed/movie?imdb=tt5433140`).

#### Player Features and Integration
The player is responsive and optimized for various devices, with features like automatic quality updates to the latest available (mostly 1080p) and a list of fastest streaming servers for user selection. An interesting feature is the ability to listen to player events (e.g., `play`, `pause`, `ended`) via `postMessage` to the parent window, allowing developers to track user interactions and enhance analytics.

#### Pricing and Premium Options
While the API is free for basic use, ad-free access is available through a premium subscription. Pricing details include:
- $10 USD/month for basic ad-free, brand-free access.
- $25 USD/month for access with 4K streams.
Payment methods supported are Bitcoin, Ethereum, Litecoin, Tether, Monero, CashApp, and PayPal. Users can upgrade to premium via [Purchase Premium](https://vidsrc.dev/purchase.php), which is particularly useful for commercial websites aiming to provide a seamless, ad-free experience.

#### Integration Tips and Best Practices
Research from community discussions, such as the [WJunction thread](https://www.wjunction.com/threads/vidsrc-me-movie-streaming-api-discussion-thread.261605/), provides practical insights:
- **Custom Domain**: Use Cloudflare and point to IP ************ with SSL mode Flexible for custom domains, reducing ads by 50% without the Vidsrc logo.
- **Easy Site Integration**: Connect your domain by changing name servers for a fully automated site with designs like 123movies, fmovies, solarmovies, soap2day, etc.
- **IPTV Integration**: Suitable for IPTV resellers on the xtreamui platform.
- **Dashboard Features**: Includes custom logo (premium), custom player design, ad placements, language selection for subtitles, and caching (e.g., saving settings like Italian subtitles and 30% volume).
- **Upcoming CMS**: A new CMS will allow setting up streaming sites with templates like 123movies, Soap2day, Fmovies, Putlocker in a few clicks.
- **Monitor Downtime**: Bookmark the domain status page ([Vidsrc.Domains](https://vidsrc.domains/)) for updates on any downtime.
- **Ad Management**: For sites with over 10,000 daily users, opt for 50% ad reduction. Choose ad earnings options: 0%, 50%, or 100% to earn from traffic, avoiding DMCA and file expiration issues.
- **Premium Option**: $1 for 1,000 plays (1 play = 1 unique user/IP watching over 5% of movie/episode, no multiple counts).

#### Common Issues and Solutions
- **Bug in Iframe Code**: A known issue is using TMDB tags with IMDb IDs; ensure correct ID usage.
- **Adding Servers to Existing Movies**: Requires starting over with plugins like psyplay or dooplay, as there’s no direct add option.
- **Plugin Issues**: For Dooplay, content might not add automatically, requiring manual intervention.

#### Code Examples and Implementation
For web integration, you can use iframes. Here’s an example for a movie:
```html
<iframe src="https://vidsrc.xyz/embed/movie?imdb=tt5433140" width="640" height="360" frameborder="0" allowfullscreen></iframe>
```
For an episode:
```html
<iframe src="https://vidsrc.xyz/embed/tv?imdb=tt0944947&season=1&episode=1" width="640" height="360" frameborder="0" allowfullscreen></iframe>
```
Community implementations, such as [GitHub - Inside4ndroid/vidsrc-api-js](https://github.com/Inside4ndroid/vidsrc-api-js), provide JavaScript examples:
- Clone the repo, install with `npm install`, and run with `npm run main`.
- Example URLs: `https://your-domain/embedsu/916224` for movies, `https://your-domain/embedsu/1429?s=1&e=1` for shows.
Another example, [GitHub - cool-dev-guy/vidsrc-api](https://github.com/cool-dev-guy/vidsrc-api), offers Python-based scraping:
- Deploy on Vercel or run locally with FastAPI, using endpoints like `/vidsrc/{db_id}`.

#### Error Handling
The API may return a 200 status code even on errors, with an empty sources array indicating failure. Check the response content for success, as seen in community implementations.

#### Legal Considerations
Although terms of service were not found on the main pages ([vidsrc.dev](https://vidsrc.dev/), [vidsrc.me.atlaq.com/](https://vidsrc.me.atlaq.com/), [vidsrc.net.atlaq.com/](https://vidsrc.net.atlaq.com/)), it’s crucial to review them for compliance, especially regarding content distribution and copyright. Ensure your use complies with all applicable laws and regulations, and contact VidSrc for legal information if needed.

#### Security and Best Practices
A note from [VidSrc - Video Streaming API](https://vidsrc.domains/) warns against fake VidSrc sites, which may steal information or provide poor user experiences. Use only legitimate domains listed in the documentation to avoid security risks.

#### Additional Resources and Community Insights
Research into GitHub repositories, such as [GitHub - cool-dev-guy/vidsrc-api](https://github.com/cool-dev-guy/vidsrc-api) and [GitHub - Inside4ndroid/vidsrc-api-js](https://github.com/Inside4ndroid/vidsrc-api-js), reveals community efforts to create extractors and wrappers for the API. These projects, often educational in nature, provide alternative implementations, such as running on platforms like Vercel, and highlight the API’s flexibility for self-hosting. However, users are advised to check for updates, as some repositories note deprecations (e.g., vidsrc.to is no longer active).

#### Conclusion
The VidSrc API is a robust tool for streaming integration, with clear endpoints for movies, TV shows, and episodes, and additional features for listing latest content. Its support for subtitles, language settings, and premium ad-free options makes it versatile for both personal and commercial use. Developers should ensure CORS compliance for subtitles, consider premium options for enhanced user experiences, and review legal terms for compliance.

### Key Citations
- [VidSrc API Documentation detailed endpoints and parameters](https://vidsrc.me/api/)
- [VidSrc Official Site movie and TV streaming API](https://vidsrc.dev/)
- [VidSrc Video Streaming API free integration overview](https://vidsrc.icu/)
- [GitHub vidsrc-api working extractor implementation](https://github.com/cool-dev-guy/vidsrc-api)
- [GitHub vidsrc-api-js JavaScript implementation](https://github.com/Inside4ndroid/vidsrc-api-js)
- [VidSrc Domains legitimate site warning](https://vidsrc.domains/)
- [Purchase Premium upgrade to ad-free access](https://vidsrc.dev/purchase.php)
- [WJunction Discussion Thread Vidsrc API pros and cons](https://www.wjunction.com/threads/vidsrc-me-movie-streaming-api-discussion-thread.261605/)
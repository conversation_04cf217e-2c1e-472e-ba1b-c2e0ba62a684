import { NextRequest, NextResponse } from 'next/server';

/**
 * GET /api/test-cloudinary
 * Test Cloudinary configuration and upload preset availability
 */
export async function GET(request: NextRequest) {
  try {
    const cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME;
    const uploadPreset = process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET;

    // Test if we can access the Cloudinary upload endpoint
    const testUrl = `https://api.cloudinary.com/v1_1/${cloudName}/image/upload`;
    
    // Create a simple test request to check if the upload preset exists
    const formData = new FormData();
    formData.append('upload_preset', uploadPreset || '');
    formData.append('file', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='); // 1x1 transparent PNG

    // Test with banner-specific settings
    const bannerFormData = new FormData();
    bannerFormData.append('upload_preset', uploadPreset || '');
    bannerFormData.append('file', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='); // 1x1 transparent PNG
    // Don't specify folder to match the current banner widget configuration

    try {
      // Test 1: Basic upload (same as before)
      const response = await fetch(testUrl, {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      // Test 2: Banner-specific upload (without folder specification)
      const bannerResponse = await fetch(testUrl, {
        method: 'POST',
        body: bannerFormData,
      });

      const bannerResult = await bannerResponse.json();

      return NextResponse.json({
        success: response.ok && bannerResponse.ok,
        cloudName,
        uploadPreset,
        testUrl,
        basicTest: {
          status: response.status,
          success: response.ok,
          response: result
        },
        bannerTest: {
          status: bannerResponse.status,
          success: bannerResponse.ok,
          response: bannerResult
        },
        message: (response.ok && bannerResponse.ok) ? 'Cloudinary configuration is working for both basic and banner uploads' : 'Some Cloudinary tests failed'
      });

    } catch (fetchError) {
      return NextResponse.json({
        success: false,
        cloudName,
        uploadPreset,
        testUrl,
        error: fetchError.message,
        message: 'Failed to connect to Cloudinary'
      });
    }

  } catch (error) {
    console.error('Error testing Cloudinary configuration:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to test Cloudinary configuration',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

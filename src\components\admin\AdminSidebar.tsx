'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Home,
  Users,
  Film,
  Settings,
  BarChart3,
  FileText,
  Menu,
  X,
  Clock,
  Shield,
  Server,
  Tag,
  Star,
  Activity,
  Layers,
  ChevronDown,
  ArrowLeft,
  UserCheck,
  UserX,
  Bell,
  Mail,
  Image,
  HelpCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { UserAvatar } from '@/components/UserAvatar';
import { useProfiles } from '@/contexts/ProfileContext';
import { useAuth } from '@/contexts/AuthContext';

interface SidebarLink {
  href: string;
  icon: React.ReactNode;
  label: string;
  notification?: boolean;
  submenu?: SidebarLink[];
  isExpanded?: boolean;
}

export function AdminSidebar() {
  const pathname = usePathname();
  const { user, isAdmin } = useAuth();
  const { activeProfile } = useProfiles();
  const [isMobileOpen, setIsMobileOpen] = React.useState(false);

  // State for profile image and related data
  const [profileImage, setProfileImage] = useState('');
  const [userInitials, setUserInitials] = useState('SV');
  const [displayName, setDisplayName] = useState('Admin User');

  const toggleMobileSidebar = () => {
    setIsMobileOpen(!isMobileOpen);
  };

  const [expandedMenus, setExpandedMenus] = React.useState<Record<string, boolean>>({});

  const toggleSubmenu = (label: string) => {
    setExpandedMenus(prev => ({
      ...prev,
      [label]: !prev[label]
    }));
  };

  // Update profile image and related state when activeProfile or user changes
  useEffect(() => {
    // Set default profile image - prefer active profile image if available
    const imgSrc = activeProfile?.avatar || user?.picture || '/favicon.svg';
    setProfileImage(imgSrc);

    // Calculate initials as fallback
    if (!user?.name) {
      setUserInitials('A');
    } else {
      const nameParts = user.name.split(' ');
      if (nameParts.length === 1) {
        setUserInitials(nameParts[0].charAt(0).toUpperCase());
      } else {
        setUserInitials((nameParts[0].charAt(0) + nameParts[nameParts.length - 1].charAt(0)).toUpperCase());
      }
    }

    // Set display name
    setDisplayName(user?.name || 'Admin User');
  }, [activeProfile, user]);

  // Format role for display
  const formatRole = (role?: string): string => {
    if (!role) return 'User';
    return role.charAt(0).toUpperCase() + role.slice(1);
  };

  const links: SidebarLink[] = [
    {
      href: '/admin',
      icon: <Home className="h-4 w-4 mr-3" />,
      label: 'Dashboard'
    },
    {
      href: '/admin/users',
      icon: <Users className="h-4 w-4 mr-3" />,
      label: 'Users',
      submenu: [
        {
          href: '/admin/users',
          icon: <Users className="h-4 w-4 mr-3" />,
          label: 'All Users'
        },
        {
          href: '/admin/visitors',
          icon: <UserX className="h-4 w-4 mr-3" />,
          label: 'Anonymous Visitors'
        },
        {
          href: '/admin/activity',
          icon: <Clock className="h-4 w-4 mr-3" />,
          label: 'Activity Logs'
        },
        {
          href: '/admin/users/permissions',
          icon: <Shield className="h-4 w-4 mr-3" />,
          label: 'Permissions'
        }
      ]
    },
    {
      href: '/admin/content',
      icon: <Film className="h-4 w-4 mr-3" />,
      label: 'Content',
      submenu: [
        {
          href: '/admin/content',
          icon: <Layers className="h-4 w-4 mr-3" />,
          label: 'All Content'
        },
        {
          href: '/admin/content/categories',
          icon: <Tag className="h-4 w-4 mr-3" />,
          label: 'Categories & Tags'
        },
        {
          href: '/admin/content/featured',
          icon: <Star className="h-4 w-4 mr-3" />,
          label: 'Featured Content'
        }
      ]
    },
    {
      href: '/admin/notifications',
      icon: <Bell className="h-4 w-4 mr-3" />,
      label: 'Notifications'
    },
    {
      href: '/admin/banner-ads',
      icon: <Image className="h-4 w-4 mr-3" />,
      label: 'Banner Ads'
    },
    {
      href: '/admin/help',
      icon: <HelpCircle className="h-4 w-4 mr-3" />,
      label: 'Help Center'
    },
    {
      href: '/admin/system',
      icon: <Server className="h-4 w-4 mr-3" />,
      label: 'System',
      submenu: [
        {
          href: '/admin/system',
          icon: <Server className="h-4 w-4 mr-3" />,
          label: 'System Health'
        },
        {
          href: '/admin/system/logs',
          icon: <FileText className="h-4 w-4 mr-3" />,
          label: 'System Logs'
        }
      ]
    },
    {
      href: '/admin/settings',
      icon: <Settings className="h-4 w-4 mr-3" />,
      label: 'Settings'
    }
  ];

  return (
    <>
      {/* Mobile Menu Toggle */}
      <div className="fixed top-4 left-4 z-50 md:hidden">
        <Button
          variant="outline"
          size="icon"
          onClick={toggleMobileSidebar}
          className="bg-vista-dark border-vista-blue/20"
        >
          {isMobileOpen ? (
            <X className="h-4 w-4" />
          ) : (
            <Menu className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Sidebar */}
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-40 w-64 bg-vista-dark-lighter border-r border-vista-blue/10 transition-transform duration-300 ease-in-out",
          isMobileOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0"
        )}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="p-4 border-b border-vista-blue/10">
            <Link href="/admin" className="block mb-4">
              <h2 className="text-xl font-bold text-vista-light flex items-center">
                <span className="text-vista-blue mr-2">Stream</span>Vista Admin
              </h2>
            </Link>

            {/* User Profile Section */}
            <div className="flex items-center space-x-3 mt-2">
              <UserAvatar
                src={profileImage}
                alt={displayName}
                fallback={userInitials}
                size="md"
                className="border-2 border-vista-blue/30"
              />
              <div>
                <p className="text-vista-light font-medium text-sm truncate max-w-[200px]">
                  {displayName}
                </p>
                <div className="flex items-center">
                  <Badge variant="outline" className="text-[10px] h-4 px-1 border-vista-blue/30 bg-vista-blue/10 text-vista-blue">
                    <UserCheck className="h-2.5 w-2.5 mr-0.5" />
                    {formatRole(user?.role)}
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 overflow-y-auto py-4">
            <div className="px-4 mb-2">
              <p className="text-vista-light/50 text-xs font-medium uppercase tracking-wider">
                Menu
              </p>
            </div>
            <ul className="space-y-1 px-2">
              {links.map((link) => (
                <li key={link.href} className="space-y-1">
                  {link.submenu ? (
                    <>
                      <button
                        className={cn(
                          "flex items-center w-full px-3 py-2 rounded-md text-vista-light/80 hover:bg-vista-dark hover:text-vista-light transition-colors",
                          (pathname === link.href || pathname.startsWith(`${link.href}/`)) &&
                            "bg-vista-dark text-vista-light font-medium"
                        )}
                        onClick={() => toggleSubmenu(link.label)}
                      >
                        {link.icon}
                        <span>{link.label}</span>
                        <ChevronDown
                          className={cn(
                            "ml-auto h-4 w-4 transition-transform",
                            expandedMenus[link.label] && "rotate-180"
                          )}
                        />
                      </button>

                      {expandedMenus[link.label] && (
                        <ul className="mt-1 ml-4 space-y-1 border-l border-vista-blue/20 pl-2">
                          {link.submenu.map((sublink) => (
                            <li key={sublink.href}>
                              <Link
                                href={sublink.href}
                                className={cn(
                                  "flex items-center px-3 py-2 rounded-md text-vista-light/70 hover:bg-vista-dark hover:text-vista-light transition-colors",
                                  pathname === sublink.href && "bg-vista-dark text-vista-light font-medium border-l-2 border-vista-blue"
                                )}
                                onClick={() => setIsMobileOpen(false)}
                              >
                                {sublink.icon}
                                <span>{sublink.label}</span>
                              </Link>
                            </li>
                          ))}
                        </ul>
                      )}
                    </>
                  ) : (
                    <Link
                      href={link.href}
                      className={cn(
                        "flex items-center px-3 py-2 rounded-md text-vista-light/80 hover:bg-vista-dark hover:text-vista-light transition-colors",
                        pathname === link.href && "bg-vista-dark text-vista-light font-medium border-l-2 border-vista-blue"
                      )}
                      onClick={() => setIsMobileOpen(false)}
                    >
                      {link.icon}
                      <span>{link.label}</span>
                      {link.notification && (
                        <Badge
                          variant="destructive"
                          className="ml-auto flex h-5 w-5 items-center justify-center rounded-full p-0 text-xs"
                        >
                          !
                        </Badge>
                      )}
                    </Link>
                  )}
                </li>
              ))}
            </ul>
          </nav>

          {/* Footer - Enhanced Return to Home Button */}
          <div className="p-4 mt-auto border-t border-vista-blue/10">
            <div className="rounded-lg overflow-hidden shadow-lg">
              <div className="bg-vista-blue/10 px-4 py-3">
                <p className="text-vista-light text-sm font-medium">Return to Main Site</p>
              </div>
              <div className="bg-gradient-to-br from-vista-dark to-vista-dark-lighter p-4">
                <p className="text-vista-light/70 text-xs mb-3">
                  Exit admin panel and return to the main StreamVista website
                </p>
                <Link href="/" passHref>
                  <Button
                    variant="default"
                    className="w-full bg-vista-blue hover:bg-vista-blue/90 text-white group transition-all duration-300 ease-in-out"
                    onClick={() => setIsMobileOpen(false)}
                  >
                    <ArrowLeft className="h-4 w-4 mr-2 group-hover:-translate-x-1 transition-transform" />
                    Return to Home
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Backdrop for mobile */}
      {isMobileOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-30 md:hidden"
          onClick={() => setIsMobileOpen(false)}
        />
      )}
    </>
  );
}
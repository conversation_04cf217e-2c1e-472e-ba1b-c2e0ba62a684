'use client';

import { useState, useEffect } from 'react';
import { VIDSRC_DOMAINS } from '@/lib/vidsrc-api';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface DomainSelectorProps {
  onDomainChange: (domain: string) => void;
  currentDomain?: string;
}

export default function DomainSelector({ onDomainChange, currentDomain }: DomainSelectorProps) {
  const [selectedDomain, setSelectedDomain] = useState(currentDomain || VIDSRC_DOMAINS[0]);

  useEffect(() => {
    // If the current domain is changed externally, update the local state
    if (currentDomain && currentDomain !== selectedDomain) {
      setSelectedDomain(currentDomain);
    }
  }, [currentDomain, selectedDomain]);

  const handleDomainChange = (domain: string) => {
    setSelectedDomain(domain);
    onDomainChange(domain);
    
    // Simple console log instead of toast notification
    console.log(`Domain changed to: ${domain}`);
  };

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm text-muted-foreground">Domain:</span>
      <Select value={selectedDomain} onValueChange={handleDomainChange}>
        <SelectTrigger className="w-40">
          <SelectValue placeholder="Select Domain" />
        </SelectTrigger>
        <SelectContent>
          {VIDSRC_DOMAINS.map(domain => (
            <SelectItem key={domain} value={domain}>
              {domain}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
} 
import { NextRequest, NextResponse } from 'next/server';

/**
 * POST /api/content/[id]/interact
 * Content interaction tracking has been disabled
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  // Content interaction tracking has been disabled
  return NextResponse.json({
    success: true,
    message: 'Content interaction tracking has been disabled'
  });
}

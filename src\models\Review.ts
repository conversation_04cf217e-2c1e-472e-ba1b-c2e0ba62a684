import mongoose, { Schema, Document } from 'mongoose';

export interface IReview extends Document {
  contentId: string;
  contentType: 'movie' | 'show';
  userId: string;
  username: string;
  rating: number;
  comment: string;
  likes: number;
  dislikes: number;
  createdAt: Date;
  updatedAt: Date;
}

// Define the Review schema
const ReviewSchema = new Schema<IReview>({
  contentId: { type: String, required: true },
  contentType: { type: String, enum: ['movie', 'show'], required: true },
  userId: { type: String, required: true },
  username: { type: String, required: true },
  rating: { type: Number, required: true, min: 1, max: 5 },
  comment: { type: String, required: true },
  likes: { type: Number, default: 0 },
  dislikes: { type: Number, default: 0 },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
}, {
  timestamps: true
});

// Add indexes for common queries
ReviewSchema.index({ contentId: 1, contentType: 1 });
ReviewSchema.index({ userId: 1 });
ReviewSchema.index({ rating: 1 });
ReviewSchema.index({ createdAt: -1 });

// Use mongoose.models.Review if it exists, otherwise create a new model
const Review = mongoose.models.Review as mongoose.Model<IReview> || 
               mongoose.model<IReview>('Review', ReviewSchema);

export default Review;

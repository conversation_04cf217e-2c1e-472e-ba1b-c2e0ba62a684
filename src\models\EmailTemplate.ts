import mongoose, { Document, Schema } from 'mongoose';

export interface EmailTemplateDocument extends Document {
  name: string;
  subject: string;
  body: string;
  description: string;
  variables: string[];
  isDefault: boolean;
  createdBy: mongoose.Types.ObjectId;
  updatedBy: mongoose.Types.ObjectId;
  active: boolean;
}

const EmailTemplateSchema = new Schema<EmailTemplateDocument>(
  {
    name: {
      type: String,
      required: true,
      unique: true,
      trim: true
    },
    subject: {
      type: String,
      required: true,
      trim: true
    },
    body: {
      type: String,
      required: true
    },
    description: {
      type: String,
      required: true
    },
    variables: [{
      type: String,
      trim: true
    }],
    isDefault: {
      type: Boolean,
      default: false
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    active: {
      type: Boolean,
      default: true
    }
  },
  {
    timestamps: true
  }
);

// Create or retrieve the model
const EmailTemplate = mongoose.models.EmailTemplate || mongoose.model<EmailTemplateDocument>('EmailTemplate', EmailTemplateSchema);

export default EmailTemplate;

/**
 * VidSrc API Service
 * 
 * Implements all VidSrc API endpoints as documented in the official VidSrc documentation.
 * Supports multiple domains with automatic fallback, proper error handling, and TypeScript types.
 */

// Official VidSrc domains as per documentation
export const VIDSRC_DOMAINS = ['vidsrc.xyz', 'vidsrc.in', 'vidsrc.pm', 'vidsrc.net'] as const;
export type VidSrcDomain = typeof VIDSRC_DOMAINS[number];

// VidSrc API response types
export interface VidSrcMovie {
  id: string;
  title: string;
  year?: string;
  poster?: string;
  added?: string;
  imdb_id?: string;
  tmdb_id?: string;
}

export interface VidSrcShow {
  id: string;
  title: string;
  year?: string;
  poster?: string;
  added?: string;
  imdb_id?: string;
  tmdb_id?: string;
}

export interface VidSrcEpisode {
  id: string;
  title: string;
  year?: string;
  poster?: string;
  added?: string;
  imdb_id?: string;
  tmdb_id?: string;
  season?: string;
  episode?: string;
  show_title?: string;
}

// VidSrc embed URL builder
export interface VidSrcEmbedOptions {
  imdbId?: string;
  tmdbId?: string;
  season?: number;
  episode?: number;
  subUrl?: string;
  dsLang?: string;
  autoplay?: boolean;
  autonext?: boolean;
  color?: string;
}

/**
 * Build VidSrc embed URL for movies
 */
export function buildMovieEmbedUrl(
  domain: VidSrcDomain,
  options: VidSrcEmbedOptions
): string {
  const { imdbId, tmdbId, subUrl, dsLang, autoplay, color } = options;
  
  if (!imdbId && !tmdbId) {
    throw new Error('Either imdbId or tmdbId is required for VidSrc embed');
  }

  let url = `https://${domain}/embed/movie`;
  
  // Add ID parameter
  if (imdbId) {
    url += `?imdb=${imdbId}`;
  } else if (tmdbId) {
    url += `?tmdb=${tmdbId}`;
  }

  // Add optional parameters
  const params = new URLSearchParams();
  
  if (subUrl) {
    params.append('sub_url', subUrl);
  }
  
  if (dsLang) {
    params.append('ds_lang', dsLang);
  }
  
  if (autoplay !== undefined) {
    params.append('autoplay', autoplay ? '1' : '0');
  }

  // Add parameters to URL
  if (params.toString()) {
    url += (url.includes('?') ? '&' : '?') + params.toString();
  }

  // Add color customization if specified
  if (color) {
    url += `/color-${color}`;
  }

  return url;
}

/**
 * Build VidSrc embed URL for TV shows
 */
export function buildTVEmbedUrl(
  domain: VidSrcDomain,
  options: VidSrcEmbedOptions
): string {
  const { imdbId, tmdbId, season, episode, subUrl, dsLang, autoplay, autonext } = options;
  
  if (!imdbId && !tmdbId) {
    throw new Error('Either imdbId or tmdbId is required for VidSrc embed');
  }

  let url = `https://${domain}/embed/tv`;
  
  // Add ID parameter
  if (imdbId) {
    url += `?imdb=${imdbId}`;
  } else if (tmdbId) {
    url += `?tmdb=${tmdbId}`;
  }

  // Add season and episode if provided
  if (season !== undefined) {
    url += (url.includes('?') ? '&' : '?') + `season=${season}`;
  }
  
  if (episode !== undefined) {
    url += (url.includes('?') ? '&' : '?') + `episode=${episode}`;
  }

  // Add optional parameters
  const params = new URLSearchParams();
  
  if (subUrl) {
    params.append('sub_url', subUrl);
  }
  
  if (dsLang) {
    params.append('ds_lang', dsLang);
  }
  
  if (autoplay !== undefined) {
    params.append('autoplay', autoplay ? '1' : '0');
  }
  
  if (autonext !== undefined) {
    params.append('autonext', autonext ? '1' : '0');
  }

  // Add parameters to URL
  if (params.toString()) {
    url += (url.includes('?') ? '&' : '?') + params.toString();
  }

  return url;
}

/**
 * Get latest movies URL
 */
export function getLatestMoviesUrl(page: number = 1, domain: VidSrcDomain = 'vidsrc.xyz'): string {
  return `https://${domain}/movies/latest/page-${page}.json`;
}

/**
 * Get latest TV shows URL
 */
export function getLatestShowsUrl(page: number = 1, domain: VidSrcDomain = 'vidsrc.xyz'): string {
  return `https://${domain}/tvshows/latest/page-${page}.json`;
}

/**
 * Get latest episodes URL
 */
export function getLatestEpisodesUrl(page: number = 1, domain: VidSrcDomain = 'vidsrc.xyz'): string {
  return `https://${domain}/episodes/latest/page-${page}.json`;
}

/**
 * Fetch data from VidSrc API with domain fallback
 */
export async function fetchVidSrcData<T>(
  urlBuilder: (domain: VidSrcDomain) => string,
  timeout: number = 10000
): Promise<T> {
  const errors: Error[] = [];

  for (const domain of VIDSRC_DOMAINS) {
    try {
      const url = urlBuilder(domain);
      console.log(`[VidSrc] Attempting to fetch from ${domain}: ${url}`);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
          'Accept': 'application/json',
          'Accept-Language': 'en-US,en;q=0.9',
        },
        signal: controller.signal,
        cache: 'no-store',
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`[VidSrc] Successfully fetched from ${domain}`);
      
      return data as T;
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      console.warn(`[VidSrc] Failed to fetch from ${domain}:`, err.message);
      errors.push(err);
    }
  }

  throw new Error(`All VidSrc domains failed. Errors: ${errors.map(e => e.message).join(', ')}`);
}

/**
 * Fetch latest movies
 */
export async function fetchLatestMovies(page: number = 1): Promise<VidSrcMovie[]> {
  return fetchVidSrcData<VidSrcMovie[]>(
    (domain) => getLatestMoviesUrl(page, domain)
  );
}

/**
 * Fetch latest TV shows
 */
export async function fetchLatestShows(page: number = 1): Promise<VidSrcShow[]> {
  return fetchVidSrcData<VidSrcShow[]>(
    (domain) => getLatestShowsUrl(page, domain)
  );
}

/**
 * Fetch latest episodes
 */
export async function fetchLatestEpisodes(page: number = 1): Promise<VidSrcEpisode[]> {
  return fetchVidSrcData<VidSrcEpisode[]>(
    (domain) => getLatestEpisodesUrl(page, domain)
  );
}

/**
 * Check if a VidSrc domain is healthy
 */
export async function checkDomainHealth(domain: VidSrcDomain): Promise<{
  domain: VidSrcDomain;
  healthy: boolean;
  responseTime: number;
  error?: string;
}> {
  const startTime = Date.now();
  
  try {
    const url = `https://${domain}/movies/latest/page-1.json`;
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
      },
      signal: controller.signal,
    });

    clearTimeout(timeoutId);
    const responseTime = Date.now() - startTime;

    return {
      domain,
      healthy: response.ok,
      responseTime,
      error: response.ok ? undefined : `HTTP ${response.status}`
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    return {
      domain,
      healthy: false,
      responseTime,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Get the healthiest VidSrc domain
 */
export async function getHealthiestDomain(): Promise<VidSrcDomain> {
  const healthChecks = await Promise.all(
    VIDSRC_DOMAINS.map(domain => checkDomainHealth(domain))
  );

  const healthyDomains = healthChecks.filter(check => check.healthy);
  
  if (healthyDomains.length === 0) {
    console.warn('[VidSrc] No healthy domains found, using fallback');
    return 'vidsrc.xyz';
  }

  // Return the fastest healthy domain
  const fastest = healthyDomains.reduce((prev, current) => 
    prev.responseTime < current.responseTime ? prev : current
  );

  return fastest.domain;
} 
'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, RefreshCw, Shield, User, Users } from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { formatDistanceToNow } from 'date-fns';

interface AuditLog {
  id: string;
  userId: string;
  userName: string;
  action: string;
  details: string;
  timestamp: string;
  metadata?: {
    targetUserId?: string;
    targetUserName?: string;
    role?: string;
    permissions?: Record<string, boolean>;
    [key: string]: any;
  };
}

interface PermissionAuditLogsProps {
  userId?: string;
  limit?: number;
}

export default function PermissionAuditLogs({ userId, limit = 20 }: PermissionAuditLogsProps) {
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch logs on mount and when dependencies change
  useEffect(() => {
    fetchLogs();
  }, [userId, limit]);

  // Fetch logs from API
  const fetchLogs = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Get adminUserId from localStorage as a fallback for cookie authentication
      const adminUserId = localStorage.getItem('userId');

      if (!adminUserId) {
        throw new Error('Admin user ID not found. Please sign in again.');
      }

      // Build URL with query parameters
      let url = `/api/admin/activity/logs?type=permission&adminUserId=${adminUserId}`;

      // If a specific user's logs are requested, add that as targetUserId to avoid confusion
      if (userId) {
        url += `&targetUserId=${userId}`;
      }

      if (limit) {
        url += `&limit=${limit}`;
      }

      const response = await fetch(url, {
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to fetch logs (${response.status})`);
      }

      const data = await response.json();
      setLogs(data.logs || []);
    } catch (error) {
      console.error('Error fetching permission audit logs:', error);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    try {
      return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
    } catch (error) {
      return 'Unknown';
    }
  };

  // Get action badge variant
  const getActionBadgeVariant = (action: string) => {
    switch (action) {
      case 'update_user_permissions':
        return 'default';
      case 'reset_user_permissions':
        return 'secondary';
      case 'update_default_permissions':
        return 'outline';
      case 'reset_default_permissions':
        return 'destructive';
      case 'bulk_update_permissions':
        return 'success';
      default:
        return 'outline';
    }
  };

  // Get action icon
  const getActionIcon = (action: string) => {
    switch (action) {
      case 'update_user_permissions':
        return <User className="h-4 w-4" />;
      case 'reset_user_permissions':
        return <RefreshCw className="h-4 w-4" />;
      case 'update_default_permissions':
        return <Shield className="h-4 w-4" />;
      case 'reset_default_permissions':
        return <Shield className="h-4 w-4" />;
      case 'bulk_update_permissions':
        return <Users className="h-4 w-4" />;
      default:
        return <Shield className="h-4 w-4" />;
    }
  };

  // Format action name
  const formatActionName = (action: string) => {
    switch (action) {
      case 'update_user_permissions':
        return 'Update User Permissions';
      case 'reset_user_permissions':
        return 'Reset User Permissions';
      case 'update_default_permissions':
        return 'Update Default Permissions';
      case 'reset_default_permissions':
        return 'Reset Default Permissions';
      case 'bulk_update_permissions':
        return 'Bulk Update Permissions';
      default:
        return action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-8 flex justify-center items-center">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-vista-blue" />
            <p className="text-vista-light">Loading permission logs...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-vista-light">Permission Audit Logs</CardTitle>
          <CardDescription>
            Track changes to user permissions
          </CardDescription>
        </CardHeader>
        <CardContent className="p-8 text-center">
          <p className="text-red-500 mb-4">{error}</p>
          <Button onClick={fetchLogs}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  // Empty state
  if (logs.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-vista-light">Permission Audit Logs</CardTitle>
          <CardDescription>
            Track changes to user permissions
          </CardDescription>
        </CardHeader>
        <CardContent className="p-8 text-center">
          <p className="text-vista-light/70 mb-4">No permission logs found.</p>
          <Button onClick={fetchLogs}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
          <div>
            <CardTitle className="text-vista-light flex items-center gap-2">
              <Shield className="h-5 w-5 text-vista-blue" />
              Permission Audit Logs
            </CardTitle>
            <CardDescription>
              Track changes to user permissions
            </CardDescription>
          </div>
          <Button variant="outline" size="sm" onClick={fetchLogs}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="border rounded-md border-vista-light/10 overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Admin</TableHead>
                <TableHead>Action</TableHead>
                <TableHead>Details</TableHead>
                <TableHead>Time</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {logs.map(log => (
                <TableRow key={log.id}>
                  <TableCell className="font-medium">{log.userName || 'Unknown'}</TableCell>
                  <TableCell>
                    <Badge
                      variant={getActionBadgeVariant(log.action)}
                      className="flex items-center gap-1"
                    >
                      {getActionIcon(log.action)}
                      {formatActionName(log.action)}
                    </Badge>
                  </TableCell>
                  <TableCell>{log.details}</TableCell>
                  <TableCell>{formatTimestamp(log.timestamp)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}

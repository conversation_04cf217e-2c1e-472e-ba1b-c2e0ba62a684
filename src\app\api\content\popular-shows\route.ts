import { NextRequest, NextResponse } from 'next/server';
import { getPopularTVShows } from '@/lib/tmdb-api';
import { formatTMDbContentForCards } from '@/lib/content-utils';

export async function GET(request: NextRequest) {
  try {
    // Get page from query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    
    // Fetch popular TV shows from TMDb
    const popularShows = await getPopularTVShows(page);
    
    // Format the data for content cards
    const formattedShows = formatTMDbContentForCards(popularShows);
    
    // Return the formatted data
    return NextResponse.json({
      success: true,
      data: formattedShows,
      page,
      hasMore: page < 5 // Limit to 5 pages for now
    });
  } catch (error) {
    console.error('Error fetching popular TV shows:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch popular TV shows' },
      { status: 500 }
    );
  }
}

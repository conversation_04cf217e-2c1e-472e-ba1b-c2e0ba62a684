// French translations (placeholder - this would include full translations)
import en from './en';

// Start with English translations as a fallback
const translations = { ...en };

// Add a few sample French translations to demonstrate functionality
const frenchTranslations = {
  'common.loading': 'Chargement...',
  'common.search': 'Rechercher',
  'common.cancel': 'Annuler',
  'common.save': 'Enregistrer',
  'common.delete': 'Supprimer',
  'common.edit': 'Modifier',

  'nav.home': 'Accueil',
  'nav.shows': 'Séries',
  'nav.movies': 'Films',
  'nav.categories': 'Catégories',
  'nav.myList': '<PERSON> Liste',
  'nav.downloads': 'Téléchargements',
  'nav.settings': 'Paramètres',

  'home.popularShows': 'Séries Populaires',
  'home.popularMovies': 'Films Populaires',
  'home.continueWatching': 'Continuer à Regarder',

  'downloads.title': 'Téléchargements',
  'downloads.subtitle': 'Regardez vos séries et films préférés hors ligne',

  'insights.title': 'Statistiques de Visionnage',
  'insights.watchHistory': 'Historique',
  'insights.overview': 'Aperçu',

  'settings.language': 'Langue',
  'settings.displayLanguage': 'Langue d\'affichage',
};

// Merge the French translations with the English fallbacks
Object.assign(translations, frenchTranslations);

export default translations;

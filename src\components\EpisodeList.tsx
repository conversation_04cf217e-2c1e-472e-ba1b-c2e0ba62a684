'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Episode } from '@/types/index';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';
import { useMediaQuery } from '@/hooks/use-media-query';
import { Play, Grid3X3, List, MoreHorizontal, Calendar, Clock, ChevronDown, ChevronUp, Star, Info } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

/**
 * Props for the EpisodeList component
 *
 * @interface EpisodeListProps
 * @property {Object[]} seasons - Array of season objects containing episodes
 * @property {number} seasons[].seasonNumber - The season number
 * @property {Episode[]} seasons[].episodes - Array of episodes for this season
 * @property {string} showTitle - The title of the TV show
 * @property {number} seasonCount - The total number of seasons available
 * @property {boolean} isLoading - Whether the episode data is currently loading
 * @property {string} [imdbId] - The IMDb ID for the show (format: 'tt1234567')
 * @property {string} [tmdbId] - The TMDb ID for the show
 * @property {string} [contentId] - The content ID used in the application
 * @property {string} [contentType=show] - The type of content (usually 'show')
 * @property {number} [activeSeason] - The active season from the parent component
 * @property {number} [activeEpisode] - The active episode from the parent component
 */
interface EpisodeListProps {
  seasons: {
    seasonNumber: number;
    episodes: Episode[];
  }[];
  showTitle: string;
  seasonCount: number;
  isLoading: boolean;
  imdbId?: string;
  tmdbId?: string;
  contentId?: string;
  contentType?: string;
  activeSeason?: number;
  activeEpisode?: number;
}

type ViewMode = 'list' | 'grid' | 'compact';

/**
 * Episode List Component - Redesigned for Full Screen Experience
 *
 * Features a modern, compact design with multiple view modes optimized for space efficiency
 * and improved user experience similar to major streaming platforms.
 */
export default function EpisodeList({
  seasons,
  showTitle,
  seasonCount,
  isLoading,
  imdbId,
  tmdbId,
  contentId,
  contentType = 'show',
  activeSeason: parentActiveSeason,
  activeEpisode: parentActiveEpisode
}: EpisodeListProps) {
  const router = useRouter();
  const [currentSeasonData, setCurrentSeasonData] = useState<{episodes: Episode[]}>({episodes: []});
  const isMobile = useMediaQuery('(max-width: 768px)');
  const isTablet = useMediaQuery('(max-width: 1024px)');
  const [isSeasonLoading, setIsSeasonLoading] = useState(false);
  const [viewMode, setViewMode] = useState<ViewMode>(isMobile ? 'compact' : 'list');
  const [isExpanded, setIsExpanded] = useState(true);

  const selectedSeason = parentActiveSeason ?? 1;
  const selectedEpisode = parentActiveEpisode ?? 1;

  // Update currentSeasonData whenever seasons or selectedSeason changes
  useEffect(() => {
    if (!seasons || seasons.length === 0 || !selectedSeason) {
      console.log(`EpisodeList: Skipping currentSeasonData update - seasons: ${seasons?.length || 0}, selectedSeason: ${selectedSeason}`);
      return;
    }

    console.log(`EpisodeList: Updating current season data`, {
      selectedSeason,
      availableSeasons: seasons.map(s => s.seasonNumber),
      seasonsLength: seasons.length
    });

    // Find the currently selected season
    const foundSeason = seasons.find(season => season.seasonNumber === selectedSeason);

    if (foundSeason) {
      console.log(`EpisodeList: Found season ${selectedSeason} with ${foundSeason.episodes.length} episodes`);
      setCurrentSeasonData(foundSeason);
      setIsSeasonLoading(false);

      // If we found the season but it has no episodes, trigger a fetch
      if (foundSeason.episodes.length === 0 && tmdbId) {
        console.log(`EpisodeList: Season ${selectedSeason} found but has no episodes, triggering a fetch`);
        const event = new CustomEvent('seasonChange', {
          detail: { season: selectedSeason }
        });
        window.dispatchEvent(event);
      }
    } else {
      console.warn(`EpisodeList: Selected season ${selectedSeason} not found in available seasons`);
      setCurrentSeasonData({episodes: []});
      setIsSeasonLoading(true); // Set loading true while fetching

      // If the selected season isn't in our list, trigger a fetch
      if (tmdbId) {
        console.log(`EpisodeList: Selected season ${selectedSeason} not loaded yet, triggering a fetch`);
        const event = new CustomEvent('seasonChange', {
          detail: { season: selectedSeason }
        });
        window.dispatchEvent(event);
      }
    }
  }, [seasons, selectedSeason, tmdbId]);

  /**
   * Handles playing an episode when selected
   */
  const handlePlayEpisode = (event?: React.MouseEvent, season?: number, episode = 1) => {
    if (event) {
      event.preventDefault();
    }

    const seasonToPlay = season || selectedSeason;
    console.log(`Play episode: S${seasonToPlay}:E${episode}`);

    const playEvent = new CustomEvent('episodeChange', {
      detail: { season: seasonToPlay, episode }
    });
    window.dispatchEvent(playEvent);
  };

  /**
   * Handles season changes from the dropdown selector
   */
  const handleSelectChange = (value: string) => {
    const newSeason = parseInt(value);
    console.log(`EpisodeList: Season selected from dropdown: ${newSeason}, current selectedSeason: ${selectedSeason}`);

    if (newSeason !== selectedSeason) {
      console.log(`EpisodeList: Changing from season ${selectedSeason} to ${newSeason}`);
      
      // Dispatch season change event to the parent
      const seasonEvent = new CustomEvent('seasonChange', {
        detail: { season: newSeason }
      });
      window.dispatchEvent(seasonEvent);
    } else {
      console.log(`EpisodeList: Season ${newSeason} is already selected, no change needed`);
    }
  };

  /**
   * Formats a date string into a human-friendly format
   */
  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (e) {
      console.error('Error formatting date:', e);
      return 'Unknown date';
    }
  };

  const renderCompactView = () => (
    <div className="w-full px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto space-y-3">
        {currentSeasonData.episodes.map((episode: Episode, index: number) => (
          <motion.div
            key={episode.id}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.02, duration: 0.3 }}
            className={cn(
              "group flex items-center bg-vista-dark/40 hover:bg-vista-dark/70 rounded-xl p-4 transition-all duration-300 cursor-pointer border border-vista-light/5 hover:border-vista-blue/30 backdrop-blur-sm",
              selectedEpisode === episode.episodeNumber && "bg-vista-dark/60 border-l-4 border-l-vista-blue border-vista-light/20 shadow-md shadow-black/20"
            )}
            onClick={() => handlePlayEpisode(undefined, episode.seasonNumber || selectedSeason, episode.episodeNumber)}
          >
            {/* Episode thumbnail */}
            <div className="relative w-24 h-14 rounded-lg overflow-hidden flex-shrink-0 bg-vista-dark shadow-lg">
              {episode.stillPath || episode.thumbnail ? (
                <Image
                  src={episode.stillPath || episode.thumbnail || ''}
                  alt={`${episode.title} thumbnail`}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                  sizes="96px"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-vista-dark">
                  <Play className="w-5 h-5 text-vista-light/40" />
                </div>
              )}
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/30 transition-colors duration-300" />
              <div className="absolute top-1 left-1 bg-vista-blue text-white text-xs px-2 py-0.5 rounded font-medium">
                E{episode.episodeNumber}
              </div>
            </div>

            {/* Episode info */}
            <div className="ml-5 flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h4 className="font-semibold text-vista-light text-base truncate group-hover:text-vista-blue transition-colors">
                  {episode.title}
                </h4>
                <Button
                  size="sm"
                  className="bg-vista-blue/80 hover:bg-vista-blue text-white px-4 py-2 h-8 opacity-0 group-hover:opacity-100 transition-opacity duration-300 ml-3 flex-shrink-0"
                  onClick={(e) => {
                    e.stopPropagation();
                    handlePlayEpisode(e, episode.seasonNumber || selectedSeason, episode.episodeNumber);
                  }}
                >
                  <Play className="w-3 h-3 mr-1" />
                  Play
                </Button>
              </div>
              <div className="flex items-center gap-4 mt-1 text-sm text-vista-light/60">
                {episode.runtime && (
                  <span className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    {episode.runtime}m
                  </span>
                )}
                {episode.airDate && (
                  <span className="flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    {formatDate(episode.airDate)}
                  </span>
                )}
              </div>
              {episode.description && (
                <p className="text-sm text-vista-light/70 line-clamp-1 mt-1 leading-relaxed">
                  {episode.description}
                </p>
              )}
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );

  const renderListView = () => (
    <div className="w-full px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto space-y-6">
        {currentSeasonData.episodes.map((episode: Episode, index: number) => (
          <motion.div
            key={episode.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.03, duration: 0.3 }}
            className={cn(
              "group flex bg-vista-dark/50 hover:bg-vista-dark/80 rounded-2xl p-6 transition-all duration-300 border border-vista-light/10 hover:border-vista-blue/30 backdrop-blur-sm shadow-lg hover:shadow-xl",
              selectedEpisode === episode.episodeNumber && "bg-vista-dark/70 border-l-4 border-l-vista-blue border-vista-light/25 shadow-xl shadow-black/30"
            )}
          >
            {/* Episode thumbnail */}
            <div className="relative w-40 h-24 rounded-xl overflow-hidden flex-shrink-0 bg-vista-dark shadow-lg">
              {episode.stillPath || episode.thumbnail ? (
                <Image
                  src={episode.stillPath || episode.thumbnail || ''}
                  alt={`${episode.title} thumbnail`}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-500"
                  sizes="160px"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-vista-dark">
                  <Play className="w-10 h-10 text-vista-light/30" />
                </div>
              )}
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-colors duration-300" />
              <div className="absolute top-2 left-2 bg-vista-blue/90 text-white text-sm px-2 py-1 rounded font-medium">
                E{episode.episodeNumber}
              </div>
              {/* Play button overlay */}
              <button
                onClick={(e) => {
                  e.preventDefault();
                  handlePlayEpisode(e, episode.seasonNumber || selectedSeason, episode.episodeNumber);
                }}
                className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                aria-label={`Play episode ${episode.episodeNumber}`}
              >
                <div className="bg-vista-blue/90 rounded-full p-3 scale-75 group-hover:scale-100 transition-transform duration-300">
                  <Play className="w-5 h-5 text-white fill-current" />
                </div>
              </button>
            </div>

            {/* Episode content */}
            <div className="ml-6 flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <h3 className="text-xl font-bold text-vista-light line-clamp-1 group-hover:text-vista-blue transition-colors">
                    {episode.title}
                  </h3>
                  <div className="flex items-center gap-6 mt-2 text-sm text-vista-light/60">
                    {episode.runtime && (
                      <span className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        {episode.runtime} min
                      </span>
                    )}
                    {episode.airDate && (
                      <span className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        {formatDate(episode.airDate)}
                      </span>
                    )}
                  </div>
                  <p className="text-base text-vista-light/80 line-clamp-2 mt-3 leading-relaxed">
                    {episode.description || "No description available"}
                  </p>
                </div>
                <div className="ml-6 flex-shrink-0">
                  <Button
                    onClick={(e) => {
                      e.preventDefault();
                      handlePlayEpisode(e, episode.seasonNumber || selectedSeason, episode.episodeNumber);
                    }}
                    className="bg-vista-blue hover:bg-vista-blue-light text-white gap-2 transition-all duration-200 px-6"
                    size="default"
                  >
                    <Play className="w-4 h-4" />
                    Play
                  </Button>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );

  const renderGridView = () => (
    <div className="w-full px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
          {currentSeasonData.episodes.map((episode: Episode, index: number) => (
            <motion.div
              key={episode.id}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.05, duration: 0.3 }}
              className={cn(
                "group flex flex-col bg-vista-dark/50 hover:bg-vista-dark/80 rounded-2xl overflow-hidden transition-all duration-300 border border-vista-light/10 hover:border-vista-blue/30 hover:scale-105 backdrop-blur-sm shadow-lg hover:shadow-xl",
                selectedEpisode === episode.episodeNumber && "bg-vista-dark/70 border-vista-blue/40 shadow-lg shadow-vista-blue/10 ring-1 ring-vista-blue/30"
              )}
            >
              <div className="relative aspect-video overflow-hidden bg-vista-dark">
                {episode.stillPath || episode.thumbnail ? (
                  <Image
                    src={episode.stillPath || episode.thumbnail || ''}
                    alt={`${episode.title} thumbnail`}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                    sizes="(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 20vw"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-vista-dark">
                    <Play className="w-12 h-12 text-vista-light/30" />
                  </div>
                )}
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/50 transition-colors duration-300" />
                <div className="absolute top-3 left-3 bg-vista-blue/90 text-white text-sm px-2 py-1 rounded font-medium">
                  E{episode.episodeNumber}
                </div>
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    handlePlayEpisode(e, episode.seasonNumber || selectedSeason, episode.episodeNumber);
                  }}
                  className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  aria-label={`Play episode ${episode.episodeNumber}`}
                >
                  <div className="bg-vista-blue/90 rounded-full p-4 group-hover:scale-110 transition-transform duration-300">
                    <Play className="w-6 h-6 text-white fill-current" />
                  </div>
                </button>
              </div>
              <div className="p-4">
                <h3 className="font-bold text-vista-light text-sm line-clamp-2 group-hover:text-vista-blue transition-colors mb-2">
                  {episode.title}
                </h3>
                <div className="flex items-center justify-between text-xs text-vista-light/60">
                  {episode.runtime && (
                    <span className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      {episode.runtime}m
                    </span>
                  )}
                  <Button
                    onClick={(e) => {
                      e.preventDefault();
                      handlePlayEpisode(e, episode.seasonNumber || selectedSeason, episode.episodeNumber);
                    }}
                    size="sm"
                    className="bg-vista-blue/80 hover:bg-vista-blue text-white px-3 py-1 h-7 text-xs"
                  >
                    <Play className="w-3 h-3 mr-1" />
                    Play
                  </Button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className="w-full bg-gradient-to-b from-vista-dark/80 to-vista-dark py-8">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center justify-between p-8 border-b border-vista-light/10">
              <div>
                <Skeleton className="h-8 w-48 mb-3" />
                <Skeleton className="h-5 w-32" />
              </div>
              <div className="flex items-center gap-4">
                <Skeleton className="h-10 w-32" />
                <Skeleton className="h-10 w-48" />
              </div>
            </div>
            <div className="p-8 space-y-4">
              {[1, 2, 3, 4].map((i) => (
                <Skeleton key={i} className="h-20 w-full rounded-xl" />
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  console.log(`EpisodeList: Rendering component with seasonCount=${seasonCount}, selectedSeason=${selectedSeason}, episodes available: ${currentSeasonData?.episodes?.length || 0}, total seasons loaded: ${seasons?.length || 0}`);

  return (
    <div className="w-full bg-gradient-to-b from-vista-dark/80 to-vista-dark backdrop-blur-xl">
      {/* Header - Full Width with Container */}
      <div className="w-full border-b border-vista-light/10">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between py-8">
              <div className="flex items-center gap-6 mb-6 lg:mb-0">
                <button
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="flex items-center gap-4 text-vista-light hover:text-vista-blue transition-colors group"
                >
                  <h2 className="text-3xl font-bold tracking-tight">{showTitle || 'Episodes'}</h2>
                  {isExpanded ? 
                    <ChevronUp className="w-6 h-6 group-hover:scale-110 transition-transform" /> : 
                    <ChevronDown className="w-6 h-6 group-hover:scale-110 transition-transform" />
                  }
                </button>
                <div className="text-base text-vista-light/60 bg-vista-dark/60 px-4 py-2 rounded-full backdrop-blur-sm border border-vista-light/10">
                  {currentSeasonData?.episodes?.length || 0} episodes • Season {selectedSeason}
                </div>
              </div>
              
              <div className="flex items-center gap-4 w-full lg:w-auto">
                {/* View mode toggle */}
                {!isMobile && (
                  <div className="flex items-center bg-vista-dark/60 rounded-xl p-1 backdrop-blur-sm border border-vista-light/10">
                    <button
                      onClick={() => setViewMode('compact')}
                      className={cn(
                        "p-3 rounded-lg transition-all duration-300",
                        viewMode === 'compact' ? "bg-vista-blue text-white shadow-lg" : "text-vista-light/60 hover:text-vista-light hover:bg-vista-dark/60"
                      )}
                    >
                      <List className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={cn(
                        "p-3 rounded-lg transition-all duration-300",
                        viewMode === 'list' ? "bg-vista-blue text-white shadow-lg" : "text-vista-light/60 hover:text-vista-light hover:bg-vista-dark/60"
                      )}
                    >
                      <MoreHorizontal className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => setViewMode('grid')}
                      className={cn(
                        "p-3 rounded-lg transition-all duration-300",
                        viewMode === 'grid' ? "bg-vista-blue text-white shadow-lg" : "text-vista-light/60 hover:text-vista-light hover:bg-vista-dark/60"
                      )}
                    >
                      <Grid3X3 className="w-4 h-4" />
                    </button>
                  </div>
                )}
                
                {/* Season selector */}
                <div className="w-full lg:w-56">
                  <Select
                    value={selectedSeason.toString()}
                    onValueChange={handleSelectChange}
                  >
                    <SelectTrigger className="w-full bg-vista-dark/60 backdrop-blur-sm border border-vista-light/20 text-vista-light hover:bg-vista-dark/80 transition-colors h-12">
                      <SelectValue placeholder="Select Season" />
                    </SelectTrigger>
                    <SelectContent className="bg-vista-dark border-vista-light/20 max-h-[300px]">
                      {Array.from({ length: seasonCount }, (_, i) => i + 1).map((seasonNum) => (
                        <SelectItem key={seasonNum} value={seasonNum.toString()}>
                          Season {seasonNum}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content - Full Width */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="py-8">
              {isSeasonLoading ? (
                <div className="px-4 sm:px-6 lg:px-8">
                  <div className="max-w-7xl mx-auto space-y-4">
                    {[1, 2, 3, 4].map((i) => (
                      <Skeleton key={i} className="h-20 w-full rounded-xl" />
                    ))}
                  </div>
                </div>
              ) : (
                <>
                  {viewMode === 'compact' && renderCompactView()}
                  {viewMode === 'list' && renderListView()}
                  {viewMode === 'grid' && renderGridView()}
                </>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

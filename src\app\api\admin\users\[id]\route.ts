import { NextRequest, NextResponse } from 'next/server';
import { getAdminUserId, verifyAdmin } from '@/lib/admin-auth';
import { ensureMongooseConnection } from '@/lib/mongodb';
import User, { IUser } from '@/models/User';
import UserActivity from '@/models/UserActivity';
import mongoose, { Document } from 'mongoose';
import { isValidObjectId } from "mongoose";
import logger from "@/lib/logger";
import { LogDetails } from '@/lib/logger';
import { v2 as cloudinary } from 'cloudinary'; // Import Cloudinary
import { validateEnvironmentVariables, logEnvironmentValidation, getMongoDBConnectionInfo } from '@/lib/env-validation';
import { createErrorResponse, withTimeout, generateRequestId, OperationalError, ErrorContext } from '@/lib/error-handler';

// Ensure Cloudinary is configured (usually in a startup file or config)
// Example: Should be configured elsewhere
// cloudinary.config({ 
//   cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
//   api_key: process.env.CLOUDINARY_API_KEY,
//   api_secret: process.env.CLOUDINARY_API_SECRET
// });

// Helper function to extract public ID from Cloudinary URL
// (Consider moving this to a dedicated lib/cloudinary-utils.ts file)
function extractPublicId(imageUrl: string): string | null {
  try {
    const url = new URL(imageUrl);
    // Example structure: https://res.cloudinary.com/cloud_name/image/upload/v12345/folder/public_id.jpg
    // Regex to capture path after /upload/ and remove version/extension
    const regex = /\/upload\/(?:v\d+\/)?([^\.]+)/;
    const match = url.pathname.match(regex);
    return match ? match[1] : null;
  } catch (e) {
    logger.error('Error parsing Cloudinary URL:', e instanceof Error ? e : new Error(String(e)));
    return null;
  }
}

// Helper function to safely convert a Mongoose document to plain object
function documentToPlainObject<T>(doc: Document): T {
  return (doc as unknown as { toObject(): T }).toObject();
}

/**
 * GET /api/admin/users/[id]
 * Get a specific user by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin authentication
    const adminSession = await verifyAdmin(request);
    if (!adminSession) {
      return NextResponse.json(
        { error: "Unauthorized access" },
        { status: 401 }
      );
    }

    // Properly await the params object before accessing properties
    const id = (await params).id;

    // Validate user ID
    if (!isValidObjectId(id)) {
      return NextResponse.json(
        { error: "Invalid user ID format" },
        { status: 400 }
      );
    }

    await ensureMongooseConnection();

    // Find user by ID, excluding password
    const user = await User.findById(id).select("-password");

    // Process profile image URLs to ensure they're valid and add cache busting
    if (user) {
      // Process Cloudinary URLs to ensure they use the correct cloud name
      const processImageUrl = (url?: string) => {
        if (!url) return null;

        // Check if the URL is a relative path (which won't work)
        if (url && !url.startsWith('http') && !url.startsWith('data:')) {
          return "https://res.cloudinary.com/streamvista/image/upload/v1743812698/defaults/default_avatar.jpg";
        }

        // If it's a Cloudinary URL, ensure it has the correct cloud name (lowercase)
        if (url.includes('cloudinary.com')) {
          // Check if the URL contains the correct cloud name (should be lowercase)
          if (!url.includes('cloudinary.com/streamvista/')) {
            // Fix the URL by replacing the cloud name with the lowercase version
            return url.replace(/cloudinary\.com\/([^\/]+)\//, 'cloudinary.com/streamvista/');
          }
        }
        return url;
      };

      // Default Cloudinary avatar URL
      const defaultAvatarUrl = "https://res.cloudinary.com/streamvista/image/upload/v1743812698/defaults/default_avatar.jpg";

      // Get profile image, ensuring it has the correct format
      let profileImage = processImageUrl(user.profileImage || user.picture) || defaultAvatarUrl;
      let picture = processImageUrl(user.picture || user.profileImage) || defaultAvatarUrl;

      // Add cache busting for Cloudinary URLs
      if (profileImage.includes('cloudinary.com')) {
        const separator = profileImage.includes('?') ? '&' : '?';
        profileImage = `${profileImage}${separator}t=${Date.now()}`;
      }

      if (picture.includes('cloudinary.com')) {
        const separator = picture.includes('?') ? '&' : '?';
        picture = `${picture}${separator}t=${Date.now()}`;
      }

      // Update the user object with the processed URLs
      user.profileImage = profileImage;
      user.picture = picture;
    }

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    return NextResponse.json(user);
  } catch (error) {
    logger.error("Error fetching user:", error instanceof Error ? error : new Error(String(error)));
    return NextResponse.json(
      { error: "Error fetching user" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/users/[id]
 * Update a user
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin authentication
    const adminSession = await verifyAdmin(request);
    if (!adminSession) {
      return NextResponse.json(
        { error: "Unauthorized access" },
        { status: 401 }
      );
    }

    // Properly await the params object before accessing properties
    const id = (await params).id;

    // Validate user ID
    if (!isValidObjectId(id)) {
      return NextResponse.json(
        { error: "Invalid user ID format" },
        { status: 400 }
      );
    }

    await ensureMongooseConnection();

    // Check if user exists
    const existingUser = await User.findById(id);
    if (!existingUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get update data from request
    const data = await request.json();

    // --- Cloudinary Deletion Logic --- START ---
    const oldImageUrl = data.oldProfileImage;
    const newImageUrl = data.profileImage; // Could be new URL or null

    if (oldImageUrl && typeof oldImageUrl === 'string' && oldImageUrl.includes('cloudinary.com')) {
      // Only proceed if old image exists and a new one is provided OR if new one is explicitly null (removal)
      if (newImageUrl !== oldImageUrl) {
        try {
          const publicId = extractPublicId(oldImageUrl); // Use utility to get public_id
          if (publicId) {
            logger.info(`Attempting to delete old Cloudinary image: ${publicId}`);
            const deletionResult = await cloudinary.uploader.destroy(publicId);
            logger.info(`Cloudinary deletion result for ${publicId}:`, deletionResult);
            if (deletionResult.result !== 'ok' && deletionResult.result !== 'not found') {
              // Log error but don't block user update
              logger.warning(`Failed to delete old image ${publicId} from Cloudinary:`, { deletionResult });
            }
          } else {
            logger.warning(`Could not extract public_id from old image URL: ${oldImageUrl}`);
          }
        } catch (cloudinaryError) {
          logger.error(`Error deleting old image ${oldImageUrl} from Cloudinary:`, cloudinaryError instanceof Error ? cloudinaryError : new Error(String(cloudinaryError)));
          // Don't block the user update if deletion fails, just log it.
        }
      }
    }
    // --- Cloudinary Deletion Logic --- END ---

    // Define type for update object
    interface UserUpdateData {
      name?: string;
      email?: string;
      role?: string;
      status?: string;
      subscription?: string; // Assuming simple string for now
      subscriptionStatus?: string;
      subscriptionRenewal?: Date;
      profileImage?: string | null;
      picture?: string | null;
      emailVerified?: Date | null;
      // Add other potential fields from your User model that can be updated
    }
    const updateObject: UserUpdateData = {};

    // Only include fields that are provided and allowed to be updated
    if (data.name !== undefined) updateObject.name = data.name;
    if (data.email !== undefined) updateObject.email = data.email;
    if (data.role !== undefined) updateObject.role = data.role;
    if (data.status !== undefined) updateObject.status = data.status;
    if (data.subscription !== undefined) updateObject.subscription = data.subscription;
    if (data.subscriptionStatus !== undefined) updateObject.subscriptionStatus = data.subscriptionStatus;
    if (data.subscriptionRenewal !== undefined) updateObject.subscriptionRenewal = data.subscriptionRenewal;
    if (data.emailVerified !== undefined) updateObject.emailVerified = data.emailVerified;

    // Explicitly handle profileImage update (new URL or null for removal)
    if (data.profileImage !== undefined) {
      updateObject.profileImage = data.profileImage; // This can be the new URL or null
      // Also update picture for consistency
      updateObject.picture = data.profileImage;
    } else if (data.profileImage === undefined && data.oldProfileImage && !data.profileImage) {
      // This case might be redundant if frontend always sends profileImage: null for removal
      // but handles case where frontend *only* sends oldProfileImage to signal removal
      updateObject.profileImage = null;
      updateObject.picture = null;
    }

    // Update user
    const updatedUser = await User.findByIdAndUpdate(id, { $set: updateObject }, { // Use $set for safety
      new: true,
      runValidators: true,
    }).select("-password");

    // Log admin activity
    logger.info(
      `Admin ${adminSession.user.email} updated user ${existingUser.email}`
    );

    return NextResponse.json(updatedUser);
  } catch (error) {
    logger.error("Error updating user:", error instanceof Error ? error : new Error(String(error)));
    return NextResponse.json(
      { error: "Error updating user" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/users/[id]
 * Delete a user
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const startTime = Date.now();
  const requestId = generateRequestId();
  let userId: string | undefined;
  let userEmail: string | undefined;
  let adminEmail: string | undefined;

  const errorContext: ErrorContext = {
    operation: 'user_deletion',
    requestId,
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    netlify: process.env.NETLIFY === 'true'
  };

  try {
    // Log environment validation for debugging but don't fail on missing vars
    const envValidation = validateEnvironmentVariables(true);
    logEnvironmentValidation(envValidation, 'User deletion operation');

    // Only fail if MONGODB_URI is missing (critical for operation)
    if (!process.env.MONGODB_URI) {
      logger.error("User deletion failed: MONGODB_URI is missing", {
        mongoInfo: getMongoDBConnectionInfo()
      });
      return NextResponse.json(
        {
          error: "Database configuration error",
          details: process.env.NODE_ENV === 'development' ? 'MONGODB_URI missing' : undefined
        },
        { status: 500 }
      );
    }

    // Verify admin authentication with timeout
    const adminSession = await withTimeout(
      verifyAdmin(request),
      10000,
      'Admin verification'
    );

    if (!adminSession) {
      throw new OperationalError('Unauthorized access', 401, 'AUTH_FAILED');
    }

    adminEmail = adminSession.user.email;
    errorContext.adminEmail = adminEmail;

    // Properly await the params object before accessing properties
    const id = (await params).id;
    userId = id;
    errorContext.userId = userId;

    // Validate user ID
    if (!isValidObjectId(id)) {
      throw new OperationalError('Invalid user ID format', 400, 'INVALID_USER_ID');
    }

    // Connect to database with timeout
    await withTimeout(
      ensureMongooseConnection(),
      15000,
      'Database connection'
    );

    // Check if user exists with timeout
    const user = await withTimeout(
      User.findById(id),
      10000,
      'User lookup'
    );

    if (!user) {
      throw new OperationalError('User not found', 404, 'USER_NOT_FOUND');
    }

    userEmail = user.email;
    errorContext.userEmail = userEmail;

    // Don't allow deletion of superadmin accounts
    if (user.role === "superadmin") {
      throw new OperationalError('Superadmin accounts cannot be deleted', 403, 'SUPERADMIN_DELETE_FORBIDDEN');
    }

    // Delete the user with timeout
    await withTimeout(
      User.findByIdAndDelete(id),
      10000,
      'User deletion'
    );

    const duration = Date.now() - startTime;
    errorContext.duration = `${duration}ms`;

    // Log admin activity
    logger.info(
      `Admin ${adminEmail} deleted user ${userEmail}`,
      {
        ...errorContext,
        success: true
      }
    );

    return NextResponse.json({
      message: "User deleted successfully",
      duration: `${duration}ms`,
      requestId
    });
  } catch (error) {
    const duration = Date.now() - startTime;
    errorContext.duration = `${duration}ms`;
    errorContext.userId = userId || 'unknown';
    errorContext.userEmail = userEmail || 'unknown';
    errorContext.adminEmail = adminEmail || 'unknown';

    // Use enhanced error handling
    return createErrorResponse(
      error instanceof Error ? error : new Error(String(error)),
      errorContext,
      process.env.NODE_ENV === 'development'
    );
  }
}

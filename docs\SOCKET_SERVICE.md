# Socket Service Documentation

## Configuration

### Socket Config
```typescript
export const SOCKET_CONFIG = {
  url: process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3001',
  options: {
    autoConnect: true,
    reconnection: true,
    reconnectionAttempts: 5,
    reconnectionDelay: 1000,
    reconnectionDelayMax: 5000,
    timeout: 20000,
    debug: process.env.NODE_ENV === 'development'
  },
  party: {
    maxMembers: 10,
    maxMessageHistory: 100,
    maxReactionHistory: 50,
    syncThreshold: 2000,
    inactivityTimeout: 1000 * 60 * 30
  },
  monitoring: {
    pingInterval: 30000,
    connectionTimeout: 10000,
    showStatusIndicator: true,
    showReconnectPrompt: true
  }
}
```

## Socket Manager

### Implementation
```typescript
export class SocketManager {
  private socket: Socket;
  private state: ConnectionState;
  private options: SocketOptions;
  private eventHandlers: Map<string, Set<Function>>;
  private reconnectAttempts: number;
  private connectionStats: ConnectionStats;

  constructor(url: string, options: SocketOptions) {
    this.socket = io(url, options);
    this.setupEventHandlers();
    this.initializeConnectionMonitoring();
  }

  // Core methods
  connect(auth?: Record<string, any>): void;
  disconnect(): void;
  reconnect(): void;
  emit<T extends SOCKET_EVENTS>(event: T, data: any): void;
  on<T extends SOCKET_EVENTS>(event: T, callback: (data: any) => void): () => void;
  off<T extends SOCKET_EVENTS>(event: T, callback: (data: any) => void): void;
}
```

### Connection States
```typescript
enum CONNECTION_STATE {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  FAILED = 'failed'
}
```

### Socket Events
```typescript
enum SOCKET_EVENTS {
  // Connection events
  CONNECT = "connect",
  DISCONNECT = "disconnect",
  ERROR = "error",
  RECONNECT = "reconnect",
  RECONNECT_ATTEMPT = "reconnect_attempt",
  RECONNECT_ERROR = "reconnect_error",
  RECONNECT_FAILED = "reconnect_failed",
  CONNECTION_ERROR = "connect_error",
  
  // Party management
  PARTY_CREATE = "party:create",
  PARTY_JOIN = "party:join",
  PARTY_LEAVE = "party:leave",
  PARTY_UPDATE = "party:update",
  
  // Chat and reactions
  CHAT_MESSAGE = "chat:message",
  REACTION = "reaction",
  
  // Playback sync
  STATE_SYNC = "state:sync",
  PLAYBACK_UPDATE = "playback:update",
  
  // User interaction
  TYPING_START = "typing:start",
  TYPING_STOP = "typing:stop",
  READY_STATE = "ready:state",
  
  // Watch party specific
  JOIN_WATCH_PARTY = "join_watch_party",
  LEAVE_WATCH_PARTY = "leave_watch_party",
  WATCH_PARTY_STATE = "watch_party_state",
  WATCH_PARTY_CREATED = "watch_party_created",
  USER_JOINED = "user_joined",
  USER_LEFT = "user_left",
  HOST_CHANGED = "host_changed",
  
  // Party discovery
  GET_AVAILABLE_PARTIES = "get_available_parties",
  AVAILABLE_PARTIES = "available_parties",
  
  // Test events
  PING = "ping",
  PONG = "pong",
  SIMULATE_DISCONNECT = "simulate-disconnect",
  CONNECTION_STATE_CHANGE = "connection_state_change"
}
```

## Socket Context

### Provider Implementation
```typescript
interface SocketContextType {
  socket: SocketManager | null;
  isConnected: boolean;
  connectionState: CONNECTION_STATE;
  isConnecting: boolean;
  connect: () => void;
  disconnect: () => void;
  reconnect: () => void;
  emit: <T extends SOCKET_EVENTS>(event: T, data: any) => void;
  on: <T extends SOCKET_EVENTS>(event: T, callback: (data: any) => void) => () => void;
  off: <T extends SOCKET_EVENTS>(event: T, callback: (data: any) => void) => void;
  manualRetry: () => void;
}

export function SocketProvider({ children }: { children: React.ReactNode }) {
  const [socket, setSocket] = useState<SocketManager | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionState, setConnectionState] = useState<CONNECTION_STATE>(CONNECTION_STATE.DISCONNECTED);
  const [connectionErrors, setConnectionErrors] = useState(0);
  const [retryCount, setRetryCount] = useState(0);
  
  // Implementation details...
}
```

## Server Implementation

### Socket Server
```typescript
// server.js
const server = http.createServer();
const io = new Server(server, {
  cors: {
    origin: '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    credentials: true
  },
  pingTimeout: 10000,
  pingInterval: 5000,
  transports: ['websocket', 'polling']
});

// Store active watch parties
const watchParties = new Map();

// Event handlers
io.on('connection', (socket) => {
  // Party creation
  socket.on(EVENTS.PARTY_CREATE, (data) => {
    const { contentId, userId, userName } = data;
    const partyId = generatePartyId();
    
    const party = {
      id: partyId,
      contentId,
      hostId: userId,
      members: [{ id: userId, name: userName, isHost: true }],
      messages: [],
      currentTime: 0,
      isPlaying: false
    };
    
    watchParties.set(partyId, party);
    socket.join(partyId);
    socket.emit(EVENTS.PARTY_CREATED, party);
  });

  // Party join
  socket.on(EVENTS.PARTY_JOIN, (data) => {
    const { partyId, userId, userName } = data;
    const party = watchParties.get(partyId);
    
    if (!party) {
      socket.emit('error', { message: 'Party not found' });
      return;
    }
    
    party.members.push({ id: userId, name: userName, isReady: false });
    socket.join(partyId);
    io.to(partyId).emit(EVENTS.PARTY_UPDATE, party);
  });

  // Party leave
  socket.on(EVENTS.PARTY_LEAVE, (data) => {
    const { partyId, userId } = data;
    const party = watchParties.get(partyId);
    
    if (!party) return;
    
    party.members = party.members.filter(m => m.id !== userId);
    
    if (party.members.length === 0) {
      watchParties.delete(partyId);
    } else {
      io.to(partyId).emit(EVENTS.PARTY_UPDATE, party);
    }
    
    socket.leave(partyId);
  });

  // Chat message
  socket.on(EVENTS.CHAT_MESSAGE, (data) => {
    const { partyId, userId, message } = data;
    const party = watchParties.get(partyId);
    
    if (!party) return;
    
    party.messages.push({ userId, message, timestamp: Date.now() });
    io.to(partyId).emit(EVENTS.CHAT_MESSAGE, data);
  });

  // Playback update
  socket.on(EVENTS.STATE_SYNC, (data) => {
    const { partyId, currentTime, isPlaying } = data;
    const party = watchParties.get(partyId);
    
    if (!party) return;
    
    party.currentTime = currentTime;
    party.isPlaying = isPlaying;
    io.to(partyId).emit(EVENTS.PLAYBACK_UPDATE, { currentTime, isPlaying });
  });
});
```

## Error Handling

### Socket Errors
```typescript
// Error types
interface SocketError {
  code: string;
  message: string;
  data?: any;
}

// Error handling in SocketManager
private handleError(error: SocketError) {
  console.error('Socket error:', error);
  this.connectionStats.errors++;
  
  if (error.code === 'connect_error') {
    this.state = CONNECTION_STATE.FAILED;
    this.emit(SOCKET_EVENTS.CONNECTION_STATE_CHANGE, this.state);
  }
}
```

## Performance Optimization

### Connection Management
```typescript
// Reconnection strategy
const reconnectionConfig = {
  attempts: 5,
  delay: 1000,
  multiplier: 1.5,
  maxDelay: 5000
};

// Connection monitoring
interface ConnectionStats {
  connected: boolean;
  lastConnected: number;
  disconnections: number;
  reconnections: number;
  errors: number;
  latency: number;
}

// Performance monitoring
setInterval(() => {
  const start = Date.now();
  socket.emit(SOCKET_EVENTS.PING);
  socket.once(SOCKET_EVENTS.PONG, () => {
    const latency = Date.now() - start;
    this.connectionStats.latency = latency;
  });
}, SOCKET_CONFIG.monitoring.pingInterval);
```

## Development Tools

### Debug Mode
```typescript
const debugSocket = {
  logEvents: true,
  logErrors: true,
  logReconnects: true,
  logPlayback: false
};

function logSocketEvent(event: string, data: any) {
  if (debugSocket.logEvents) {
    console.log(`[Socket] ${event}:`, data);
  }
}
```

### Testing Utilities
```typescript
// Mock socket for testing
const mockSocket = {
  emit: jest.fn(),
  on: jest.fn(),
  off: jest.fn(),
  connect: jest.fn(),
  disconnect: jest.fn()
};

// Connection simulation
function simulateDisconnect() {
  socket.emit(SOCKET_EVENTS.SIMULATE_DISCONNECT);
  socket.disconnect();
}

// Event simulation
function simulatePartyJoin(partyId: string, userId: string) {
  socket.emit(SOCKET_EVENTS.PARTY_JOIN, { partyId, userId });
}
``` 
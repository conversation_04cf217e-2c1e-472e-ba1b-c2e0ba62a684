// This file is a compatibility layer that re-exports from mongodb.ts
// It exists to maintain backward compatibility with code that imports from '@/lib/mongoose'

// Import all exports from mongodb.ts
import mongodb, {
  connectToDatabase,
  ensureMongooseConnection,
  checkMongoHealth,
  resetMongoConnections,
  clientPromise,
  dbConnect
} from './mongodb';

// Re-export everything
export {
  connectToDatabase,
  ensureMongooseConnection,
  checkMongoHealth,
  resetMongoConnections,
  clientPromise,
  dbConnect
};

// Export default
export default mongodb;
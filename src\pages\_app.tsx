import { AppProps } from 'next/app';
import { useEffect } from 'react';

function MyApp({ Component, pageProps }: AppProps) {
  // Set a flag to indicate client-side rendering
  useEffect(() => {
    // Set a global flag to indicate browser environment
    (window as any).__IS_BROWSER__ = true;
    
    // Log that we're in the browser environment
    console.log('App running in browser environment');
  }, []);

  return <Component {...pageProps} />;
}

export default MyApp;

'use client';

import { useState, useEffect } from 'react';
import { Check, Globe, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/lib/i18n/LanguageContext';
import { Language } from '@/lib/i18n';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface LanguageSelectorProps {
  variant?: 'icon' | 'text' | 'full';
  className?: string;
  showFlag?: boolean;
  align?: 'start' | 'center' | 'end';
}

export default function LanguageSelector({
  variant = 'full',
  className = '',
  showFlag = true,
  align = 'end'
}: LanguageSelectorProps) {
  const { language, setLanguage, languages, t } = useLanguage();
  const [mounted, setMounted] = useState(false);

  // Avoid hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  const selectedLanguage = languages[language];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size={variant === 'icon' ? 'icon' : 'sm'}
          className={`text-vista-light hover:bg-vista-light/10 rounded-full p-0 ${className}`}
        >
          {variant === 'icon' ? (
            <Globe className="h-5 w-5" />
          ) : (
            <div className="flex items-center gap-1.5">
              {variant !== 'text' && <Globe className="h-4 w-4" />}
              {variant === 'full' && (
                <>
                  <span>{showFlag ? selectedLanguage.nativeName : 'Language'}</span>
                  <ChevronDown className="h-3.5 w-3.5 ml-0.5 opacity-70" />
                </>
              )}
              {variant === 'text' && (
                <span className="text-current text-xl font-medium">Language</span>
              )}
            </div>
          )}
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent align={align} side="right" className="bg-vista-dark-lighter border-vista-light/10">
        {(Object.keys(languages) as Language[]).map(code => {
          const { name, nativeName } = languages[code];
          const isSelected = code === language;

          return (
            <DropdownMenuItem
              key={code}
              className={`flex cursor-pointer items-center gap-2 hover:bg-vista-light/10 ${
                isSelected ? 'bg-vista-blue/10 text-vista-blue' : 'text-vista-light'
              }`}
              onClick={() => setLanguage(code)}
            >
              {isSelected && <Check className="h-4 w-4" />}
              <span className={isSelected ? 'ml-0' : 'ml-5'}>
                {nativeName} {nativeName !== name && <span className="opacity-50">({name})</span>}
              </span>
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

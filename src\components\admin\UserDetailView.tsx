'use client';

import { useState, useEffect, useCallback } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Avatar } from '@/components/ui/avatar';
import { UserAvatar } from '@/components/UserAvatar';
import { toast } from '@/components/ui/use-toast';
import {
  User,
  Mail,
  Calendar,
  Shield,
  CheckCircle,
  XCircle,
  Clock,
  Edit,
  Trash2,
  RefreshCw,
  Key,
  Lock,
  Activity,
  FileText,
  Settings,
  CreditCard
} from 'lucide-react';
import UserPermissions from './UserPermissions';
import UserActivityLogs from './UserActivityLogs';
import UserSubscriptionManager from './UserSubscriptionManager';
import UserPaymentMethodManager from './UserPaymentMethodManager';
import DeleteUserDialog from './DeleteUserDialog';
import { formatDistanceToNow } from 'date-fns';

interface UserDetailViewProps {
  userId: string;
  onEdit: () => void;
  onDelete: () => void;
}

interface UserDetails {
  id: string;
  name: string;
  email: string;
  profileImage?: string;
  picture?: string;
  role: string;
  emailVerified?: string | null;
  createdAt: string;
  updatedAt: string;
  subscription?: {
    plan: string;
    status: string;
    renewalDate?: string;
  };
  lastLogin?: string;
  totalWatchTime?: number;
  favoriteGenres?: string[];
}

export default function UserDetailView({ userId, onEdit, onDelete }: UserDetailViewProps) {
  const [user, setUser] = useState<UserDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  // Fetch user data
  const fetchUserData = useCallback(async (silentRefresh = false) => {
    if (!silentRefresh) {
      setIsLoading(true);
    }
    setError(null);

    // Create a default user object with minimal data
    const defaultUser = {
      id: userId,
      name: 'User',
      email: '<EMAIL>',
      role: 'user',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      totalWatchTime: 0,
      favoriteGenres: [],
      subscription: {
        plan: 'free',
        status: 'active'
      }
    };

    try {
      // Fetch basic user data with text() instead of json() to avoid parsing errors
      let userData = {};
      try {
        // Get the admin userId from localStorage
        const adminUserId = localStorage.getItem('userId');

        // Add timestamp to prevent caching
        const timestamp = Date.now();
        const userResponse = await fetch(`/api/admin/users/${userId}?userId=${adminUserId}&t=${timestamp}`, {
          credentials: 'include',
          headers: {
            'Authorization': `Bearer ${adminUserId}`,
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          },
          cache: 'no-store'
        });

        if (userResponse.ok) {
          // Get the response as text first
          const userDataText = await userResponse.text();

          // Only try to parse if we have content
          if (userDataText && userDataText.trim()) {
            try {
              userData = JSON.parse(userDataText);
              console.log('Received user data from API:', userData);
            } catch (parseError) {
              console.error('Error parsing user data JSON:', parseError, 'Raw response:', userDataText);
              setError('Error parsing user data');
            }
          } else {
            console.error('Empty user data response');
            setError('Received empty user data');
          }
        } else {
          console.error(`Failed to fetch user data: ${userResponse.status}`);
          setError(`Failed to fetch user data: ${userResponse.status}`);
        }
      } catch (userFetchError) {
        console.error('Error fetching user data:', userFetchError);
        setError('Network error while fetching user data');
      }

      // If we have no user data, use default and stop here
      if (Object.keys(userData).length === 0) {
        setUser(defaultUser);
        setIsLoading(false);
        return;
      }

      // Fetch user statistics with text() instead of json() to avoid parsing errors
      let userStats = {};
      try {
        // Get the admin userId from localStorage
        const adminUserId = localStorage.getItem('userId');

        // Add timestamp to prevent caching
        const statsTimestamp = Date.now();
        const statsResponse = await fetch(`/api/admin/users/${userId}/stats?userId=${adminUserId}&t=${statsTimestamp}`, {
          credentials: 'include',
          headers: {
            'Authorization': `Bearer ${adminUserId}`,
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          },
          cache: 'no-store'
        });

        if (statsResponse.ok) {
          // Get the response as text first
          const statsDataText = await statsResponse.text();

          // Only try to parse if we have content
          if (statsDataText && statsDataText.trim()) {
            try {
              userStats = JSON.parse(statsDataText);
            } catch (parseError) {
              console.error('Error parsing stats JSON:', parseError, 'Raw response:', statsDataText);
              // Continue with empty stats rather than failing completely
            }
          } else {
            console.warn('Empty stats response');
          }
        } else {
          console.warn(`Could not fetch user statistics: ${statsResponse.status}`);
        }
      } catch (statsFetchError) {
        console.error('Error fetching user statistics:', statsFetchError);
        // Continue with empty stats rather than failing completely
      }

      // Combine user data with statistics
      const enhancedUserData = {
        ...userData,
        ...userStats
      };

      console.log('Setting enhanced user data:', enhancedUserData);
      setUser(enhancedUserData as UserDetails);
    } catch (err) {
      console.error('Error fetching user:', err);
      // Use default user as fallback
      setUser(defaultUser);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to load user data',
        variant: 'destructive'
      });
    } finally {
      if (!silentRefresh) {
        setIsLoading(false);
      }
    }
  }, [userId]);

  useEffect(() => {
    fetchUserData();

    // Set up a refresh interval to keep the data fresh
    const refreshInterval = setInterval(() => {
      fetchUserData(true);
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(refreshInterval);
  }, [userId, fetchUserData]);

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Format time ago
  const formatTimeAgo = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return formatDistanceToNow(new Date(dateString), { addSuffix: true });
  };

  // Format watch time
  const formatWatchTime = (minutes?: number) => {
    if (!minutes) return 'N/A';

    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;

    if (hours === 0) {
      return `${remainingMinutes} minutes`;
    } else if (hours === 1) {
      return `1 hour ${remainingMinutes > 0 ? `${remainingMinutes} minutes` : ''}`;
    } else {
      return `${hours} hours ${remainingMinutes > 0 ? `${remainingMinutes} minutes` : ''}`;
    }
  };

  // Get user initials for avatar fallback
  const getUserInitials = (): string => {
    if (!user?.name) return 'U';

    const nameParts = user.name.split(' ');
    if (nameParts.length === 1) return nameParts[0].charAt(0).toUpperCase();

    return (nameParts[0].charAt(0) + nameParts[nameParts.length - 1].charAt(0)).toUpperCase();
  };

  // Loading state
  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <RefreshCw className="h-12 w-12 mx-auto mb-4 text-vista-blue animate-spin" />
          <h2 className="text-xl font-semibold text-vista-light mb-2">Loading User Data</h2>
          <p className="text-vista-light/70">Please wait while we fetch the user details...</p>
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (error || !user) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <XCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
          <h2 className="text-xl font-semibold text-vista-light mb-2">Error Loading User</h2>
          <p className="text-vista-light/70 mb-4">{error || 'User not found'}</p>
          <Button onClick={() => fetchUserData()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* User Profile Card */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="flex items-center gap-4">
              <Avatar className="h-16 w-16 border-2 border-vista-blue/30">
                <UserAvatar
                  userId={user.id}
                  src={user.profileImage || user.picture ?
                    `${user.profileImage || user.picture}${(user.profileImage || user.picture)?.includes('?') ? '&' : '?'}t=${Date.now()}` :
                    undefined}
                  alt={user.name}
                  fallback={getUserInitials()}
                  size="xl"
                />
              </Avatar>
              <div>
                <CardTitle className="text-2xl text-vista-light">{user.name}</CardTitle>
                <div className="flex items-center gap-2 mt-1">
                  <Badge
                    variant={
                      user.role === 'admin'
                        ? 'success'
                        : user.role === 'superadmin'
                          ? 'destructive'
                          : 'default'
                    }
                    className="capitalize">
                    {user.role}
                  </Badge>
                  {user.emailVerified ? (
                    <Badge variant="outline" className="bg-green-500/10 text-green-500 border-green-500/20">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Verified
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-yellow-500/10 text-yellow-500 border-yellow-500/20">
                      <XCircle className="h-3 w-3 mr-1" />
                      Not Verified
                    </Badge>
                  )}
                </div>
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => fetchUserData()}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button variant="outline" onClick={onEdit}>
                <Edit className="h-4 w-4 mr-2" />
                Edit User
              </Button>
              <DeleteUserDialog userId={userId} userName={user.name} onSuccess={onDelete} />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="activity">Activity</TabsTrigger>
              <TabsTrigger value="permissions">Permissions</TabsTrigger>
              <TabsTrigger value="subscription">Subscription</TabsTrigger>
              <TabsTrigger value="payment">Payment Methods</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Basic Information */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg text-vista-light flex items-center">
                      <User className="h-5 w-5 mr-2 text-vista-blue" />
                      Basic Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <div className="flex items-center text-vista-light/70 text-sm mb-1">
                        <Mail className="h-4 w-4 mr-2" />
                        Email Address
                      </div>
                      <p className="text-vista-light">{user.email}</p>
                    </div>
                    <div>
                      <div className="flex items-center text-vista-light/70 text-sm mb-1">
                        <Calendar className="h-4 w-4 mr-2" />
                        Joined
                      </div>
                      <p className="text-vista-light">{formatDate(user.createdAt)}</p>
                      <p className="text-vista-light/70 text-sm">{formatTimeAgo(user.createdAt)}</p>
                    </div>
                    <div>
                      <div className="flex items-center text-vista-light/70 text-sm mb-1">
                        <Clock className="h-4 w-4 mr-2" />
                        Last Login
                      </div>
                      <p className="text-vista-light">{formatDate(user.lastLogin)}</p>
                      <p className="text-vista-light/70 text-sm">{formatTimeAgo(user.lastLogin)}</p>
                    </div>
                  </CardContent>
                </Card>

                {/* Account Status */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg text-vista-light flex items-center">
                      <Shield className="h-5 w-5 mr-2 text-vista-blue" />
                      Account Status
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <div className="flex items-center text-vista-light/70 text-sm mb-1">
                        <Key className="h-4 w-4 mr-2" />
                        Account Role
                      </div>
                      <div className="flex items-center">
                        <Badge
                          variant={
                            user.role === 'admin'
                              ? 'success'
                              : user.role === 'superadmin'
                                ? 'destructive'
                                : 'default'
                          }
                          className="capitalize">
                          {user.role}
                        </Badge>
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center text-vista-light/70 text-sm mb-1">
                        <Lock className="h-4 w-4 mr-2" />
                        Email Verification
                      </div>
                      <div className="flex items-center">
                        {user.emailVerified ? (
                          <Badge variant="outline" className="bg-green-500/10 text-green-500 border-green-500/20">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Verified on {formatDate(user.emailVerified)}
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="bg-yellow-500/10 text-yellow-500 border-yellow-500/20">
                            <XCircle className="h-3 w-3 mr-1" />
                            Not Verified
                          </Badge>
                        )}
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center text-vista-light/70 text-sm mb-1">
                        <CreditCard className="h-4 w-4 mr-2" />
                        Subscription Plan
                      </div>
                      <div className="flex items-center">
                        <Badge variant="outline" className="capitalize">
                          {user.subscription?.plan || 'Free'} Plan
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Usage Statistics */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg text-vista-light flex items-center">
                    <Activity className="h-5 w-5 mr-2 text-vista-blue" />
                    Usage Statistics
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-vista-dark-lighter p-4 rounded-md">
                      <div className="text-vista-light/70 text-sm mb-1">Total Watch Time</div>
                      <div className="text-xl font-semibold text-vista-light">{formatWatchTime(user.totalWatchTime)}</div>
                    </div>
                    <div className="bg-vista-dark-lighter p-4 rounded-md">
                      <div className="text-vista-light/70 text-sm mb-1">Favorite Genres</div>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {user.favoriteGenres?.map(genre => (
                          <Badge key={genre} variant="outline" className="bg-vista-blue/10 border-vista-blue/20">
                            {genre}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div className="bg-vista-dark-lighter p-4 rounded-md">
                      <div className="text-vista-light/70 text-sm mb-1">Account Age</div>
                      <div className="text-xl font-semibold text-vista-light">
                        {formatTimeAgo(user.createdAt).replace('about ', '')}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="activity" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-vista-light flex items-center">
                    <Activity className="h-5 w-5 mr-2 text-vista-blue" />
                    Recent Activity
                  </CardTitle>
                  <CardDescription>
                    User's recent actions and interactions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {/* Use the UserActivityLogs component with a limit of 10 logs to reduce database load */}
                  <UserActivityLogs userId={userId} limit={10} />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="permissions">
              <UserPermissions userId={userId} />
            </TabsContent>

            <TabsContent value="subscription" className="space-y-6">
              <UserSubscriptionManager userId={userId} onSuccess={fetchUserData} />
            </TabsContent>

            <TabsContent value="payment">
              <UserPaymentMethodManager userId={userId} onSuccess={fetchUserData} />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}

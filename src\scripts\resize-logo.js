import sharp from 'sharp';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const inputPath = path.join(__dirname, '../../public/logo.webp');
const outputDir = path.join(__dirname, '../../public');

// Create different sizes for various use cases
const sizes = [
  { name: 'icon-192.png', webpName: 'icon-192.webp', width: 192, height: 192 },
  { name: 'icon-512.png', webpName: 'icon-512.webp', width: 512, height: 512 },
  { name: 'maskable-icon.png', webpName: 'maskable-icon.webp', width: 512, height: 512 },
  { name: 'favicon.ico', width: 32, height: 32 },
  { name: 'apple-touch-icon.png', webpName: 'apple-touch-icon.webp', width: 180, height: 180 },
];

async function resizeImages() {
  try {
    // Ensure the input file exists
    if (!fs.existsSync(inputPath)) {
      console.error('Input file not found:', inputPath);
      return;
    }

    console.log('Resizing logo to different sizes...');

    for (const size of sizes) {
      // Create PNG version
      const outputPath = path.join(outputDir, size.name);
      
      // For favicon.ico, convert to ICO format
      if (size.name === 'favicon.ico') {
        await sharp(inputPath)
          .resize(size.width, size.height)
          .toFormat('png')
          .toBuffer()
          .then(data => {
            fs.writeFileSync(outputPath, data);
            console.log(`Created ${size.name}`);
          });
      } else {
        await sharp(inputPath)
          .resize(size.width, size.height)
          .toFile(outputPath);
        console.log(`Created ${size.name}`);
        
        // Create WebP version if it has a webpName
        if (size.webpName) {
          const webpOutputPath = path.join(outputDir, size.webpName);
          await sharp(inputPath)
            .resize(size.width, size.height)
            .webp({ quality: 100 })
            .toFile(webpOutputPath);
          console.log(`Created ${size.webpName}`);
        }
      }
    }

    console.log('All images have been resized successfully!');
  } catch (error) {
    console.error('Error resizing images:', error);
  }
}

resizeImages();

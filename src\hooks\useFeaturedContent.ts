'use client';

import useS<PERSON> from 'swr';
import { useToastHelpers } from '@/lib/ToastContext';

/**
 * Hook for fetching and managing featured content
 */
export function useFeaturedContent() {
  const toast = useToastHelpers();
   
  const {
    data,
    error,
    isLoading,
    mutate
  } = useSWR(
    '/api/admin/featured',
    async (url: string) => {
      try {
        const response = await fetch(url);
         
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch featured content');
        }
         
        return await response.json();
      } catch (error) {
        console.error('Error fetching featured content:', error);
        toast.error('Error', 'Failed to load featured content');
        throw error;
      }
    },
    {
      revalidateOnFocus: false,
      dedupingInterval: 5000
    }
  );
   
  // Add featured content
  const addFeaturedContent = async (contentId: string, startDate?: Date, endDate?: Date) => {
    try {
      const response = await fetch('/api/admin/featured', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          contentId,
          startDate,
          endDate
        })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to add featured content (${response.status})`);
      }
      
      // Refresh data
      mutate();
      
      return await response.json();
    } catch (error) {
      console.error('Error adding featured content:', error);
      toast.error('Error', error instanceof Error ? error.message : 'Failed to add featured content');
      throw error;
    }
  };
  
  // Update featured content
  const updateFeaturedContent = async (id: string, order?: number, startDate?: Date, endDate?: Date) => {
    try {
      const response = await fetch(`/api/admin/featured/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          order,
          startDate,
          endDate
        })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to update featured content (${response.status})`);
      }
      
      // Refresh data
      mutate();
      
      return await response.json();
    } catch (error) {
      console.error('Error updating featured content:', error);
      toast.error('Error', error instanceof Error ? error.message : 'Failed to update featured content');
      throw error;
    }
  };
  
  // Remove featured content
  const removeFeaturedContent = async (id: string) => {
    try {
      const response = await fetch(`/api/admin/featured/${id}`, {
        method: 'DELETE'
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to remove featured content (${response.status})`);
      }
      
      // Refresh data
      mutate();
      
      return await response.json();
    } catch (error) {
      console.error('Error removing featured content:', error);
      toast.error('Error', error instanceof Error ? error.message : 'Failed to remove featured content');
      throw error;
    }
  };
  
  // Reorder featured content
  const reorderFeaturedContent = async (items: { id: string }[]) => {
    try {
      const response = await fetch('/api/admin/featured/reorder', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ items })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to reorder featured content (${response.status})`);
      }
      
      // Refresh data
      mutate();
      
      return await response.json();
    } catch (error) {
      console.error('Error reordering featured content:', error);
      toast.error('Error', error instanceof Error ? error.message : 'Failed to reorder featured content');
      throw error;
    }
  };
   
  return {
    featuredContent: data,
    error,
    isLoading,
    refetch: mutate,
    addFeaturedContent,
    updateFeaturedContent,
    removeFeaturedContent,
    reorderFeaturedContent
  };
}

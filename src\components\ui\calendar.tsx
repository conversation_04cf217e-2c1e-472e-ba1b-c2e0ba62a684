"use client"

import * as React from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { DayPicker } from "react-day-picker"
import { cn } from "@/lib/utils"
import { buttonVariants } from "@/components/ui/button"

export type CalendarProps = React.ComponentProps<typeof DayPicker>

// Type for the Chevron component props
interface ChevronProps {
  className?: string
  size?: number
  disabled?: boolean
  orientation?: "left" | "right" | "up" | "down"
}

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  components: userComponents,
  ...props
}: CalendarProps) {
  const defaultClassNames = {
    months: "relative flex flex-col sm:flex-row gap-4",
    month: "w-full",
    month_caption: "relative mx-10 mb-1 flex h-9 items-center justify-center z-20",
    caption_label: "text-sm font-medium text-vista-light",
    nav: "absolute top-0 flex w-full justify-between z-10",
    button_previous: cn(
      buttonVariants({ variant: "ghost" }),
      "h-9 w-9 text-vista-light/80 hover:text-vista-blue p-0",
    ),
    button_next: cn(
      buttonVariants({ variant: "ghost" }),
      "h-9 w-9 text-vista-light/80 hover:text-vista-blue p-0",
    ),
    weekday: "h-9 w-9 p-0 text-xs font-medium text-vista-light/80",
    day_button:
      "relative flex h-9 w-9 items-center justify-center whitespace-nowrap rounded-lg p-0 text-vista-light outline-offset-2 group-[[data-selected]:not(.range-middle)]:[transition-property:color,background-color,border-radius,box-shadow] group-[[data-selected]:not(.range-middle)]:duration-150 focus:outline-none group-data-[disabled]:pointer-events-none focus-visible:z-10 hover:bg-vista-dark-lighter group-data-[selected]:bg-vista-blue hover:text-vista-light group-data-[selected]:text-white group-data-[disabled]:text-vista-light/30 group-data-[disabled]:line-through group-data-[outside]:text-vista-light/30 group-data-[outside]:group-data-[selected]:text-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-vista-blue/70 group-[.range-start:not(.range-end)]:rounded-e-none group-[.range-end:not(.range-start)]:rounded-s-none group-[.range-middle]:rounded-none group-data-[selected]:group-[.range-middle]:bg-vista-dark-lighter group-data-[selected]:group-[.range-middle]:text-vista-light",
    day: "group h-9 w-9 px-0 text-sm",
    range_start: "range-start",
    range_end: "range-end",
    range_middle: "range-middle",
    today:
      "*:after:pointer-events-none *:after:absolute *:after:bottom-1 *:after:start-1/2 *:after:z-10 *:after:h-1 *:after:w-1 *:after:-translate-x-1/2 *:after:rounded-full *:after:bg-vista-blue [&[data-selected]:not(.range-middle)>*]:after:bg-white [&[data-disabled]>*]:after:bg-vista-light/30 *:after:transition-colors",
    outside: "text-vista-light/30 data-selected:bg-vista-dark-lighter/50 data-selected:text-vista-light/50",
    hidden: "invisible",
    week_number: "h-9 w-9 p-0 text-xs font-medium text-vista-light/80",
  }

  const mergedClassNames: typeof defaultClassNames = Object.keys(defaultClassNames).reduce(
    (acc, key) => ({
      ...acc,
      [key]: classNames?.[key as keyof typeof classNames]
        ? cn(
            defaultClassNames[key as keyof typeof defaultClassNames],
            classNames[key as keyof typeof classNames],
          )
        : defaultClassNames[key as keyof typeof defaultClassNames],
    }),
    {} as typeof defaultClassNames,
  )

  const defaultComponents = {
    Chevron: (props: ChevronProps) => {
      if (props.orientation === "left") {
        return <ChevronLeft size={16} strokeWidth={2} className={cn("text-vista-blue", props.className)} aria-hidden="true" />
      }
      return <ChevronRight size={16} strokeWidth={2} className={cn("text-vista-blue", props.className)} aria-hidden="true" />
    },
  }

  const mergedComponents = {
    ...defaultComponents,
    ...userComponents,
  }

  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={cn("w-fit", className)}
      classNames={mergedClassNames}
      components={mergedComponents}
      {...props}
    />
  )
}
Calendar.displayName = "Calendar"

export { Calendar }

import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import BannerAd from '@/models/BannerAd';

/**
 * GET /api/banner-ads/active
 * Get all currently active banner ads for public display
 * This endpoint is public and doesn't require authentication
 */
export async function GET(request: NextRequest) {
  try {
    await ensureMongooseConnection();

    const now = new Date();

    // Find active banner ads that are currently valid
    // Fixed: Combined the two $or conditions into proper $and with nested $or
    const activeBannerAds = await BannerAd.find({
      isActive: true,
      $and: [
        {
          $or: [
            { startDate: { $lte: now } },
            { startDate: { $exists: false } }
          ]
        },
        {
          $or: [
            { endDate: { $gte: now } },
            { endDate: { $exists: false } },
            { endDate: null }
          ]
        }
      ]
    })
    .select('title description imageUrl linkUrl bannerType styling priority analytics')
    .sort({ priority: -1, createdAt: -1 })
    .limit(5) // Limit to 5 active banners max
    .lean();

    const response = NextResponse.json({
      banners: activeBannerAds,
      count: activeBannerAds.length
    });

    // Add cache headers for browser and CDN caching
    response.headers.set('Cache-Control', 'public, max-age=30, s-maxage=60');
    response.headers.set('CDN-Cache-Control', 'public, max-age=60');
    response.headers.set('Vary', 'Accept-Encoding');
    
    return response;

  } catch (error) {
    console.error('Error fetching active banner ads:', error);
    return NextResponse.json(
      { error: 'Failed to fetch active banner ads' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { isAdmin } from '@/lib/middleware';

/**
 * GET /api/admin/analytics/views
 * Get content view statistics for admin dashboard
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is admin using the proper middleware function
    const adminCheck = await isAdmin(request);
    if (!adminCheck.isAuthorized) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const User = mongoose.default.models.User ||
                mongoose.default.model('User', new mongoose.default.Schema({
                  role: String
                }));

    // Check if user is admin
    const user = await User.findById(adminCheck.user?._id).select('role').lean();
    if (!user || (user as unknown as { role: string }).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the ContentView model directly
    const ContentViewSchema = new mongoose.default.Schema({
      contentId: {
        type: mongoose.default.Schema.Types.ObjectId,
        required: true,
        refPath: 'contentType'
      },
      contentType: {
        type: String,
        required: true,
        enum: ['movie', 'show', 'episode']
      },
      userId: {
        type: mongoose.default.Schema.Types.ObjectId,
        required: true,
        ref: 'User'
      },
      timestamp: {
        type: Date,
        default: Date.now
      },
      duration: {
        type: Number,
        default: 0
      },
      completed: {
        type: Boolean,
        default: false
      },
      progress: {
        type: Number,
        default: 0,
        min: 0,
        max: 100
      }
    }, {
      timestamps: true
    });

    // Get the ContentView model
    const ContentView = mongoose.default.models.ContentView ||
                       mongoose.default.model('ContentView', ContentViewSchema);

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const period = searchParams.get('period') || '30d'; // Default to last 30 days

    // Calculate date range based on period
    const endDate = new Date();
    const startDate = new Date();

    switch (period) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(endDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(endDate.getDate() - 30);
    }

    // Get total views
    const totalViews = await ContentView.countDocuments({
      timestamp: { $gte: startDate, $lte: endDate }
    });

    // Get views by content type
    const viewsByType = await ContentView.aggregate([
      {
        $match: {
          timestamp: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: '$contentType',
          count: { $sum: 1 }
        }
      }
    ]);

    // Get daily views for the period
    const dailyViews = await ContentView.aggregate([
      {
        $match: {
          timestamp: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$timestamp' }
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { '_id': 1 }
      }
    ]);

    // Get most viewed content
    const popularContent = await ContentView.aggregate([
      {
        $match: {
          timestamp: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: {
            contentId: '$contentId',
            contentType: '$contentType'
          },
          count: { $sum: 1 },
          avgProgress: { $avg: '$progress' },
          completionRate: {
            $avg: { $cond: [{ $eq: ['$completed', true] }, 1, 0] }
          }
        }
      },
      {
        $sort: { count: -1 }
      },
      {
        $limit: 10
      }
    ]);

    // Get completion rate overall
    const completionRate = await ContentView.aggregate([
      {
        $match: {
          timestamp: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: null,
          totalViews: { $sum: 1 },
          completedViews: {
            $sum: { $cond: [{ $eq: ['$completed', true] }, 1, 0] }
          }
        }
      },
      {
        $project: {
          _id: 0,
          completionRate: {
            $cond: [
              { $eq: ['$totalViews', 0] },
              0,
              { $multiply: [{ $divide: ['$completedViews', '$totalViews'] }, 100] }
            ]
          }
        }
      }
    ]);

    // Format the response
    return NextResponse.json({
      totalViews,
      viewsByType: viewsByType.reduce((acc: Record<string, number>, item: { _id: string; count: number }) => {
        acc[item._id] = item.count;
        return acc;
      }, {}),
      dailyViews: dailyViews.map((item: { _id: string; count: number }) => ({
        date: item._id,
        count: item.count
      })),
      popularContent,
      completionRate: completionRate.length > 0 ? completionRate[0].completionRate : 0,
      period,
      startDate,
      endDate,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error fetching view statistics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch view statistics', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

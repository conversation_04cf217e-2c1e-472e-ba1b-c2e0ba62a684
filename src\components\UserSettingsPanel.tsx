'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import {
  Settings, Bell, Video, User, Monitor,
  Shield, Download, CreditCard, Languages,
  Lock, EyeOff, UserCog, LogOut,
  SaveIcon, Smartphone, UserPlus, Users, Pencil, Eye, EyeOff as EyeSlash, X, Calendar, Gamepad, Play,
  Subtitles, Mail
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import ThemeToggle from '@/components/ThemeToggle';
import { useLanguage } from '@/lib/i18n/LanguageContext';
import { Language } from '@/lib/i18n';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { ProfileImageUploader } from './ProfileImageUploader';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { debounce } from 'lodash';
import { cn } from '@/lib/utils';
import { useProfiles } from '@/contexts/ProfileContext';
import { UserAvatar } from '@/components/UserAvatar';
import { Input } from '@/components/ui/input';
import { useRouter } from 'next/navigation';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Loader2 } from "lucide-react";
import React from 'react';
import { useNotifications } from '@/contexts/NotificationContext';
import { SettingsLayout } from '@/components/SettingsLayout';
import { SettingsCard, SettingsField } from '@/components/SettingsCard';
import { ProfileManagementCard } from '@/components/ProfileManagementCard';
import { SubscriptionCard } from '@/components/SubscriptionCard';
import { AccountInfoCard } from '@/components/AccountInfoCard';
import { PaymentMethodsSection } from '@/components/PaymentMethodsSection';
import { BillingHistorySection } from '@/components/BillingHistorySection';

type SettingsTab = 'account' | 'preferences' | 'notifications' | 'playback' | 'privacy' | 'payment' | 'admin' | 'analytics';

export default function UserSettingsPanel() {
  const { user, signOut, isAdmin } = useAuth();
  const userIsAdmin = isAdmin();
  const { activeProfile, profiles, updateProfile } = useProfiles();
  const [activeTab, setActiveTab] = useState<SettingsTab>('account');
  // Initialize with default values, will be updated from user preferences
  const [videoQuality, setVideoQuality] = useState(user?.preferences?.playback?.videoQuality || 'auto');
  const [subtitlesEnabled, setSubtitlesEnabled] = useState(user?.preferences?.playback?.subtitlesEnabled !== false);
  const [autoplayEnabled, setAutoplayEnabled] = useState(user?.preferences?.playback?.autoplayEnabled !== false);

  // Update playback preferences when user data changes
  useEffect(() => {
    if (user?.preferences?.playback) {
      setVideoQuality(user.preferences.playback.videoQuality || 'auto');
      setSubtitlesEnabled(user.preferences.playback.subtitlesEnabled !== false);
      setAutoplayEnabled(user.preferences.playback.autoplayEnabled !== false);
    }
  }, [user?.preferences?.playback]);
  const { language, setLanguage, languages } = useLanguage();
  // Initialize notifications with default values or from user preferences
  const [notifications, setNotifications] = useState({
    newContent: user?.preferences?.notifications?.newContent !== false,
    recommendations: user?.preferences?.notifications?.recommendations !== false,
    updates: user?.preferences?.notifications?.updates === true,
    marketing: user?.preferences?.notifications?.marketing === true,
  });

  // Update notifications when user data changes
  useEffect(() => {
    if (user?.preferences?.notifications) {
      setNotifications({
        newContent: user.preferences.notifications.newContent !== false,
        recommendations: user.preferences.notifications.recommendations !== false,
        updates: user.preferences.notifications.updates === true,
        marketing: user.preferences.notifications.marketing === true,
      });
    }
  }, [user?.preferences?.notifications]);

  const { fetchNotifications } = useNotifications();

  // Initialize privacy settings with default values or from user preferences
  const [privacy, setPrivacy] = useState({
    shareWatchHistory: user?.preferences?.privacy?.shareWatchHistory !== false,
    allowRecommendations: user?.preferences?.privacy?.allowRecommendations !== false,
  });

  // Update privacy settings when user data changes
  useEffect(() => {
    if (user?.preferences?.privacy) {
      setPrivacy({
        shareWatchHistory: user.preferences.privacy.shareWatchHistory !== false,
        allowRecommendations: user.preferences.privacy.allowRecommendations !== false,
      });
    }
  }, [user?.preferences?.privacy]);
  // Real subscription data from user
  const [subscription, setSubscription] = useState({
    plan: user?.subscription?.plan || 'free',
    status: user?.subscription?.status || 'active',
    renewalDate: user?.subscription?.renewalDate ? new Date(user.subscription.renewalDate) : null
  });

  // Update subscription when user data changes
  useEffect(() => {
    if (user?.subscription) {
      setSubscription({
        plan: user.subscription.plan || 'free',
        status: user.subscription.status || 'active',
        renewalDate: user.subscription.renewalDate ? new Date(user.subscription.renewalDate) : null
      });
    }
  }, [user?.subscription]);

  // Loading state
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Get main profile for display
  const mainProfile = profiles.find(p => p.isPrimary);
  const isActiveProfileMain = activeProfile?.isPrimary || false;

  // Add state for profile name input
  const [profileNameInput, setProfileNameInput] = useState('');
  const [isProfileNameUpdating, setIsProfileNameUpdating] = useState(false);

  // Add state for profile name editing
  const [isEditingName, setIsEditingName] = useState(false);
  const [nameBeforeEdit, setNameBeforeEdit] = useState('');

  // Update local profile name state when active profile changes
  useEffect(() => {
    if (activeProfile?.name) {
      setProfileNameInput(activeProfile.name);
      setNameBeforeEdit(activeProfile.name);
    }
  }, [activeProfile?.name]);

  // Handle profile name editing start
  const handleStartEditing = () => {
    setIsEditingName(true);
    setNameBeforeEdit(profileNameInput);
  };

  // Handle profile name editing cancel
  const handleCancelEditing = () => {
    setIsEditingName(false);
    setProfileNameInput(nameBeforeEdit);
  };

  // Handle profile name save
  const handleSaveProfileName = async () => {
    if (!activeProfile?.id) return;

    // Validate name
    if (!profileNameInput || profileNameInput.trim() === '') {
      toast.error("Profile name cannot be empty");
      return;
    }

    setIsProfileNameUpdating(true);

    try {
      const trimmedName = profileNameInput.trim();

      // Don't update if the name hasn't changed after trimming
      if (trimmedName === activeProfile.name) {
        setIsEditingName(false);
        setIsProfileNameUpdating(false);
        return;
      }

      const result = await updateProfile(activeProfile.id, { name: trimmedName });

      if (!result.success) {
        toast.error(result.error || "Failed to update profile name");
        setProfileNameInput(nameBeforeEdit);
      } else {
        // Update local state immediately for a responsive UI
        toast.success("Profile name updated successfully");

        // Update the nameBeforeEdit state with the new name
        setNameBeforeEdit(trimmedName);

        // Exit editing mode
        setIsEditingName(false);

        // If active profile is maintained locally in this component,
        // we should update it here as well to reflect the change immediately
        if (activeProfile) {
          const updatedProfile = { ...activeProfile, name: trimmedName };
          // This will reflect the change immediately in the UI
          // The actual context update happens in updateProfile
        }
      }
    } catch (error) {
      console.error("Error updating profile name:", error);
      toast.error("Failed to update profile name");
      setProfileNameInput(nameBeforeEdit);
    } finally {
      setIsProfileNameUpdating(false);
    }
  };

  // Handle profile name change
  const handleProfileNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setProfileNameInput(e.target.value);
  };

  const router = useRouter();

  // Fetch user settings when component mounts
  useEffect(() => {
    if (user?.id) {
      // Check if settings are already in local storage to prevent unnecessary API calls
      const cachedSettings = localStorage.getItem(`userSettings_${user.id}`);

      if (cachedSettings) {
        try {
          const parsedSettings = JSON.parse(cachedSettings);
          const cacheAge = Date.now() - parsedSettings.timestamp;

          // If cache is less than 5 minutes old, use it instead of making an API call
          if (cacheAge < 5 * 60 * 1000) {
            console.log('Using cached settings');

            // Apply cached settings
            if (parsedSettings.data.preferences) {
              if (parsedSettings.data.preferences.playback) {
                setVideoQuality(parsedSettings.data.preferences.playback.videoQuality || 'auto');
                setSubtitlesEnabled(parsedSettings.data.preferences.playback.subtitlesEnabled ?? true);
                setAutoplayEnabled(parsedSettings.data.preferences.playback.autoplayEnabled ?? true);
              }

              if (parsedSettings.data.preferences.notifications) {
                setNotifications({
                  newContent: parsedSettings.data.preferences.notifications.newContent ?? true,
                  recommendations: parsedSettings.data.preferences.notifications.recommendations ?? true,
                  updates: parsedSettings.data.preferences.notifications.updates ?? false,
                  marketing: parsedSettings.data.preferences.notifications.marketing ?? false,
                });
              }

              if (parsedSettings.data.preferences.privacy) {
                setPrivacy({
                  shareWatchHistory: parsedSettings.data.preferences.privacy.shareWatchHistory ?? true,
                  allowRecommendations: parsedSettings.data.preferences.privacy.allowRecommendations ?? true,
                });
              }
            }

            // Set subscription info
            if (parsedSettings.data.subscription) {
              setSubscription({
                plan: parsedSettings.data.subscription.plan || 'free',
                status: parsedSettings.data.subscription.status || 'active',
                renewalDate: parsedSettings.data.subscription.renewalDate
                  ? new Date(parsedSettings.data.subscription.renewalDate)
                  : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
              });
            }

            setIsLoading(false);
            return;
          }
        } catch (error) {
          console.error('Error parsing cached settings:', error);
          // Continue with API call if cache is invalid
        }
      }

      // If no valid cache exists, fetch from API
      fetchUserSettings();
    }
  }, [user]);

  // Fetch user settings from API
  const fetchUserSettings = async () => {
    if (!user?.id) return;

    setIsLoading(true);
    try {
      const response = await fetch(`/api/user/settings?userId=${user.id}`);
      const data = await response.json();

      if (response.ok && data.success) {
        // Update state with fetched settings
        const { preferences, subscription: sub } = data.settings;

        if (preferences) {
          if (preferences.playback) {
            setVideoQuality(preferences.playback.videoQuality || 'auto');
            setSubtitlesEnabled(preferences.playback.subtitlesEnabled ?? true);
            setAutoplayEnabled(preferences.playback.autoplayEnabled ?? true);
          }

          if (preferences.notifications) {
            setNotifications({
              newContent: preferences.notifications.newContent ?? true,
              recommendations: preferences.notifications.recommendations ?? true,
              updates: preferences.notifications.updates ?? false,
              marketing: preferences.notifications.marketing ?? false,
            });
          }

          if (preferences.privacy) {
            setPrivacy({
              shareWatchHistory: preferences.privacy.shareWatchHistory ?? true,
              allowRecommendations: preferences.privacy.allowRecommendations ?? true,
            });
          }
        }

        // Set subscription info (this would be real in a production app)
        if (sub) {
          setSubscription({
            plan: sub.plan || 'free',
            status: sub.status || 'active',
            renewalDate: sub.renewalDate ? new Date(sub.renewalDate) : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          });
        }
      } else {
        console.error('Failed to fetch settings:', data.error);
        toast.error('Failed to load settings');
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
      toast.error('Failed to load settings');
    } finally {
      setIsLoading(false);
    }
  };

  // Debounce the save settings function to prevent rapid fire API calls
  const debouncedSaveSettings = useCallback(
    debounce(async (userId: string, settingsToSave: any) => {
      try {
        const response = await fetch('/api/user/settings', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId,
            settings: settingsToSave,
          }),
        });

        const data = await response.json();

        if (response.ok && data.success) {
          // Cache the settings
          const cacheData = {
            timestamp: Date.now(),
            data: data.settings
          };
          localStorage.setItem(`userSettings_${userId}`, JSON.stringify(cacheData));

          toast.success('Settings saved successfully');
        } else {
          console.error('Failed to save settings:', data.error);
          toast.error('Failed to save settings');
        }
      } catch (error) {
        console.error('Error saving settings:', error);
        toast.error('Failed to save settings');
      } finally {
        setIsSaving(false);
      }
    }, 1000),
    []
  );

  // Save settings to API
  const saveSettings = async () => {
    if (!user?.id) {
      toast.error('User not found');
      return;
    }

    setIsSaving(true);

    try {
      // Prepare settings data to save
      const settingsToSave = {
        preferences: {
          playback: {
            videoQuality,
            subtitlesEnabled,
            autoplayEnabled,
          },
          notifications,
          privacy,
          language,
        }
      };

      // Call the API to save settings
      const response = await fetch('/api/user/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
          settings: settingsToSave,
        }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // Update local cache
        localStorage.setItem(`userSettings_${user.id}`, JSON.stringify({
          timestamp: Date.now(),
          data: settingsToSave,
        }));

        toast.success('Settings saved successfully');
      } else {
        console.error('Failed to save settings:', data.error);
        toast.error('Failed to save settings');
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('An error occurred while saving settings');
    } finally {
      setIsSaving(false);
    }
  };

  // Toggle switch component with improved UI
  const ToggleSwitch = ({
    enabled,
    onChange,
    label,
    description
  }: {
    enabled: boolean;
    onChange: () => void;
    label: string;
    description?: string
  }) => (
    <div className="flex items-center justify-between gap-4 mb-5 group hover:bg-vista-dark/20 p-2 rounded-md transition-colors">
      <div className="space-y-0.5">
        <h3 className="text-vista-light font-medium group-hover:text-vista-blue transition-colors">{label}</h3>
        {description && <p className="text-sm text-vista-light/70">{description}</p>}
      </div>
      <Switch checked={enabled} onCheckedChange={onChange} />
    </div>
  );

  // Password change state
  const [isPasswordDialogOpen, setIsPasswordDialogOpen] = useState(false);

  // Password strength calculation
  const getPasswordStrength = (password: string) => {
    if (!password) {
      return {
        score: 0,
        label: "None",
        color: "bg-vista-light/20",
        message: "Enter a password"
      };
    }

    let score = 0;

    // Length check
    if (password.length >= 8) score += 1;
    if (password.length >= 12) score += 1;

    // Complexity checks
    if (/[A-Z]/.test(password)) score += 1;
    if (/[a-z]/.test(password)) score += 1;
    if (/[0-9]/.test(password)) score += 1;
    if (/[^a-zA-Z0-9]/.test(password)) score += 1;

    // Score interpretation
    if (score < 3) {
      return {
        score,
        label: "Weak",
        color: "bg-red-500",
        message: "Your password is too weak and easy to guess"
      };
    } else if (score < 5) {
      return {
        score,
        label: "Moderate",
        color: "bg-amber-500",
        message: "Your password meets basic requirements but could be stronger"
      };
    } else {
      return {
        score,
        label: "Strong",
        color: "bg-green-500",
        message: "Your password is strong and secure"
      };
    }
  };

  // Password Dialog Component - Use React.memo to prevent unnecessary re-renders
  const PasswordChangeDialog = React.memo(() => {
    // Move state inside the component to isolate rendering
    const [newPassword, setNewPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [passwordError, setPasswordError] = useState('');
    const [isChangingPassword, setIsChangingPassword] = useState(false);
    const [showNewPassword, setShowNewPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [passwordStrength, setPasswordStrength] = useState({ score: 0, label: '', color: '', message: '' });

    // Use refs instead of derived state for input values
    const newPasswordRef = useRef<HTMLInputElement>(null);
    const confirmPasswordRef = useRef<HTMLInputElement>(null);

    // Calculate password strength when new password changes
    useEffect(() => {
      // Debounce password strength calculation to prevent flickering
      const handler = setTimeout(() => {
        setPasswordStrength(getPasswordStrength(newPassword));
      }, 100);

      return () => clearTimeout(handler);
    }, [newPassword]);

    // Handle password change through a memoized callback
    const handlePasswordSubmit = useCallback(async () => {
      // Get current values from refs for most up-to-date state
      const newPasswordValue = newPasswordRef.current?.value || newPassword;
      const confirmPasswordValue = confirmPasswordRef.current?.value || confirmPassword;

      // Reset error
      setPasswordError('');

      // Basic validation
      if (!newPasswordValue) {
        setPasswordError('New password is required');
        return;
      }

      if (newPasswordValue.length < 8) {
        setPasswordError('Password must be at least 8 characters');
        return;
      }

      if (newPasswordValue !== confirmPasswordValue) {
        setPasswordError('Passwords do not match');
        return;
      }

      // Check if user has Google login
      if (user?.googleId) {
        setPasswordError('Cannot change password for Google accounts');
        return;
      }

      setIsChangingPassword(true);

      try {
        const response = await fetch('/api/user/password', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId: user?.id,
            newPassword: newPasswordValue,
          }),
        });

        const data = await response.json();

        if (response.ok && data.success) {
          toast.success('Password updated successfully');
          setIsPasswordDialogOpen(false);
          // Reset form
          if (newPasswordRef.current) newPasswordRef.current.value = '';
          if (confirmPasswordRef.current) confirmPasswordRef.current.value = '';
          setNewPassword('');
          setConfirmPassword('');
        } else {
          setPasswordError(data.error || 'Failed to update password');
        }
      } catch (error) {
        console.error('Error updating password:', error);
        setPasswordError('An unexpected error occurred');
      } finally {
        setIsChangingPassword(false);
      }
    }, [user, setIsPasswordDialogOpen]);

    return (
      <Dialog open={isPasswordDialogOpen} onOpenChange={setIsPasswordDialogOpen}>
        <DialogContent className="sm:max-w-md bg-vista-dark border-vista-light/10 text-vista-light">
          <DialogHeader>
            <DialogTitle className="text-vista-light">Change Password</DialogTitle>
            <DialogDescription className="text-vista-light/70">
              Update your account password. Make sure it's at least 8 characters and includes a mix of letters, numbers, and symbols.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {passwordError && (
              <div className="p-3 bg-red-500/20 border border-red-500/30 rounded-md text-red-200 text-sm">
                {passwordError}
              </div>
            )}

            {/* New Password */}
            <div className="space-y-2">
              <label htmlFor="new-password" className="text-sm font-medium text-vista-light/90">
                New Password
              </label>
              <div className="relative">
                <Input
                  id="new-password"
                  ref={newPasswordRef}
                  type={showNewPassword ? 'text' : 'password'}
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  className="pr-10 bg-vista-dark/60 border-vista-light/20 focus:border-vista-blue"
                />
                <button
                  type="button"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-vista-light/50 hover:text-vista-light"
                >
                  {showNewPassword ? <EyeSlash className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>

            {/* Password strength meter */}
            {newPassword && (
              <div className="mt-2 space-y-1">
                <div className="h-1.5 w-full bg-vista-light/10 rounded-full overflow-hidden">
                  <div
                    className={`h-full transition-all duration-300 ${passwordStrength.color}`}
                    style={{ width: `${(passwordStrength.score / 6) * 100}%` }}
                  ></div>
                </div>
                <div className="flex justify-between text-xs">
                  <span className={passwordStrength.score >= 3 ? 'text-vista-light' : 'text-vista-light/50'}>
                    {passwordStrength.label}
                  </span>
                  <span className="text-vista-light/50">{passwordStrength.message}</span>
                </div>
              </div>
            )}

            {/* Confirm Password */}
            <div className="space-y-2">
              <label htmlFor="confirm-password" className="text-sm font-medium text-vista-light/90">
                Confirm Password
              </label>
              <div className="relative">
                <Input
                  id="confirm-password"
                  ref={confirmPasswordRef}
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className={`pr-10 bg-vista-dark/60 border-vista-light/20 focus:border-vista-blue ${
                    confirmPassword && newPassword !== confirmPassword ? 'border-red-500' : ''
                  }`}
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-vista-light/50 hover:text-vista-light"
                >
                  {showConfirmPassword ? <EyeSlash className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
              {confirmPassword && newPassword !== confirmPassword && (
                <p className="text-red-400 text-xs mt-1">Passwords do not match</p>
              )}
            </div>
          </div>

          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              onClick={() => setIsPasswordDialogOpen(false)}
              className="border-vista-light/20 text-vista-light hover:bg-vista-light/10"
            >
              Cancel
            </Button>
            <Button
              onClick={handlePasswordSubmit}
              disabled={isChangingPassword || !newPassword || !confirmPassword || newPassword !== confirmPassword}
              className="bg-vista-blue hover:bg-vista-blue/90 text-white"
            >
              {isChangingPassword ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                'Update Password'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  });

  // Handle profile image selection
  const handleProfileImageSelected = useCallback(async (imageUrl: string) => {
    if (!activeProfile?.id) return;

    try {
      setIsSaving(true);
      const result = await updateProfile(activeProfile.id, { avatar: imageUrl });

      if (!result.success) {
        toast.error(result.error || "Failed to update profile image");
      } else {
        toast.success("Profile image updated successfully");
      }
    } catch (error) {
      console.error("Error updating profile image:", error);
      toast.error("Failed to update profile image");
    } finally {
      setIsSaving(false);
    }
  }, [activeProfile?.id, updateProfile]);

  // Render account settings tab
  const renderAccountSettings = () => (
    <div className="space-y-8">
      {/* Profile Management - Conditionally render if activeProfile exists */}
      {activeProfile && (
        <ProfileManagementCard
          activeProfile={activeProfile}
          profiles={profiles}
          onProfileImageUpdate={handleProfileImageSelected}
          className="border-vista-blue/10"
        />
      )}

      {/* Account Info and Subscription Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
        {/* Account Info Card */}
        <AccountInfoCard
          email={user?.email || ''}
          name={user?.name || ''}
          onPasswordChange={() => setIsPasswordDialogOpen(true)}
          onSignOut={handleLogout}
          className="h-full"
        />

        {/* Subscription Card */}
        <SubscriptionCard
          plan={subscription.plan}
          status={subscription.status}
          nextBillingDate={subscription.renewalDate?.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          })}
          price={subscription.plan !== 'free' ? '$14.99' : undefined}
          className="h-full"
        />
      </div>

      {/* Security Card */}
      <SettingsCard
        title="Security"
        description="Manage your account security settings"
        icon={Shield}
        accentColor="rose"
      >
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
          <Button
            variant="outline"
            className="bg-black/20 hover:bg-black/40 border-white/5 hover:border-white/10 w-full justify-start h-auto py-3"
            onClick={() => setIsPasswordDialogOpen(true)}
          >
            <Lock className="mr-2 h-4 w-4 flex-shrink-0 text-rose-400" />
            <span className="text-sm">Change Password</span>
          </Button>
          <Button
            variant="outline"
            className="bg-black/20 hover:bg-black/40 border-white/5 hover:border-white/10 w-full justify-start h-auto py-3"
          >
            <Shield className="mr-2 h-4 w-4 flex-shrink-0 text-rose-400" />
            <span className="text-sm">Two-Factor Authentication</span>
          </Button>
          <Button
            variant="outline"
            className="bg-black/20 hover:bg-black/40 border-white/5 hover:border-white/10 w-full justify-start h-auto py-3 sm:col-span-2 md:col-span-1"
            onClick={handleLogout}
          >
            <LogOut className="mr-2 h-4 w-4 flex-shrink-0 text-rose-400" />
            <span className="text-sm">Sign Out from All Devices</span>
          </Button>
        </div>
      </SettingsCard>
    </div>
  );

  // Render preferences settings tab
  const renderPreferencesSettings = () => (
    <div className="space-y-6">
      <SettingsCard
        title="Appearance"
        description="Customize how StreamVista looks"
        icon={Monitor}
        accentColor="amber"
      >
        <div className="space-y-4">
          <SettingsField
            label="Theme"
            description="Choose between light and dark mode"
            icon={<Monitor className="h-4 w-4 text-amber-400" />}
          >
            <ThemeToggle />
          </SettingsField>

          <div className="p-4 rounded-xl bg-gradient-to-br from-amber-500/10 to-amber-600/5 border border-amber-500/10">
            <h4 className="text-amber-400 font-medium flex items-center">
              <Monitor className="h-4 w-4 mr-2" />
              Theme Preview
            </h4>
            <p className="text-sm text-vista-light/70 mt-1 mb-3">
              See how your content will look with your current theme settings
            </p>
            <div className="grid grid-cols-3 gap-2">
              <div className="aspect-video rounded-md bg-black/40 border border-white/5"></div>
              <div className="aspect-video rounded-md bg-black/40 border border-white/5"></div>
              <div className="aspect-video rounded-md bg-black/40 border border-white/5"></div>
            </div>
          </div>
        </div>
      </SettingsCard>

      <SettingsCard
        title="Language Settings"
        description="Select your preferred language for the interface"
        icon={Languages}
        accentColor="blue"
        footer={
          <Button
            onClick={saveSettings}
            className="bg-blue-500 hover:bg-blue-600 text-white"
            disabled={isSaving}
          >
            {isSaving ? (
              <>
                <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <SaveIcon className="w-4 h-4 mr-2" /> Save Changes
              </>
            )}
          </Button>
        }
      >
        <SettingsField
          label="Display Language"
          description="Choose how you want the interface displayed"
          icon={<Languages className="h-4 w-4 text-blue-400" />}
        >
          <select
            value={language}
            onChange={(e) => setLanguage(e.target.value as Language)}
            className="bg-black/30 border border-white/10 rounded-lg px-3 py-2 text-vista-light focus:outline-none focus:ring-2 focus:ring-blue-500/50"
          >
            {Object.entries(languages).map(([code, { name, nativeName }]) => (
              <option key={code} value={code}>
                {nativeName} {nativeName !== name && `(${name})`}
              </option>
            ))}
          </select>
        </SettingsField>
      </SettingsCard>
    </div>
  );

  // Render playback settings tab
  const renderPlaybackSettings = () => (
    <div className="space-y-6">
      <SettingsCard
        title="Video Quality"
        description="Configure your streaming quality preferences"
        icon={Video}
        accentColor="emerald"
      >
        <div className="space-y-4">
          <SettingsField
            label="Default Quality"
            description="Select your preferred streaming quality"
            icon={<Video className="h-4 w-4 text-emerald-400" />}
          >
            <select
              value={videoQuality}
              onChange={(e) => setVideoQuality(e.target.value as typeof videoQuality)}
              className="bg-black/30 border border-white/10 rounded-lg px-3 py-2 text-vista-light focus:outline-none focus:ring-2 focus:ring-emerald-500/50"
            >
              <option value="auto">Auto (Recommended)</option>
              <option value="low">Low (Data Saver)</option>
              <option value="medium">Medium (720p)</option>
              <option value="high">High (1080p)</option>
              <option value="ultra">Ultra (4K)</option>
            </select>
          </SettingsField>

          <div className="p-4 rounded-xl bg-gradient-to-br from-emerald-500/10 to-emerald-600/5 border border-emerald-500/10">
            <h4 className="text-emerald-400 font-medium flex items-center">
              <Video className="h-4 w-4 mr-2" />
              Quality Information
            </h4>
            <p className="text-sm text-vista-light/70 mt-1">
              Higher quality settings require more bandwidth. Ultra (4K) quality requires a Premium subscription.
            </p>
          </div>
        </div>
      </SettingsCard>

      <SettingsCard
        title="Playback Settings"
        description="Configure your viewing experience"
        icon={Settings}
        accentColor="purple"
        footer={
          <Button
            onClick={saveSettings}
            className="bg-purple-500 hover:bg-purple-600 text-white"
            disabled={isSaving}
          >
            {isSaving ? (
              <>
                <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <SaveIcon className="w-4 h-4 mr-2" /> Save Changes
              </>
            )}
          </Button>
        }
      >
        <div className="space-y-5">
          <SettingsField
            label="Autoplay Next Episode"
            description="Automatically play the next episode when the current one ends"
            icon={<Play className="h-4 w-4 text-purple-400" />}
          >
            <ToggleSwitch
              label="Autoplay Next Episode"
              enabled={autoplayEnabled}
              onChange={() => setAutoplayEnabled(!autoplayEnabled)}
            />
          </SettingsField>

          <SettingsField
            label="Enable Subtitles by Default"
            description="Automatically show subtitles when available"
            icon={<Subtitles className="h-4 w-4 text-purple-400" />}
          >
            <ToggleSwitch
              label="Enable Subtitles by Default"
              enabled={subtitlesEnabled}
              onChange={() => setSubtitlesEnabled(!subtitlesEnabled)}
            />
          </SettingsField>
        </div>
      </SettingsCard>
    </div>
  );

  // Render notifications settings tab
  const renderNotificationsSettings = () => (
    <div className="space-y-6">
      <SettingsCard
        title="Content Notifications"
        description="Manage how you're notified about content"
        icon={Bell}
        accentColor="amber"
      >
        <div className="space-y-5">
          <ToggleSwitch
            enabled={notifications.newContent}
            onChange={() => {
              const newValue = !notifications.newContent;
              setNotifications({...notifications, newContent: newValue});
              // Save immediately for better user experience with notifications
              if (user?.id) {
                debouncedSaveSettings(user.id, {
                  preferences: {
                    notifications: {
                      ...notifications,
                      newContent: newValue
                    }
                  }
                });
                toast.success(newValue ? 'New content notifications enabled' : 'New content notifications disabled');
              }
            }}
            label="New Content Alerts"
            description="Get notified when new episodes or movies are available"
          />

          <ToggleSwitch
            enabled={notifications.recommendations}
            onChange={() => {
              const newValue = !notifications.recommendations;
              setNotifications({...notifications, recommendations: newValue});
              // Save immediately for better user experience with notifications
              if (user?.id) {
                debouncedSaveSettings(user.id, {
                  preferences: {
                    notifications: {
                      ...notifications,
                      recommendations: newValue
                    }
                  }
                });
                toast.success(newValue ? 'Recommendation notifications enabled' : 'Recommendation notifications disabled');
              }
            }}
            label="Recommendations"
            description="Receive personalized content recommendations"
          />

          <ToggleSwitch
            enabled={notifications.updates}
            onChange={() => {
              const newValue = !notifications.updates;
              setNotifications({...notifications, updates: newValue});
              // Save immediately for better user experience with notifications
              if (user?.id) {
                debouncedSaveSettings(user.id, {
                  preferences: {
                    notifications: {
                      ...notifications,
                      updates: newValue
                    }
                  }
                });
                toast.success(newValue ? 'Update notifications enabled' : 'Update notifications disabled');
              }
            }}
            label="Service Updates"
            description="Get notified about app updates and new features"
          />

          <ToggleSwitch
            enabled={notifications.marketing}
            onChange={() => {
              const newValue = !notifications.marketing;
              setNotifications({...notifications, marketing: newValue});
              // Save immediately for better user experience with notifications
              if (user?.id) {
                debouncedSaveSettings(user.id, {
                  preferences: {
                    notifications: {
                      ...notifications,
                      marketing: newValue
                    }
                  }
                });
                toast.success(newValue ? 'Marketing notifications enabled' : 'Marketing notifications disabled');
              }
            }}
            label="Marketing Communications"
            description="Receive promotional offers and surveys"
          />
        </div>
      </SettingsCard>

      <SettingsCard
        title="Email Preferences"
        description="Control what emails you receive"
        icon={Mail}
        accentColor="blue"
        footer={
          <div className="flex justify-between w-full">
            <Button
              variant="outline"
              onClick={(event) => {
                if (!user?.id) {
                  toast.error('User ID not found');
                  return;
                }

                // Prevent multiple clicks
                const button = event.currentTarget as HTMLButtonElement;
                if (button.disabled) return;

                // Disable button during operation
                button.disabled = true;
                button.innerHTML = '<div class="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin mr-2"></div> Generating...';

                // Generate sample notifications for testing
                fetch(`/api/notifications/generate-samples?userId=${user.id}`, {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({ userId: user.id }),
                })
                  .then(response => response.json())
                  .then(data => {
                    toast.success(`${data.notificationsCreated} sample notifications created`);
                    // Refresh notifications
                    fetchNotifications();

                    // Re-enable button after success
                    button.disabled = false;
                    button.innerHTML = 'Generate Test Notifications';
                  })
                  .catch(error => {
                    console.error('Error generating sample notifications:', error);
                    toast.error('Failed to generate sample notifications');

                    // Re-enable button after error
                    button.disabled = false;
                    button.innerHTML = 'Generate Test Notifications';
                  });
              }}
              className="bg-black/20 hover:bg-black/40 border-white/5 hover:border-white/10 text-blue-400"
            >
              Generate Test Notifications
            </Button>
            <Button
              onClick={saveSettings}
              className="bg-blue-500 hover:bg-blue-600 text-white"
              disabled={isSaving}
            >
              {isSaving ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <SaveIcon className="w-4 h-4 mr-2" /> Save Changes
                </>
              )}
            </Button>
          </div>
        }
      >
        <SettingsField
          label="Email Notification Preferences"
          description="Choose which emails you receive from StreamVista"
          icon={<Mail className="h-4 w-4 text-blue-400" />}
        >
          <Button variant="outline" size="sm" className="bg-black/20 hover:bg-black/40 border-white/5 hover:border-white/10 text-blue-400 whitespace-nowrap">
            Manage Emails
          </Button>
        </SettingsField>
      </SettingsCard>
    </div>
  );

  // Render privacy settings tab
  const renderPrivacySettings = () => (
    <div className="space-y-6">
      <SettingsCard
        title="Privacy Preferences"
        description="Manage how your data is used"
        icon={EyeOff}
        accentColor="rose"
      >
        <div className="space-y-5">
          <ToggleSwitch
            enabled={privacy.shareWatchHistory}
            onChange={() => setPrivacy({...privacy, shareWatchHistory: !privacy.shareWatchHistory})}
            label="Share Watch History"
            description="Allow StreamVista to use your watch history to improve recommendations"
          />

          <ToggleSwitch
            enabled={privacy.allowRecommendations}
            onChange={() => setPrivacy({...privacy, allowRecommendations: !privacy.allowRecommendations})}
            label="Personalized Recommendations"
            description="Allow personalized content recommendations based on your viewing habits"
          />
        </div>
      </SettingsCard>

      <SettingsCard
        title="Security Settings"
        description="Protect your account with additional security"
        icon={Shield}
        footer={
          <Button
            onClick={saveSettings}
            className="bg-vista-blue hover:bg-vista-blue/90 text-white"
            disabled={isSaving}
          >
            {isSaving ? (
              <>
                <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <SaveIcon className="w-4 h-4 mr-2" /> Save Changes
              </>
            )}
          </Button>
        }
      >
        <div className="space-y-5">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center py-2 gap-3 border-b border-vista-dark/30 pb-4">
            <div>
              <h3 className="text-vista-light font-medium">Two-Factor Authentication</h3>
              <p className="text-vista-light/70 text-sm">Add an extra layer of security to your account</p>
            </div>
            <Button variant="outline" size="sm" className="border-vista-light/20 text-vista-light hover:bg-vista-light/10 whitespace-nowrap">
              Setup 2FA
            </Button>
          </div>

          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center py-2 gap-3">
            <div>
              <h3 className="text-vista-light font-medium">Login History</h3>
              <p className="text-vista-light/70 text-sm">View recent login activity</p>
            </div>
            <Button variant="outline" size="sm" className="border-vista-light/20 text-vista-light hover:bg-vista-light/10 whitespace-nowrap">
              View History
            </Button>
          </div>
        </div>
      </SettingsCard>
    </div>
  );

  // Render payment settings tab
  const renderPaymentSettings = () => (
    <div className="space-y-6">
      <SettingsCard
        title="Subscription Details"
        description="Manage your StreamVista subscription"
        icon={CreditCard}
        accentColor="emerald"
      >
        <div className="flex flex-col md:flex-row items-start justify-between mb-6 gap-4">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="px-2 py-0.5 text-xs">
                {subscription.status.toUpperCase()}
              </Badge>
              <h3 className="text-lg font-semibold text-vista-blue">
                {subscription.plan === 'free' ? 'Free Plan' : 'Premium Plan'}
              </h3>
            </div>
            <p className="text-sm text-vista-light/70">
              {subscription.plan === 'free'
                ? 'Upgrade to Premium for ad-free viewing and more features'
                : 'Thanks for being a premium member'}
            </p>
          </div>

          {userIsAdmin ? (
            <div className="flex items-center gap-2">
              <Badge className="bg-purple-500 text-white border-none px-2 py-0.5">
                Admin Account
              </Badge>
              <Button
                variant="outline"
                className="bg-black/20 hover:bg-black/40 border-white/5 hover:border-white/10 text-emerald-400"
                onClick={() => router.push('/admin/subscriptions')}
              >
                Manage Subscriptions
              </Button>
            </div>
          ) : subscription.plan === 'free' ? (
            <Button
              className="bg-emerald-500 hover:bg-emerald-600 text-white"
              onClick={() => router.push('/subscription')}
            >
              Upgrade to Premium
            </Button>
          ) : (
            <Button
              variant="outline"
              className="bg-black/20 hover:bg-black/40 border-white/5 hover:border-white/10 text-emerald-400"
              onClick={() => router.push('/subscription/manage')}
            >
              Manage Plan
            </Button>
          )}
        </div>

        <div className="space-y-4 bg-vista-dark/40 p-4 rounded-lg border border-vista-light/10">
          <h4 className="text-vista-light font-medium">Billing Summary</h4>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-vista-light/70">Billing Cycle</span>
              <span className="text-vista-light font-medium">Monthly</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-vista-light/70">Next Billing Date</span>
              <span className="text-vista-light font-medium">
                {subscription.renewalDate ? (
                  subscription.renewalDate.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })
                ) : (
                  'Not applicable'
                )}
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-vista-light/70">Subscription Cost</span>
              <span className="text-vista-light font-medium">
                {subscription.plan === 'free' ? 'Free' : '$14.99/month'}
              </span>
            </div>
          </div>
        </div>
      </SettingsCard>

      {subscription.plan !== 'free' && (
        <SettingsCard
          title="Payment Methods"
          description="Manage your payment details"
          icon={CreditCard}
          accentColor="emerald"
        >
          <PaymentMethodsSection userId={user?.id} />
        </SettingsCard>
      )}

      <SettingsCard
        title="Billing History"
        description="View your past transactions"
        icon={Download}
        accentColor="blue"
      >
        <BillingHistorySection
          userId={user?.id}
          subscriptionPlan={userIsAdmin ? 'premium' : subscription.plan}
          onUpgradeClick={() => router.push('/subscription')}
          isAdmin={userIsAdmin}
        />
      </SettingsCard>
    </div>
  );

  // Handle logout
  const handleLogout = () => {
    signOut();
    router.push('/');
  };

  return (
    <SettingsLayout
      activeTab={activeTab}
      setActiveTab={setActiveTab}
    >
      {isLoading ? (
        <div className="flex items-center justify-center py-20">
          <div className="w-12 h-12 border-4 border-vista-light/20 border-t-vista-blue rounded-full animate-spin"></div>
        </div>
      ) : (
        <>
          {activeTab === 'account' && renderAccountSettings()}
          {activeTab === 'preferences' && renderPreferencesSettings()}
          {activeTab === 'playback' && renderPlaybackSettings()}
          {activeTab === 'notifications' && renderNotificationsSettings()}
          {activeTab === 'privacy' && renderPrivacySettings()}
          {activeTab === 'payment' && renderPaymentSettings()}
        </>
      )}
      <PasswordChangeDialog />
    </SettingsLayout>
  );
}

/**
 * Nickname Generator
 * 
 * This utility generates friendly, memorable nicknames for anonymous visitors
 * based on adjectives and nouns. The nicknames are deterministic based on the
 * visitor ID, so the same visitor will always get the same nickname.
 */

// List of friendly adjectives
const adjectives = [
  '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>',
  '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>',
  'Cheerful', '<PERSON>', 'Jo<PERSON>', '<PERSON>yal', 'Peaceful', '<PERSON><PERSON>'
];

// List of animal nouns
const nouns = [
  '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>lphin', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>',
  '<PERSON>ynx', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>'
];

/**
 * Generate a deterministic hash from a string
 * 
 * @param str The string to hash
 * @returns A number between 0 and 2^32 - 1
 */
function hashString(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return Math.abs(hash);
}

/**
 * Generate a friendly nickname from a visitor ID
 * 
 * @param visitorId The visitor ID to generate a nickname from
 * @returns A friendly nickname like "SwiftPanda" or "BrightFox"
 */
export function generateNickname(visitorId: string): string {
  const hash = hashString(visitorId);
  
  // Use the hash to deterministically select an adjective and noun
  const adjective = adjectives[hash % adjectives.length];
  const noun = nouns[(hash >> 8) % nouns.length];
  
  return `${adjective}${noun}`;
}

/**
 * Generate a color from a visitor ID
 * 
 * @param visitorId The visitor ID to generate a color from
 * @returns A hex color code like "#ff5733"
 */
export function generateColor(visitorId: string): string {
  const hash = hashString(visitorId);
  
  // Generate a pastel color using HSL
  // Use the hash to get a hue between 0 and 360
  const hue = hash % 360;
  
  // Fixed saturation and lightness for pastel colors
  const saturation = 70;
  const lightness = 65;
  
  // Convert HSL to hex
  const h = hue / 360;
  const s = saturation / 100;
  const l = lightness / 100;
  
  let r, g, b;
  
  if (s === 0) {
    r = g = b = l; // achromatic
  } else {
    const hue2rgb = (p: number, q: number, t: number) => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1/6) return p + (q - p) * 6 * t;
      if (t < 1/2) return q;
      if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
      return p;
    };
    
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;
    
    r = hue2rgb(p, q, h + 1/3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1/3);
  }
  
  const toHex = (x: number) => {
    const hex = Math.round(x * 255).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };
  
  return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
}

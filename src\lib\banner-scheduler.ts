import { ensureMongooseConnection } from '@/lib/mongodb';
import BannerAd from '@/models/BannerAd';

/**
 * Banner Scheduler Service
 * Handles banner scheduling, expiration, and cleanup tasks
 */
export class BannerScheduler {
  /**
   * Get all currently active banners based on scheduling rules
   */
  static async getActiveBanners(position?: 'top' | 'center' | 'bottom' | 'hero-overlay' | 'between-sections') {
    await ensureMongooseConnection();

    const now = new Date();
    
    // Build query for active banners
    const query: any = {
      isActive: true,
      $or: [
        { startDate: { $lte: now } },
        { startDate: { $exists: false } }
      ],
      $and: [
        {
          $or: [
            { endDate: { $gte: now } },
            { endDate: { $exists: false } },
            { endDate: null }
          ]
        }
      ]
    };

    // Filter by position if specified
    if (position) {
      query['styling.positions'] = position;
    }

    const banners = await BannerAd.find(query)
      .select('title description imageUrl linkUrl styling priority analytics')
      .sort({ priority: -1, createdAt: -1 })
      .limit(10) // Reasonable limit to prevent performance issues
      .lean();

    return banners;
  }

  /**
   * Check and update expired banners
   * This should be run periodically (e.g., via cron job)
   */
  static async updateExpiredBanners() {
    await ensureMongooseConnection();

    const now = new Date();

    // Find banners that should be deactivated due to expiration
    const expiredBanners = await BannerAd.find({
      isActive: true,
      endDate: { $lt: now }
    });

    if (expiredBanners.length > 0) {
      // Deactivate expired banners
      await BannerAd.updateMany(
        {
          isActive: true,
          endDate: { $lt: now }
        },
        {
          $set: { isActive: false }
        }
      );

      console.log(`Deactivated ${expiredBanners.length} expired banners`);
    }

    return {
      deactivatedCount: expiredBanners.length,
      deactivatedBanners: expiredBanners.map(banner => ({
        id: banner._id,
        title: banner.title,
        endDate: banner.endDate
      }))
    };
  }

  /**
   * Activate scheduled banners that should start now
   */
  static async activateScheduledBanners() {
    await ensureMongooseConnection();

    const now = new Date();

    // Find banners that should be activated
    const scheduledBanners = await BannerAd.find({
      isActive: false,
      startDate: { $lte: now },
      $or: [
        { endDate: { $gte: now } },
        { endDate: { $exists: false } },
        { endDate: null }
      ]
    });

    if (scheduledBanners.length > 0) {
      // Activate scheduled banners
      await BannerAd.updateMany(
        {
          isActive: false,
          startDate: { $lte: now },
          $or: [
            { endDate: { $gte: now } },
            { endDate: { $exists: false } },
            { endDate: null }
          ]
        },
        {
          $set: { isActive: true }
        }
      );

      console.log(`Activated ${scheduledBanners.length} scheduled banners`);
    }

    return {
      activatedCount: scheduledBanners.length,
      activatedBanners: scheduledBanners.map(banner => ({
        id: banner._id,
        title: banner.title,
        startDate: banner.startDate
      }))
    };
  }

  /**
   * Get banner analytics summary
   */
  static async getBannerAnalytics(startDate?: Date, endDate?: Date) {
    await ensureMongooseConnection();

    const matchQuery: any = {};
    
    if (startDate || endDate) {
      matchQuery.createdAt = {};
      if (startDate) matchQuery.createdAt.$gte = startDate;
      if (endDate) matchQuery.createdAt.$lte = endDate;
    }

    const analytics = await BannerAd.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: null,
          totalBanners: { $sum: 1 },
          activeBanners: {
            $sum: {
              $cond: [{ $eq: ['$isActive', true] }, 1, 0]
            }
          },
          totalViews: { $sum: '$analytics.views' },
          totalClicks: { $sum: '$analytics.clicks' },
          totalImpressions: { $sum: '$analytics.impressions' }
        }
      },
      {
        $project: {
          _id: 0,
          totalBanners: 1,
          activeBanners: 1,
          totalViews: 1,
          totalClicks: 1,
          totalImpressions: 1,
          clickThroughRate: {
            $cond: [
              { $gt: ['$totalImpressions', 0] },
              {
                $multiply: [
                  { $divide: ['$totalClicks', '$totalImpressions'] },
                  100
                ]
              },
              0
            ]
          }
        }
      }
    ]);

    return analytics[0] || {
      totalBanners: 0,
      activeBanners: 0,
      totalViews: 0,
      totalClicks: 0,
      totalImpressions: 0,
      clickThroughRate: 0
    };
  }

  /**
   * Get top performing banners
   */
  static async getTopPerformingBanners(limit = 5) {
    await ensureMongooseConnection();

    const topBanners = await BannerAd.find({})
      .select('title analytics createdAt')
      .sort({ 'analytics.clicks': -1, 'analytics.views': -1 })
      .limit(limit)
      .lean();

    return topBanners.map(banner => ({
      id: banner._id,
      title: banner.title,
      views: banner.analytics.views,
      clicks: banner.analytics.clicks,
      impressions: banner.analytics.impressions,
      clickThroughRate: banner.analytics.impressions > 0 
        ? ((banner.analytics.clicks / banner.analytics.impressions) * 100).toFixed(2)
        : '0.00',
      createdAt: banner.createdAt
    }));
  }

  /**
   * Clean up old banner analytics data
   * Removes analytics data older than specified days
   */
  static async cleanupOldAnalytics(daysToKeep = 90) {
    await ensureMongooseConnection();

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    // Reset analytics for old banners
    const result = await BannerAd.updateMany(
      {
        createdAt: { $lt: cutoffDate },
        'analytics.views': { $gt: 0 }
      },
      {
        $set: {
          'analytics.views': 0,
          'analytics.clicks': 0,
          'analytics.impressions': 0
        }
      }
    );

    console.log(`Reset analytics for ${result.modifiedCount} old banners`);

    return {
      resetCount: result.modifiedCount,
      cutoffDate
    };
  }

  /**
   * Run all maintenance tasks
   * This should be called periodically (e.g., every hour)
   */
  static async runMaintenance() {
    console.log('Running banner maintenance tasks...');

    const results = {
      timestamp: new Date(),
      expiredBanners: await this.updateExpiredBanners(),
      activatedBanners: await this.activateScheduledBanners(),
    };

    console.log('Banner maintenance completed:', results);
    return results;
  }
}

/**
 * Utility function to validate banner scheduling
 */
export function validateBannerSchedule(startDate?: Date, endDate?: Date, duration?: number) {
  const errors: string[] = [];
  const now = new Date();

  if (startDate && startDate < now) {
    errors.push('Start date cannot be in the past');
  }

  if (endDate && startDate && endDate <= startDate) {
    errors.push('End date must be after start date');
  }

  if (duration && duration <= 0) {
    errors.push('Duration must be a positive number');
  }

  if (duration && endDate) {
    errors.push('Cannot specify both duration and end date');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Calculate end date from start date and duration
 */
export function calculateEndDate(startDate: Date, duration: number): Date {
  const endDate = new Date(startDate);
  endDate.setDate(endDate.getDate() + duration);
  return endDate;
}

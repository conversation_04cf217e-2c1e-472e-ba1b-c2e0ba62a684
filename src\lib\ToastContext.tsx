"use client"

import React, { createContext, use<PERSON><PERSON>back, useContext, useReducer, useRef, useEffect } from 'react'
import { v4 as uuidv4 } from 'uuid'
import { cn } from '@/lib/utils'
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert'

// Types for our toast system
export type ToastType = 'success' | 'error' | 'warning' | 'info' | 'default'

export type ToastPosition = 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center'

export interface ToastItem {
  id: string
  title: string
  message: string
  type: ToastType
  position?: ToastPosition
  duration: number
  createdAt: number
  autoDismiss?: boolean
}

// Action types for our reducer
type ToastAction =
  | { type: 'ADD_TOAST'; payload: Omit<ToastItem, 'id' | 'createdAt'> }
  | { type: 'REMOVE_TOAST'; payload: { id: string } }
  | { type: 'UPDATE_TOAST'; payload: { id: string } & Partial<ToastItem> }

// State type for our context
interface ToastState {
  toasts: ToastItem[]
}

// Context type
interface ToastContextType {
  state: ToastState
  dispatch: React.Dispatch<ToastAction>
}

// Toast reducer to handle actions
const toastReducer = (state: ToastState, action: ToastAction): ToastState => {
  switch (action.type) {
    case 'ADD_TOAST':
      return {
        ...state,
        toasts: [
          ...state.toasts,
          {
            id: uuidv4(),
            createdAt: Date.now(),
            ...action.payload,
          },
        ],
      }
    case 'REMOVE_TOAST':
      return {
        ...state,
        toasts: state.toasts.filter((toast) => toast.id !== action.payload.id),
      }
    case 'UPDATE_TOAST':
      return {
        ...state,
        toasts: state.toasts.map((toast) =>
          toast.id === action.payload.id
            ? { ...toast, ...action.payload }
            : toast
        ),
      }
    default:
      return state
  }
}

// Create context with initial state
const ToastContext = createContext<ToastContextType | null>(null)

// Map toast type to Alert variant
function getAlertVariant(type: ToastType | null | undefined): string {
  switch (type) {
    case 'success':
      return 'success'
    case 'error':
      return 'destructive'
    case 'warning':
      return 'warning'
    case 'info':
      return 'info'
    default:
      return 'default'
  }
}

// Custom Toast component with progress bar
function Toast({ toast, onDismiss }: { toast: ToastItem; onDismiss: () => void }) {
  const progressRef = useRef<HTMLDivElement>(null)
  const timeoutRef = useRef<NodeJS.Timeout>()

  // Auto dismiss after animation completes
  useEffect(() => {
    if (toast.autoDismiss) {
      // Clear any existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }

      // Set a new timeout to match the animation duration (4 seconds)
      timeoutRef.current = setTimeout(() => {
        onDismiss()
      }, 4000) // Match this with the CSS animation duration

      // Add a backup timeout in case the first one fails
      const backupTimeout = setTimeout(() => {
        onDismiss()
      }, 6000) // Backup timeout after 6 seconds

      // Store both timeouts
      const timeouts = { main: timeoutRef.current, backup: backupTimeout }
      timeoutRef.current = timeouts as any
    }

    // Cleanup on unmount
    return () => {
      if (timeoutRef.current) {
        // Clear both timeouts if they exist
        if (typeof timeoutRef.current === 'object' && 'main' in timeoutRef.current) {
          clearTimeout((timeoutRef.current as any).main)
          clearTimeout((timeoutRef.current as any).backup)
        } else {
          clearTimeout(timeoutRef.current)
        }
      }
    }
  }, [toast.autoDismiss, onDismiss])

  // Animation classes for toast appearance/disappearance
  const getAnimationClasses = () => {
    return 'animate-in fade-in slide-in-from-right duration-300'
  }

  // Handle progress bar animation end
  const handleAnimationEnd = () => {
    if (toast.autoDismiss) {
      onDismiss()
    }
  }

  return (
    <Alert
      key={toast.id}
      variant={getAlertVariant(toast.type) as any}
      className={cn(
        'shadow-lg relative overflow-hidden',
        getAnimationClasses(),
        'duration-300 transition-all'
      )}
    >
      <button
        onClick={onDismiss}
        className="absolute top-2 right-2 text-vista-light/50 hover:text-vista-light"
        aria-label="Close toast"
      >
        &times;
      </button>

      {toast.title && (
        <AlertTitle className="font-semibold">{toast.title}</AlertTitle>
      )}

      {toast.message && (
        <AlertDescription className="text-sm">
          {toast.message}
        </AlertDescription>
      )}

      {toast.autoDismiss && (
        <div
          ref={progressRef}
          className="absolute bottom-0 left-0 h-1"
          style={{
            animation: 'progress-bar 4s linear forwards', // Add 'forwards' to maintain end state
            width: '100%',
            backgroundColor: toast.type === 'success' ? 'rgba(34, 197, 94, 0.5)' :
                            toast.type === 'error' ? 'rgba(239, 68, 68, 0.5)' :
                            toast.type === 'warning' ? 'rgba(234, 179, 8, 0.5)' :
                            toast.type === 'info' ? 'rgba(59, 130, 246, 0.5)' :
                            'rgba(255, 255, 255, 0.3)'
          }}
          onAnimationEnd={handleAnimationEnd}
        />
      )}
    </Alert>
  )
}

// Toast container that renders all active toasts
function ToastContainer() {
  const contextValue = useContext(ToastContext)

  if (!contextValue) {
    throw new Error('useToast must be used within a ToastProvider')
  }

  const { state, dispatch } = contextValue

  const handleDismiss = (id: string) => {
    dispatch({ type: 'REMOVE_TOAST', payload: { id } })
  }

  return (
    <div className="toast-container fixed top-4 right-4 z-50 w-80 flex flex-col gap-2">
      {state.toasts.map((toast) => (
        <Toast key={toast.id} toast={toast} onDismiss={() => handleDismiss(toast.id)} />
      ))}
    </div>
  )
}

// Add the progress bar animation and toast limit styles to global styles
const GlobalStyles = () => {
  return (
    <style jsx global>{`
      @keyframes progress-bar {
        from { width: 100%; }
        to { width: 0%; }
      }

      /* Limit the number of toasts visible at once */
      .toast-container > *:nth-child(n+4) {
        display: none;
      }
    `}</style>
  )
}

// Export the ToastProvider with global styles
export function ToastProvider({
  children,
}: {
  children: React.ReactNode
}) {
  const [state, dispatch] = useReducer(toastReducer, { toasts: [] })

  return (
    <ToastContext.Provider value={{ state, dispatch }}>
      {children}
      <div
        className="fixed z-50 top-0 right-0 bottom-0 left-0 pointer-events-none"
        aria-live="polite"
      >
        <ToastContainer />
      </div>

      <GlobalStyles />
    </ToastContext.Provider>
  )
}

// Hook for using the toast system
export function useToastHelpers() {
  const context = useContext(ToastContext)

  if (!context) {
    throw new Error('useToast must be used within a ToastProvider')
  }

  const { dispatch } = context

  // Track recent toasts to prevent duplicates
  const recentToasts = useRef<Set<string>>(new Set());

  // Common function to add toast with deduplication
  const addToast = useCallback(
    (
      title: string,
      message: string,
      type: ToastType = 'default',
      duration: number = 4000,
      position: ToastPosition = 'top-right'
    ) => {
      // Create a key for this toast to check for duplicates
      const toastKey = `${type}:${title}:${message}`;

      // Check if we've shown this exact toast recently (within 5 seconds)
      if (recentToasts.current.has(toastKey)) {
        console.log('Preventing duplicate toast:', toastKey);
        return; // Skip showing duplicate toast
      }

      // Add this toast to our recent set
      recentToasts.current.add(toastKey);

      // Remove it after 5 seconds
      setTimeout(() => {
        recentToasts.current.delete(toastKey);
      }, 5000);

      // Dispatch the toast
      dispatch({
        type: 'ADD_TOAST',
        payload: {
          title,
          message,
          type,
          duration,
          position,
          autoDismiss: true,
        },
      })
    },
    [dispatch]
  )

  // Convenience methods for different toast types
  const success = useCallback(
    (title: string, message: string) => {
      addToast(title, message, 'success')
    },
    [addToast]
  )

  const error = useCallback(
    (title: string, message: string) => {
      addToast(title, message, 'error')
    },
    [addToast]
  )

  const warning = useCallback(
    (title: string, message: string) => {
      addToast(title, message, 'warning')
    },
    [addToast]
  )

  const info = useCallback(
    (title: string, message: string) => {
      addToast(title, message, 'info')
    },
    [addToast]
  )

  // Remove a toast by ID
  const removeToast = useCallback(
    (id: string) => {
      dispatch({
        type: 'REMOVE_TOAST',
        payload: { id },
      })
    },
    [dispatch]
  )

  return {
    addToast,
    success,
    error,
    warning,
    info,
    removeToast,
  }
}

"use client"

import React, { createContext, useContext, useState, ReactNode } from 'react'
import { Alert } from '@/components/ui/alert'
import { X } from 'lucide-react'

// Types for our error/alert system
export type AlertType = 'info' | 'success' | 'warning' | 'error'

export interface AlertMessage {
  id: string
  type: AlertType
  title: string
  message: string
  autoClose?: boolean
  duration?: number
}

interface ErrorContextType {
  alerts: AlertMessage[]
  addAlert: (alert: Omit<AlertMessage, 'id'>) => void
  removeAlert: (id: string) => void
}

// Create the context
const ErrorContext = createContext<ErrorContextType | undefined>(undefined)

// Helper function to map alert type to variant
function mapAlertTypeToVariant(type: AlertType): string {
  switch (type) {
    case 'error':
      return 'destructive'
    case 'warning':
      return 'warning'
    case 'success':
      return 'success'
    case 'info':
      return 'info'
    default:
      return 'default'
  }
}

// Create a provider component
export function Error<PERSON>rovider({ children }: { children: ReactNode }) {
  const [alerts, setAlerts] = useState<AlertMessage[]>([])

  // Add a new alert
  const addAlert = (alert: Omit<AlertMessage, 'id'>) => {
    const id = Math.random().toString(36).substring(2, 9)
    const newAlert = { ...alert, id }
    setAlerts((prev) => [...prev, newAlert])

    // Auto-close alert if specified
    if (alert.autoClose) {
      const duration = alert.duration || 5000 // Default to 5 seconds
      setTimeout(() => {
        removeAlert(id)
      }, duration)
    }
  }

  // Remove an alert by id
  const removeAlert = (id: string) => {
    setAlerts((prev) => prev.filter((alert) => alert.id !== id))
  }

  return (
    <ErrorContext.Provider value={{ alerts, addAlert, removeAlert }}>
      {children}

      {/* Alert Display */}
      {alerts.length > 0 && (
        <div className="fixed bottom-4 right-4 z-50 flex flex-col gap-2 max-w-md">
          {alerts.map((alert) => (
            <Alert
              key={alert.id}
              variant={mapAlertTypeToVariant(alert.type)}
              className="shadow-lg animate-in slide-in-from-right-5"
            >
              <div className="flex justify-between items-start">
                <div>
                  <div className="font-medium">{alert.title}</div>
                  <div className="text-sm mt-1">{alert.message}</div>
                </div>
                <button
                  onClick={() => removeAlert(alert.id)}
                  className="ml-2 text-vista-light/70 hover:text-vista-light"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            </Alert>
          ))}
        </div>
      )}
    </ErrorContext.Provider>
  )
}

// Custom hook to use the error context
export function useError() {
  const context = useContext(ErrorContext)
  if (context === undefined) {
    throw new Error('useError must be used within an ErrorProvider')
  }
  return context
}

// Helper functions for common error types
export function useErrorHelpers() {
  const { addAlert } = useError()

  const showError = (title: string, message: string, autoClose = true, duration = 8000) => {
    addAlert({ type: 'error', title, message, autoClose, duration })
  }

  const showWarning = (title: string, message: string, autoClose = true, duration = 6000) => {
    addAlert({ type: 'warning', title, message, autoClose, duration })
  }

  const showSuccess = (title: string, message: string, autoClose = true, duration = 5000) => {
    addAlert({ type: 'success', title, message, autoClose, duration })
  }

  const showInfo = (title: string, message: string, autoClose = true, duration = 5000) => {
    addAlert({ type: 'info', title, message, autoClose, duration })
  }

  return {
    showError,
    showWarning,
    showSuccess,
    showInfo
  }
}

import mongoose, { Document, Schema } from 'mongoose';

export interface IUserActivity extends Document {
  userId: mongoose.Types.ObjectId;
  type: string;
  action: string;
  details: string;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

const UserActivitySchema = new Schema<IUserActivity>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true
    },
    type: {
      type: String,
      required: true,
      enum: ['auth', 'content', 'profile', 'admin', 'payment', 'error', 'system'],
      index: true
    },
    action: {
      type: String,
      required: true,
      index: true
    },
    details: {
      type: String,
      required: true
    },
    ipAddress: {
      type: String
    },
    userAgent: {
      type: String
    },
    timestamp: {
      type: Date,
      default: Date.now,
      index: true
    },
    metadata: {
      type: Schema.Types.Mixed
    }
  },
  {
    timestamps: true
  }
);

// Create indexes for efficient querying
// Note: userId, type, action, and timestamp already have individual indexes from the schema definition
// These are compound indexes for specific query patterns
UserActivitySchema.index({ userId: 1, timestamp: -1 });
UserActivitySchema.index({ type: 1, action: 1 });
UserActivitySchema.index({ timestamp: -1 }); // Add index for timestamp-based queries

// Create the model if it doesn't exist already
const UserActivity = mongoose.models.UserActivity || mongoose.model<IUserActivity>('UserActivity', UserActivitySchema);

// Export a function to get the model to avoid issues with dynamic imports
export function getUserActivityModel() {
  return mongoose.models.UserActivity || mongoose.model<IUserActivity>('UserActivity', UserActivitySchema);
}

export default UserActivity;

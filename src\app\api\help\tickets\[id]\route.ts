import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongoose';
import { HelpTicket, HelpTicketResponse } from '@/models/HelpTicket';
import User from '@/models/User';

interface TicketQuery {
  _id: string;
  userId?: string;
}

interface TicketUpdates {
  status?: string;
  priority?: string;
  category?: string;
  assignedTo?: string;
  assignedToName?: string;
  tags?: string[];
  internalNotes?: string;
  resolutionNotes?: string;
  resolvedAt?: Date;
  resolvedBy?: string;
  escalated?: boolean;
  escalatedAt?: Date;
  escalatedBy?: string;
  escalationReason?: string;
  satisfactionRating?: number;
  satisfactionFeedback?: string;
  updatedAt?: Date;
}

interface UserFieldUpdates {
  [key: string]: number | string | undefined;
  satisfactionRating?: number;
  satisfactionFeedback?: string;
}

/**
 * GET /api/help/tickets/[id]
 * Get a specific help ticket
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await ensureMongooseConnection();

    // Get userId from query parameters
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized - userId required' }, { status: 401 });
    }

    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const { id } = await Promise.resolve(params);

    // Build query - users can only see their own tickets unless they're admin
    const query: TicketQuery = { _id: id };
    if (user.role !== 'admin' && user.role !== 'superadmin') {
      query.userId = userId;
    }

    const ticket = await HelpTicket.findOne(query)
      .populate('userId', 'name email profileImage')
      .populate('assignedTo', 'name email')
      .populate('lastResponseBy', 'name email')
      .populate('resolvedBy', 'name email')
      .populate('escalatedBy', 'name email')
      .lean();

    if (!ticket) {
      return NextResponse.json({ error: 'Ticket not found' }, { status: 404 });
    }

    // Get responses for this ticket
    const responses = await HelpTicketResponse.find({ ticketId: id })
      .populate('responderId', 'name email profileImage role')
      .sort({ createdAt: 1 })
      .lean();

    // Filter out internal responses for non-admin users
    const filteredResponses = user.role === 'admin' || user.role === 'superadmin' 
      ? responses 
      : responses.filter(response => !response.isInternal);

    return NextResponse.json({
      ticket,
      responses: filteredResponses
    });

  } catch (error) {
    console.error('Error fetching help ticket:', error);
    return NextResponse.json(
      { error: 'Failed to fetch help ticket' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/help/tickets/[id]
 * Update a help ticket
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Try to get the user ID from multiple sources (same pattern as other endpoints)
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    // Get request body first to check for userId
    const body = await request.json();

    // If still no userId, try to get it from request body
    if (!userId) {
      userId = body.userId;
    }

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized - userId required' }, { status: 401 });
    }

    await ensureMongooseConnection();

    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const { id } = await Promise.resolve(params);

    // Build query - users can only update their own tickets unless they're admin
    const query: TicketQuery = { _id: id };
    if (user.role !== 'admin' && user.role !== 'superadmin') {
      query.userId = userId;
      // Regular users can only update limited fields
      const allowedFields = ['satisfactionRating', 'satisfactionFeedback'];
      const updates = Object.keys(body).reduce((acc, key) => {
        if (allowedFields.includes(key)) {
          acc[key] = body[key];
        }
        return acc;
      }, {} as UserFieldUpdates);
      
      if (Object.keys(updates).length === 0) {
        return NextResponse.json(
          { error: 'No valid fields to update' },
          { status: 400 }
        );
      }

      const ticket = await HelpTicket.findOneAndUpdate(
        query,
        { ...updates, updatedAt: new Date() },
        { new: true }
      ).populate('userId', 'name email profileImage');

      if (!ticket) {
        return NextResponse.json({ error: 'Ticket not found' }, { status: 404 });
      }

      return NextResponse.json({
        message: 'Ticket updated successfully',
        ticket
      });
    }

    // Admin updates
    const {
      status,
      priority,
      assignedTo,
      assignedToName,
      tags,
      internalNotes,
      resolutionNotes,
      escalated,
      escalationReason
    } = body;

    const updates: TicketUpdates = {};
    
    if (status !== undefined) {
      updates.status = status;
      if (status === 'resolved' || status === 'closed') {
        updates.resolvedAt = new Date();
        updates.resolvedBy = userId;
      }
    }
    
    if (priority !== undefined) updates.priority = priority;
    if (assignedTo !== undefined) {
      updates.assignedTo = assignedTo;
      if (assignedTo) {
        const assignedUser = await User.findById(assignedTo);
        updates.assignedToName = assignedUser?.name;
      } else {
        updates.assignedToName = undefined;
      }
    }
    if (assignedToName !== undefined) updates.assignedToName = assignedToName;
    if (tags !== undefined) updates.tags = tags;
    if (internalNotes !== undefined) updates.internalNotes = internalNotes;
    if (resolutionNotes !== undefined) updates.resolutionNotes = resolutionNotes;
    
    if (escalated !== undefined) {
      updates.escalated = escalated;
      if (escalated && !updates.escalatedAt) {
        updates.escalatedAt = new Date();
        updates.escalatedBy = userId;
        if (escalationReason) updates.escalationReason = escalationReason;
      }
    }

    const ticket = await HelpTicket.findByIdAndUpdate(
      id,
      { ...updates, updatedAt: new Date() },
      { new: true }
    )
      .populate('userId', 'name email profileImage')
      .populate('assignedTo', 'name email')
      .populate('lastResponseBy', 'name email');

    if (!ticket) {
      return NextResponse.json({ error: 'Ticket not found' }, { status: 404 });
    }

    return NextResponse.json({
      message: 'Ticket updated successfully',
      ticket
    });

  } catch (error) {
    console.error('Error updating help ticket:', error);
    return NextResponse.json(
      { error: 'Failed to update help ticket' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/help/tickets/[id]
 * Delete a help ticket (admin only)
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Try to get the user ID from multiple sources (same pattern as banner-ads)
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    if (!userId) {
      console.error('Help tickets DELETE API: No user ID found in request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await ensureMongooseConnection();

    // Check if user exists and is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    if (user.role !== 'admin' && user.role !== 'superadmin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id } = await Promise.resolve(params);

    // Delete the ticket and all its responses
    await HelpTicketResponse.deleteMany({ ticketId: id });
    const deletedTicket = await HelpTicket.findByIdAndDelete(id);

    if (!deletedTicket) {
      return NextResponse.json({ error: 'Ticket not found' }, { status: 404 });
    }

    return NextResponse.json({
      message: 'Ticket deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting help ticket:', error);
    return NextResponse.json(
      { error: 'Failed to delete help ticket' },
      { status: 500 }
    );
  }
}

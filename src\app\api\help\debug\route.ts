import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongoose';
import User from '@/models/User';
import { authMiddleware } from '@/lib/middleware';

/**
 * GET /api/help/debug
 * Debug endpoint to check authentication for help system
 */
export async function GET(request: NextRequest) {
  try {
    await ensureMongooseConnection();

    // Debug: Log all cookies
    const allCookies = request.cookies.getAll();
    console.log('Help debug - All cookies:', allCookies.map(c => `${c.name}=${c.value.substring(0, 10)}...`));

    const debugInfo = {
      cookieCount: allCookies.length,
      cookieNames: allCookies.map(c => c.name),
      timestamp: new Date().toISOString()
    };

    // Get userId from query parameters
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({
        ...debugInfo,
        authenticated: false,
        message: 'No userId provided in query parameters'
      });
    }

    // Find user
    const user = await User.findById(userId).select('name email role').lean();

    if (!user) {
      return NextResponse.json({
        ...debugInfo,
        authenticated: false,
        userFound: false,
        userId,
        message: 'User not found in database'
      });
    }

    return NextResponse.json({
      ...debugInfo,
      authenticated: true,
      userFound: true,
      userId,
      user: {
        name: user.name,
        email: user.email,
        role: user.role
      },
      message: 'Authentication successful via query parameter'
    });

  } catch (error) {
    console.error('Help debug error:', error);
    return NextResponse.json(
      { error: 'Debug check failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

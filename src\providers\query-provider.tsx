"use client"

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useState, ReactNode } from 'react'

interface QueryProviderProps {
  children: ReactNode
}

export function QueryProvider({ children }: QueryProviderProps) {
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        // 10 minutes stale time for general queries
        staleTime: 1000 * 60 * 10,
        // Cache for 30 minutes
        gcTime: 1000 * 60 * 30,
        // 3 retries with exponential backoff
        retry: 3,
        retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
        // Refetch on window focus, but with a reasonable configuration
        refetchOnWindowFocus: 'always',
        // Disable automatic refetch on reconnect to save bandwidth
        refetchOnReconnect: false,
      },
    },
  }))

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
} 
'use client';

import React from 'react';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  ArrowLeft,
  RefreshCw,
  XCircle
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import UserFormModal from '@/components/admin/UserFormModal';
import UserDetailView from '@/components/admin/UserDetailView';

// Define a type for the user data
interface UserData {
  id: string;
  name: string;
  email: string;
  role: string;
  createdAt?: string;
  lastLogin?: string;
  status?: string;
  picture?: string;
  emailVerified?: Date | null;
  [key: string]: any; // Allow for additional properties
}

interface PageParams {
  id: string;
}

export default function UserDetailPage({ params }: { params: Promise<PageParams> }) {
  const { user: currentUser, isAdmin } = useAuth();
  const router = useRouter();

  // Unwrap params with React.use() as recommended by Next.js
  const unwrappedParams = React.use(params);
  const userId = unwrappedParams.id;

  // State with proper typing
  const [user, setUser] = useState<UserData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  // Redirect non-admin users
  useEffect(() => {
    if (currentUser && !isAdmin()) {
      router.push('/');
    }
  }, [currentUser, isAdmin, router]);

  // Fetch user data
  const fetchUserData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Add timestamp to prevent caching
      const timestamp = Date.now();
      const response = await fetch(`/api/admin/users/${userId}?t=${timestamp}`, {
        credentials: 'include',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
        cache: 'no-store'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to fetch user (${response.status})`);
      }

      const userData = await response.json();
      // Ensure role is always present
      if (!userData.role) {
        userData.role = 'user'; // Default to 'user' if role is not specified
      }

      // Log the user data for debugging
      console.log('Fetched user data:', {
        id: userData.id,
        name: userData.name,
        profileImage: userData.profileImage,
        picture: userData.picture
      });

      setUser(userData);
    } catch (error) {
      console.error('Error fetching user:', error);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Memoize fetchUserData to avoid dependency issues
  const memoizedFetchUserData = React.useCallback(fetchUserData, [userId]);

  // Fetch user on component mount and when userId changes
  useEffect(() => {
    memoizedFetchUserData();

    // Set up a refresh interval to keep the data fresh
    const refreshInterval = setInterval(() => {
      memoizedFetchUserData();
    }, 15000); // Refresh every 15 seconds

    return () => clearInterval(refreshInterval);
  }, [memoizedFetchUserData]);

  // Handle user deletion success - just navigate back to the users list
  const handleDeleteSuccess = () => {
    router.push('/admin/users');
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <h1 className="text-3xl font-bold text-vista-light">User Details</h1>
        </div>
        <Card>
          <CardContent className="p-8 flex justify-center items-center">
            <RefreshCw className="h-8 w-8 animate-spin text-vista-light/50" />
          </CardContent>
        </Card>
      </div>
    );
  }

  // Error state
  if (error || !user) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <h1 className="text-3xl font-bold text-vista-light">User Details</h1>
        </div>
        <Card>
          <CardContent className="p-8 text-center">
            <XCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
            <h2 className="text-xl font-semibold text-vista-light mb-2">Error Loading User</h2>
            <p className="text-vista-light/70 mb-4">{error || 'User not found'}</p>
            <Button onClick={() => fetchUserData()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2 mb-4">
        <Button variant="outline" size="sm" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <h1 className="text-3xl font-bold text-vista-light">User Details</h1>
      </div>

      <UserDetailView
        userId={userId}
        onEdit={() => setIsEditModalOpen(true)}
        onDelete={handleDeleteSuccess}
      />

      {/* Edit User Modal */}
      <UserFormModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onSuccess={fetchUserData}
        editUser={user}
      />
    </div>
  );
}

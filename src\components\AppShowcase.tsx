'use client';

import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { PlayCircle, Monitor, Smartphone, Tablet, Laptop, Cast, Tv } from 'lucide-react';

export default function AppShowcase() {
  return (
    <section className="py-16 relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-b from-vista-dark via-vista-dark/95 to-vista-dark z-0" />

      <div className="container mx-auto px-4 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-vista-light mb-6">
              Stream Anywhere, Anytime
            </h2>

            <p className="text-vista-light/80 text-lg mb-8 leading-relaxed">
              Enjoy StreamVista on any device with a web browser. Watch on your phone, tablet, or computer with our responsive web interface designed for the best viewing experience.
            </p>

            <div className="space-y-4 mb-8">
              <div className="flex items-start gap-3">
                <div className="p-2 bg-vista-blue/20 rounded-lg text-vista-blue mt-1">
                  <Monitor className="h-5 w-5" />
                </div>
                <div>
                  <h3 className="text-vista-light font-medium text-lg">Multi-device Streaming</h3>
                  <p className="text-vista-light/70">Watch seamlessly across all your devices with automatic sync</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="p-2 bg-vista-blue/20 rounded-lg text-vista-blue mt-1">
                  <Cast className="h-5 w-5" />
                </div>
                <div>
                  <h3 className="text-vista-light font-medium text-lg">Cast to TV</h3>
                  <p className="text-vista-light/70">Cast content to your smart TV or streaming device</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="p-2 bg-vista-blue/20 rounded-lg text-vista-blue mt-1">
                  <PlayCircle className="h-5 w-5" />
                </div>
                <div>
                  <h3 className="text-vista-light font-medium text-lg">Continue Watching</h3>
                  <p className="text-vista-light/70">Pick up exactly where you left off on any device</p>
                </div>
              </div>
            </div>

            <div className="flex flex-wrap gap-4">
              <Link href="/features">
                <Button className="bg-vista-blue hover:bg-vista-blue/90 gap-2">
                  <Tv className="h-5 w-5" />
                  Explore Features
                </Button>
              </Link>

              <Link href="/watch-party">
                <Button variant="outline" className="bg-white text-black hover:bg-white/90 border-none gap-2">
                  Try Watch Party
                </Button>
              </Link>
            </div>
          </motion.div>

          {/* Device mockups */}
          <motion.div
            className="relative h-[400px] md:h-[500px]"
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {/* Phone mockup */}
            <motion.div
              className="absolute left-0 top-1/2 -translate-y-1/2 z-10 shadow-xl rounded-xl overflow-hidden border-8 border-vista-dark-lighter"
              initial={{ y: 0 }}
              animate={{ y: [0, -10, 0] }}
              transition={{ duration: 5, repeat: Infinity, repeatType: "reverse" }}
            >
              <div className="relative w-[160px] h-[320px]">
                <Image
                  src="https://images.unsplash.com/photo-1611162617213-7d7a39e9b1d7?q=80&w=600"
                  alt="Mobile streaming"
                  fill
                  className="object-cover"
                />
              </div>
            </motion.div>

            {/* Tablet mockup */}
            <motion.div
              className="absolute right-0 top-1/2 -translate-y-1/2 z-0 shadow-xl rounded-xl overflow-hidden border-8 border-vista-dark-lighter"
              initial={{ y: 0 }}
              animate={{ y: [0, 10, 0] }}
              transition={{ duration: 5, repeat: Infinity, repeatType: "reverse", delay: 1 }}
            >
              <div className="relative w-[220px] h-[300px]">
                <Image
                  src="https://images.unsplash.com/photo-1585790050230-5dd28404ccb9?q=80&w=600"
                  alt="Tablet streaming"
                  fill
                  className="object-cover"
                />
              </div>
            </motion.div>

            {/* Laptop mockup */}
            <motion.div
              className="absolute left-1/4 top-1/4 z-20 shadow-xl rounded-xl overflow-hidden"
              initial={{ y: 0 }}
              animate={{ y: [0, 15, 0] }}
              transition={{ duration: 6, repeat: Infinity, repeatType: "reverse", delay: 0.5 }}
            >
              <div className="relative w-[280px] h-[180px]">
                <Image
                  src="https://images.unsplash.com/photo-1593784991095-a205069533cd?q=80&w=600"
                  alt="Desktop streaming"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="h-[14px] bg-vista-dark-lighter"></div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Pencil, Users, UserPlus, Calendar, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ProfileImageUploader } from './ProfileImageUploader';
import { SettingsCard } from './SettingsCard';
import { cn } from '@/lib/utils';
import { ProfileSession } from '@/lib/types';
import { formatDate } from '@/lib/utils';

interface ProfileManagementCardProps {
  activeProfile: ProfileSession;
  profiles: ProfileSession[];
  onProfileImageUpdate: (imageUrl: string) => void;
  className?: string;
}

export function ProfileManagementCard({
  activeProfile,
  profiles,
  onProfileImageUpdate,
  className
}: ProfileManagementCardProps) {
  const router = useRouter();
  const [isHovering, setIsHovering] = useState(false);
  const { isAdmin } = useAuth();
  const userIsAdmin = isAdmin();

  // Fetch profile details including creation date
  const [profileDetails, setProfileDetails] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const fetchProfileDetails = async () => {
      if (!activeProfile?.id) return;

      setIsLoading(true);
      try {
        // Add credentials to ensure cookies are sent
        const response = await fetch(`/api/profiles/${activeProfile.id}/details`, {
          credentials: 'include',
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        });

        if (response.ok) {
          const data = await response.json();
          console.log('Profile details:', data);
          setProfileDetails(data);
        } else {
          console.error('Error response status:', response.status);
          // Try to get error details
          try {
            const errorData = await response.json();
            console.error('Error details:', errorData);
          } catch (e) {
            console.error('Could not parse error response');
          }
        }
      } catch (error) {
        console.error('Error fetching profile details:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProfileDetails();

  }, [activeProfile?.id]);

  return (
    <SettingsCard
      title="Profile Management"
      description="Your active profile and settings"
      className={className}
    >
      <div className="flex flex-col md:flex-row items-center md:items-start gap-4 md:gap-8 py-2 md:py-4">
        {/* Profile Image Section */}
        <div className="relative group mx-auto md:mx-0">
          <ProfileImageUploader
            size="xl"
            profileMode={true}
            className="w-32 h-32 md:w-40 md:h-40 overflow-hidden cursor-pointer transition-all rounded-xl"
            onImageUpdated={onProfileImageUpdate}
            shape="circle"
          />
        </div>

        {/* Profile Details Section */}
        <div className="flex-1 space-y-4 md:space-y-6 w-full text-center md:text-left">
          {/* Profile Name and Type */}
          <div>
            <div className="flex flex-col md:flex-row md:items-center gap-2 mb-2">
              <h3 className="text-2xl font-semibold text-white">{activeProfile?.name}</h3>
              <Badge
                variant="outline"
                className="bg-vista-blue/10 text-vista-blue border-vista-blue/20 self-center md:self-auto"
              >
                Main Profile
              </Badge>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
              {/* Profile Type */}
              <div className="flex items-center gap-2 p-2.5 rounded-lg bg-black/20 border border-white/5">
                <div className="w-8 h-8 md:w-10 md:h-10 rounded-md bg-blue-500/10 flex items-center justify-center">
                  <Users className="h-4 w-4 md:h-5 md:w-5 text-blue-500" />
                </div>
                <div>
                  <p className="text-xs md:text-sm text-vista-light/70">Profile Type</p>
                  <p className="font-medium text-vista-light text-sm md:text-base">Standard Profile</p>
                </div>
              </div>

              {/* Creation Date */}
              <div className="flex items-center gap-2 p-2.5 rounded-lg bg-black/20 border border-white/5">
                <div className="w-8 h-8 md:w-10 md:h-10 rounded-md bg-purple-500/10 flex items-center justify-center">
                  <Calendar className="h-4 w-4 md:h-5 md:w-5 text-purple-500" />
                </div>
                <div>
                  <p className="text-xs md:text-sm text-vista-light/70">Created</p>
                  <p className="font-medium text-vista-light text-sm md:text-base">
                    {isLoading ? (
                      <span className="text-vista-light/50">Loading...</span>
                    ) : profileDetails?.createdAt ? (
                      formatDate(new Date(profileDetails.createdAt))
                    ) : userIsAdmin ? (
                      // Fallback for admin users
                      <span className="text-purple-400">Admin Access</span>
                    ) : (
                      <span className="text-vista-light/50">Not available</span>
                    )}
                  </p>
                </div>
              </div>

              {/* Admin Status */}
              {userIsAdmin && (
                <div className="flex items-center gap-2 p-2.5 rounded-lg bg-purple-500/10 border border-purple-500/20">
                  <div className="w-8 h-8 md:w-10 md:h-10 rounded-md bg-purple-500/20 flex items-center justify-center">
                    <Shield className="h-4 w-4 md:h-5 md:w-5 text-purple-500" />
                  </div>
                  <div>
                    <p className="text-xs md:text-sm text-vista-light/70">Admin Status</p>
                    <div className="flex items-center gap-2">
                      <Badge className="bg-purple-500 text-white border-none px-2 py-0.5 mt-1 text-xs">
                        Admin
                      </Badge>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Profile Actions */}
          <div>
            <h3 className="text-xs md:text-sm font-medium text-vista-light/60 mb-2">Profile Actions</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              <Button
                variant="outline"
                size="sm"
                className="bg-black/20 hover:bg-black/40 border-white/5 hover:border-white/10 w-full flex items-center justify-center h-9 md:h-10"
                onClick={() => router.push('/profiles')}
              >
                <Users className="h-3.5 w-3.5 mr-1.5 text-vista-blue" />
                <span className="text-xs md:text-sm">Switch Profile</span>
              </Button>

              {profiles.length < 5 && (
                <Button
                  variant="outline"
                  size="sm"
                  className="bg-vista-blue/10 border-vista-blue/20 text-vista-blue hover:bg-vista-blue/20 hover:border-vista-blue/30 w-full flex items-center justify-center h-9 md:h-10"
                  onClick={() => router.push('/profiles/create')}
                >
                  <UserPlus className="h-3.5 w-3.5 mr-1.5" />
                  <span className="text-xs md:text-sm">Add Profile</span>
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </SettingsCard>
  );
}

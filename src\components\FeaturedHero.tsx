"use client";

import Image from "next/image";
import { useEffect, useState, useRef, useCallback } from "react";
import { Button } from "./ui/button";
import { Play, Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useToastHelpers } from "@/lib/ToastContext";
import { motion, AnimatePresence } from "framer-motion";
import { getTrendingDaily, MappedContent } from "@/lib/tmdb-api";
import { Badge } from "./ui/badge";

interface FeaturedContent {
  id: string;
  title: string;
  name?: string;
  backdrop_path: string;
  backdropUrl: string;
  overview: string;
  release_date?: string;
  first_air_date?: string;
  vote_average?: number;
  mediaType: "movie" | "tv";
  year?: number;
}

const FeaturedHero = () => {
  const toast = useToastHelpers();
  const router = useRouter();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [previousIndex, setPreviousIndex] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [featuredContent, setFeaturedContent] = useState<FeaturedContent[]>([]);
  const [isMobile, setIsMobile] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [imagesPreloaded, setImagesPreloaded] = useState<Record<number, boolean>>({});
  const autoRotateRef = useRef<NodeJS.Timeout | null>(null);

  // Detect mobile devices
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Helper for image url
  const getImageUrl = useCallback((path: string, size?: string) => {
    const baseUrl = "https://image.tmdb.org/t/p";
    // Use smaller image size for mobile
    const imageSize = size || (isMobile ? "/w780" : "/original");
    return `${baseUrl}${imageSize}${path}`;
  }, [isMobile]);

  // Preload images for smoother transitions
  const preloadImages = useCallback((contentItems: FeaturedContent[]) => {
    if (typeof window === 'undefined' || contentItems.length === 0) return;

    contentItems.forEach((item, index) => {
      const img = new window.Image();
      img.src = getImageUrl(item.backdrop_path);
      img.onload = () => {
        setImagesPreloaded(prev => ({
          ...prev,
          [index]: true
        }));
      };
    });
  }, [getImageUrl]);

  // Fetch trending data from TMDb
  useEffect(() => {
    const fetchFeaturedContent = async () => {
      try {
        setIsLoading(true);
        const data = await getTrendingDaily('all');

        // Filter for items with backdrop images and high vote counts
        const filteredData = data
          .filter(item =>
            item.backdropUrl &&
            item.voteAverage >= 7.5 &&
            item.overview &&
            item.overview.length > 50
          )
          .slice(0, 5); // Take top 5

        const processedContent = filteredData.map(item => ({
          id: item.id, // Keep as string to avoid issues with API calls
          title: item.title,
          backdrop_path: item.backdropUrl?.replace('https://image.tmdb.org/t/p/original', '') || '',
          backdropUrl: item.backdropUrl || '',
          overview: item.overview || '',
          vote_average: item.voteAverage,
          release_date: item.releaseDate || undefined,
          year: item.year,
          mediaType: item.mediaType || (item.id.includes('tv') ? 'tv' : 'movie')
        }));

        setFeaturedContent(processedContent);

        // Preload images after setting content
        preloadImages(processedContent);
      } catch (error) {
        console.error('Error fetching featured content:', error);
        setFeaturedContent([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchFeaturedContent();
  }, [preloadImages]);

  // Handle rotating to the next slide with transition state
  const goToNextSlide = useCallback(() => {
    if (isTransitioning || featuredContent.length <= 1) return;

    setIsTransitioning(true);
    setPreviousIndex(currentIndex);
    setCurrentIndex((prev) => (prev + 1) % featuredContent.length);

    // Reset transition state after animation completes
    setTimeout(() => {
      setIsTransitioning(false);
    }, 1000);
  }, [isTransitioning, featuredContent.length, currentIndex]);

  // Auto rotate featured content when available
  useEffect(() => {
    if (featuredContent.length <= 1) return;

    // Clear any existing interval
    if (autoRotateRef.current) {
      clearInterval(autoRotateRef.current);
    }

    // Set new interval
    autoRotateRef.current = setInterval(() => {
      goToNextSlide();
    }, 8000); // 8 second rotation

    return () => {
      if (autoRotateRef.current) {
        clearInterval(autoRotateRef.current);
      }
    };
  }, [featuredContent.length, currentIndex, isTransitioning, goToNextSlide]);

  // Loading state - use consistent height to prevent layout shift
  if (isLoading) {
    return (
      <div className="hero-section relative w-full overflow-hidden pt-16 md:pt-0 h-[60vh] sm:h-[75vh] md:h-[85vh] flex items-center justify-center bg-vista-dark">
        <Loader2 className="w-12 h-12 text-vista-blue animate-spin" />
      </div>
    );
  }

  // Fallback if no content - use consistent height to prevent layout shift
  if (featuredContent.length === 0) {
    return (
      <div className="hero-section relative w-full overflow-hidden pt-16 md:pt-0 h-[60vh] sm:h-[75vh] md:h-[85vh] flex items-center justify-center bg-vista-dark">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-2">Featured Content</h2>
          <p className="text-gray-300">Unable to load featured content at this time.</p>
        </div>
      </div>
    );
  }

  const current = featuredContent[currentIndex];
  const contentType = current.mediaType;

  return (
    <div className="hero-section relative w-full overflow-hidden pt-16 md:pt-0 h-[60vh] sm:h-[75vh] md:h-[85vh]">
      <div className="absolute inset-0 bg-vista-dark" />

      {/* Previous image (for smooth fade transition) */}
      {previousIndex !== null && (
        <div
          className="absolute inset-0 z-0 transition-opacity duration-1000"
          style={{ opacity: isTransitioning ? 1 : 0 }}
        >
          <Image
            src={getImageUrl(featuredContent[previousIndex].backdrop_path)}
            alt=""
            fill
            sizes={isMobile ?
              "(max-width: 640px) 640px, (max-width: 768px) 768px" :
              "(max-width: 1200px) 1200px, 100vw"}
            quality={isMobile ? 85 : 90}
            style={{
              objectFit: "cover",
              objectPosition: isMobile ? "center top" : "center 20%"
            }}
            className={`min-h-full min-w-full ${isMobile ? 'transform scale-110' : ''}`}
          />
          <div className="absolute inset-0 bg-gradient-to-t from-vista-dark via-transparent to-transparent" />
          <div className="absolute inset-0 bg-gradient-to-r from-vista-dark via-vista-dark/60 to-transparent" />
          {isMobile && (
            <div className="absolute inset-0 bg-gradient-to-b from-vista-dark/70 via-transparent to-transparent" />
          )}
        </div>
      )}

      {/* Current image with animation */}
      <motion.div
        key={`hero-${current.id}`}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 1 }}
        className="absolute inset-0 z-10"
      >
        {!imagesPreloaded[currentIndex] && (
          <div className="absolute inset-0 bg-vista-dark/80 backdrop-blur-sm z-20" />
        )}

        <Image
          key={`img-${current.id}`}
          src={getImageUrl(current.backdrop_path)}
          alt={current.title || "Featured Media"}
          fill
          sizes={isMobile ?
            "(max-width: 640px) 640px, (max-width: 768px) 768px" :
            "(max-width: 1200px) 1200px, 100vw"}
          quality={isMobile ? 85 : 90}
          priority
          style={{
            objectFit: "cover",
            objectPosition: isMobile ? "center top" : "center 20%"
          }}
          placeholder="blur"
          blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mN8/+F9PQAJNANkDFLcyAAAAABJRU5ErkJggg=="
          onLoad={() => {
            setImagesPreloaded(prev => ({
              ...prev,
              [currentIndex]: true
            }));
          }}
          className={`min-h-full min-w-full ${isMobile ? 'transform scale-110' : ''}`}
        />
        <div className="absolute inset-0 bg-gradient-to-t from-vista-dark via-transparent to-transparent" />
        <div className="absolute inset-0 bg-gradient-to-r from-vista-dark via-vista-dark/60 to-transparent" />
        {isMobile && (
          <div className="absolute inset-0 bg-gradient-to-b from-vista-dark/70 via-transparent to-transparent" />
        )}
      </motion.div>

      <div className="hero-content relative h-full flex flex-col justify-end px-4 sm:px-6 md:px-12 pb-6 sm:pb-12 md:pb-20 z-20 max-w-screen-xl mx-auto">
        <motion.div
          key={`content-${current.id}`}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="max-w-3xl"
        >
          {/* Status badges */}
          <div className="flex flex-wrap gap-2 mb-2 sm:mb-3">
            {current.release_date && new Date(current.release_date) > new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) && (
              <Badge className="bg-vista-blue text-white text-xs sm:text-sm">
                New
              </Badge>
            )}
            {current.vote_average && current.vote_average > 8 && (
              <Badge className="bg-yellow-600 text-white text-xs sm:text-sm">
                Top Rated
              </Badge>
            )}
            <Badge variant="outline" className="border-vista-light/30 text-vista-light text-xs sm:text-sm">
              {current.year || (current.release_date ? new Date(current.release_date).getFullYear() : '')}
            </Badge>
            {current.vote_average && current.vote_average > 0 && (
              <Badge variant="outline" className="border-vista-light/30 text-vista-light text-xs sm:text-sm">
                {current.vote_average.toFixed(1)}
              </Badge>
            )}
          </div>

          <h1 className="text-2xl sm:text-3xl md:text-5xl font-bold text-white mb-2 sm:mb-3 md:mb-4">
            {current.title}
          </h1>
          <p className="text-gray-200 text-xs sm:text-sm md:text-base line-clamp-2 md:line-clamp-3 mb-3 sm:mb-4 md:mb-6">
            {current.overview}
          </p>

          <div className="flex items-center gap-2 sm:gap-3">
            {/* Watch button - always visible */}
            <Button
              size="sm"
              className="watch-button h-8 sm:h-9 md:h-10 bg-white text-vista-dark hover:bg-white/90 font-medium shadow-md text-xs sm:text-sm"
              onClick={() => {
                // Validate ID before navigation
                if (!current.id || current.id === 'undefined' || current.id === 'null' || current.id === 'unknown' || current.id === '') {
                  console.error('Invalid ID detected in FeaturedHero Play button:', current.id);
                  return;
                }

                // Ensure we're using the correct content type format
                const contentType = current.mediaType === 'tv' ? 'show' : 'movie';
                // Log the navigation for debugging
                console.log(`Navigating to watch content: id=${current.id}, type=${contentType}`);
                // Use the correct URL format with explicit content type
                router.push(`/watch/${current.id}?forcePlay=true&contentType=${contentType}`);
              }}
            >
              <Play className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" /> Watch
            </Button>

            {/* Details button - always visible */}
            <Button
              size="sm"
              variant="outline"
              className="details-button h-8 sm:h-9 md:h-10 border-vista-blue/80 text-white hover:bg-vista-blue/20 font-medium backdrop-blur-sm bg-black/30 shadow-md text-xs sm:text-sm"
              onClick={() => {
                // Validate ID before navigation
                if (!current.id || current.id === 'undefined' || current.id === 'null' || current.id === 'unknown' || current.id === '') {
                  console.error('Invalid ID detected in FeaturedHero:', current.id);
                  return;
                }

                const contentType = current.mediaType === 'tv' ? 'shows' : 'movies';
                console.log(`Navigating to details: id=${current.id}, type=${contentType}`);
                router.push(`/details/${contentType}/${current.id}`);
              }}
            >
              Details
            </Button>
          </div>
        </motion.div>
      </div>

      {/* Navigation dots */}
      {featuredContent.length > 1 && (
        <div className="absolute bottom-1 sm:bottom-2 md:bottom-4 left-1/2 transform -translate-x-1/2 flex gap-1 sm:gap-2 z-20">
          {featuredContent.map((_, index) => (
            <button
              key={index}
              className={`w-1.5 sm:w-2 h-1.5 sm:h-2 rounded-full transition-all ${
                index === currentIndex
                  ? 'bg-vista-blue w-4 sm:w-6'
                  : 'bg-vista-light/30 hover:bg-vista-light/50'
              }`}
              onClick={() => {
                if (!isTransitioning) {
                  setPreviousIndex(currentIndex);
                  setCurrentIndex(index);
                  setIsTransitioning(true);

                  // Clear auto-rotation and restart it
                  if (autoRotateRef.current) {
                    clearInterval(autoRotateRef.current);
                  }

                  // Reset transition state after animation completes
                  setTimeout(() => {
                    setIsTransitioning(false);
                  }, 1000);
                }
              }}
              aria-label={`View featured item ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default FeaturedHero;
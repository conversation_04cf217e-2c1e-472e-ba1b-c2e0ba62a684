import React, { useEffect, useRef, useState } from 'react'
import { ChatInput } from './ChatInput'
import { ChatMessage } from './ChatMessage'
import { ScrollArea } from '@/components/ui/scroll-area'
import { ChatState } from '@/types/chat'
import { useWatchParty } from '@/lib/WatchPartyContext'
import { WATCH_PARTY_EVENTS } from '@/lib/pusher-server'

// Define typing indicators state management (no longer dependent on Socket.io)
interface TypingUser {
  userId: string;
  userName: string;
  timestamp: number;
}

export function ChatContainer() {
  const {
    currentParty,
    sendMessage,
    sendReaction,
    connectionState
  } = useWatchParty()

  const [chatState, setChatState] = useState<ChatState>({
    messages: [],
    typingUsers: [],
    unreadCount: 0,
    lastReadTimestamp: new Date().toISOString()
  })

  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([])
  const scrollRef = useRef<HTMLDivElement>(null)
  const [autoScroll, setAutoScroll] = useState(true)
  const typingTimeoutRef = useRef<NodeJS.Timeout>()

  // Check if connected based on connectionState
  const isConnected = connectionState === 'connected'

  // Handle new messages
  useEffect(() => {
    if (!currentParty) return

    setChatState(prev => ({
      ...prev,
      messages: currentParty.messages.map(msg => ({
        id: msg.id,
        type: msg.type,
        content: msg.content,
        sender: currentParty.members.find(m => m.id === msg.memberId) || {
          id: msg.memberId,
          name: msg.memberName,
          isHost: false,
          joinedAt: new Date().toISOString(),
          isReady: false
        },
        timestamp: msg.timestamp,
        metadata: {
          edited: false,
          reactions: {}
        }
      }))
    }))
  }, [currentParty])

  // Auto-scroll handling
  useEffect(() => {
    if (autoScroll && scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight
    }
  }, [chatState.messages, autoScroll])

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget
    const isAtBottom = scrollHeight - scrollTop - clientHeight < 50
    setAutoScroll(isAtBottom)
  }

  const handleSendMessage = (content: string) => {
    if (!isConnected) return
    sendMessage(content)
  }

  // We'll simulate typing indicators locally since we're no longer using Socket.io
  const handleStartTyping = () => {
    if (!currentParty) return
    
    const currentMember = currentParty.members.find(m => m.id === currentParty.hostId)
    if (!currentMember) return

    // Update local typing state
    setTypingUsers(prev => {
      // Filter out current user and add them with new timestamp
      const filtered = prev.filter(u => u.userId !== currentMember.id)
      return [...filtered, {
        userId: currentMember.id,
        userName: currentMember.name,
        timestamp: Date.now()
      }]
    })

    // Update chat state for display
    setChatState(prev => ({
      ...prev,
      typingUsers: [
        ...prev.typingUsers.filter(u => u.userId !== currentMember.id),
        { userId: currentMember.id, userName: currentMember.name, timestamp: Date.now() }
      ]
    }))
  }

  const handleStopTyping = () => {
    if (!currentParty) return
    
    const currentMember = currentParty.members.find(m => m.id === currentParty.hostId)
    if (!currentMember) return

    // Update local typing state
    setTypingUsers(prev => prev.filter(u => u.userId !== currentMember.id))

    // Update chat state for display
    setChatState(prev => ({
      ...prev,
      typingUsers: prev.typingUsers.filter(u => u.userId !== currentMember.id)
    }))
  }

  // Clean up stale typing indicators
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now()
      // Remove typing indicators older than 3 seconds
      setTypingUsers(prev => prev.filter(user => now - user.timestamp < 3000))
      setChatState(prev => ({
        ...prev,
        typingUsers: prev.typingUsers.filter(user => now - user.timestamp < 3000)
      }))
    }, 1000)

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="flex flex-col h-full">
      <ScrollArea
        ref={scrollRef}
        className="flex-1 p-4"
        onScroll={handleScroll}
      >
        <div className="space-y-4">
          {chatState.messages.map(message => (
            <ChatMessage
              key={message.id}
              message={message}
              currentUserId={currentParty?.hostId || ''}
              onReaction={sendReaction}
            />
          ))}
        </div>

        {chatState.typingUsers.length > 0 && (
          <div className="h-6 px-4 text-sm text-muted-foreground">
            {chatState.typingUsers
              .map(user => user.userName)
              .join(', ')}{' '}
            {chatState.typingUsers.length === 1 ? 'is' : 'are'} typing...
          </div>
        )}
      </ScrollArea>

      <ChatInput
        onSendMessage={handleSendMessage}
        onStartTyping={handleStartTyping}
        onStopTyping={handleStopTyping}
        disabled={!isConnected}
        placeholder={
          !isConnected
            ? 'Connecting to chat...'
            : 'Type a message...'
        }
      />
    </div>
  )
}
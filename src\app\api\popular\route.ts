import { NextRequest, NextResponse } from 'next/server';
import { fetchFromTMDB } from '@/lib/tmdb';

/**
 * API route for popular content
 * Fetches popular movies or TV shows from TMDb
 */
export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const type = searchParams.get('type') || 'movie';
  const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit') as string) : 20;

  try {
    // Determine the proper endpoint based on content type
    const endpoint = type === 'movie' ? 'movie/popular' : 'tv/popular';
    
    const data = await fetchFromTMDB(endpoint);
    
    if (!data || !data.results) {
      console.log('No popular content found or invalid response format');
      return NextResponse.json([]);
    }

    // Map TMDb results to our format
    const popularContent = data.results.map((item: any) => ({
      id: item.id,
      tmdbId: item.id,
      title: type === 'movie' ? item.title : item.name,
      overview: item.overview,
      posterPath: item.poster_path 
        ? `https://image.tmdb.org/t/p/w300${item.poster_path}`
        : null,
      backdropPath: item.backdrop_path
        ? `https://image.tmdb.org/t/p/w1280${item.backdrop_path}`
        : null,
      year: type === 'movie' 
        ? item.release_date ? new Date(item.release_date).getFullYear() : null
        : item.first_air_date ? new Date(item.first_air_date).getFullYear() : null,
      rating: item.vote_average,
      type: type === 'movie' ? 'movie' : 'show'
    }));

    // Return limited results
    return NextResponse.json(popularContent.slice(0, limit));
  } catch (error) {
    console.error('Error fetching popular content:', error);
    return NextResponse.json(
      { error: 'Failed to fetch popular content' },
      { status: 500 }
    );
  }
} 
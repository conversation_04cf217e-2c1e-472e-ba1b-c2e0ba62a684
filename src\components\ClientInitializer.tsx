'use client';

import { useEffect, useRef } from 'react';

// Function to reset chat that can be called from console
function resetChatAssistant() {
  try {
    if (typeof window === 'undefined') return "Cannot reset chat on server";

    // Clear all previous values
    localStorage.removeItem('chatAssistantDismissed');

    // Set fresh values - ALWAYS set to visible
    localStorage.setItem('chatAssistantDismissed', 'false');

    console.log("[resetChatAssistant] Chat state reset successfully");

    // Force storage events to notify all components
    window.dispatchEvent(new StorageEvent('storage', {
      key: 'chatAssistantDismissed',
      newValue: 'false'
    }));

    // Force reload the page
    window.location.reload();

    return "Chat assistant reset! Page will reload.";
  } catch (e) {
    console.error("[resetChatAssistant] Error:", e);
    return "Error resetting chat assistant: " + e;
  }
}

// Define a custom window interface with our extension
interface WindowWithChatReset {
  resetChatAssistant: typeof resetChatAssistant;
}

export function ClientInitializer() {
  // Use a ref to track if we've already initialized
  const initializedRef = useRef(false);

  useEffect(() => {
    // Skip server-side execution and avoid double initialization
    if (typeof window === 'undefined' || initializedRef.current) return;
    initializedRef.current = true;

    // Mark the page as fully initialized to help with loading issues
    document.documentElement.classList.add('app-initialized');

    // Initialize chat assistant state
    try {
      console.log("[ClientInitializer] Initializing chat assistant...");

      // Clear all previous values
      localStorage.removeItem('chatAssistantDismissed');

      // ALWAYS set to visible on initial load
      localStorage.setItem('chatAssistantDismissed', 'false');

      console.log("[ClientInitializer] Chat state reset successfully");

      // Force storage events to notify all components
      window.dispatchEvent(new StorageEvent('storage', {
        key: 'chatAssistantDismissed',
        newValue: 'false'
      }));

      // Add global function for console debugging
      (window as unknown as WindowWithChatReset).resetChatAssistant = resetChatAssistant;
      console.log("[ClientInitializer] Added resetChatAssistant() function to window object");
    } catch (e) {
      console.error("[ClientInitializer] Error initializing chat state:", e);
    }

    // Force a re-render after a short delay to ensure everything is properly loaded
    setTimeout(() => {
      console.log("[ClientInitializer] Triggering final initialization");
      window.dispatchEvent(new Event('app-fully-initialized'));
    }, 100);
  }, []);

  // This component doesn't render anything
  return null;
}
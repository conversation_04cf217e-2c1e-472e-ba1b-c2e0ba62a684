import { NextRequest, NextResponse } from 'next/server';
import mongoose from 'mongoose';
import User from '@/models/User';
import Profile from '@/models/Profile';
import { ensureMongooseConnection } from '@/lib/mongodb';

/**
 * POST /api/admin/fix-user-profile-image
 * Fix profile image for a specific user
 */
export async function POST(request: NextRequest) {
  try {
    // Parse the request body to get the target user ID
    const { targetUserId } = await request.json();

    if (!targetUserId) {
      return NextResponse.json({ error: 'Target user ID is required' }, { status: 400 });
    }

    // Try to get the admin user ID from the cookie directly
    let adminUserId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!adminUserId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        adminUserId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no adminUserId, check query parameters
    if (!adminUserId) {
      const url = new URL(request.url);
      adminUserId = url.searchParams.get('userId') || '';
    }

    if (!adminUserId) {
      console.error('Admin fix-user-profile-image API: No admin user ID found in request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Check if the requesting user is an admin
    const adminUser = await User.findById(adminUserId).select('role').lean();
    if (!adminUser || (adminUser as { role?: string }).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Find the user to fix
    const user = await User.findById(targetUserId);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Process Cloudinary URLs to ensure they use the correct cloud name
    const processCloudinaryUrl = (url?: string) => {
      if (!url) return null;

      // Check if the URL is a relative path (which won't work)
      if (url && !url.startsWith('http') && !url.startsWith('data:')) {
        return "https://res.cloudinary.com/streamvista/image/upload/v1743812698/defaults/default_avatar.jpg";
      }

      // If it's a Cloudinary URL, ensure it has the correct cloud name (lowercase)
      if (url.includes('cloudinary.com')) {
        // Check if the URL contains the correct cloud name (should be lowercase)
        if (!url.includes('cloudinary.com/streamvista/')) {
          // Fix the URL by replacing the cloud name with the lowercase version
          return url.replace(/cloudinary\.com\/([^\/]+)\//, 'cloudinary.com/streamvista/');
        }
      }
      return url;
    };

    // Default avatar URL
    const defaultAvatarUrl = "https://res.cloudinary.com/streamvista/image/upload/v1743812698/defaults/default_avatar.jpg";

    // Prepare updates
    let updates = {};
    let needsUpdate = false;

    // Check and fix profileImage
    if (user.profileImage) {
      const fixedProfileImage = processCloudinaryUrl(user.profileImage);
      if (fixedProfileImage && fixedProfileImage !== user.profileImage) {
        updates = { ...updates, profileImage: fixedProfileImage };
        needsUpdate = true;
      }
    }

    // Check and fix picture
    if (user.picture) {
      const fixedPicture = processCloudinaryUrl(user.picture);
      if (fixedPicture && fixedPicture !== user.picture) {
        updates = { ...updates, picture: fixedPicture };
        needsUpdate = true;
      }
    }

    // If user has no profile image but has a picture, set profileImage = picture
    if (!user.profileImage && user.picture) {
      updates = { ...updates, profileImage: user.picture };
      needsUpdate = true;
    }

    // If user has no picture but has a profileImage, set picture = profileImage
    if (!user.picture && user.profileImage) {
      updates = { ...updates, picture: user.profileImage };
      needsUpdate = true;
    }

    // If user has neither profileImage nor picture, set default avatar
    if (!user.profileImage && !user.picture) {
      // Add cache busting parameter to the default avatar URL
      const timestamp = Date.now();
      const cachedDefaultAvatarUrl = `${defaultAvatarUrl}?t=${timestamp}`;
      updates = { ...updates, profileImage: cachedDefaultAvatarUrl, picture: cachedDefaultAvatarUrl };
      needsUpdate = true;
    }

    // Add cache busting to any Cloudinary URLs in the updates
    if (updates.profileImage && typeof updates.profileImage === 'string' &&
        updates.profileImage.includes('cloudinary.com') && !updates.profileImage.includes('t=')) {
      const separator = updates.profileImage.includes('?') ? '&' : '?';
      updates.profileImage = `${updates.profileImage}${separator}t=${Date.now()}`;
    }

    if (updates.picture && typeof updates.picture === 'string' &&
        updates.picture.includes('cloudinary.com') && !updates.picture.includes('t=')) {
      const separator = updates.picture.includes('?') ? '&' : '?';
      updates.picture = `${updates.picture}${separator}t=${Date.now()}`;
    }

    // Update user if needed
    if (needsUpdate) {
      console.log(`Updating user ${user.name} (${user.email}):`, {
        before: {
          profileImage: user.profileImage,
          picture: user.picture
        },
        updates
      });

      await User.updateOne({ _id: user._id }, { $set: updates });

      // Verify the update was successful
      const updatedUser = await User.findById(user._id);
      console.log(`User ${user.name} after update:`, {
        profileImage: updatedUser?.profileImage,
        picture: updatedUser?.picture
      });

      // Update only the primary profile for this user
      // Each profile has its own avatar, but the user's profile image should match the primary profile
      const primaryProfile = await Profile.findOne({
        userId: user._id,
        isPrimary: true
      });

      if (primaryProfile) {
        // Get the image URL with cache busting
        let avatarUrl = updatedUser?.profileImage || updatedUser?.picture || defaultAvatarUrl;

        // Add cache busting if it's a Cloudinary URL and doesn't already have a timestamp
        if (avatarUrl.includes('cloudinary.com') && !avatarUrl.includes('t=')) {
          const separator = avatarUrl.includes('?') ? '&' : '?';
          avatarUrl = `${avatarUrl}${separator}t=${Date.now()}`;
        }

        // Update only the primary profile with the same image as the user's profile image
        const profileUpdates = { avatar: avatarUrl };
        await Profile.findByIdAndUpdate(primaryProfile._id, { $set: profileUpdates });
        console.log(`Updated primary profile for user ${user.name} with new avatar: ${avatarUrl}`);
      } else {
        console.log(`No primary profile found for user ${user.name}`);
      }

      return NextResponse.json({
        success: true,
        message: `Fixed profile image for user ${user.name}`,
        updates,
        profileUpdated: !!primaryProfile
      });
    } else {
      return NextResponse.json({
        success: true,
        message: `No updates needed for user ${user.name}`,
        noChangesNeeded: true
      });
    }
  } catch (error) {
    console.error('Error fixing user profile image:', error);
    return NextResponse.json(
      { error: 'Failed to fix profile image', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

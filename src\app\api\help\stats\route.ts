import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongoose';
import { HelpTicket, HelpCategory } from '@/models/HelpTicket';
import User from '@/models/User';
import { authMiddleware } from '@/lib/middleware';

/**
 * GET /api/help/stats
 * Get help system statistics (admin only)
 */
export async function GET(request: NextRequest) {
  try {
    await ensureMongooseConnection();

    // Get userId from query parameters
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized - userId required' }, { status: 401 });
    }

    // Get user to check role
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if user is admin
    if (user.role !== 'admin' && user.role !== 'superadmin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get additional query parameters
    const period = searchParams.get('period') || '30'; // days
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    // Get overall statistics
    const totalTickets = await HelpTicket.countDocuments();
    const openTickets = await HelpTicket.countDocuments({ status: 'open' });
    const inProgressTickets = await HelpTicket.countDocuments({ status: 'in_progress' });
    const resolvedTickets = await HelpTicket.countDocuments({ status: 'resolved' });
    const closedTickets = await HelpTicket.countDocuments({ status: 'closed' });
    const escalatedTickets = await HelpTicket.countDocuments({ escalated: true });

    // Get tickets created in the specified period
    const recentTickets = await HelpTicket.countDocuments({
      createdAt: { $gte: startDate }
    });

    // Get tickets resolved in the specified period
    const recentResolved = await HelpTicket.countDocuments({
      resolvedAt: { $gte: startDate }
    });

    // Get average resolution time (in hours)
    const resolvedTicketsWithTime = await HelpTicket.find({
      status: { $in: ['resolved', 'closed'] },
      resolvedAt: { $exists: true },
      createdAt: { $gte: startDate }
    }).select('createdAt resolvedAt');

    let averageResolutionTime = 0;
    if (resolvedTicketsWithTime.length > 0) {
      const totalResolutionTime = resolvedTicketsWithTime.reduce((sum, ticket) => {
        const resolutionTime = (ticket.resolvedAt!.getTime() - ticket.createdAt.getTime()) / (1000 * 60 * 60); // hours
        return sum + resolutionTime;
      }, 0);
      averageResolutionTime = totalResolutionTime / resolvedTicketsWithTime.length;
    }

    // Get tickets by category
    const ticketsByCategory = await HelpTicket.aggregate([
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 },
          open: { $sum: { $cond: [{ $eq: ['$status', 'open'] }, 1, 0] } },
          resolved: { $sum: { $cond: [{ $eq: ['$status', 'resolved'] }, 1, 0] } }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Get tickets by priority
    const ticketsByPriority = await HelpTicket.aggregate([
      {
        $group: {
          _id: '$priority',
          count: { $sum: 1 }
        }
      }
    ]);

    // Get tickets by status over time (last 30 days)
    const ticketsOverTime = await HelpTicket.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
            status: '$status'
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.date': 1 } }
    ]);

    // Get top assigned agents
    const topAgents = await HelpTicket.aggregate([
      {
        $match: {
          assignedTo: { $exists: true },
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: '$assignedTo',
          assignedToName: { $first: '$assignedToName' },
          totalTickets: { $sum: 1 },
          resolvedTickets: { $sum: { $cond: [{ $eq: ['$status', 'resolved'] }, 1, 0] } }
        }
      },
      { $sort: { totalTickets: -1 } },
      { $limit: 10 }
    ]);

    // Get satisfaction ratings
    const satisfactionStats = await HelpTicket.aggregate([
      {
        $match: {
          satisfactionRating: { $exists: true },
          resolvedAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: null,
          averageRating: { $avg: '$satisfactionRating' },
          totalRatings: { $sum: 1 },
          ratings: { $push: '$satisfactionRating' }
        }
      }
    ]);

    // Calculate satisfaction distribution
    const satisfactionDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
    if (satisfactionStats.length > 0) {
      satisfactionStats[0].ratings.forEach((rating: number) => {
        satisfactionDistribution[rating as keyof typeof satisfactionDistribution]++;
      });
    }

    return NextResponse.json({
      overview: {
        totalTickets,
        openTickets,
        inProgressTickets,
        resolvedTickets,
        closedTickets,
        escalatedTickets,
        recentTickets,
        recentResolved,
        averageResolutionTime: Math.round(averageResolutionTime * 100) / 100
      },
      ticketsByCategory,
      ticketsByPriority,
      ticketsOverTime,
      topAgents,
      satisfaction: {
        averageRating: satisfactionStats.length > 0 ? Math.round(satisfactionStats[0].averageRating * 100) / 100 : 0,
        totalRatings: satisfactionStats.length > 0 ? satisfactionStats[0].totalRatings : 0,
        distribution: satisfactionDistribution
      },
      period: parseInt(period)
    });

  } catch (error) {
    console.error('Error fetching help statistics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch help statistics' },
      { status: 500 }
    );
  }
}

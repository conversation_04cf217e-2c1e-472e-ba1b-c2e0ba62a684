import { NextRequest, NextResponse } from 'next/server';
import { verifyAdmin } from '@/lib/admin-auth';
import { ensureMongooseConnection } from '@/lib/mongodb';

interface TestResult {
  name: string;
  status: 'PASS' | 'FAIL' | 'ERROR' | 'UNKNOWN';
  details: string | Record<string, unknown>;
}

interface TestResults {
  timestamp: string;
  environment: string | undefined;
  tests: TestResult[];
}

/**
 * GET /api/debug/banner-test
 * Test endpoint to verify banner creation prerequisites
 */
export async function GET(request: NextRequest) {
  try {
    const testResults: TestResults = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      tests: []
    };

    // Test 1: Check admin authentication
    try {
      const adminSession = await verifyAdmin(request);
      testResults.tests.push({
        name: 'Admin Authentication',
        status: adminSession ? 'PASS' : 'FAIL',
        details: adminSession ? {
          userId: adminSession.userId,
          isAuthorized: adminSession.isAuthorized,
          userEmail: adminSession.user?.email
        } : 'No admin session found'
      });
    } catch (error) {
      testResults.tests.push({
        name: 'Admin Authentication',
        status: 'ERROR',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 2: Check database connection
    try {
      await ensureMongooseConnection();
      testResults.tests.push({
        name: 'Database Connection',
        status: 'PASS',
        details: 'MongoDB connection successful'
      });
    } catch (error) {
      testResults.tests.push({
        name: 'Database Connection',
        status: 'FAIL',
        details: error instanceof Error ? error.message : 'Connection failed'
      });
    }

    // Test 3: Check BannerAd model
    try {
      const { default: BannerAd } = await import('@/models/BannerAd');
      const bannerCount = await BannerAd.countDocuments();
      testResults.tests.push({
        name: 'BannerAd Model',
        status: 'PASS',
        details: `Model loaded successfully. Current banner count: ${bannerCount}`
      });
    } catch (error) {
      testResults.tests.push({
        name: 'BannerAd Model',
        status: 'FAIL',
        details: error instanceof Error ? error.message : 'Model load failed'
      });
    }

    // Test 4: Check analytics endpoint
    try {
      const analyticsTest = await fetch(new URL('/api/banner-ads/analytics', request.url), {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ bannerId: '507f1f77bcf86cd799439011', action: 'view' })
      });
      
      testResults.tests.push({
        name: 'Analytics Endpoint',
        status: analyticsTest.status === 404 ? 'PASS' : 'UNKNOWN',
        details: `Status: ${analyticsTest.status}. Expected 404 for test banner ID.`
      });
    } catch (error) {
      testResults.tests.push({
        name: 'Analytics Endpoint',
        status: 'ERROR',
        details: error instanceof Error ? error.message : 'Analytics test failed'
      });
    }

    // Overall status
    const hasFailures = testResults.tests.some(test => test.status === 'FAIL' || test.status === 'ERROR');
    
    return NextResponse.json({
      ...testResults,
      overallStatus: hasFailures ? 'ISSUES_DETECTED' : 'ALL_TESTS_PASSED',
      recommendation: hasFailures 
        ? 'Please address the failed tests before creating banners'
        : 'System is ready for banner creation'
    });

  } catch (error) {
    return NextResponse.json({
      timestamp: new Date().toISOString(),
      overallStatus: 'CRITICAL_ERROR',
      error: error instanceof Error ? error.message : 'Unknown error',
      recommendation: 'System has critical issues that prevent testing'
    }, { status: 500 });
  }
}

/**
 * POST /api/debug/banner-test
 * Test banner creation with sample data
 */
export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminSession = await verifyAdmin(request);
    if (!adminSession || !adminSession.isAuthorized) {
      return NextResponse.json({
        error: 'Admin authentication required for banner creation test',
        recommendation: 'Please ensure you are logged in as an admin'
      }, { status: 401 });
    }

    await ensureMongooseConnection();

    const testBannerData = {
      title: 'Test Banner - ' + new Date().toISOString(),
      description: 'This is a test banner created for debugging purposes',
      bannerType: 'solid-color',
      isActive: false, // Set to false so it doesn't show up publicly
      priority: 1,
      styling: {
        backgroundColor: '#2563eb',
        textColor: '#ffffff',
        titleSize: '1.5rem',
        descriptionSize: '1rem',
        borderRadius: '0.5rem',
        padding: '1rem',
        animation: 'fadeIn',
        animationDuration: '0.5s',
        positions: ['top'],
        layout: 'horizontal',
        textAlign: 'left'
      }
    };

    // Test banner creation
    const createResponse = await fetch(new URL('/api/admin/banner-ads', request.url), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': request.headers.get('Cookie') || ''
      },
      body: JSON.stringify(testBannerData)
    });

    const createResult = await createResponse.json();

    if (createResponse.ok) {
      return NextResponse.json({
        success: true,
        message: 'Test banner created successfully',
        bannerId: createResult._id,
        bannerData: createResult,
        recommendation: 'Banner creation is working. You can now create real banners.'
      });
    } else {
      return NextResponse.json({
        success: false,
        error: createResult.error || 'Unknown error',
        details: createResult.details || 'No additional details',
        recommendation: 'Check the error details and fix the issue before creating banners'
      }, { status: createResponse.status });
    }

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      recommendation: 'System error during banner creation test'
    }, { status: 500 });
  }
} 
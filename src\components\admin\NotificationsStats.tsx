import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { <PERSON>, Eye, History, BarC<PERSON>, Clock, AlertCircle } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { NotificationType } from '@/models/Notification';
import { AnimatedCounter } from '@/components/admin/AnimatedCounter';
import { formatDistanceToNow } from 'date-fns';

interface NotificationsStatsProps {
  notifications: Array<{
    _id: string;
    type: NotificationType;
    title: string;
    message: string;
    read: boolean;
    createdAt: string;
    expiresAt?: string;
    userId?: string;
    recipientCount?: number;
  }>;
  isLoading: boolean;
}

export function NotificationsStats({ notifications, isLoading }: NotificationsStatsProps) {
  const [stats, setStats] = useState({
    total: 0,
    totalRecipients: 0,
    unread: 0,
    readPercentage: 0,
    byType: {
      system: 0,
      update: 0,
      new_content: 0,
      recommendation: 0,
    },
    typePercentages: {
      system: 0,
      update: 0,
      new_content: 0,
      recommendation: 0,
    },
    mostRecent: null as string | null,
    nextExpiration: null as string | null,
    withExpiration: 0,
    withoutExpiration: 0,
  });

  // Calculate stats when notifications change
  useEffect(() => {
    if (notifications.length === 0) {
      setStats({
        total: 0,
        totalRecipients: 0,
        unread: 0,
        readPercentage: 0,
        byType: {
          system: 0,
          update: 0,
          new_content: 0,
          recommendation: 0,
        },
        typePercentages: {
          system: 0,
          update: 0,
          new_content: 0,
          recommendation: 0,
        },
        mostRecent: null,
        nextExpiration: null,
        withExpiration: 0,
        withoutExpiration: 0,
      });
      return;
    }

    // Calculate total notifications (unique broadcasts)
    const total = notifications.length;

    // Calculate total recipients (sum of all recipientCount values)
    const totalRecipients = notifications.reduce((sum, n) => sum + (n.recipientCount || 1), 0);

    // Calculate unread notifications
    const unread = notifications.filter(n => !n.read).length;
    const readPercentage = Math.round(((total - unread) / total) * 100);

    // Count by type
    const byType = {
      system: 0,
      update: 0,
      new_content: 0,
      recommendation: 0,
    };

    notifications.forEach(n => {
      byType[n.type as keyof typeof byType] += 1;
    });

    // Calculate percentages
    const typePercentages = {
      system: Math.round((byType.system / total) * 100),
      update: Math.round((byType.update / total) * 100),
      new_content: Math.round((byType.new_content / total) * 100),
      recommendation: Math.round((byType.recommendation / total) * 100),
    };

    // Get most recent
    const sorted = [...notifications].sort((a, b) =>
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
    const mostRecent = sorted.length > 0 ? sorted[0].createdAt : null;

    // Calculate expiration stats
    const withExpiration = notifications.filter(n => n.expiresAt).length;
    const withoutExpiration = total - withExpiration;

    // Find the next notification to expire
    const now = new Date();
    const futureExpirations = notifications
      .filter(n => n.expiresAt && new Date(n.expiresAt) > now)
      .sort((a, b) => new Date(a.expiresAt!).getTime() - new Date(b.expiresAt!).getTime());

    const nextExpiration = futureExpirations.length > 0 ? futureExpirations[0].expiresAt || null : null;

    setStats({
      total,
      totalRecipients,
      unread,
      readPercentage,
      byType,
      typePercentages,
      mostRecent,
      nextExpiration,
      withExpiration,
      withoutExpiration,
    });
  }, [notifications]);

  // Format the most recent date
  const formattedMostRecent = stats.mostRecent
    ? new Date(stats.mostRecent).toLocaleDateString(undefined, {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      })
    : 'N/A';

  // Format the next expiration date
  const formattedNextExpiration = stats.nextExpiration
    ? new Date(stats.nextExpiration).toLocaleDateString(undefined, {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      })
    : 'N/A';

  // Get relative time for next expiration
  const expirationTimeFromNow = stats.nextExpiration
    ? formatDistanceToNow(new Date(stats.nextExpiration), { addSuffix: true })
    : 'N/A';

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="bg-vista-dark-lighter/50 border-vista-light/10">
            <CardHeader className="pb-2">
              <div className="h-5 w-24 bg-vista-dark-lighter rounded-md animate-pulse"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 w-16 bg-vista-dark-lighter rounded-md animate-pulse mb-2"></div>
              <div className="h-4 w-full bg-vista-dark-lighter rounded-md animate-pulse"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5 mb-6">
      <Card className="bg-vista-dark-lighter border-vista-light/10 shadow-md overflow-hidden">
        <div className="h-1 w-full bg-vista-blue/50"></div>
        <CardHeader className="pb-2 pt-4">
          <CardTitle className="text-vista-light text-lg flex items-center">
            <div className="bg-vista-dark w-10 h-10 rounded-full flex items-center justify-center mr-3 shadow-md">
              <Bell className="h-5 w-5 text-vista-light" />
            </div>
            Total Notifications
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-4xl font-bold text-vista-light flex items-baseline">
            <AnimatedCounter value={stats.total} />
          </div>
          <div className="text-sm text-vista-light/70 mt-2 space-y-1">
            <p className="flex items-center">
              <span className="inline-block w-2 h-2 rounded-full bg-vista-light/40 mr-2"></span>
              Unique notifications
            </p>
            <p className="flex items-center">
              <span className="inline-block w-2 h-2 rounded-full bg-vista-blue/40 mr-2"></span>
              <span className="text-vista-light/80">
                <span className="font-medium">{stats.totalRecipients}</span> total recipients
              </span>
            </p>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-vista-dark-lighter border-vista-light/10 shadow-md overflow-hidden">
        <div className="h-1 w-full bg-vista-light/30"></div>
        <CardHeader className="pb-2 pt-4">
          <CardTitle className="text-vista-light text-lg flex items-center">
            <div className="bg-vista-dark w-10 h-10 rounded-full flex items-center justify-center mr-3 shadow-md">
              <Eye className="h-5 w-5 text-vista-light" />
            </div>
            Read Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-4xl font-bold text-vista-light flex items-baseline">
            <AnimatedCounter value={stats.total - stats.unread} />
            <span className="text-base font-normal text-vista-light/60 ml-2"> / {stats.total}</span>
          </div>
          <div className="mt-4 space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-vista-light/70 flex items-center">
                <span className="inline-block w-2 h-2 rounded-full bg-vista-light/40 mr-2"></span>
                Read
              </span>
              <span className="text-vista-light/90 font-medium">{stats.readPercentage}%</span>
            </div>
            <Progress
              value={stats.readPercentage}
              className="h-2 bg-vista-dark rounded-full"
              indicatorClassName="bg-vista-light/50"
              style={{
                transition: 'width 1.5s cubic-bezier(0.65, 0, 0.35, 1)'
              }}
            />
          </div>
        </CardContent>
      </Card>

      <Card className="bg-vista-dark-lighter border-vista-light/10 shadow-md overflow-hidden">
        <div className="h-1 w-full bg-vista-light/30"></div>
        <CardHeader className="pb-2 pt-4">
          <CardTitle className="text-vista-light text-lg flex items-center">
            <div className="bg-vista-dark w-10 h-10 rounded-full flex items-center justify-center mr-3 shadow-md">
              <BarChart className="h-5 w-5 text-vista-light" />
            </div>
            By Type
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Object.entries(stats.byType).map(([type, count]) => (
              count > 0 && (
                <div key={type} className="space-y-2">
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-vista-light/70 capitalize flex items-center">
                      <span className="inline-block w-2 h-2 rounded-full bg-vista-light/40 mr-2"></span>
                      {type.replace('_', ' ')}
                    </span>
                    <span className="text-vista-light/90 font-medium">{count}</span>
                  </div>
                  <Progress
                    value={stats.typePercentages[type as keyof typeof stats.typePercentages]}
                    className="h-2 bg-vista-dark rounded-full"
                    indicatorClassName="bg-vista-light/50"
                    style={{
                      transition: 'width 1.5s cubic-bezier(0.65, 0, 0.35, 1)'
                    }}
                  />
                </div>
              )
            ))}
          </div>
        </CardContent>
      </Card>

      <Card className="bg-vista-dark-lighter border-vista-light/10 shadow-md overflow-hidden">
        <div className="h-1 w-full bg-vista-light/30"></div>
        <CardHeader className="pb-2 pt-4">
          <CardTitle className="text-vista-light text-lg flex items-center">
            <div className="bg-vista-dark w-10 h-10 rounded-full flex items-center justify-center mr-3 shadow-md">
              <Clock className="h-5 w-5 text-vista-light" />
            </div>
            Timing
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="text-sm font-medium text-vista-light/70 mb-1 flex items-center">
                <History className="h-3.5 w-3.5 mr-1.5" />
                Most Recent
              </div>
              <div className="text-lg font-semibold text-vista-light">{formattedMostRecent.split(',')[0]}</div>
              <div className="text-sm font-medium text-vista-light/60">{formattedMostRecent.split(',')[1]}</div>
            </div>

            <div className="border-t border-vista-light/10 pt-3">
              <div className="text-sm font-medium text-vista-light/70 mb-1 flex items-center">
                <AlertCircle className="h-3.5 w-3.5 mr-1.5" />
                Next Expiration
              </div>
              {stats.nextExpiration ? (
                <>
                  <div className="text-lg font-semibold text-amber-300">{formattedNextExpiration.split(',')[0]}</div>
                  <div className="text-sm font-medium text-amber-300/60">{formattedNextExpiration.split(',')[1]}</div>
                  <div className="text-xs text-amber-300/80 mt-1">
                    Expires {expirationTimeFromNow}
                  </div>
                </>
              ) : (
                <div className="text-sm text-vista-light/60">No scheduled expirations</div>
              )}
            </div>

            <div className="text-xs text-vista-light/70 flex items-center justify-between border-t border-vista-light/10 pt-2 mt-2">
              <span>{stats.withExpiration} with expiration</span>
              <span>{stats.withoutExpiration} without expiration</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}


// Remove 'use client';

// Keep necessary imports for data fetching and the new client component
import { Suspense } from 'react';
import { getPopularMovies, getTopRatedMovies, getTrendingDaily, getPopularTVShows, getTopRatedTVShows, MappedContent } from '@/lib/tmdb-api';
import { formatTMDbContentForCards, ContentCardType } from '@/lib/content-utils';
import HomePageClientContent from '@/components/HomePageClientContent'; // Import the new client component
import { Loader2 } from 'lucide-react'; // Keep Loader for Suspense fallback

// Make the component async
export default async function Home() {

  // Fetch initial data directly on the server
  // We wrap this in a try/catch block for basic error handling
  let initialContentData: {
    popularMovies: MappedContent[];
    topRatedMovies: MappedContent[];
    popularShows: MappedContent[];
    topRatedShows: MappedContent[];
    trending: MappedContent[];
    continueWatching: ContentCardType[];
  } = {
    popularMovies: [],
    topRatedMovies: [],
    popularShows: [],
    topRatedShows: [],
    trending: [],
    continueWatching: []
  };

  try {
    const results = await Promise.allSettled([
      getPopularMovies(1),
      getTopRatedMovies(1),
      getPopularTVShows(1),
      getTopRatedTVShows(1),
      getTrendingDaily('all', 1),
      // Fetch actual continue watching data here if possible, otherwise use placeholder
      Promise.resolve([]) // Placeholder for continue watching
    ]);

    const popularMoviesData = results[0].status === 'fulfilled' ? results[0].value : [];
    const topRatedMoviesData = results[1].status === 'fulfilled' ? results[1].value : [];
    const popularShowsData = results[2].status === 'fulfilled' ? results[2].value : [];
    const topRatedShowsData = results[3].status === 'fulfilled' ? results[3].value : [];
    const trendingData = results[4].status === 'fulfilled' ? results[4].value : [];
    const continueWatchingData = results[5].status === 'fulfilled' ? results[5].value : []; // Assume fetched as ContentCardType[]

    // Pre-format continue watching data on the server
    const formattedContinueWatching = continueWatchingData.length > 0
      ? continueWatchingData
      : formatTMDbContentForCards(trendingData.slice(0, 4))
        .map(item => ({ ...item, watchTimeMinutes: Math.floor(Math.random() * 60) + 10 }));

    initialContentData = {
      popularMovies: popularMoviesData,
      topRatedMovies: topRatedMoviesData,
      popularShows: popularShowsData,
      topRatedShows: topRatedShowsData,
      trending: trendingData,
      continueWatching: formattedContinueWatching
    };

  } catch (error) {
    console.error("Error fetching initial content data on server:", error);
    // initialContentData remains empty/default on error
  }

  // Render the client component, passing the fetched data as props.
  // Navbar and Footer are now rendered inside HomePageClientContent.
  // Wrap in Suspense for better UX during client component loading (optional but good practice)
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-vista-dark flex justify-center items-center">
        <Loader2 className="w-10 h-10 text-vista-blue animate-spin" />
      </div>
    }>
      <HomePageClientContent initialContent={initialContentData} />
    </Suspense>
  );
}

// Remove all the old client-side hooks (useState, useEffect, useMemo, etc.)
// Remove the old return statement and related variables (isLoading, isMobile, etc.)
// Remove unused imports (useLanguage, Button, motion, Separator, etc. - they are now in HomePageClientContent)

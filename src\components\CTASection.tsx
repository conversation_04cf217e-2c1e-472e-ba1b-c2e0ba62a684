"use client";

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';

export default function CTASection() {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !email.includes('@')) return;

    setIsSubmitting(true);

    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      setIsSubmitted(true);
      setEmail('');

      // Reset success message after a delay
      setTimeout(() => {
        setIsSubmitted(false);
      }, 5000);
    }, 1500);
  };

  return (
    <section className="py-16 md:py-24 relative overflow-hidden">
      {/* Background with gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-vista-dark to-black z-0" />

      {/* Animated background rings */}
      <div className="absolute inset-0 overflow-hidden z-0">
        <div className="absolute -top-[20%] -left-[10%] w-[50%] h-[50%] rounded-full border border-vista-accent/10 animate-pulse" style={{ animationDuration: '8s' }} />
        <div className="absolute top-[60%] -right-[5%] w-[40%] h-[40%] rounded-full border border-vista-accent-alt/10 animate-pulse" style={{ animationDuration: '12s' }} />
        <div className="absolute top-[30%] left-[60%] w-[30%] h-[30%] rounded-full border border-vista-accent/5 animate-pulse" style={{ animationDuration: '10s' }} />
      </div>

      <div className="container px-4 md:px-6 mx-auto relative z-10">
        <div className="max-w-3xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7 }}
          >
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">
              <span className="text-gradient">Get Total Access</span>
              <br />
              <span className="text-vista-light">to StreamVista</span>
            </h2>
            <p className="text-vista-light/80 text-lg md:text-xl mb-8 md:mb-10 max-w-2xl mx-auto">
              Stream star-studded StreamVista Originals, blockbuster movies, binge-worthy TV shows, and more.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7, delay: 0.2 }}
          >
            <div className="bg-black/50 backdrop-blur-sm p-6 md:p-8 rounded-xl border border-vista-light/10 mb-6">
              {isSubmitted ? (
                <div className="flex items-center justify-center text-vista-light space-x-2">
                  <svg
                    className="w-6 h-6 text-green-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  <span>Thanks! We'll be in touch soon.</span>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="flex flex-col md:flex-row gap-4">
                  <div className="flex-grow">
                    <label htmlFor="email" className="sr-only">Email address</label>
                    <input
                      type="email"
                      id="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="Enter your email address"
                      className="w-full px-4 py-3 rounded-lg bg-black/80 border border-vista-light/20 text-vista-light placeholder:text-vista-light/50 focus:outline-none focus:ring-2 focus:ring-vista-accent"
                      required
                    />
                  </div>
                  <Button
                    type="submit"
                    className="bg-vista-accent hover:bg-vista-accent-alt text-white font-medium py-3 px-8"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <div className="flex items-center gap-2">
                        <span className="h-4 w-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                        <span>Processing...</span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        <span>Start Free Trial</span>
                        <ArrowRight className="h-4 w-4" />
                      </div>
                    )}
                  </Button>
                </form>
              )}
            </div>

            <p className="text-sm text-vista-light/60">
              7-day free trial, then $9.99/month. Cancel anytime.
            </p>
          </motion.div>
        </div>
      </div>
    </section>
  );
}

'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

import { Trash2, Loader2, AlertCircle, UserX, Shield } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/AuthContext';

interface DeleteUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  userId: string;
  userName: string;
  userEmail?: string;
  userRole?: string;
  userImage?: string;
  onSuccess?: () => void;
}

// Helper function to get user initials
function getUserInitials(name: string): string {
  return name
    .split(' ')
    .map(part => part[0])
    .join('')
    .toUpperCase()
    .substring(0, 2);
}

// Get role color for badge
function getRoleColor(role?: string) {
  switch (role) {
    case 'superadmin': return 'bg-purple-600 text-white';
    case 'admin': return 'bg-red-600 text-white';
    case 'moderator': return 'bg-orange-600 text-white';
    default: return 'bg-blue-600 text-white';
  }
}

export default function DeleteUserDialog({ 
  open,
  onOpenChange,
  userId, 
  userName, 
  userEmail, 
  userRole = 'user',
  userImage,
  onSuccess 
}: DeleteUserDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const router = useRouter();
  const { user: currentUser } = useAuth();

  // Prevent deletion of superadmin accounts
  const isSuperAdmin = userRole === 'superadmin';
  const canDelete = !isSuperAdmin;

  const handleDelete = async () => {
    if (!userId || !canDelete || isDeleting) return;

    setIsDeleting(true);

    try {
      // Add current user ID as query parameter for authentication fallback
      const currentUserId = currentUser?.id;
      const url = currentUserId 
        ? `/api/admin/users/${userId}?userId=${currentUserId}`
        : `/api/admin/users/${userId}`;
        
      const response = await fetch(url, {
        method: 'DELETE',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({
          error: 'Unknown error',
          type: 'network',
          shouldRetry: response.status >= 500
        }));

        // Enhanced error message based on status code
        let errorMessage = errorData.error || `Failed to delete user (${response.status})`;

        if (response.status === 408) {
          errorMessage = 'Operation timed out. Please try again.';
        } else if (response.status === 503) {
          errorMessage = 'Service temporarily unavailable. Please try again in a moment.';
        } else if (response.status === 401) {
          errorMessage = 'Authentication failed. Please refresh the page and try again.';
        } else if (response.status === 403) {
          errorMessage = errorData.error || 'You do not have permission to delete this user.';
        }

        throw new Error(errorMessage);
      }

      toast({
        title: 'User Deleted',
        description: `${userName} has been successfully deleted.`,
        variant: 'default',
      });

      // Close the dialog
      onOpenChange(false);

      // Call the success callback or navigate back
      if (onSuccess) {
        onSuccess();
      } else {
        router.push('/admin/users');
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete user',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!isDeleting) {
      onOpenChange(newOpen);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={handleOpenChange}>
      <AlertDialogContent className="bg-vista-dark border-vista-dark-lighter max-w-md w-full p-0 overflow-hidden">
        <div className="bg-red-600/10 p-6 flex flex-col items-center justify-center border-b border-vista-dark-lighter">
          <div className="bg-red-600/20 p-3 rounded-full mb-4">
            <UserX className="h-8 w-8 text-red-500" />
          </div>
          <AlertDialogHeader className="text-center space-y-1">
            <AlertDialogTitle className="text-xl text-vista-light">Delete User Account</AlertDialogTitle>
            
            {/* User Info Card */}
            <div className="bg-vista-dark-lighter p-4 rounded-lg mt-4 space-y-3">
              <div className="flex items-center justify-center gap-3">
                <Avatar className="h-12 w-12 border-2 border-vista-dark-lighter">
                  <AvatarImage src={userImage} alt={userName} />
                  <AvatarFallback className="bg-vista-blue text-white">
                    {getUserInitials(userName)}
                  </AvatarFallback>
                </Avatar>
                <div className="text-left">
                  <div className="font-semibold text-vista-light">{userName}</div>
                  {userEmail && (
                    <div className="text-sm text-vista-light/70">{userEmail}</div>
                  )}
                  <div className="mt-1">
                    <Badge className={`text-xs ${getRoleColor(userRole)}`}>
                      {userRole === 'superadmin' && <Shield className="h-3 w-3 mr-1" />}
                      {userRole.toUpperCase()}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          </AlertDialogHeader>
        </div>

        <div className="p-6">
          {isSuperAdmin ? (
            // Super admin protection message
            <div className="text-vista-light/80 mb-6">
              <div className="flex items-start gap-2 bg-red-500/10 p-4 rounded-md border border-red-500/20">
                <Shield className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
                <div>
                  <div className="font-semibold text-red-500 mb-1">Superadmin Protection</div>
                  <div className="text-sm text-vista-light/90">
                    Superadmin accounts cannot be deleted to protect system integrity. 
                    Please contact a system administrator if this account needs to be removed.
                  </div>
                </div>
              </div>
            </div>
          ) : (
            // Normal deletion flow
            <div className="text-vista-light/80 space-y-4">
              <AlertDialogDescription className="text-center">
                You are about to permanently delete this user account and all associated data. This action <span className="text-red-500 font-semibold">cannot be undone</span>.
              </AlertDialogDescription>

              <div className="flex items-start gap-2 bg-yellow-500/10 p-3 rounded-md border border-yellow-500/20">
                <AlertCircle className="h-5 w-5 text-yellow-500 flex-shrink-0 mt-0.5" />
                <div className="text-sm text-vista-light/90">
                  This will remove all user data including profiles, watch history, preferences, and activity logs.
                </div>
              </div>
            </div>
          )}

          <AlertDialogFooter className="flex flex-col sm:flex-row gap-3 sm:gap-2 mt-6">
            <AlertDialogCancel
              className="bg-vista-dark-lighter text-vista-light hover:bg-vista-dark-lighter/80 hover:text-vista-light w-full sm:w-auto"
              disabled={isDeleting}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-600 text-white hover:bg-red-700 focus:ring-red-600 w-full sm:w-auto disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={(e) => {
                e.preventDefault();
                handleDelete();
              }}
              disabled={isDeleting || !canDelete}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete User Account'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </div>
      </AlertDialogContent>
    </AlertDialog>
  );
}

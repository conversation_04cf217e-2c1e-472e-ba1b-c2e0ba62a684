'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { toast } from '@/components/ui/use-toast';
import { Loader2, CreditCard, Calendar, RefreshCw, CheckCircle2 } from 'lucide-react';
import { format, addMonths, addYears } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

interface Subscription {
  id?: string;
  plan: string;
  status: string;
  startDate?: Date;
  endDate?: Date;
  renewalDate?: Date;
  price?: number;
  interval?: string;
}

interface Transaction {
  id: string;
  amount: number;
  currency: string;
  status: string;
  type: string;
  description: string;
  date: Date;
}

interface UserSubscriptionManagerProps {
  userId: string;
  onSuccess?: () => void;
}

export default function UserSubscriptionManager({ userId, onSuccess }: UserSubscriptionManagerProps) {
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [subscriptionHistory, setSubscriptionHistory] = useState<Subscription[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Form state
  const [selectedPlan, setSelectedPlan] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [selectedInterval, setSelectedInterval] = useState('monthly');
  const [renewalDate, setRenewalDate] = useState('');
  const [isEditMode, setIsEditMode] = useState(false);

  // Fetch subscription data on mount
  useEffect(() => {
    fetchSubscriptionData();
  }, [userId]);

  // Fetch subscription data from API
  const fetchSubscriptionData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/admin/users/${userId}/subscription`, {
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to fetch subscription data (${response.status})`);
      }

      const data = await response.json();
      
      // Set current subscription
      setSubscription(data.currentSubscription || null);
      
      // Set form values based on current subscription
      if (data.currentSubscription) {
        setSelectedPlan(data.currentSubscription.plan);
        setSelectedStatus(data.currentSubscription.status);
        setSelectedInterval(data.currentSubscription.interval || 'monthly');
        setRenewalDate(data.currentSubscription.renewalDate ? 
          format(new Date(data.currentSubscription.renewalDate), 'yyyy-MM-dd') : 
          '');
      }
      
      // Set subscription history
      setSubscriptionHistory(data.history || []);
      
      // Set transactions
      setTransactions(data.transactions || []);
    } catch (error) {
      console.error('Error fetching subscription data:', error);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedPlan || !selectedStatus) {
      toast({
        title: 'Missing Information',
        description: 'Please select a plan and status.',
        variant: 'destructive'
      });
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Calculate renewal date if not provided
      let calculatedRenewalDate = renewalDate ? new Date(renewalDate) : null;
      
      if (!calculatedRenewalDate) {
        calculatedRenewalDate = selectedInterval === 'monthly' ? 
          addMonths(new Date(), 1) : 
          addYears(new Date(), 1);
      }
      
      const response = await fetch(`/api/admin/users/${userId}/subscription`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          plan: selectedPlan,
          status: selectedStatus,
          interval: selectedInterval,
          renewalDate: calculatedRenewalDate
        }),
        credentials: 'include'
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to update subscription (${response.status})`);
      }
      
      const data = await response.json();
      
      toast({
        title: 'Subscription Updated',
        description: `Successfully updated subscription to ${selectedPlan} plan.`,
        variant: 'success'
      });
      
      // Update subscription data
      setSubscription(data.subscription);
      
      // Exit edit mode
      setIsEditMode(false);
      
      // Refresh data
      fetchSubscriptionData();
      
      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error updating subscription:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get badge variant based on status
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'pending':
        return 'warning';
      case 'canceled':
        return 'destructive';
      case 'expired':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  // Format date
  const formatDate = (date: Date | string | undefined) => {
    if (!date) return 'N/A';
    return format(new Date(date), 'MMM d, yyyy');
  };

  // Format currency
  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency
    }).format(amount);
  };

  // Loading state
  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-8 flex justify-center items-center">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-vista-blue" />
            <p className="text-vista-light">Loading subscription data...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-vista-light">Subscription Management</CardTitle>
          <CardDescription>
            Manage user subscription plan and settings
          </CardDescription>
        </CardHeader>
        <CardContent className="p-8 text-center">
          <p className="text-red-500 mb-4">{error}</p>
          <Button onClick={fetchSubscriptionData}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
          <div>
            <CardTitle className="text-vista-light flex items-center gap-2">
              <CreditCard className="h-5 w-5 text-vista-blue" />
              Subscription Management
            </CardTitle>
            <CardDescription>
              Manage user subscription plan and settings
            </CardDescription>
          </div>
          {!isEditMode && (
            <Button variant="outline" size="sm" onClick={() => setIsEditMode(true)}>
              Edit Subscription
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current Subscription */}
        {!isEditMode ? (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-vista-light/70">Current Plan</h3>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="capitalize text-base py-1 px-3">
                    {subscription?.plan || 'Free Plan'}
                  </Badge>
                  {subscription?.interval && (
                    <Badge variant="secondary" className="capitalize">
                      {subscription.interval}
                    </Badge>
                  )}
                </div>
              </div>
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-vista-light/70">Status</h3>
                <Badge 
                  variant={getStatusBadgeVariant(subscription?.status || 'active')}
                  className="capitalize"
                >
                  {subscription?.status || 'Active'}
                </Badge>
              </div>
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-vista-light/70">Start Date</h3>
                <p className="text-vista-light">
                  {subscription?.startDate ? formatDate(subscription.startDate) : 'N/A'}
                </p>
              </div>
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-vista-light/70">Next Renewal</h3>
                <p className="text-vista-light flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-vista-light/70" />
                  {subscription?.renewalDate ? formatDate(subscription.renewalDate) : 'N/A'}
                </p>
              </div>
              {subscription?.price !== undefined && (
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-vista-light/70">Price</h3>
                  <p className="text-vista-light">
                    {formatCurrency(subscription.price)}
                    {subscription.interval && ` / ${subscription.interval === 'monthly' ? 'month' : 'year'}`}
                  </p>
                </div>
              )}
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="plan">Subscription Plan</Label>
                <Select 
                  value={selectedPlan} 
                  onValueChange={setSelectedPlan}
                >
                  <SelectTrigger id="plan">
                    <SelectValue placeholder="Select a plan" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="free">Free Plan</SelectItem>
                    <SelectItem value="basic">Basic Plan</SelectItem>
                    <SelectItem value="premium">Premium Plan</SelectItem>
                    <SelectItem value="family">Family Plan</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select 
                  value={selectedStatus} 
                  onValueChange={setSelectedStatus}
                >
                  <SelectTrigger id="status">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="canceled">Canceled</SelectItem>
                    <SelectItem value="expired">Expired</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>Billing Interval</Label>
                <RadioGroup 
                  value={selectedInterval} 
                  onValueChange={setSelectedInterval}
                  className="flex gap-4"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="monthly" id="monthly" />
                    <Label htmlFor="monthly">Monthly</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="yearly" id="yearly" />
                    <Label htmlFor="yearly">Yearly</Label>
                  </div>
                </RadioGroup>
              </div>
              <div className="space-y-2">
                <Label htmlFor="renewalDate">Next Renewal Date</Label>
                <Input
                  id="renewalDate"
                  type="date"
                  value={renewalDate}
                  onChange={(e) => setRenewalDate(e.target.value)}
                  placeholder="Select date"
                />
                <p className="text-xs text-vista-light/70">
                  Leave blank to use default ({selectedInterval === 'monthly' ? '1 month' : '1 year'} from today)
                </p>
              </div>
            </div>
            <div className="flex gap-2 justify-end">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setIsEditMode(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button 
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <CheckCircle2 className="mr-2 h-4 w-4" />
                )}
                Save Changes
              </Button>
            </div>
          </form>
        )}

        {/* Subscription History */}
        {subscriptionHistory.length > 0 && (
          <>
            <Separator className="my-4" />
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-vista-light">Subscription History</h3>
              <div className="border rounded-md border-vista-light/10 overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Plan</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Start Date</TableHead>
                      <TableHead>End Date</TableHead>
                      <TableHead>Price</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {subscriptionHistory.map((sub) => (
                      <TableRow key={sub.id}>
                        <TableCell className="font-medium capitalize">
                          {sub.plan} {sub.interval && `(${sub.interval})`}
                        </TableCell>
                        <TableCell>
                          <Badge variant={getStatusBadgeVariant(sub.status)} className="capitalize">
                            {sub.status}
                          </Badge>
                        </TableCell>
                        <TableCell>{formatDate(sub.startDate)}</TableCell>
                        <TableCell>{formatDate(sub.endDate)}</TableCell>
                        <TableCell>
                          {sub.price !== undefined ? formatCurrency(sub.price) : 'N/A'}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </>
        )}

        {/* Recent Transactions */}
        {transactions.length > 0 && (
          <>
            <Separator className="my-4" />
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-vista-light">Recent Transactions</h3>
              <div className="border rounded-md border-vista-light/10 overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {transactions.map((tx) => (
                      <TableRow key={tx.id}>
                        <TableCell>{formatDate(tx.date)}</TableCell>
                        <TableCell>{tx.description}</TableCell>
                        <TableCell>{formatCurrency(tx.amount, tx.currency)}</TableCell>
                        <TableCell>
                          <Badge 
                            variant={tx.status === 'completed' ? 'success' : 
                                    tx.status === 'pending' ? 'warning' : 
                                    tx.status === 'refunded' ? 'secondary' : 'destructive'} 
                            className="capitalize"
                          >
                            {tx.status}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </>
        )}
      </CardContent>
      <CardFooter className="flex justify-between border-t border-vista-light/10 pt-6">
        <div className="text-sm text-vista-light/70">
          <p>Last updated: {new Date().toLocaleDateString()}</p>
        </div>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={fetchSubscriptionData}
          disabled={isLoading}
        >
          <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </CardFooter>
    </Card>
  );
}

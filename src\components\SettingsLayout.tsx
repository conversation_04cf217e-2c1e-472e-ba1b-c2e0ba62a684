'use client';

import { ReactNode } from 'react';
import { cn } from '@/lib/utils';
import {
  Settings, Bell, Video, User, Monitor, Shield,
  CreditCard, Languages, LogOut, ArrowLeft
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';

export type SettingsTab = 'account' | 'preferences' | 'notifications' | 'playback' | 'privacy' | 'payment' | 'admin' | 'analytics';

interface SettingsLayoutProps {
  children: ReactNode;
  activeTab: SettingsTab;
  setActiveTab: (tab: SettingsTab) => void;
  header?: ReactNode;
  footer?: ReactNode;
}

export function SettingsLayout({
  children,
  activeTab,
  setActiveTab,
  header,
  footer
}: SettingsLayoutProps) {
  const router = useRouter();

  // Configuration for sidebar tabs
  const tabs = [
    {
      id: 'account' as SettingsTab,
      label: 'Account',
      icon: User
    },
    {
      id: 'preferences' as SettingsTab,
      label: 'Preferences',
      icon: Monitor
    },
    {
      id: 'playback' as SettingsTab,
      label: 'Playback & Quality',
      icon: Video
    },
    {
      id: 'notifications' as SettingsTab,
      label: 'Notifications',
      icon: Bell
    },
    {
      id: 'privacy' as SettingsTab,
      label: 'Privacy & Security',
      icon: Shield
    },
    {
      id: 'payment' as SettingsTab,
      label: 'Billing & Subscription',
      icon: CreditCard
    }
  ];

  return (
    <div className="flex flex-col md:flex-row max-w-screen-2xl mx-auto w-full gap-4 md:gap-8 px-4 lg:px-8">
      {/* Mobile Header */}
      <div className="md:hidden flex items-center justify-between py-2 pt-12">
        <Button
          variant="outline"
          size="sm"
          className="flex items-center text-vista-light gap-2 border-white/5 bg-black/20 hover:bg-black/30"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4 text-blue-400" />
          Back
        </Button>
        <div className="flex items-center gap-2">
          <div className="flex items-center justify-center w-7 h-7 rounded-lg bg-gradient-to-br from-blue-600 to-blue-500 shadow-md">
            <Settings className="h-3.5 w-3.5 text-white" />
          </div>
          <h1 className="text-xl font-semibold">Settings</h1>
        </div>
        <div className="w-12"></div>
      </div>

      {/* Sidebar */}
      <aside className="md:w-72 lg:w-80 shrink-0">
        <div className="hidden md:block sticky top-24 pt-6">
          <div className="flex items-center mb-8">
            <div className="flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-br from-blue-600 to-blue-500 shadow-lg shadow-blue-500/20 mr-4">
              <Settings className="h-6 w-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-semibold">Settings</h2>
              <p className="text-vista-light/60 text-sm">Manage your account and preferences</p>
            </div>
          </div>

          <ScrollArea className="h-[calc(100vh-220px)] pr-4">
            <nav className="space-y-2">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={cn(
                      "flex items-center w-full px-4 py-3.5 rounded-xl text-sm font-medium transition-all",
                      activeTab === tab.id
                        ? "bg-gradient-to-r from-blue-600 to-blue-500 text-white shadow-lg shadow-blue-500/20"
                        : "text-vista-light hover:bg-black/20 hover:border-white/5 border border-transparent"
                    )}
                  >
                    <div className={cn(
                      "flex items-center justify-center rounded-md mr-3",
                      "w-8 h-8",
                      activeTab === tab.id
                        ? "bg-white/20"
                        : "bg-black/30 border border-white/5"
                    )}>
                      <Icon className={cn(
                        "h-4 w-4 transition-transform",
                        activeTab === tab.id ? "text-white" : "text-blue-400"
                      )} />
                    </div>
                    {tab.label}
                  </button>
                );
              })}
            </nav>

            <Separator className="my-6 bg-vista-dark-lighter" />

            <Button
              variant="ghost"
              size="sm"
              className="w-full flex items-center justify-start text-vista-light/70 hover:text-vista-light hover:bg-vista-dark-lighter rounded-lg px-4 py-3 mb-4"
              onClick={() => router.push('/')}
            >
              <ArrowLeft className="h-4 w-4 mr-3" />
              Back to Home
            </Button>
          </ScrollArea>
        </div>

        {/* Mobile tab selector */}
        <div className="md:hidden">
          <div className="overflow-x-auto scrollbar-hide pb-2 px-1">
            <div className="flex space-x-2 w-max">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                const isTabActive = activeTab === tab.id;
                return (
                  <Button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    variant={isTabActive ? "default" : "outline"}
                    size="sm"
                    className={cn(
                      "rounded-full flex items-center gap-1.5 h-8 px-3",
                      isTabActive
                        ? "bg-gradient-to-r from-blue-600 to-blue-500 text-white shadow-md shadow-blue-500/20 border-none"
                        : "text-vista-light border-white/5 bg-black/20 hover:bg-black/30"
                    )}
                  >
                    <div className={cn(
                      "flex items-center justify-center rounded-full",
                      "w-4 h-4",
                      isTabActive ? "bg-white/20" : "bg-black/30"
                    )}>
                      <Icon className={cn(
                        "h-2.5 w-2.5",
                        isTabActive ? "text-white" : "text-blue-400"
                      )} />
                    </div>
                    <span className="text-xs font-medium">{tab.label}</span>
                    {isTabActive && (
                      <div className="ml-1 w-1.5 h-1.5 rounded-full bg-white"></div>
                    )}
                  </Button>
                );
              })}
            </div>
          </div>
        </div>
      </aside>

      {/* Main content */}
      <div className="flex-1 min-w-0 pb-8">
        {header && (
          <div className="hidden md:block sticky top-24 z-10 py-6 bg-vista-dark/90 backdrop-blur-sm">
            {header}
          </div>
        )}

        <div className="pt-2 md:pt-8">
          {children}
        </div>

        {footer && (
          <div className="mt-6 md:mt-8">
            {footer}
          </div>
        )}
      </div>
    </div>
  );
}
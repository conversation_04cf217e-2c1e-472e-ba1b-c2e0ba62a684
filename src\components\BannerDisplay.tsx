'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { bannerCache } from '@/lib/banner-cache';

interface BannerAd {
  _id: string;
  title: string;
  description?: string;
  imageUrl?: string;
  linkUrl?: string;
  bannerType: 'image' | 'solid-color';
  styling: {
    backgroundColor: string;
    textColor: string;
    titleSize: string;
    descriptionSize: string;
    borderRadius: string;
    padding: string;
    animation: 'none' | 'fadeIn' | 'slideIn' | 'bounce' | 'pulse';
    animationDuration: string;
    positions: ('top' | 'center' | 'bottom' | 'hero-overlay' | 'between-sections')[];
    layout: 'horizontal' | 'full-width' | 'centered';
    textAlign: 'left' | 'center' | 'right';
  };
  priority: number;
  analytics: {
    impressions: number;
    views: number;
    clicks: number;
  };
}

interface BannerDisplayProps {
  position?: 'top' | 'center' | 'bottom' | 'hero-overlay' | 'between-sections';
  className?: string;
}

export default function BannerDisplay({ position = 'top', className = '' }: BannerDisplayProps) {
  const [banners, setBanners] = useState<BannerAd[]>([]);
  const [currentBannerIndex, setCurrentBannerIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set());
  const [hasTrackedView, setHasTrackedView] = useState<Set<string>>(new Set());
  
  const bannerRef = useRef<HTMLDivElement>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout>();

  // Intersection Observer for accurate view tracking
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          setIsVisible(entry.isIntersecting && entry.intersectionRatio > 0.5);
        });
      },
      { 
        threshold: [0, 0.5, 1],
        rootMargin: '-50px 0px' // Only trigger when banner is well within viewport
      }
    );

    const currentRef = bannerRef.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, []);

  // Fetch active banners using cache service
  const fetchBanners = useCallback(async () => {
    try {
      setError(null);
      const data = await bannerCache.getActiveBanners();
      
      if (!data || !Array.isArray(data.banners)) {
        throw new Error('Invalid response format from banner API');
      }

      // Filter banners by position and exclude ones with image errors
      const filteredBanners = data.banners.filter(
        (banner: BannerAd) =>
          banner.styling.positions?.includes(position) &&
          // Only exclude image banners with image errors, allow solid color banners
          // Treat undefined bannerType as 'image' for backwards compatibility
          (banner.bannerType === 'solid-color' || !imageErrors.has(banner._id))
      );

      console.log('Filtered banners for position', position, ':', filteredBanners);
      setBanners(filteredBanners);
      
    } catch (error) {
      console.error('Error fetching banners:', error);
      setError(error instanceof Error ? error.message : 'Failed to load banners');
      
      // Exponential backoff retry
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
      retryTimeoutRef.current = setTimeout(() => {
        fetchBanners();
      }, 5000); // Retry after 5 seconds
    } finally {
      setLoading(false);
    }
  }, [position, imageErrors]);

  useEffect(() => {
    fetchBanners();
    
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, [fetchBanners]);

  // Cycle through banners if multiple exist
  useEffect(() => {
    if (banners.length > 1) {
      const interval = setInterval(() => {
        setCurrentBannerIndex((prev) => (prev + 1) % banners.length);
      }, 8000); // Change banner every 8 seconds

      return () => clearInterval(interval);
    }
  }, [banners.length]);

  // Track view when banner becomes visible and hasn't been tracked yet
  useEffect(() => {
    if (banners.length > 0 && isVisible) {
      const currentBanner = banners[currentBannerIndex];
      if (currentBanner && !hasTrackedView.has(currentBanner._id)) {
        // Track view using cache service (batched)
        bannerCache.trackAnalytics(currentBanner._id, 'view');
        setHasTrackedView(prev => new Set([...prev, currentBanner._id]));
      }
    }
  }, [banners, currentBannerIndex, isVisible, hasTrackedView]);

  // Handle banner click
  const handleBannerClick = (banner: BannerAd) => {
    // Track click using cache service (batched)
    bannerCache.trackAnalytics(banner._id, 'click');
    if (banner.linkUrl) {
      window.open(banner.linkUrl, '_blank', 'noopener,noreferrer');
    }
  };

  // Retry function for error state
  const handleRetry = () => {
    setLoading(true);
    setError(null);
    setHasTrackedView(new Set()); // Reset view tracking
    bannerCache.clearCache(); // Clear cache to force fresh data
    fetchBanners();
  };

  // Loading state
  if (loading) {
    return (
      <div className={`flex items-center justify-center py-4 ${className}`}>
        <div className="flex items-center space-x-2 text-vista-light/50">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-vista-blue"></div>
          <span className="text-sm">Loading banners...</span>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`flex items-center justify-center py-4 ${className}`}>
        <div className="flex flex-col items-center space-y-2 text-center">
          <AlertCircle className="h-5 w-5 text-yellow-500" />
          <p className="text-sm text-vista-light/70">Unable to load banners</p>
          <Button
            onClick={handleRetry}
            variant="ghost"
            size="sm"
            className="text-vista-blue hover:text-vista-blue/80"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Retry
          </Button>
        </div>
      </div>
    );
  }

  // No banners available - return null for cleaner UI
  if (banners.length === 0 || !isVisible) {
    return null;
  }

  const currentBanner = banners[currentBannerIndex];

  // Animation variants
  const getAnimationVariants = (animation: string) => {
    switch (animation) {
      case 'fadeIn':
        return {
          initial: { opacity: 0 },
          animate: { opacity: 1 },
          exit: { opacity: 0 }
        };
      case 'slideIn':
        return {
          initial: { x: position === 'top' ? -100 : 100, opacity: 0 },
          animate: { x: 0, opacity: 1 },
          exit: { x: position === 'top' ? 100 : -100, opacity: 0 }
        };
      case 'bounce':
        return {
          initial: { scale: 0.8, opacity: 0 },
          animate: { scale: 1, opacity: 1, transition: { type: 'spring', bounce: 0.4 } },
          exit: { scale: 0.8, opacity: 0 }
        };
      case 'pulse':
        return {
          initial: { scale: 0.95, opacity: 0 },
          animate: { 
            scale: 1, 
            opacity: 1,
            transition: { 
              scale: { repeat: Infinity, repeatType: 'reverse', duration: 2 },
              opacity: { duration: 0.5 }
            }
          },
          exit: { scale: 0.95, opacity: 0 }
        };
      default:
        return {
          initial: {},
          animate: {},
          exit: {}
        };
    }
  };

  const animationVariants = getAnimationVariants(currentBanner.styling.animation);
  const animationDuration = parseFloat(currentBanner.styling.animationDuration);

  // Special styling for hero overlay position
  const isHeroOverlay = position === 'hero-overlay';
  const overlayStyles = isHeroOverlay ? {
    backgroundColor: `${currentBanner.styling.backgroundColor}CC`, // Add transparency
    backdropFilter: 'blur(8px)',
  } : {
    backgroundColor: currentBanner.styling.backgroundColor,
  };

  // Render banner content based on layout and type
  const renderBannerContent = () => {
    const layout = currentBanner.styling.layout || 'full-width';
    const textAlign = currentBanner.styling.textAlign || 'left';
    // Treat undefined bannerType as 'image' for backwards compatibility
    const isImageBanner = (currentBanner.bannerType === 'image' || !currentBanner.bannerType) && currentBanner.imageUrl;

    const commonProps = {
      className: currentBanner.linkUrl ? 'cursor-pointer' : '',
      onClick: () => currentBanner.linkUrl && handleBannerClick(currentBanner)
    };

    // Full-width banner layout
    if (layout === 'full-width') {
      return (
        <div {...commonProps} className={`relative h-[200px] flex items-center ${commonProps.className}`}>
          {/* Background image for image banners */}
          {isImageBanner && !imageErrors.has(currentBanner._id) && (
            <div className="absolute inset-0 overflow-hidden" style={{ borderRadius: currentBanner.styling.borderRadius }}>
              <Image
                src={currentBanner.imageUrl!}
                alt={currentBanner.title}
                fill
                className="object-cover"
                onError={(e) => {
                  console.error('Image failed to load:', currentBanner.imageUrl, e);
                  setImageErrors(prev => new Set([...prev, currentBanner._id]));
                }}
                onLoad={() => console.log('Image loaded successfully:', currentBanner.imageUrl)}
                unoptimized={true}
              />
              <div className="absolute inset-0 bg-black/40" />
            </div>
          )}

          {/* Content overlay */}
          <div className={`relative z-10 w-full px-6 py-4 text-${textAlign}`}>
            <h3
              className="font-bold mb-2"
              style={{
                color: currentBanner.styling.textColor,
                fontSize: currentBanner.styling.titleSize,
                textShadow: isImageBanner ? '0 2px 4px rgba(0,0,0,0.5)' : 'none'
              }}
            >
              {currentBanner.title}
            </h3>
            {currentBanner.description && (
              <p
                className="opacity-90 max-w-2xl"
                style={{
                  color: currentBanner.styling.textColor,
                  fontSize: currentBanner.styling.descriptionSize,
                  textShadow: isImageBanner ? '0 1px 2px rgba(0,0,0,0.5)' : 'none'
                }}
              >
                {currentBanner.description}
              </p>
            )}
            {currentBanner.linkUrl && (
              <div className="mt-3">
                <span
                  className="inline-block text-sm font-medium px-4 py-2 rounded border opacity-80 hover:opacity-100 transition-opacity"
                  style={{
                    color: currentBanner.styling.textColor,
                    borderColor: currentBanner.styling.textColor,
                    backgroundColor: isImageBanner ? 'rgba(0,0,0,0.3)' : 'transparent'
                  }}
                >
                  Learn More →
                </span>
              </div>
            )}
          </div>
        </div>
      );
    }

    // Centered layout
    if (layout === 'centered') {
      return (
        <div {...commonProps} className={`text-center py-8 ${commonProps.className}`}>
          {isImageBanner && !imageErrors.has(currentBanner._id) && (
            <div className="mb-4 flex justify-center">
              <div className="relative w-24 h-24 rounded-lg overflow-hidden">
                <Image
                  src={currentBanner.imageUrl!}
                  alt={currentBanner.title}
                  fill
                  className="object-cover"
                  onError={(e) => {
                    console.error('Image failed to load (centered):', currentBanner.imageUrl, e);
                    setImageErrors(prev => new Set([...prev, currentBanner._id]));
                  }}
                  onLoad={() => console.log('Image loaded successfully (centered):', currentBanner.imageUrl)}
                  unoptimized={true}
                />
              </div>
            </div>
          )}
          <h3
            className="font-semibold mb-2"
            style={{
              color: currentBanner.styling.textColor,
              fontSize: currentBanner.styling.titleSize,
            }}
          >
            {currentBanner.title}
          </h3>
          {currentBanner.description && (
            <p
              className="opacity-90 max-w-md mx-auto"
              style={{
                color: currentBanner.styling.textColor,
                fontSize: currentBanner.styling.descriptionSize,
              }}
            >
              {currentBanner.description}
            </p>
          )}
          {currentBanner.linkUrl && (
            <div className="mt-4">
              <span
                className="inline-block text-sm font-medium px-4 py-2 rounded border opacity-80 hover:opacity-100 transition-opacity"
                style={{
                  color: currentBanner.styling.textColor,
                  borderColor: currentBanner.styling.textColor,
                }}
              >
                Learn More →
              </span>
            </div>
          )}
        </div>
      );
    }

    // Default horizontal layout
    return (
      <div {...commonProps} className={`flex items-center gap-4 ${commonProps.className}`}>
        {/* Banner image */}
        {isImageBanner && (
          <div className="relative flex-shrink-0 w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 rounded overflow-hidden">
            {imageErrors.has(currentBanner._id) ? (
              <div
                className="w-full h-full flex items-center justify-center text-xs font-medium"
                style={{
                  backgroundColor: currentBanner.styling.backgroundColor,
                  color: currentBanner.styling.textColor
                }}
              >
                AD
              </div>
            ) : (
              <Image
                src={currentBanner.imageUrl!}
                alt={currentBanner.title}
                fill
                className="object-cover"
                sizes="(max-width: 640px) 64px, (max-width: 768px) 80px, 96px"
                onError={(e) => {
                  console.error('Image failed to load (horizontal):', currentBanner.imageUrl, e);
                  setImageErrors(prev => new Set([...prev, currentBanner._id]));
                }}
                onLoad={() => console.log('Image loaded successfully (horizontal):', currentBanner.imageUrl)}
                unoptimized={true}
              />
            )}
          </div>
        )}

        {/* Banner text */}
        <div className="flex-1 min-w-0">
          <h3
            className="font-semibold mb-1 truncate"
            style={{
              color: currentBanner.styling.textColor,
              fontSize: currentBanner.styling.titleSize,
            }}
          >
            {currentBanner.title}
          </h3>
          {currentBanner.description && (
            <p
              className="text-sm opacity-90 line-clamp-2"
              style={{
                color: currentBanner.styling.textColor,
                fontSize: currentBanner.styling.descriptionSize,
              }}
            >
              {currentBanner.description}
            </p>
          )}
        </div>

        {/* Link indicator */}
        {currentBanner.linkUrl && (
          <div className="flex-shrink-0">
            <span
              className="text-xs px-2 py-1 rounded border opacity-80"
              style={{
                color: currentBanner.styling.textColor,
                borderColor: currentBanner.styling.textColor,
              }}
            >
              →
            </span>
          </div>
        )}
      </div>
    );
  };

  return (
    <div ref={bannerRef}>
      <AnimatePresence mode="wait">
        <motion.div
          key={`${currentBanner._id}-${currentBannerIndex}`}
          className={`relative w-full ${className} ${isHeroOverlay ? 'backdrop-blur-sm' : ''}`}
          initial={animationVariants.initial}
          animate={animationVariants.animate}
          exit={animationVariants.exit}
          transition={{ duration: animationDuration }}
          style={{
            ...overlayStyles,
            borderRadius: currentBanner.styling.borderRadius,
            padding: currentBanner.styling.padding,
          }}
        >
          {/* Banner content */}
          {renderBannerContent()}

          {/* Multiple banners indicator */}
          {banners.length > 1 && (
            <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1">
              {banners.map((_, index) => (
                <div
                  key={index}
                  className={`w-2 h-2 rounded-full transition-opacity ${
                    index === currentBannerIndex ? 'opacity-100' : 'opacity-40'
                  }`}
                  style={{ backgroundColor: currentBanner.styling.textColor }}
                />
              ))}
            </div>
          )}
        </motion.div>
      </AnimatePresence>
    </div>
  );
}

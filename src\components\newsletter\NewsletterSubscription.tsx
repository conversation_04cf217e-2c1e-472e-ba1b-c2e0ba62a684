'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Send, Mail } from 'lucide-react';

export default function NewsletterSubscription() {
  const [email, setEmail] = useState('');
  const [isSubscribed, setIsSubscribed] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (email) {
      setIsSubscribed(true);
      setEmail('');
      // In a real app, you would send this to your backend
    }
  };

  return (
    <section className="py-12">
      <div className="container mx-auto px-4">
        <div className="bg-vista-dark-lighter rounded-xl overflow-hidden p-8 relative">
          {/* Background decorative elements */}
          <div className="absolute top-0 right-0 w-32 h-32 opacity-10">
            <Mail className="w-full h-full text-vista-blue" />
          </div>
          
          <div className="max-w-3xl mx-auto text-center">
            <motion.h2 
              className="text-2xl md:text-3xl font-bold text-vista-light mb-3"
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              Get Updates on New Releases
            </motion.h2>
            
            <motion.p 
              className="text-vista-light/70 mb-6"
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              Subscribe to our newsletter and be the first to know about new movies, shows, and exclusive content.
            </motion.p>
            
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              {isSubscribed ? (
                <div className="bg-vista-blue/20 border border-vista-blue/30 text-vista-blue p-4 rounded-lg">
                  Thanks for subscribing! We'll keep you updated on the latest releases.
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-3 max-w-md mx-auto">
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email"
                    className="flex-1 px-4 py-2 bg-vista-dark rounded-lg border border-vista-light/20 text-vista-light placeholder:text-vista-light/50 focus:outline-none focus:ring-2 focus:ring-vista-blue"
                    required
                  />
                  <Button type="submit" className="bg-vista-blue hover:bg-vista-blue/90 gap-2">
                    Subscribe
                    <Send className="h-4 w-4" />
                  </Button>
                </form>
              )}
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
} 
import { NextRequest, NextResponse } from 'next/server';
import { isAdmin } from '@/lib/middleware';
import { ensureMongooseConnection } from '@/lib/mongoose';
import Content, { IContent } from '@/models/Content';
import { getPopularMovies, getPopularShows, getTopRatedMovies, getTopRatedShows } from '@/services/tmdb';

// GET handler to fetch content items with pagination, filtering and search
export async function GET(request: NextRequest) {
  try {
    // Check if user is admin
    const adminCheck = await isAdmin(request);
    if (!adminCheck.isAuthorized) {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const type = searchParams.get('type') || '';
    const status = searchParams.get('status') || '';
    const genre = searchParams.get('genre') || '';
    const action = searchParams.get('action') || '';

    // Handle special action for syncing with TMDB
    if (action === 'sync') {
      return await handleSyncAction();
    }

    // Build query
    const query: any = {};

    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { overview: { $regex: search, $options: 'i' } }
      ];
    }

    if (type) {
      query.type = type;
    }

    if (status) {
      query.status = status;
    }

    if (genre) {
      query.genres = { $in: [genre] };
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Fetch content
    const content = await Content.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // Get total count for pagination
    const total = await Content.countDocuments(query);

    // Return content with pagination info
    return NextResponse.json({
      content: content.map((item: IContent & { _id: any }) => ({
        id: item._id.toString(),
        title: item.title,
        type: item.type,
        tmdbId: item.tmdbId,
        imdbId: item.imdbId,
        posterPath: item.posterPath,
        year: item.year,
        genres: item.genres,
        rating: item.rating,
        status: item.status || 'published',
        featured: item.featured || false,
        trending: item.trending || false,
        views: item.views || 0,
        createdAt: item.createdAt
      })),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching content:', error);
    return NextResponse.json(
      { error: 'Failed to fetch content' },
      { status: 500 }
    );
  }
}

// POST handler to create a new content item
export async function POST(request: NextRequest) {
  try {
    // Check if user is admin
    const adminCheck = await isAdmin(request);
    if (!adminCheck.isAuthorized) {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Get request body
    const body = await request.json();

    // Validate required fields
    if (!body.title || !body.type) {
      return NextResponse.json(
        { error: 'Title and type are required fields' },
        { status: 400 }
      );
    }

    // Create new content item
    const newContent = new Content({
      title: body.title,
      type: body.type,
      tmdbId: body.tmdbId,
      imdbId: body.imdbId,
      posterPath: body.posterPath,
      backdropPath: body.backdropPath,
      overview: body.overview,
      year: body.year,
      releaseDate: body.releaseDate,
      genres: body.genres || [],
      runtime: body.runtime,
      rating: body.rating,
      seasons: body.seasons,
      episodes: body.episodes,
      status: body.status || 'published',
      featured: body.featured || false,
      trending: body.trending || false,
      views: body.views || 0
    });

    // Save content
    await newContent.save();

    // Return success
    return NextResponse.json({
      success: true,
      content: {
        id: (newContent as IContent & { _id: any })._id.toString(),
        title: newContent.title,
        type: newContent.type,
        tmdbId: newContent.tmdbId,
        posterPath: newContent.posterPath,
        year: newContent.year,
        genres: newContent.genres,
        status: newContent.status
      }
    });
  } catch (error) {
    console.error('Error creating content:', error);
    return NextResponse.json(
      { error: 'Failed to create content' },
      { status: 500 }
    );
  }
}

// Function to handle TMDB sync action
async function handleSyncAction() {
  try {
    // Connect to MongoDB
    await ensureMongooseConnection();

    // Fetch data from TMDB
    const [popularMovies, popularShows, topRatedMovies, topRatedShows] = await Promise.all([
      getPopularMovies(),
      getPopularShows(),
      getTopRatedMovies(),
      getTopRatedShows()
    ]);

    // Combine all content
    const allContent = [
      ...popularMovies,
      ...popularShows,
      ...topRatedMovies,
      ...topRatedShows
    ];

    // Remove duplicates based on tmdbId
    const uniqueContent = allContent.filter((item, index, self) =>
      index === self.findIndex(t => t.tmdbId === item.tmdbId)
    );

    // Insert or update content in the database
    let insertedCount = 0;
    let updatedCount = 0;

    for (const item of uniqueContent) {
      // Check if content already exists
      const existingContent = await Content.findOne({ tmdbId: item.tmdbId });

      if (existingContent) {
        // Update existing content
        await Content.updateOne(
          { tmdbId: item.tmdbId },
          {
            $set: {
              title: item.title,
              posterPath: item.posterPath,
              backdropPath: item.backdropPath,
              overview: item.overview,
              year: item.year,
              genres: item.genres,
              rating: item.rating,
              runtime: item.runtime,
              seasons: item.seasons,
              episodes: item.episodes,
              imdbId: item.imdbId,
              updatedAt: new Date()
            }
          }
        );
        updatedCount++;
      } else {
        // Create new content
        const newContent = new Content({
          title: item.title,
          type: item.type,
          tmdbId: item.tmdbId,
          imdbId: item.imdbId,
          posterPath: item.posterPath,
          backdropPath: item.backdropPath,
          overview: item.overview,
          year: item.year,
          genres: item.genres,
          rating: item.rating,
          runtime: item.runtime,
          seasons: item.seasons,
          episodes: item.episodes,
          status: 'published',
          featured: false,
          trending: false,
          views: 0
        });

        await newContent.save();
        insertedCount++;
      }
    }

    return NextResponse.json({
      success: true,
      message: `Sync completed: ${insertedCount} items inserted, ${updatedCount} items updated`,
      inserted: insertedCount,
      updated: updatedCount,
      total: uniqueContent.length
    });
  } catch (error) {
    console.error('Error syncing content with TMDB:', error);
    return NextResponse.json(
      { error: 'Failed to sync content with TMDB' },
      { status: 500 }
    );
  }
}

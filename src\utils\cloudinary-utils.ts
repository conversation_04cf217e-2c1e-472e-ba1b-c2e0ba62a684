/**
 * Utility functions for Cloudinary operations
 */

/**
 * Extract the Cloudinary public ID from a URL
 * @param url The Cloudinary URL
 * @returns The public ID or null if not a Cloudinary URL
 */
export function getCloudinaryPublicId(url: string): string | null {
  try {
    // If the URL is a Cloudinary URL, extract the public ID
    if (url && url.includes('cloudinary.com')) {
      // Format is typically: https://res.cloudinary.com/cloud_name/image/upload/v1234567890/folder/public_id.jpg
      const matches = url.match(/\/upload\/(?:v\d+\/)?(.+?)(?:\.[^.]+)?$/);
      return matches ? matches[1] : null;
    }
    return null;
  } catch (error) {
    console.error('Error extracting Cloudinary public ID:', error);
    return null;
  }
}

/**
 * Delete an image from Cloudinary
 * @param publicId The public ID of the image to delete
 * @returns Promise resolving to true if deletion was successful, false otherwise
 */
export async function deleteFromCloudinary(publicId: string): Promise<boolean> {
  try {
    const cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME;
    const apiKey = process.env.CLOUDINARY_API_KEY;
    const apiSecret = process.env.CLOUDINARY_API_SECRET;

    if (!cloudName || !apiKey || !apiSecret) {
      console.error('Cloudinary credentials not configured');
      return false;
    }

    const timestamp = Math.round(new Date().getTime() / 1000);
    const signature = require('crypto')
      .createHash('sha1')
      .update(`public_id=${publicId}&timestamp=${timestamp}${apiSecret}`)
      .digest('hex');

    const formData = new FormData();
    formData.append('public_id', publicId);
    formData.append('timestamp', timestamp.toString());
    formData.append('api_key', apiKey);
    formData.append('signature', signature);

    const response = await fetch(`https://api.cloudinary.com/v1_1/${cloudName}/image/destroy`, {
      method: 'POST',
      body: formData,
    });

    const result = await response.json();
    return result.result === 'ok';
  } catch (error) {
    console.error('Error deleting image from Cloudinary:', error);
    return false;
  }
}

"use client"

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Calendar as CalendarIcon, Info } from 'lucide-react'
import { format } from 'date-fns'
import { useContentUpload } from './ContentUploadProvider'
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
} from '@/components/ui/form'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Switch } from '@/components/ui/switch'
import { Calendar } from '@/components/ui/calendar'
import { Button } from '@/components/ui/button'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'

export function SettingsStep() {
  const { uploadData, updateSettings } = useContentUpload()
  const { settings } = uploadData
  const [date, setDate] = useState<Date | undefined>(settings.releaseDate || undefined)

  // Handle visibility change
  const handleVisibilityChange = (value: 'public' | 'private' | 'unlisted') => {
    updateSettings({ visibility: value })
  }

  // Handle monetization change
  const handleMonetizationChange = (checked: boolean) => {
    updateSettings({ monetization: checked })
  }

  // Handle age restriction change
  const handleAgeRestrictionChange = (checked: boolean) => {
    updateSettings({ ageRestriction: checked })
  }

  // Handle date change
  const handleDateChange = (date: Date | undefined) => {
    setDate(date)
    updateSettings({ releaseDate: date || null })
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="space-y-2">
        <h2 className="text-xl font-semibold text-vista-light">Publishing Settings</h2>
        <p className="text-vista-light/70">
          Configure how your content will be published and who can view it.
        </p>
      </div>

      <Form>
        <div className="space-y-8">
          {/* Visibility Settings */}
          <FormField
            name="visibility"
            render={() => (
              <FormItem className="space-y-3">
                <div className="flex items-center justify-between">
                  <FormLabel className="text-base">Visibility</FormLabel>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-4 w-4 text-vista-light/60" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Control who can view your content</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <FormControl>
                  <RadioGroup
                    defaultValue={settings.visibility}
                    onValueChange={(value) => handleVisibilityChange(value as 'public' | 'private' | 'unlisted')}
                    className="space-y-3"
                  >
                    <FormItem className="flex items-start space-x-3 space-y-0 rounded-md border border-vista-light/10 p-4">
                      <FormControl>
                        <RadioGroupItem value="public" id="public" />
                      </FormControl>
                      <div className="space-y-1">
                        <FormLabel className="font-medium" htmlFor="public">Public</FormLabel>
                        <FormDescription>
                          Anyone can watch this content. It may appear in recommendations and search results.
                        </FormDescription>
                      </div>
                    </FormItem>

                    <FormItem className="flex items-start space-x-3 space-y-0 rounded-md border border-vista-light/10 p-4">
                      <FormControl>
                        <RadioGroupItem value="unlisted" id="unlisted" />
                      </FormControl>
                      <div className="space-y-1">
                        <FormLabel className="font-medium" htmlFor="unlisted">Unlisted</FormLabel>
                        <FormDescription>
                          Anyone with the link can watch this content. It won't appear in recommendations or search results.
                        </FormDescription>
                      </div>
                    </FormItem>

                    <FormItem className="flex items-start space-x-3 space-y-0 rounded-md border border-vista-light/10 p-4">
                      <FormControl>
                        <RadioGroupItem value="private" id="private" />
                      </FormControl>
                      <div className="space-y-1">
                        <FormLabel className="font-medium" htmlFor="private">Private</FormLabel>
                        <FormDescription>
                          Only you and people you choose can watch this content.
                        </FormDescription>
                      </div>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
              </FormItem>
            )}
          />

          {/* Schedule Release */}
          <FormItem className="space-y-3">
            <div className="flex items-center justify-between">
              <FormLabel className="text-base">Schedule Release</FormLabel>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="h-4 w-4 text-vista-light/60" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Set a future date for your content to be published</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <FormDescription className="mt-0">
              Choose a date to automatically publish your content in the future.
            </FormDescription>

            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant={date ? "outline" : "secondary"}
                  className="w-full justify-start text-left font-normal"
                  disabled={settings.visibility !== 'public'}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {date ? format(date, "PPP") : "Select release date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={handleDateChange}
                  disabled={(date) => date < new Date()}
                  initialFocus
                />
              </PopoverContent>
            </Popover>

            {date && (
              <Button
                variant="ghost"
                size="sm"
                className="text-red-500 hover:text-red-600 hover:bg-red-500/10"
                onClick={() => handleDateChange(undefined)}
              >
                Clear scheduled date
              </Button>
            )}
          </FormItem>

          {/* Toggle Settings */}
          <div className="space-y-4">
            <h3 className="text-base font-medium text-vista-light">Additional Settings</h3>

            {/* Monetization */}
            <div className="flex items-center justify-between rounded-md border border-vista-light/10 p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">Monetization</FormLabel>
                <FormDescription>
                  Allow ads to be displayed on your content and earn revenue
                </FormDescription>
              </div>
              <Switch
                checked={settings.monetization}
                onCheckedChange={handleMonetizationChange}
              />
            </div>

            {/* Age Restriction */}
            <div className="flex items-center justify-between rounded-md border border-vista-light/10 p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">Age Restriction</FormLabel>
                <FormDescription>
                  Restrict this content to viewers 18 years or older
                </FormDescription>
              </div>
              <Switch
                checked={settings.ageRestriction}
                onCheckedChange={handleAgeRestrictionChange}
              />
            </div>
          </div>
        </div>
      </Form>
    </motion.div>
  )
}

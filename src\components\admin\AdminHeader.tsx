'use client';

import { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import {
  Menu,
  BellRing,
  Search,
  X,
  Home,
  Settings,
  ChevronDown,
  LogOut,
  User,
  Film,
  BarChart,
  Server,
  AlarmClock,
  Calendar,
  CircleUser,
  MapPin,
  Sun,
  Bell,
  UserPlus,
  LogIn,
  HomeIcon,
  ImageIcon
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useNotifications } from '@/contexts/NotificationContext';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuGroup
} from '@/components/ui/dropdown-menu';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { useLogout } from '../../hooks/useLogout';
import { Session } from "next-auth";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { formatDistanceToNow } from 'date-fns';

export default function AdminHeader() {
  const { user, signOut } = useAuth();
  const { notifications, unreadCount, markAllAsRead, markAsRead, fetchNotifications } = useNotifications();
  const router = useRouter();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [searchOpen, setSearchOpen] = useState(false);

  const searchRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Handle clicks outside search area
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setSearchOpen(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Focus search input when opened
  useEffect(() => {
    if (searchOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [searchOpen]);

  // Fetch notifications when component mounts
  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  // Handle marking a single notification as read
  const handleMarkAsRead = (id: string) => {
    markAsRead(id);
  };

  return (
    <header className="bg-gradient-to-r from-vista-dark to-vista-dark/95 border-b border-vista-light/10 py-3 px-4 md:px-6 flex items-center justify-between sticky top-0 z-30 shadow-md backdrop-blur-sm">
      {/* Logo and Brand - visible on both mobile and desktop */}
      <div className="flex items-center space-x-2">
        {/* Mobile menu button */}
        <button
          className="md:hidden text-vista-light p-2 rounded-md hover:bg-vista-blue/10 transition-colors"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          aria-label="Toggle mobile menu"
        >
          <Menu className="h-5 w-5" />
        </button>

        <Link
          href="/admin"
          className="font-bold text-lg text-vista-light flex items-center"
        >
          <span className="bg-vista-blue h-2.5 w-2.5 rounded-full mr-2 shadow-glow-sm"></span>
          <span className="hidden sm:inline">StreamVista</span> Admin
        </Link>
      </div>

      {/* Search & Actions */}
      <div className="flex items-center space-x-2 sm:space-x-3">
        {/* Back to Home link */}
        <Link
          href="/"
          className="hidden md:flex items-center text-vista-light/80 hover:text-vista-light px-3 py-1.5 rounded-md hover:bg-vista-blue/10 transition-colors"
        >
          <Home className="h-4 w-4 mr-2" />
          <span className="text-sm">Home</span>
        </Link>

        {/* Search toggle */}
        <div className="relative" ref={searchRef}>
          <button
            onClick={() => setSearchOpen(!searchOpen)}
            className="text-vista-light/80 hover:text-vista-light p-2 rounded-md hover:bg-vista-blue/10 transition-colors"
            aria-label="Search"
          >
            {searchOpen ? <X className="h-5 w-5" /> : <Search className="h-5 w-5" />}
          </button>

          {/* Search overlay with animation */}
          <AnimatePresence>
            {searchOpen && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.15 }}
                className="absolute right-0 top-full mt-1 bg-vista-dark-lighter border border-vista-light/10 p-4 rounded-lg shadow-xl w-72 md:w-80"
              >
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-vista-light/50 h-4 w-4" />
                  <input
                    ref={searchInputRef}
                    type="search"
                    placeholder="Search admin dashboard..."
                    className="w-full py-2 pl-10 pr-4 bg-vista-dark/60 border border-vista-light/20 rounded-md focus:outline-none focus:ring-1 focus:ring-vista-blue focus:border-vista-blue text-vista-light text-sm"
                  />
                </div>
                <div className="mt-2 text-xs text-vista-light/50 flex items-center">
                  <span className="mr-1">Press</span>
                  <kbd className="px-1.5 py-0.5 bg-vista-dark border border-vista-light/20 rounded text-vista-light/70 text-xs">Enter</kbd>
                  <span className="ml-1">to search</span>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Notification dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="text-vista-light/80 hover:text-vista-light p-2 rounded-md hover:bg-vista-blue/10 transition-colors relative">
              <Bell className="h-5 w-5" />
              {unreadCount > 0 && (
                <span className="absolute top-0 right-0 h-4 w-4 bg-indigo-500 rounded-full ring-2 ring-vista-dark flex items-center justify-center">
                  <span className="text-[10px] font-medium text-white">{unreadCount}</span>
                </span>
              )}
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[380px] bg-vista-dark-lighter/95 backdrop-blur-md border-vista-light/20">
            <DropdownMenuLabel className="flex items-center justify-between py-3">
              <span className="text-vista-light font-medium flex items-center">
                <Bell className="h-4 w-4 mr-2 text-vista-light/70" />
                Notifications
                {unreadCount > 0 && (
                  <Badge className="ml-2 bg-indigo-500/20 text-indigo-400 text-xs px-2 py-0.5 font-normal">
                    {unreadCount} new
                  </Badge>
                )}
              </span>
              {unreadCount > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={markAllAsRead}
                  className="h-auto py-1 px-2 text-xs text-vista-blue hover:text-vista-blue hover:bg-vista-blue/10"
                >
                  Mark all as read
                </Button>
              )}
            </DropdownMenuLabel>
            <DropdownMenuSeparator className="bg-vista-light/10" />
            <div className="max-h-[320px] overflow-y-auto custom-scrollbar">
              {notifications.length > 0 ? (
                notifications.map((notification) => (
                  <DropdownMenuItem
                    key={notification._id}
                    className="py-4 px-4 cursor-pointer hover:bg-vista-blue/5 transition-colors focus:bg-vista-blue/5"
                    onClick={() => handleMarkAsRead(notification._id)}
                  >
                    <div className="flex-1">
                      <div className="flex items-center">
                        <span className={`text-sm ${notification.read ? 'text-vista-light/70' : 'text-vista-light font-medium'}`}>
                          {notification.title}
                        </span>
                        {!notification.read && (
                          <span className="ml-2 h-2 w-2 rounded-full bg-indigo-500"></span>
                        )}
                      </div>
                      <p className="text-xs text-vista-light/60 mt-1 line-clamp-1">{notification.message}</p>
                      <span className="text-xs text-vista-light/50 mt-1.5 block">
                        {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                      </span>
                    </div>
                  </DropdownMenuItem>
                ))
              ) : (
                <div className="py-8 text-center text-vista-light/50">
                  <Bell className="h-8 w-8 mx-auto mb-2 opacity-30" />
                  <p className="text-sm">No notifications</p>
                </div>
              )}
            </div>
            <DropdownMenuSeparator className="bg-vista-light/10" />
            <DropdownMenuItem asChild className="py-2.5 focus:bg-vista-blue/5">
              <Link href="/admin/notifications" className="cursor-pointer justify-center text-vista-blue hover:text-vista-blue">
                View all notifications
              </Link>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* User Profile Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="flex items-center space-x-2 rounded-full py-1 px-1.5 sm:px-2 hover:bg-vista-blue/10 transition-colors">
              <div className="w-7 h-7 rounded-full overflow-hidden border-2 border-transparent group-hover:border-vista-blue transition-colors">
                {user?.picture ? (
                  <Image
                    src={user.picture}
                    alt={user.name || "User"}
                    width={32}
                    height={32}
                    className="object-cover"
                  />
                ) : (
                  <div className="bg-gradient-to-br from-vista-blue to-vista-blue/80 w-full h-full flex items-center justify-center shadow-inner">
                    <span className="text-white text-xs font-medium">
                      {user?.name?.charAt(0) || 'A'}
                    </span>
                  </div>
                )}
              </div>
              <span className="hidden sm:block text-sm text-vista-light/90 font-medium">{user?.name?.split(' ')[0] || 'Admin'}</span>
              <ChevronDown className="h-4 w-4 text-vista-light/50 hidden sm:block" />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56 bg-vista-dark-lighter/90 backdrop-blur-md border-vista-light/20">
            <DropdownMenuLabel>
              <div className="flex flex-col">
                <span className="font-medium text-vista-light">{user?.name || 'Admin User'}</span>
                <span className="text-xs text-vista-light/50">{user?.email || ''}</span>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator className="bg-vista-light/10" />
            <DropdownMenuGroup>
              <DropdownMenuItem
                className="cursor-pointer hover:bg-vista-blue/5 focus:bg-vista-blue/5"
                onClick={() => router.push('/admin/settings/profile')}
              >
                <User className="h-4 w-4 mr-2 text-vista-light/70" />
                <span>Profile</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="cursor-pointer hover:bg-vista-blue/5 focus:bg-vista-blue/5"
                onClick={() => router.push('/admin/settings')}
              >
                <Settings className="h-4 w-4 mr-2 text-vista-light/70" />
                <span>Settings</span>
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator className="bg-vista-light/10" />
            <DropdownMenuItem
              className="cursor-pointer text-vista-blue hover:bg-vista-blue/5 focus:bg-vista-blue/5"
              onClick={() => router.push('/')}
            >
              <Home className="h-4 w-4 mr-2" />
              <span>Return to Home</span>
            </DropdownMenuItem>
            <DropdownMenuItem
              className="cursor-pointer text-red-400 hover:bg-red-500/5 focus:bg-red-500/5"
              onClick={() => signOut()}
            >
              <LogOut className="h-4 w-4 mr-2" />
              <span>Sign out</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Mobile menu */}
      <AnimatePresence>
        {mobileMenuOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="fixed inset-0 bg-black/50 z-40 md:hidden backdrop-blur-sm"
              onClick={() => setMobileMenuOpen(false)}
            />

            {/* Slide-in menu */}
            <motion.div
              initial={{ x: -280 }}
              animate={{ x: 0 }}
              exit={{ x: -280 }}
              transition={{ type: "spring", damping: 25, stiffness: 300 }}
              className="fixed inset-y-0 left-0 w-64 bg-vista-dark-lighter border-r border-vista-light/10 z-50 overflow-y-auto"
              onClick={e => e.stopPropagation()}
            >
              <div className="p-4 border-b border-vista-light/10 flex items-center justify-between">
                <Link href="/admin" className="text-vista-light font-bold flex items-center" onClick={() => setMobileMenuOpen(false)}>
                  <span className="h-2.5 w-2.5 bg-vista-blue rounded-full mr-2"></span>
                  StreamVista Admin
                </Link>
                <button
                  onClick={() => setMobileMenuOpen(false)}
                  className="text-vista-light p-1 rounded-md hover:bg-vista-dark-lighter transition-colors"
                  aria-label="Close menu"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              {/* Mobile user profile */}
              <div className="p-4 border-b border-vista-light/10">
                <div className="flex items-center mb-3">
                  <div className="w-10 h-10 rounded-full overflow-hidden mr-3 border-2 border-vista-blue/30">
                    {user?.picture ? (
                      <Image
                        src={user.picture}
                        alt={user.name || "User"}
                        width={40}
                        height={40}
                        className="object-cover"
                      />
                    ) : (
                      <div className="bg-gradient-to-br from-vista-blue to-vista-blue/80 w-full h-full flex items-center justify-center">
                        <span className="text-white text-sm font-medium">
                          {user?.name?.charAt(0) || 'A'}
                        </span>
                      </div>
                    )}
                  </div>
                  <div>
                    <div className="font-medium text-vista-light">{user?.name || 'Admin User'}</div>
                    <div className="text-xs text-vista-light/50">{user?.email || ''}</div>
                  </div>
                </div>

                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1 h-8 border-vista-light/20 hover:bg-vista-blue/10 text-vista-light/70"
                    onClick={() => {
                      router.push('/admin/settings/profile');
                      setMobileMenuOpen(false);
                    }}
                  >
                    Profile
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1 h-8 border-vista-light/20 hover:bg-vista-blue/10 text-vista-light/70"
                    onClick={() => {
                      signOut();
                      setMobileMenuOpen(false);
                    }}
                  >
                    Sign out
                  </Button>
                </div>
              </div>

              {/* Mobile menu links */}
              <div className="p-2">
                <nav className="space-y-1">
                  {[
                    { href: '/admin', label: 'Dashboard', icon: 'home' },
                    { href: '/admin/users', label: 'Users', icon: 'users' },
                    { href: '/admin/content', label: 'Content', icon: 'film' },
                    { href: '/admin/analytics', label: 'Analytics', icon: 'bar-chart' },
                    { href: '/admin/banner-ads', label: 'Banner Ads', icon: 'image' },
                    { href: '/admin/system', label: 'System', icon: 'server' },
                    { href: '/admin/settings', label: 'Settings', icon: 'settings' },
                    { href: '/admin/notifications', label: 'Notifications', icon: 'bell' },
                  ].map((item) => (
                    <Link
                      key={item.href}
                      href={item.href}
                      className="flex items-center px-3 py-2.5 rounded-md text-sm font-medium text-vista-light/80 hover:bg-vista-blue/10 hover:text-vista-light transition-colors"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      <span className="mr-3 text-vista-light/60">{getIcon(item.icon)}</span>
                      {item.label}
                    </Link>
                  ))}
                </nav>

                <div className="mt-6 pt-4 border-t border-vista-light/10">
                  <Link
                    href="/"
                    className="flex items-center px-3 py-2.5 rounded-md text-sm font-medium text-vista-blue hover:bg-vista-blue/10 transition-colors"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <Home className="mr-3 h-4 w-4" />
                    Return to Website
                  </Link>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </header>
  );
}

// Helper function to render icons for mobile menu
function getIcon(name: string) {
  switch (name) {
    case 'home':
      return <Home className="h-4 w-4" />;
    case 'users':
      return <User className="h-4 w-4" />;
    case 'film':
      return <Film className="h-4 w-4" />;
    case 'bar-chart':
      return <BarChart className="h-4 w-4" />;
    case 'server':
      return <Server className="h-4 w-4" />;
    case 'settings':
      return <Settings className="h-4 w-4" />;
    case 'bell':
      return <Bell className="h-4 w-4" />;
    case 'image':
      return <ImageIcon className="h-4 w-4" />;
    default:
      return <div className="h-4 w-4" />;
  }
}

// CSS utility for shadow glow effect
const styles = `
.shadow-glow-sm {
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.5);
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.15);
}
`;

// For the notification bell design
const NotificationBell = () => {
  return (
    <button
      className="flex items-center justify-center h-10 w-10 rounded-full border border-zinc-700 hover:bg-zinc-800 transition-colors relative"
      aria-label="Notifications"
    >
      <Bell className="h-5 w-5 text-zinc-200" />
      <span className="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-indigo-600 text-[10px] font-semibold text-white">
        3
      </span>
    </button>
  );
};
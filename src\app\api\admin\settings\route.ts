import { NextRequest, NextResponse } from 'next/server';
import { initializeDefaultSettings, SettingValue } from '@/lib/settings';

// Define a MongoDB document type for settings
interface SettingDocument {
  _id: { toString(): string };
  key: string;
  value: SettingValue;
  group: string;
  label?: string;
  description?: string;
  type?: string;
  options?: unknown[];
  isPublic?: boolean;
  isProtected?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

// Define a type for user document
interface UserDocument {
  role: string;
}

/**
 * GET /api/admin/settings
 * Get all settings or settings by group
 */
export async function GET(request: NextRequest) {
  try {
    // Try to get the user ID from the cookie directly
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, try to get it from the request body
    if (!userId) {
      try {
        const body = await request.json();
        userId = body.userId;
        // Clone the request since we've consumed the body
        request = new NextRequest(request.url, {
          headers: request.headers,
          method: request.method,
          body: JSON.stringify(body),
          cache: request.cache,
          credentials: request.credentials,
          integrity: request.integrity,
          keepalive: request.keepalive,
          mode: request.mode,
          redirect: request.redirect,
          referrer: request.referrer,
          referrerPolicy: request.referrerPolicy,
          signal: request.signal,
        });
      } catch (error) {
        // Ignore JSON parsing errors
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    if (!userId) {
      console.error('Admin settings API: No user ID found in request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const User = mongoose.default.models.User ||
                mongoose.default.model('User', new mongoose.default.Schema({
                  role: String
                }));

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    // Use type assertion with unknown first for proper type narrowing
    if (!user || ((user as unknown) as { role?: string }).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the Setting schema directly
    const SettingSchema = new mongoose.default.Schema({
      key: { type: String, required: true, unique: true },
      value: mongoose.default.Schema.Types.Mixed,
      group: { type: String, required: true, index: true },
      label: String,
      description: String,
      type: { type: String, enum: ['string', 'number', 'boolean', 'json', 'array'] },
      options: [mongoose.default.Schema.Types.Mixed],
      isPublic: { type: Boolean, default: false },
      isProtected: { type: Boolean, default: false }
    }, {
      timestamps: true
    });

    // Get the Setting model
    const Setting = mongoose.default.models.Setting ||
                  mongoose.default.model('Setting', SettingSchema);

    // Initialize default settings if they don't exist
    await initializeDefaultSettings();

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const group = searchParams.get('group');

    // Get settings
    let settings;
    if (group) {
      // Get settings by group
      settings = await Setting.find({ group }).lean();
    } else {
      // Get all settings
      settings = await Setting.find({}).lean();
    }

    // Format settings for response - use type assertion to handle Mongoose documents
    const formattedSettings = settings.map((setting: unknown) => {
      const typedSetting = setting as {
        _id: { toString(): string };
        key: string;
        value: SettingValue;
        group: string;
        label?: string;
        description?: string;
        type?: string;
        options?: unknown[];
        isPublic?: boolean;
        isProtected?: boolean;
        createdAt?: Date;
        updatedAt?: Date;
      };
      
      return {
        id: typedSetting._id.toString(),
        key: typedSetting.key,
        value: typedSetting.value,
        group: typedSetting.group,
        label: typedSetting.label,
        description: typedSetting.description,
        type: typedSetting.type,
        options: typedSetting.options,
        isPublic: typedSetting.isPublic,
        isProtected: typedSetting.isProtected,
        createdAt: typedSetting.createdAt,
        updatedAt: typedSetting.updatedAt
      };
    });

    // Return settings
    return NextResponse.json(formattedSettings);
  } catch (error) {
    console.error('Error fetching settings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch settings', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/settings
 * Update multiple settings
 */
export async function POST(request: NextRequest) {
  try {
    // Try to get the user ID from the cookie directly
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    // For POST requests, we'll try to extract the userId from the body
    if (!userId) {
      try {
        const bodyData = await request.json();
        userId = bodyData.userId;
        // Clone the request since we've consumed the body
        request = new NextRequest(request.url, {
          headers: request.headers,
          method: request.method,
          body: JSON.stringify(bodyData),
          cache: request.cache,
          credentials: request.credentials,
          integrity: request.integrity,
          keepalive: request.keepalive,
          mode: request.mode,
          redirect: request.redirect,
          referrer: request.referrer,
          referrerPolicy: request.referrerPolicy,
          signal: request.signal,
        });
      } catch (error) {
        // Ignore JSON parsing errors
      }
    }

    if (!userId) {
      console.error('Admin settings API (POST): No user ID found in request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const User = mongoose.default.models.User ||
                mongoose.default.model('User', new mongoose.default.Schema({
                  role: String
                }));

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    // Use type assertion with unknown first for proper type narrowing
    if (!user || ((user as unknown) as { role?: string }).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the Setting schema directly
    const SettingSchema = new mongoose.default.Schema({
      key: { type: String, required: true, unique: true },
      value: mongoose.default.Schema.Types.Mixed,
      group: { type: String, required: true, index: true },
      label: String,
      description: String,
      type: { type: String, enum: ['string', 'number', 'boolean', 'json', 'array'] },
      options: [mongoose.default.Schema.Types.Mixed],
      isPublic: { type: Boolean, default: false },
      isProtected: { type: Boolean, default: false }
    }, {
      timestamps: true
    });

    // Get the Setting model
    const Setting = mongoose.default.models.Setting ||
                  mongoose.default.model('Setting', SettingSchema);

    // Initialize default settings if they don't exist
    await initializeDefaultSettings();

    // Define the UserActivity schema directly
    const UserActivitySchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      action: String,
      details: String,
      ipAddress: String,
      userAgent: String,
      timestamp: { type: Date, default: Date.now },
      metadata: mongoose.default.Schema.Types.Mixed
    }, {
      timestamps: true
    });

    // Get the UserActivity model
    const UserActivity = mongoose.default.models.UserActivity ||
                        mongoose.default.model('UserActivity', UserActivitySchema);

    // Get request data
    const data = await request.json();

    // Validate data
    if (!data || !Array.isArray(data.settings)) {
      return NextResponse.json(
        { error: 'Invalid request data. Expected an array of settings.' },
        { status: 400 }
      );
    }

    // Define type for settings to update
    interface SettingToUpdate {
      key: string;
      value: SettingValue;
      group: string;
    }

    // Update settings directly
    let success = true;
    const bulkOps = data.settings.map((setting: unknown) => {
      const typedSetting = setting as SettingToUpdate;
      return {
        updateOne: {
          filter: { key: typedSetting.key },
          update: { $set: { value: typedSetting.value } },
          upsert: true
        }
      };
    });

    if (bulkOps.length > 0) {
      try {
        await Setting.bulkWrite(bulkOps);
      } catch (error) {
        console.error('Error updating settings:', error);
        success = false;
      }
    }

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to update settings' },
        { status: 500 }
      );
    }

    // Get IP address from headers
    const ipAddress = request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown';

    // Log admin activity directly
    await UserActivity.create({
      userId: new mongoose.default.Types.ObjectId(userId),
      type: 'admin',
      action: 'update_settings',
      details: `Admin updated system settings`,
      ipAddress,
      userAgent: request.headers.get('user-agent') || 'unknown',
      timestamp: new Date(),
      metadata: {
        settingsCount: data.settings.length,
        groups: [...new Set(data.settings.map((s: unknown) => (s as SettingToUpdate).group))]
      }
    });

    // Return success
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error updating settings:', error);
    return NextResponse.json(
      { error: 'Failed to update settings', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * API Utility functions for StreamVista
 */

/**
 * Generic API response type
 */
export interface ApiResponse<T = unknown> {
  data?: T;
  error?: string;
  message?: string;
  status?: number;
  [key: string]: unknown;
}

/**
 * Helper function for making admin API requests that automatically
 * adds the internal API header to avoid rate limiting
 */
export async function fetchAdminApi<T = unknown>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  // Ensure the endpoint starts with /api/admin
  const apiEndpoint = endpoint.startsWith('/api/admin') 
    ? endpoint 
    : `/api/admin/${endpoint}`;

  // Merge custom headers with required headers
  const headers = {
    'Content-Type': 'application/json',
    'Cache-Control': 'no-cache',
    'X-Internal-API': 'true',
    ...(options.headers || {})
  };

  // Prepare request options
  const requestOptions: RequestInit = {
    ...options,
    headers,
    credentials: 'include',
    cache: 'no-store'
  };

  // Make the request
  const response = await fetch(apiEndpoint, requestOptions);

  // Handle error responses
  if (!response.ok) {
    let errorMessage = `Request failed with status ${response.status}`;

    try {
      const errorData = await response.json() as ApiResponse;
      errorMessage = errorData.error || errorData.message || errorMessage;
    } catch (e) {
      // If parsing fails, use the default error message
    }

    throw new Error(errorMessage);
  }

  // Return the JSON response
  return response.json() as Promise<T>;
}

/**
 * GET request to admin API
 */
export async function getAdminApi<T = unknown>(
  endpoint: string,
  queryParams: Record<string, string | number | boolean | undefined> = {}
): Promise<T> {
  // Build query string
  const params = new URLSearchParams();
  Object.entries(queryParams).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      params.append(key, String(value));
    }
  });

  const queryString = params.toString();
  const url = queryString ? `${endpoint}?${queryString}` : endpoint;

  return fetchAdminApi<T>(url, { method: 'GET' });
}

/**
 * POST request to admin API
 */
export async function postAdminApi<T = unknown, D = Record<string, unknown>>(
  endpoint: string,
  data?: D
): Promise<T> {
  return fetchAdminApi<T>(endpoint, {
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined
  });
}

/**
 * PUT request to admin API
 */
export async function putAdminApi<T = unknown, D = Record<string, unknown>>(
  endpoint: string,
  data?: D
): Promise<T> {
  return fetchAdminApi<T>(endpoint, {
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined
  });
}

/**
 * DELETE request to admin API
 */
export async function deleteAdminApi<T = unknown>(
  endpoint: string
): Promise<T> {
  return fetchAdminApi<T>(endpoint, { method: 'DELETE' });
} 
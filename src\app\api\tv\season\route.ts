import { NextRequest, NextResponse } from 'next/server';
import { fetchFromTMDB } from '@/lib/tmdb';

/**
 * API route for fetching TV season details from TMDb
 */
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const id = searchParams.get('id');
  const season = searchParams.get('season');

  console.log(`API: Fetching TV season data for show ID: ${id}, season: ${season}`);

  if (!id) {
    console.error('API: Missing TV show ID in request');
    return NextResponse.json({ error: 'TV show ID is required' }, { status: 400 });
  }

  if (!season) {
    console.error('API: Missing season number in request');
    return NextResponse.json({ error: 'Season number is required' }, { status: 400 });
  }

  const seasonNumber = parseInt(season);
  if (isNaN(seasonNumber) || seasonNumber < 1) {
    console.error(`API: Invalid season number: ${season}`);
    return NextResponse.json({ error: 'Invalid season number' }, { status: 400 });
  }

  try {
    // First, fetch show details to check if the requested season exists
    console.log(`API: First checking show details to validate season ${seasonNumber}...`);
    const showEndpoint = `tv/${id}`;
    const showDetails = await fetchFromTMDB(showEndpoint);

    if (!showDetails) {
      console.error(`API: Could not fetch TV show details for ID ${id}`);
      return NextResponse.json({ error: 'TV show not found' }, { status: 404 });
    }

    console.log(`API: TV show has ${showDetails.number_of_seasons || 'unknown'} seasons`);

    // Validate that the requested season exists
    if (showDetails.number_of_seasons && seasonNumber > showDetails.number_of_seasons) {
      console.warn(`API: Requested season ${seasonNumber} exceeds available seasons (${showDetails.number_of_seasons})`);
      return NextResponse.json({
        error: `Season ${seasonNumber} not found. Show only has ${showDetails.number_of_seasons} seasons.`,
        availableSeasons: showDetails.number_of_seasons
      }, { status: 404 });
    }

    // Fetch TV season details directly using fetchFromTMDB
    const endpoint = `tv/${id}/season/${seasonNumber}`;
    console.log(`API: Requesting TMDB endpoint: ${endpoint}`);
    const seasonDetails = await fetchFromTMDB(endpoint);

    console.log(`API: Received data for show ID ${id}, season ${seasonNumber}:`, JSON.stringify({
      hasData: !!seasonDetails,
      hasEpisodes: seasonDetails?.episodes?.length > 0,
      episodeCount: seasonDetails?.episodes?.length || 0
    }));

    if (!seasonDetails) {
      console.error(`API: No season details returned for show ID ${id}, season ${seasonNumber}`);
      return NextResponse.json({
        error: `Could not fetch details for season ${seasonNumber}`
      }, { status: 404 });
    }

    if (!seasonDetails.episodes || !Array.isArray(seasonDetails.episodes) || seasonDetails.episodes.length === 0) {
      console.warn(`API: No episodes found for show ID ${id}, season ${seasonNumber}`);
      return NextResponse.json({
        error: `No episodes found for season ${seasonNumber}`
      }, { status: 404 });
    }

    // Process episodes to ensure all required fields
    const processedEpisodes = seasonDetails.episodes.map((episode: any) => ({
      ...episode,
      still_path: episode.still_path
        ? `https://image.tmdb.org/t/p/w780${episode.still_path}`
        : null,
      // Ensure these fields exist even if null
      runtime: episode.runtime || (episode.episode_number ? 30 + (episode.episode_number % 10) : 30), // Estimate runtime if not provided
      overview: episode.overview || `Episode ${episode.episode_number}`,
      name: episode.name || `Episode ${episode.episode_number}`
    }));

    const result = {
      ...seasonDetails,
      episodes: processedEpisodes
    };

    console.log(`API: Successfully returned TV season details for show ID ${id}, season ${seasonNumber} with ${processedEpisodes.length} episodes`);

    return NextResponse.json(result);
  } catch (error) {
    console.error(`API: Error fetching TV season details:`, error);
    return NextResponse.json({
      error: 'Failed to fetch season data from TMDb API'
    }, { status: 500 });
  }
}
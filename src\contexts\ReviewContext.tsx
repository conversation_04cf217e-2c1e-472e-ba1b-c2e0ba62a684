'use client';

import React, { createContext, useContext, useState, useEffect, useRef, ReactNode } from 'react';
import { toast } from 'sonner';

interface Review {
  _id: string;
  contentId: string;
  contentType: 'movie' | 'show';
  userId: string;
  username: string;
  rating: number;
  comment: string;
  likes: number;
  dislikes: number;
  createdAt: string;
  updatedAt: string;
}

interface ReviewStats {
  averageRating: number;
  reviewCount: number;
  ratingDistribution: number[];
}

interface ReviewContextType {
  reviews: Review[];
  stats: ReviewStats;
  loading: boolean;
  error: string | null;
  page: number;
  totalPages: number;
  fetchReviews: (contentId: string, contentType: string, page?: number) => Promise<void>;
  addReview: (review: Omit<Review, '_id' | 'likes' | 'dislikes' | 'createdAt' | 'updatedAt'>) => Promise<boolean>;
  likeReview: (reviewId: string) => Promise<void>;
  dislikeReview: (reviewId: string) => Promise<void>;
  deleteReview: (reviewId: string, userId: string, isAdmin?: boolean) => Promise<boolean>;
  setReviews: React.Dispatch<React.SetStateAction<Review[]>>;
  setStats: React.Dispatch<React.SetStateAction<ReviewStats>>;
}

const defaultStats: ReviewStats = {
  averageRating: 0,
  reviewCount: 0,
  ratingDistribution: [0, 0, 0, 0, 0]
};

const ReviewContext = createContext<ReviewContextType | undefined>(undefined);

export function ReviewProvider({ children }: { children: ReactNode }) {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [stats, setStats] = useState<ReviewStats>(defaultStats);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);

  // Add cache to prevent duplicate requests
  const [requestCache, setRequestCache] = useState<Record<string, {
    timestamp: number;
    data: any;
  }>>({});

  // Add a ref to track if a request is in progress
  const activeRequests = useRef<Record<string, boolean>>({});

  const fetchReviews = async (contentId: string, contentType: string, pageNum: number = 1) => {
    // Create a cache key
    const cacheKey = `${contentId}-${contentType}-${pageNum}`;

    // Check if this request is already in progress
    if (activeRequests.current[cacheKey]) {
      console.log(`Request already in progress for ${cacheKey}, skipping duplicate`);
      return;
    }

    // Check if we have a recent cached response (less than 30 seconds old)
    const cachedResponse = requestCache[cacheKey];
    if (cachedResponse && Date.now() - cachedResponse.timestamp < 30000) {
      console.log(`Using cached response for ${cacheKey}`);
      setReviews(cachedResponse.data.reviews);
      setStats(cachedResponse.data.stats);
      setPage(cachedResponse.data.pagination.page);
      setTotalPages(cachedResponse.data.pagination.totalPages);
      return;
    }

    // Mark this request as in progress
    activeRequests.current[cacheKey] = true;

    setLoading(true);
    setError(null);
    try {
      console.log(`Fetching reviews for ${contentId}, ${contentType}, page ${pageNum}`);
      const response = await fetch(`/api/reviews?contentId=${contentId}&contentType=${contentType}&page=${pageNum}&limit=5`);

      if (!response.ok) {
        throw new Error('Failed to fetch reviews');
      }

      const data = await response.json();

      // Cache the response
      setRequestCache(prev => ({
        ...prev,
        [cacheKey]: {
          timestamp: Date.now(),
          data
        }
      }));

      setReviews(data.reviews);
      setStats(data.stats);
      setPage(data.pagination.page);
      setTotalPages(data.pagination.totalPages);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error fetching reviews:', err);
    } finally {
      setLoading(false);
      // Mark this request as complete
      activeRequests.current[cacheKey] = false;
    }
  };

  const addReview = async (review: Omit<Review, '_id' | 'likes' | 'dislikes' | 'createdAt' | 'updatedAt'>): Promise<boolean> => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/reviews', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(review),
      });

      const data = await response.json();

      if (!response.ok) {
        // Handle duplicate review more gracefully
        if (data.error && data.error.includes('already reviewed')) {
          // If the API returned the existing review, we could offer to update it
          if (data.existingReview) {
            toast.info('You have already submitted a review for this content');

            // In the future, we could implement an update feature here
            // For now, just close the dialog
          } else {
            toast.info('You have already submitted a review for this content');
          }

          // Still return true to close the dialog
          return true;
        }

        throw new Error(data.error || 'Failed to add review');
      }

      // Add the new review to the state immediately
      const newReview = data.review;

      // Update the reviews list with the new review at the top
      setReviews(prevReviews => [newReview, ...prevReviews]);

      // Update the stats
      const newRating = newReview.rating;
      setStats(prevStats => {
        const newDistribution = [...prevStats.ratingDistribution];
        newDistribution[newRating - 1] += 1;

        const newCount = prevStats.reviewCount + 1;
        const newAverage = ((prevStats.averageRating * prevStats.reviewCount) + newRating) / newCount;

        return {
          reviewCount: newCount,
          averageRating: newAverage,
          ratingDistribution: newDistribution
        };
      });

      // Also refresh reviews in the background to ensure everything is in sync
      fetchReviews(review.contentId, review.contentType).catch(err =>
        console.error('Error refreshing reviews after adding new review:', err)
      );

      toast.success('Review added successfully');
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error adding review:', err);
      toast.error(err instanceof Error ? err.message : 'Failed to add review');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const likeReview = async (reviewId: string) => {
    try {
      const response = await fetch(`/api/reviews/${reviewId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'like' }),
      });

      if (!response.ok) {
        throw new Error('Failed to like review');
      }

      const data = await response.json();

      // Update the review in the state
      setReviews(prevReviews =>
        prevReviews.map(review =>
          review._id === reviewId ? { ...review, likes: data.review.likes } : review
        )
      );
    } catch (err) {
      console.error('Error liking review:', err);
      toast.error('Failed to like review');
    }
  };

  const dislikeReview = async (reviewId: string) => {
    try {
      const response = await fetch(`/api/reviews/${reviewId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'dislike' }),
      });

      if (!response.ok) {
        throw new Error('Failed to dislike review');
      }

      const data = await response.json();

      // Update the review in the state
      setReviews(prevReviews =>
        prevReviews.map(review =>
          review._id === reviewId ? { ...review, dislikes: data.review.dislikes } : review
        )
      );
    } catch (err) {
      console.error('Error disliking review:', err);
      toast.error('Failed to dislike review');
    }
  };

  const deleteReview = async (reviewId: string, userId: string, isAdmin: boolean = false): Promise<boolean> => {
    try {
      // Find the review before deleting it to update stats
      const reviewToDelete = reviews.find(review => review._id === reviewId);

      if (!reviewToDelete) {
        throw new Error('Review not found');
      }

      const response = await fetch(`/api/reviews/${reviewId}?userId=${userId}&isAdmin=${isAdmin}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete review');
      }

      // Remove the review from the state
      setReviews(prevReviews => prevReviews.filter(review => review._id !== reviewId));

      // Update stats immediately
      setStats(prevStats => {
        // Get the rating of the deleted review
        const deletedRating = reviewToDelete.rating;

        // Update distribution
        const newDistribution = [...prevStats.ratingDistribution];
        newDistribution[deletedRating - 1] = Math.max(0, newDistribution[deletedRating - 1] - 1);

        // Update count and average
        const newCount = Math.max(0, prevStats.reviewCount - 1);

        // Calculate new average, handling the case where all reviews are deleted
        let newAverage = 0;
        if (newCount > 0) {
          // Remove the deleted rating from the total and recalculate
          newAverage = ((prevStats.averageRating * prevStats.reviewCount) - deletedRating) / newCount;
        }

        return {
          reviewCount: newCount,
          averageRating: newAverage,
          ratingDistribution: newDistribution
        };
      });

      return true;
    } catch (err) {
      console.error('Error deleting review:', err);
      toast.error('Failed to delete review');
      return false;
    }
  };

  const value = {
    reviews,
    stats,
    loading,
    error,
    page,
    totalPages,
    fetchReviews,
    addReview,
    likeReview,
    dislikeReview,
    deleteReview,
    setReviews,
    setStats,
  };

  return <ReviewContext.Provider value={value}>{children}</ReviewContext.Provider>;
}

export function useReviews() {
  const context = useContext(ReviewContext);
  if (context === undefined) {
    throw new Error('useReviews must be used within a ReviewProvider');
  }
  return context;
}

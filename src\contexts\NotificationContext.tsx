'use client';

import React, { createContext, useContext, useState, useEffect, useRef, useCallback, ReactNode } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/ui/use-toast';
import { NotificationType } from '@/models/Notification';
import { pusherClient } from '@/lib/pusher-client';

// Define the notification interface
export interface Notification {
  _id: string;
  type: NotificationType;
  title: string;
  message: string;
  contentId?: string;
  contentType?: 'movie' | 'show';
  image?: string;
  read: boolean;
  createdAt: string;
  expiresAt?: string;
}

// Define the context interface
interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  loading: boolean;
  error: string | null;
  fetchNotifications: () => Promise<void>;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (id: string) => Promise<void>;
}

// Create the context with default values
const NotificationContext = createContext<NotificationContextType>({
  notifications: [],
  unreadCount: 0,
  loading: false,
  error: null,
  fetchNotifications: async () => {},
  markAsRead: async () => {},
  markAllAsRead: async () => {},
  deleteNotification: async () => {},
});

// Provider component
export function NotificationProvider({ children }: { children: ReactNode }) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated, user } = useAuth();
  const { toast } = useToast();

  // Use refs to track state without triggering re-renders
  const isFetchingRef = useRef(false);
  const lastFetchTimeRef = useRef(0);
  const hasFetchedRef = useRef(false);
  const FETCH_COOLDOWN = 60000; // 1 minute cooldown

  // Fetch notifications with built-in cooldown and safeguards
  const fetchNotifications = useCallback(async (force = false) => {
    // Skip if not authenticated or no user ID
    if (!isAuthenticated || !user?.id) return;

    // Skip if already fetching
    if (isFetchingRef.current) return;

    // Check cooldown unless forced
    const now = Date.now();
    const timeSinceLastFetch = now - lastFetchTimeRef.current;
    if (!force && hasFetchedRef.current && timeSinceLastFetch < FETCH_COOLDOWN) {
      return;
    }

    // Set fetching state
    isFetchingRef.current = true;
    setLoading(true);
    setError(null);

    try {
      console.log(`Fetching notifications for user ${user.id}`);
      const response = await fetch(`/api/notifications?userId=${user.id}`, {
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch notifications');
      }

      const data = await response.json();
      console.log(`Received ${data.notifications?.length || 0} notifications, unread count: ${data.unreadCount || 0}`);

      setNotifications(data.notifications || []);
      setUnreadCount(data.unreadCount || 0);

      // Update tracking refs
      lastFetchTimeRef.current = now;
      hasFetchedRef.current = true;
    } catch (err) {
      setError('Failed to load notifications');
      console.error('Error fetching notifications:', err);
    } finally {
      setLoading(false);
      isFetchingRef.current = false;
    }
  }, [isAuthenticated, user?.id]);

  // Mark a notification as read
  const markAsRead = useCallback(async (id: string) => {
    if (!user?.id) return;

    try {
      const response = await fetch(`/api/notifications/${id}?userId=${user.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: user.id, read: true }),
      });

      if (!response.ok) {
        throw new Error('Failed to mark notification as read');
      }

      // Update local state
      setNotifications(prev =>
        prev.map(notification =>
          notification._id === id
            ? { ...notification, read: true }
            : notification
        )
      );

      // Update unread count
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (err) {
      console.error('Error marking notification as read:', err);
      toast({
        title: 'Error',
        description: 'Failed to mark notification as read',
        variant: 'destructive',
      });
    }
  }, [user?.id, toast]);

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    if (!user?.id) return;

    try {
      const response = await fetch(`/api/notifications/mark-all-read?userId=${user.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: user.id }),
      });

      if (!response.ok) {
        throw new Error('Failed to mark all notifications as read');
      }

      // Update local state
      setNotifications(prev =>
        prev.map(notification => ({ ...notification, read: true }))
      );

      // Reset unread count
      setUnreadCount(0);

      toast({
        title: 'Success',
        description: 'All notifications marked as read',
      });
    } catch (err) {
      console.error('Error marking all notifications as read:', err);
      toast({
        title: 'Error',
        description: 'Failed to mark all notifications as read',
        variant: 'destructive',
      });
    }
  }, [user?.id, toast]);

  // Delete a notification
  const deleteNotification = useCallback(async (id: string) => {
    if (!user?.id) return;

    try {
      const response = await fetch(`/api/notifications/${id}?userId=${user.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete notification');
      }

      // Check if the notification was unread
      const wasUnread = notifications.find(n => n._id === id)?.read === false;

      // Update local state
      setNotifications(prev => prev.filter(notification => notification._id !== id));

      // Update unread count if needed
      if (wasUnread) {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    } catch (err) {
      console.error('Error deleting notification:', err);
      toast({
        title: 'Error',
        description: 'Failed to delete notification',
        variant: 'destructive',
      });
    }
  }, [user?.id, notifications, toast]);

  // Set up Pusher subscription for real-time notifications
  useEffect(() => {
    // Only run in browser environment and when authenticated
    if (typeof window === 'undefined' || !isAuthenticated || !user?.id) return;

    console.log(`Setting up Pusher subscriptions for user ${user.id}`);

    // Only subscribe to the global notifications channel
    // This is more efficient for the Pusher free plan
    console.log('Subscribing to global-notifications channel');
    const globalChannel = pusherClient.subscribe('global-notifications');

    // Handle new notification event with improved data
    globalChannel.bind('new-notification', (data: {
      message: string,
      timestamp: string,
      notificationId?: string,
      title?: string,
      type?: string,
      count?: number,
      fullNotification?: Notification,
      showToast?: boolean
    }) => {
      console.log('Received new notification event:', data);

      // If we have the full notification data, add it directly to state
      if (data.fullNotification) {
        console.log('Adding full notification directly to state:', data.fullNotification);
        setNotifications(prev => {
          // Check if we already have this notification
          const exists = prev.some(n => n._id === data.fullNotification?._id);
          if (exists) {
            return prev;
          }
          // Add the new notification to the beginning of the array
          // Use type assertion to ensure TypeScript knows this is a valid Notification
          return [data.fullNotification as Notification, ...prev];
        });

        // Update unread count
        setUnreadCount(prev => prev + 1);
      } else {
        // Otherwise, fetch the latest notifications
        fetchNotifications(true);
      }

      // Show a toast notification only if showToast is true or undefined (default behavior)
      // This allows us to control whether to show a toast from the server
      if (data.showToast !== false) {
        toast({
          title: data.title || 'New Notification',
          description: data.count ? `You have ${data.count} new notification${data.count > 1 ? 's' : ''}` : data.message,
          duration: 4000,
        });
      }
    });

    // Handle notification deletion event with improved feedback
    globalChannel.bind('notification-deleted-all', (data: {
      timestamp: string,
      adminId: string,
      deletedCount?: number
    }) => {
      console.log('Received notification deletion event:', data);

      // Clear all notifications when admin deletes them
      // Silently update the UI without showing a toast notification
      setNotifications([]);
      setUnreadCount(0);
    });

    // Handle notification content deletion (when admin deletes a specific notification type)
    globalChannel.bind('notification-content-deleted', (data: {
      timestamp: string,
      adminId: string,
      title: string,
      message: string,
      type: string,
      createdAt: string,
      deletedCount?: number
    }) => {
      console.log('Received notification content deletion event:', data);

      // Remove all notifications with matching content
      setNotifications(prev => {
        const filtered = prev.filter(n =>
          !(n.title === data.title &&
            n.message === data.message &&
            n.type === data.type &&
            new Date(n.createdAt).getTime() === new Date(data.createdAt).getTime())
        );

        // Calculate how many were removed
        const removedCount = prev.length - filtered.length;

        // Update unread count if needed
        if (removedCount > 0) {
          const removedUnread = prev.filter(n =>
            !n.read &&
            n.title === data.title &&
            n.message === data.message &&
            n.type === data.type &&
            new Date(n.createdAt).getTime() === new Date(data.createdAt).getTime()
          ).length;

          if (removedUnread > 0) {
            setUnreadCount(prevCount => Math.max(0, prevCount - removedUnread));
          }
        }

        return filtered;
      });

      // Silently update the UI without showing a toast notification
      // This prevents unnecessary popups when notifications are deleted
    });

    // Set up user-specific channel for more reliable notification delivery
    if (user?.id) {
      console.log(`Subscribing to user-specific channel: user-${user.id}`);
      const userChannel = pusherClient.subscribe(`user-${user.id}`);

      // Handle new notifications on user-specific channel
      userChannel.bind('new-notification', (data: {
        message: string,
        timestamp: string,
        notificationId?: string,
        title?: string,
        type?: string,
        fullNotification?: Notification,
        showToast?: boolean
      }) => {
        console.log('Received user-specific notification event:', data);

        // If we have the full notification data, add it directly to state
        if (data.fullNotification) {
          console.log('Adding full notification directly to state from user channel:', data.fullNotification);
          setNotifications(prev => {
            // Check if we already have this notification
            const exists = prev.some(n => n._id === data.fullNotification?._id);
            if (exists) {
              return prev;
            }
            // Add the new notification to the beginning of the array
            return [data.fullNotification as Notification, ...prev];
          });

          // Update unread count
          setUnreadCount(prev => prev + 1);

          // Show a toast notification only if showToast is true or undefined (default behavior)
          if (data.showToast !== false) {
            toast({
              title: data.title || 'New Notification',
              description: data.message,
              duration: 4000,
            });
          }
        } else {
          // Otherwise, fetch the latest notifications
          fetchNotifications(true);
        }
      });

      // Handle individual notification deletion
      userChannel.bind('notification-deleted', (data: {
        notificationId: string,
        timestamp: string,
        adminId: string
      }) => {
        console.log('Received individual notification deletion event:', data);

        // Remove the deleted notification
        setNotifications(prev => {
          const filtered = prev.filter(n => n._id !== data.notificationId);
          // Update unread count if needed
          const removedNotification = prev.find(n => n._id === data.notificationId);
          if (removedNotification && !removedNotification.read) {
            setUnreadCount(prevCount => Math.max(0, prevCount - 1));
          }
          return filtered;
        });
      });
    }

    // Cleanup function
    return () => {
      try {
        // Safely unsubscribe from user channel
        if (user?.id) {
          // Try to unbind events first
          try {
            // Just unsubscribe directly - the unbind will happen automatically
            pusherClient.unsubscribe(`user-${user.id}`);
          } catch (e) {
            console.warn('Error unsubscribing from user channel:', e);
          }
        }

        // Safely unsubscribe from global channel
        try {
          globalChannel.unbind('new-notification');
          globalChannel.unbind('notification-deleted-all');
          globalChannel.unbind('notification-content-deleted');
          pusherClient.unsubscribe('global-notifications');
        } catch (e) {
          console.warn('Error unsubscribing from global channel:', e);
        }
      } catch (error) {
        console.error('Error during Pusher cleanup:', error);
      }
    };
  }, [isAuthenticated, user?.id, toast, fetchNotifications]);

  // Initial fetch on mount or auth change - only once
  useEffect(() => {
    // Only run in browser environment
    if (typeof window === 'undefined') return;

    // Reset state when auth changes
    if (!isAuthenticated) {
      setNotifications([]);
      setUnreadCount(0);
      hasFetchedRef.current = false;
      return;
    }

    // Only fetch if authenticated, has user ID, and hasn't fetched yet
    if (isAuthenticated && user?.id && !hasFetchedRef.current) {
      fetchNotifications(true);
    }
  }, [isAuthenticated, user?.id, fetchNotifications]);

  // Provide the context value
  const value = {
    notifications,
    unreadCount,
    loading,
    error,
    fetchNotifications: useCallback((force = true) => fetchNotifications(force), [fetchNotifications]),
    markAsRead,
    markAllAsRead,
    deleteNotification,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
}

// Custom hook to use the notification context
export function useNotifications() {
  const context = useContext(NotificationContext);
  return context;
}

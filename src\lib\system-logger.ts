import mongoose, { Document } from 'mongoose';
import SystemLog, { ISystemLog } from '@/models/SystemLog';
import { ensureMongooseConnection } from './mongoose';

// Cache for system logs to reduce database queries
const logsCache = {
  data: null as any,
  timestamp: 0,
  cacheDuration: 60000, // 1 minute cache
};

/**
 * Log a system event
 * @param level Log level (info, warning, error, debug)
 * @param message Log message
 * @param source Source of the log (e.g., 'api', 'database', 'auth')
 * @param details Optional additional details
 */
export async function logSystemEvent(
  level: 'info' | 'warning' | 'error' | 'debug',
  message: string,
  source: string,
  details?: Record<string, any>
) {
  try {
    // Skip logging startup and shutdown events
    if (message.toLowerCase().includes('startup') || message.toLowerCase().includes('shutdown')) {
      console.log(`System ${message} event not logged (disabled)`);
      return true;
    }

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Create log
    await SystemLog.create({
      level,
      message,
      source,
      details,
      timestamp: new Date()
    });

    // Invalidate cache when a new log is added
    logsCache.data = null;

    return true;
  } catch (error) {
    console.error('Error logging system event:', error);
    return false;
  }
}

/**
 * Get system logs
 * @param level Optional log level to filter by
 * @param source Optional source to filter by
 * @param limit Number of logs to return
 * @param skip Number of logs to skip (for pagination)
 */
export async function getSystemLogs(
  level?: 'info' | 'warning' | 'error' | 'debug',
  source?: string,
  limit: number = 50,
  skip: number = 0
) {
  try {
    const now = Date.now();
    const cacheKey = `${level || 'all'}-${source || 'all'}-${limit}-${skip}`;

    // Check if we have valid cached data
    if (logsCache.data &&
        logsCache.data.cacheKey === cacheKey &&
        now - logsCache.timestamp < logsCache.cacheDuration) {
      return logsCache.data.result;
    }

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Build query
    const query: any = {};
    if (level) {
      query.level = level;
    }
    if (source) {
      query.source = source;
    }

    // Get logs
    const logs = await SystemLog.find(query)
      .sort({ timestamp: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count for pagination
    const total = await SystemLog.countDocuments(query);

    const result = {
      logs: logs.map((log: any) => ({
        _id: log._id ? log._id.toString() : 'unknown',
        level: log.level,
        message: log.message,
        source: log.source,
        details: log.details,
        timestamp: log.timestamp
      })),
      pagination: {
        total,
        limit,
        skip
      }
    };

    // Update cache
    logsCache.data = {
      result,
      cacheKey
    };
    logsCache.timestamp = now;

    return result;
  } catch (error) {
    console.error('Error getting system logs:', error);
    throw error;
  }
}

// Convenience methods for different log levels
export const systemLogger = {
  info: (message: string, source: string, details?: Record<string, any>) =>
    logSystemEvent('info', message, source, details),

  warning: (message: string, source: string, details?: Record<string, any>) =>
    logSystemEvent('warning', message, source, details),

  error: (message: string, source: string, details?: Record<string, any>) =>
    logSystemEvent('error', message, source, details),

  debug: (message: string, source: string, details?: Record<string, any>) =>
    logSystemEvent('debug', message, source, details)
};

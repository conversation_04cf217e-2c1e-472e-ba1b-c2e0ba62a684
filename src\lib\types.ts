import { Document, Types } from 'mongoose';

// Mongoose document with _id typing
export interface MongoDocument extends Document {
  _id: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

// User related types
export interface UserData {
  googleId?: string;
  email: string;
  password?: string;
  name: string;
  givenName?: string;
  familyName?: string;
  picture?: string;
  role?: 'user' | 'admin' | 'superadmin';
  watchHistory?: Array<{
    contentId: string;
    progress: number;
    lastWatched: Date;
  }>;
  preferences?: {
    theme?: 'light' | 'dark' | 'system';
    language?: string;
    playback?: {
      videoQuality?: 'auto' | 'low' | 'medium' | 'high' | 'ultra';
      subtitlesEnabled?: boolean;
      autoplayEnabled?: boolean;
    };
    notifications?: {
      newContent?: boolean;
      recommendations?: boolean;
      updates?: boolean;
      marketing?: boolean;
    };
    privacy?: {
      shareWatchHistory?: boolean;
      allowRecommendations?: boolean;
    };
  };
  subscription?: {
    plan?: 'free' | 'basic' | 'premium' | 'family';
    status?: 'active' | 'canceled' | 'expired' | 'pending';
    renewalDate?: Date;
  };
}

export interface UserMethods {
  comparePassword: (candidatePassword: string) => Promise<boolean>;
}

export interface UserDocument extends UserData, MongoDocument, UserMethods {}

// User session for client
export interface UserSession {
  id: string;
  googleId?: string;
  email: string;
  name: string;
  picture?: string;
  role?: 'user' | 'admin' | 'superadmin';
  createdAt: string;
  preferences?: {
    theme?: 'light' | 'dark' | 'system';
    language?: string;
    playback?: {
      videoQuality?: 'auto' | 'low' | 'medium' | 'high' | 'ultra';
      subtitlesEnabled?: boolean;
      autoplayEnabled?: boolean;
    };
    notifications?: {
      newContent?: boolean;
      recommendations?: boolean;
      updates?: boolean;
      marketing?: boolean;
    };
    privacy?: {
      shareWatchHistory?: boolean;
      allowRecommendations?: boolean;
    };
  };
  subscription?: {
    plan?: 'free' | 'basic' | 'premium' | 'family';
    status?: 'active' | 'canceled' | 'expired' | 'pending';
    renewalDate?: string;
  };
}

// Profile related types
export interface ProfileData {
  userId: Types.ObjectId;
  name: string;
  avatar: string;
  isKids: boolean;
  isPrimary: boolean;
  preferences: {
    language: string;
    maturityLevel: 'kids' | 'teen' | 'adult';
    autoplayEnabled: boolean;
    subtitlesEnabled: boolean;
    subtitlesLanguage: string;
  };
  watchHistory: Array<{
    contentId: string;
    progress: number;
    lastWatched: Date;
  }>;
  favoriteGenres: string[];
  myList: string[];
}

export interface ProfileDocument extends ProfileData, MongoDocument {}

// Client-side profile type (for use in components)
export interface ProfileSession {
  id: string;
  name: string;
  avatar: string;
  isKids: boolean;
  isPrimary: boolean;
}
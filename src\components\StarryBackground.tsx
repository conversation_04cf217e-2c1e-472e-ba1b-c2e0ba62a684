'use client';

import { useEffect, useState, memo, useMemo } from 'react';

// Performance-optimized star configuration
const STAR_COUNT_DESKTOP = 150;
const STAR_COUNT_MOBILE = 75;
const SHOOTING_STAR_COUNT_DESKTOP = 8;
const SHOOTING_STAR_COUNT_MOBILE = 4;

interface Star {
  id: number;
  size: number;
  top: number;
  left: number;
  opacity: number;
  duration: number;
  delay: number;
  color: string;
  glowIntensity: number;
  twinkleType: 'gentle' | 'pulse' | 'fadeout' | 'blink';
}

interface ShootingStar {
  id: number;
  size: number;
  top: number;
  left: number;
  duration: number;
  delay: number;
  length: number;
  angle: number;
}

// Star color palette for varied appearance
const starColors = [
  'rgba(255, 255, 255, 0.9)', // White
  'rgba(230, 245, 255, 0.9)', // Slight blue tint
  'rgba(255, 240, 230, 0.9)', // Slight orange tint
  'rgba(240, 240, 255, 0.9)', // Slight purple tint
  'rgba(200, 220, 255, 0.9)', // Blue-white
  'rgba(255, 220, 180, 0.9)', // Warm tint
];

// Memoized star component for better performance
const StarComponent = memo(({ star }: { star: Star }) => {
  // Different animation styles for each type
  const animationStyle = useMemo(() => {
    switch (star.twinkleType) {
      case 'gentle':
        return `twinkle ${star.duration}s ease-in-out infinite`;
      case 'pulse':
        return `pulse ${star.duration * 0.7}s ease-in-out infinite`;
      case 'fadeout':
        return `fadeout ${star.duration * 1.5}s ease-in-out infinite`;
      case 'blink':
        return `blink ${star.duration * 0.5}s ease-in-out infinite`;
      default:
        return `twinkle ${star.duration}s ease-in-out infinite`;
    }
  }, [star.twinkleType, star.duration]);

  return (
    <div
      className="absolute rounded-full will-change-transform"
      style={{
        width: `${star.size}px`,
        height: `${star.size}px`,
        top: `${star.top}%`,
        left: `${star.left}%`,
        backgroundColor: star.color,
        boxShadow: `0 0 ${star.size * star.glowIntensity}px ${star.color.replace(')', ', 0.8)')}`,
        opacity: star.opacity,
        animation: animationStyle,
        animationDelay: `${star.delay}s`,
      }}
    />
  );
});

StarComponent.displayName = 'StarComponent';

// Memoized shooting star component
const ShootingStarComponent = memo(({ star }: { star: ShootingStar }) => (
  <div 
    className="absolute will-change-transform"
    style={{
      top: `${star.top}%`,
      left: `${star.left}%`,
    }}
  >
    {/* Star head */}
    <div
      className="absolute rounded-full will-change-transform"
      style={{
        width: `${star.size}px`,
        height: `${star.size}px`,
        background: 'linear-gradient(to right, rgba(255, 255, 255, 0.95), rgba(220, 240, 255, 0.95))',
        boxShadow: `0 0 ${star.size * 6}px rgba(255, 255, 255, 0.7)`,
        animation: `shootingStar ${star.duration}s cubic-bezier(0.05, 0.5, 0.25, 1) infinite`,
        animationDelay: `${star.delay}s`,
        zIndex: 2
      }}
    />
    
    {/* Star trail */}
    <div
      className="absolute will-change-transform"
      style={{
        height: '2px',
        width: '0',
        borderRadius: '4px',
        background: 'linear-gradient(to right, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8) 30%, rgba(59, 130, 246, 0.4) 80%, transparent)',
        transform: `rotate(${star.angle}deg)`,
        transformOrigin: 'left center',
        animation: `shootingStarTrail ${star.duration}s cubic-bezier(0.05, 0.5, 0.25, 1) infinite`,
        animationDelay: `${star.delay}s`,
        zIndex: 1,
        maxWidth: `${star.length}px`
      }}
    />
  </div>
));

ShootingStarComponent.displayName = 'ShootingStarComponent';

// Generate stars with varied animations
function generateStars(count: number): Star[] {
  const twinkleTypes: Array<Star['twinkleType']> = ['gentle', 'pulse', 'fadeout', 'blink'];
  const generatedStars: Star[] = [];
  
  for (let i = 0; i < count; i++) {
    const size = Math.random() * 2.5 + 0.5;
    // Larger stars get more glow
    const glowIntensity = size < 1.5 ? 
                          Math.random() * 2 + 1 : // Smaller stars
                          Math.random() * 3 + 2;  // Larger stars
    
    generatedStars.push({
      id: i,
      size,
      top: Math.random() * 100,
      left: Math.random() * 100,
      opacity: Math.random() * 0.7 + 0.3,
      duration: Math.random() * 5 + 3,
      delay: Math.random() * 8,
      color: starColors[Math.floor(Math.random() * starColors.length)],
      glowIntensity,
      twinkleType: twinkleTypes[Math.floor(Math.random() * twinkleTypes.length)]
    });
  }
  return generatedStars;
}

function generateShootingStars(count: number): ShootingStar[] {
  const generatedShootingStars: ShootingStar[] = [];
  for (let i = 0; i < count; i++) {
    generatedShootingStars.push({
      id: i,
      size: Math.random() * 2 + 1.5,
      top: Math.random() * 70,
      left: Math.random() * 30 - 20,
      duration: Math.random() * 8 + 10, // Slower for smoother appearance
      delay: Math.random() * 30 + i * 5,
      length: Math.random() * 120 + 80,
      angle: Math.random() * 15 + 310
    });
  }
  return generatedShootingStars;
}

export default function StarryBackground() {
  const [stars, setStars] = useState<Star[]>([]);
  const [shootingStars, setShootingStars] = useState<ShootingStar[]>([]);
  const [isMobile, setIsMobile] = useState(false);
  
  useEffect(() => {
    // Check if we're on mobile
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    // Generate stars based on device
    const starCount = isMobile ? STAR_COUNT_MOBILE : STAR_COUNT_DESKTOP;
    const shootingStarCount = isMobile ? SHOOTING_STAR_COUNT_MOBILE : SHOOTING_STAR_COUNT_DESKTOP;
    
    setStars(generateStars(starCount));
    setShootingStars(generateShootingStars(shootingStarCount));
    
    return () => window.removeEventListener('resize', checkMobile);
  }, [isMobile]);

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Fixed stars */}
      <div className="absolute inset-0 z-10">
        {stars.map((star) => (
          <StarComponent key={`star-${star.id}`} star={star} />
        ))}
      </div>

      {/* Shooting stars */}
      <div className="absolute inset-0 z-20">
        {shootingStars.map((star) => (
          <ShootingStarComponent key={`shooting-${star.id}`} star={star} />
        ))}
      </div>
    </div>
  );
} 
import { useState, useEffect, useCallback, useRef } from 'react';
import { toast } from '@/components/ui/use-toast';
import { subDays } from 'date-fns';

export interface VisitorFilters {
  page?: number;
  limit?: number;
  search?: string;
  dateFrom?: string | null;
  dateTo?: string | null;
  country?: string;
  device?: string;
  browser?: string;
  os?: string;
  converted?: boolean | null;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  compareToPrevious?: boolean;
}

export interface VisitorStats {
  totalVisitors: number;
  totalVisits: number;
  totalPagesViewed: number;
  convertedCount: number;
  averageVisits: number;
  averagePagesViewed: number;
  percentNewVisitors: number;
  bounceRate: number;
  averageSessionTime: number;
  growth?: {
    visitors?: number;
    visits?: number;
    pagesViewed?: number;
    signups?: number;
  };
}

export interface VisitorDistribution {
  _id: string;
  count: number;
}

// Define a Visitor interface to replace any[]
export interface Visitor {
  _id: string;
  visitorId: string;
  nickname?: string;
  ipAddress?: string;
  userAgent?: string;
  browser?: string;
  os?: string;
  device?: string;
  country?: string;
  countryCode?: string;
  region?: string;
  city?: string;
  timezone?: string;
  latitude?: number;
  longitude?: number;
  firstVisit: string | Date;
  lastVisit: string | Date;
  visitCount: number;
  pagesViewed: number;
  convertedToUser?: boolean;
  convertedUserId?: string;
  referrer?: string;
  [key: string]: unknown; // For any additional properties
}

export interface VisitorInsights {
  // Time series data for hourly and daily patterns
  hourlyActivity: Array<{
    hour: number;
    visits: number;
  }>;
  weekdayActivity: Array<{
    day: string;
    visits: number;
  }>;

  // Retention data
  retention: {
    cohort: string;
    rates: number[];
  }[];

  // Bounce rate and session metrics
  bounceRate: number;
  averageSessionTime: number;

  // Page engagement metrics
  topLandingPages: Array<{
    page: string;
    visits: number;
    bounceRate: number;
  }>;

  // Conversion funnel
  conversionSteps: Array<{
    step: string;
    visitors: number;
    conversionRate: number;
  }>;

  // Anomaly detection
  anomalies: Array<{
    date: string;
    metric: string;
    value: number;
    expected: number;
    percentChange: number;
  }>;
}

export interface EngagementMetrics {
  // Session metrics
  sessionDuration: {
    average: number;
    byDate: Array<{
      date: string;
      value: number;
    }>;
    distribution: Array<{
      range: string;
      count: number;
    }>;
  };

  // Pages per visit
  pagesPerVisit: {
    average: number;
    byDate: Array<{
      date: string;
      value: number;
    }>;
    distribution: Array<{
      range: string;
      count: number;
    }>;
  };

  // Visitor loyalty
  visitorLoyalty: {
    newVsReturning: {
      new: number;
      returning: number;
    };
    visitFrequency: Array<{
      visits: string;
      count: number;
    }>;
  };

  // Retention data
  retention: {
    overall: number;
    byWeek: Array<{
      week: string;
      rate: number;
    }>;
  };
}

export interface VisitorData {
  visitors: Visitor[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  stats: VisitorStats;
  distributions: {
    device: VisitorDistribution[];
    browser: VisitorDistribution[];
    os: VisitorDistribution[];
    country: VisitorDistribution[];
  };
  dailyVisitors: {
    _id: string;
    count: number;
  }[];
  insights?: VisitorInsights;
  engagement?: EngagementMetrics;
  previousPeriod?: {
    stats: VisitorStats;
    dailyVisitors: {
      _id: string;
      count: number;
    }[];
  };
}

// Default empty data structure
const emptyData: VisitorData = {
  visitors: [],
  pagination: { page: 1, limit: 10, total: 0, pages: 1 },
  stats: {
    totalVisitors: 0,
    totalVisits: 0,
    totalPagesViewed: 0,
    convertedCount: 0,
    averageVisits: 0,
    averagePagesViewed: 0,
    percentNewVisitors: 0,
    bounceRate: 0,
    averageSessionTime: 0,
    growth: {
      visitors: 0,
      visits: 0,
      pagesViewed: 0,
      signups: 0
    }
  },
  distributions: {
    device: [],
    browser: [],
    os: [],
    country: []
  },
  dailyVisitors: [],
  insights: {
    hourlyActivity: [],
    weekdayActivity: [],
    retention: [],
    bounceRate: 0,
    averageSessionTime: 0,
    topLandingPages: [],
    conversionSteps: [],
    anomalies: []
  },
  engagement: {
    sessionDuration: {
      average: 0,
      byDate: [],
      distribution: []
    },
    pagesPerVisit: {
      average: 0,
      byDate: [],
      distribution: []
    },
    visitorLoyalty: {
      newVsReturning: { new: 0, returning: 0 },
      visitFrequency: []
    },
    retention: {
      overall: 0,
      byWeek: []
    }
  }
};

export function useVisitorData(filters: VisitorFilters = {}) {
  const [data, setData] = useState<VisitorData>(emptyData);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);

  // Use refs to prevent unnecessary re-renders and API calls
  const isInitialMount = useRef(true);
  const lastFetchTime = useRef<number>(0);
  const debounceTimer = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Cache for storing previous requests
  const cache = useRef<Map<string, { data: VisitorData, timestamp: number }>>(new Map());

  // Cache TTL in milliseconds (5 minutes)
  const CACHE_TTL = 5 * 60 * 1000;

  // Stringify filters to detect changes
  const filtersString = JSON.stringify(filters);

  // Check authentication state first
  useEffect(() => {
    const checkAuth = () => {
      const userId = localStorage.getItem('userId');
      const userJson = localStorage.getItem('user');

      if (userId && userJson) {
        try {
          const userData = JSON.parse(userJson);
          const isUserAdmin = userData.role === 'admin' || userData.role === 'superadmin';
          setIsAuthenticated(isUserAdmin);
        } catch (e) {
          console.error('Error parsing user data:', e);
          setIsAuthenticated(false);
        }
      } else {
        setIsAuthenticated(false);
      }
    };

    checkAuth();

    // Listen for storage changes that might affect authentication
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'userId' || e.key === 'user') {
        checkAuth();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  const fetchVisitors = useCallback(async (force = false) => {
    // Check authentication first
    const userId = localStorage.getItem('userId');
    if (!userId) {
      console.log('Cannot fetch visitor data: No userId in localStorage');
      setError('Authentication required');
      setData(emptyData);
      setIsLoading(false);
      return;
    }

    // Retrieve user data to verify admin status
    const userJson = localStorage.getItem('user');
    if (!userJson) {
      console.log('Cannot fetch visitor data: No user data in localStorage');
      setError('Authentication required');
      setData(emptyData);
      setIsLoading(false);
      return;
    }

    try {
      const userData = JSON.parse(userJson);
      const isUserAdmin = userData.role === 'admin' || userData.role === 'superadmin';
      if (!isUserAdmin) {
        console.log('Cannot fetch visitor data: User is not an admin');
        setError('Unauthorized access');
        setData(emptyData);
        setIsLoading(false);
        return;
      }
    } catch (e) {
      console.error('Error parsing user data:', e);
      setError('Authentication error');
      setData(emptyData);
      setIsLoading(false);
      return;
    }

    // Prevent fetching too frequently
    const now = Date.now();
    if (!force && now - lastFetchTime.current < 1000) {
      console.log('Throttling API call - too frequent');
      return;
    }

    // Check cache first
    const cacheKey = filtersString;
    const cachedItem = cache.current.get(cacheKey);

    if (!force && cachedItem && (now - cachedItem.timestamp) < CACHE_TTL) {
      console.log('Using cached data for visitor stats');
      setData(cachedItem.data);
      setIsLoading(false);
      return;
    }

    // Cancel any in-flight requests
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create a new abort controller for this request
    abortControllerRef.current = new AbortController();

    // Build query string
    const queryParams = new URLSearchParams();

    // Get user ID from localStorage
    if (userId) {
      queryParams.append('userId', userId);
    }

    // Add pagination parameters
    if (filters.page) queryParams.append('page', filters.page.toString());
    if (filters.limit) queryParams.append('limit', filters.limit.toString());

    // Add filter parameters
    if (filters.search) queryParams.append('search', filters.search);
    if (filters.dateFrom) queryParams.append('dateFrom', filters.dateFrom);
    if (filters.dateTo) queryParams.append('dateTo', filters.dateTo);
    if (filters.country) queryParams.append('country', filters.country);
    if (filters.device) queryParams.append('device', filters.device);
    if (filters.browser) queryParams.append('browser', filters.browser);
    if (filters.os) queryParams.append('os', filters.os);
    if (filters.converted !== null && filters.converted !== undefined) {
      queryParams.append('converted', filters.converted.toString());
    }

    // Add sort parameters
    if (filters.sortBy) queryParams.append('sortBy', filters.sortBy);
    if (filters.sortOrder) queryParams.append('sortOrder', filters.sortOrder);

    // Add comparison parameter
    if (filters.compareToPrevious) queryParams.append('compareToPrevious', 'true');

    // Always include insights and engagement data
    queryParams.append('includeInsights', 'true');
    queryParams.append('includeEngagement', 'true');

    // Set loading state
    setIsLoading(true);
    lastFetchTime.current = now;

    // Make API request
    try {
      const response = await fetch(`/api/admin/visitors?${queryParams.toString()}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
        },
        signal: abortControllerRef.current.signal
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to fetch visitor data (${response.status})`);
      }

      const responseData = await response.json();

      // Cache the response
      cache.current.set(cacheKey, {
        data: responseData,
        timestamp: now
      });

      setData(responseData);
      setError(null);
    } catch (err) {
      // Ignore abort errors
      if (err instanceof Error && err.name === 'AbortError') {
        console.log('Request was aborted');
        return;
      }

      console.error('Error fetching visitor data:', err);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      toast({
        title: 'Error',
        description: 'Failed to fetch visitor data',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  }, [
    filtersString,
    CACHE_TTL,
    filters.page,
    filters.limit,
    filters.search,
    filters.dateFrom,
    filters.dateTo,
    filters.country,
    filters.device,
    filters.browser,
    filters.os,
    filters.converted,
    filters.sortBy,
    filters.sortOrder,
    filters.compareToPrevious
  ]);

  // Use effect for filter changes only (not initial fetch)
  useEffect(() => {
    // Skip the first render to prevent double fetching
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    // Clear any existing timer
    if (debounceTimer.current) {
      clearTimeout(debounceTimer.current);
    }

    // Set debounce timer for search queries to avoid too many API calls
    debounceTimer.current = setTimeout(() => {
      if (isAuthenticated) {
        fetchVisitors();
      }
    }, 300);

    // Clean up on unmount
    return () => {
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [fetchVisitors, isAuthenticated]);

  // Initial data fetch
  useEffect(() => {
    if (isAuthenticated) {
      // Force fetch on initial load to ensure we have fresh data
      fetchVisitors(true);

      // Set up periodic refresh every 2 minutes
      const refreshInterval = setInterval(() => {
        fetchVisitors(true);
      }, 2 * 60 * 1000);

      // Add event listener for page visibility changes
      const handleVisibilityChange = () => {
        if (document.visibilityState === 'visible') {
          console.log('Page became visible, refreshing visitor data');
          fetchVisitors(true);
        }
      };

      document.addEventListener('visibilitychange', handleVisibilityChange);

      return () => {
        clearInterval(refreshInterval);
        document.removeEventListener('visibilitychange', handleVisibilityChange);
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }
      };
    }
  }, [fetchVisitors, isAuthenticated]);

  /**
   * Helper function to fetch data for a specific time range
   */
  const fetchTimeRange = useCallback((from: Date | null, to: Date | null, compareToPrevious = true) => {
    // Check authentication first
    const userId = localStorage.getItem('userId');
    if (!userId) {
      console.log('Cannot fetch time range data: No userId in localStorage');
      setError('Authentication required');
      setData(emptyData);
      setIsLoading(false);
      return;
    }

    // Create a copy of the current filters
    const newFilters: VisitorFilters = { ...filters };

    // Set date range
    newFilters.dateFrom = from ? from.toISOString() : null;
    newFilters.dateTo = to ? to.toISOString() : null;
    newFilters.compareToPrevious = compareToPrevious;

    // Set loading state
    setIsLoading(true);

    // Build filters string for cache key
    const newFiltersString = JSON.stringify(newFilters);

    // Check cache
    const now = Date.now();
    const cachedItem = cache.current.get(newFiltersString);

    if (cachedItem && (now - cachedItem.timestamp) < CACHE_TTL) {
      console.log('Using cached data for time range');
      setData(cachedItem.data);
      setIsLoading(false);
      return;
    }

    // Build query string
    const queryParams = new URLSearchParams();

    // Get user ID from localStorage
    if (userId) {
      queryParams.append('userId', userId);
    }

    // Add all filters
    if (newFilters.page) queryParams.append('page', newFilters.page.toString());
    if (newFilters.limit) queryParams.append('limit', newFilters.limit.toString());
    if (newFilters.dateFrom) queryParams.append('dateFrom', newFilters.dateFrom);
    if (newFilters.dateTo) queryParams.append('dateTo', newFilters.dateTo);
    if (newFilters.compareToPrevious) queryParams.append('compareToPrevious', 'true');
    queryParams.append('includeInsights', 'true');
    queryParams.append('includeEngagement', 'true');

    // Cancel any in-flight requests
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create a new abort controller
    abortControllerRef.current = new AbortController();

    // Fetch data
    fetch(`/api/admin/visitors?${queryParams.toString()}`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      },
      signal: abortControllerRef.current.signal
    })
      .then(response => {
        if (!response.ok) {
          return response.json().then(errorData => {
            throw new Error(errorData.error || `Failed to fetch time range data (${response.status})`);
          });
        }
        return response.json();
      })
      .then(responseData => {
        // Cache the response
        cache.current.set(newFiltersString, {
          data: responseData,
          timestamp: now
        });

        setData(responseData);
        setIsLoading(false);
      })
      .catch(err => {
        // Ignore abort errors
        if (err instanceof Error && err.name === 'AbortError') {
          console.log('Time range request was aborted');
          return;
        }

        console.error('Error fetching time range data:', err);
        setError(err instanceof Error ? err.message : 'An unexpected error occurred');
        toast({
          title: 'Error',
          description: 'Failed to fetch time range data',
          variant: 'destructive'
        });
        setIsLoading(false);
      });
  }, [filters, CACHE_TTL]);

  /**
   * Predefined time range helpers
   */
  const fetchLast7Days = useCallback(() => {
    const now = new Date();
    const from = subDays(now, 7);
    fetchTimeRange(from, now);
  }, [fetchTimeRange]);

  const fetchLast30Days = useCallback(() => {
    const now = new Date();
    const from = subDays(now, 30);
    fetchTimeRange(from, now);
  }, [fetchTimeRange]);

  const fetchLast90Days = useCallback(() => {
    const now = new Date();
    const from = subDays(now, 90);
    fetchTimeRange(from, now);
  }, [fetchTimeRange]);

  const fetchYearToDate = useCallback(() => {
    const now = new Date();
    const from = new Date(now.getFullYear(), 0, 1); // January 1st of current year
    fetchTimeRange(from, now);
  }, [fetchTimeRange]);

  const fetchAllTime = useCallback(() => {
    fetchTimeRange(null, null, false);
  }, [fetchTimeRange]);

  return {
    data,
    isLoading,
    error,
    isAuthenticated,
    refetch: fetchVisitors,
    fetchTimeRange,
    fetchLast7Days,
    fetchLast30Days,
    fetchLast90Days,
    fetchYearToDate,
    fetchAllTime
  };
}

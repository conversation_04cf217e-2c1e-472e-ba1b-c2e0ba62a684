# Routing and Navigation Service

## Overview

StreamVista uses Next.js App Router for routing and navigation, implementing a hybrid approach that combines static and dynamic routes with client-side navigation. The system provides seamless navigation between pages, handles dynamic content loading, and supports watch party functionality.

## Route Structure

### Core Routes

```typescript
// Static Routes
/                   // Home page with content discovery
/auth              // Authentication page
/profiles          // Profile selection and management
/search            // Search results page
/settings          // User settings

// Dynamic Routes
/[type]/[id]       // Content details (movie/show)
/watch/[id]        // Content playback
/watch-party/[id]  // Watch party room
```

### Route Handlers

#### Content Routes

```typescript
// app/api/content/route.ts
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const id = searchParams.get('id');
  const type = searchParams.get('type') as 'movie' | 'show';
  
  if (!id || !type) {
    return NextResponse.json({ error: 'Invalid parameters' }, { status: 400 });
  }
  
  try {
    const content = type === 'movie' 
      ? await getMovieDetails(id)
      : await getTVDetails(id);
      
    return NextResponse.json(content);
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch content' }, { status: 500 });
  }
}
```

#### Search Routes

```typescript
// app/api/search/route.ts
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const query = searchParams.get('query');
  const type = searchParams.get('type') as 'movie' | 'show';
  const limit = parseInt(searchParams.get('limit') || '20');
  
  try {
    const results = await search(query, type, limit);
    return NextResponse.json(results);
  } catch (error) {
    return NextResponse.json({ error: 'Search failed' }, { status: 500 });
  }
}
```

## Navigation Components

### Navbar Component

```typescript
interface NavbarProps {
  isLoggedIn?: boolean;
  userProfileImg?: string;
  notificationCount?: number;
  showConnectionStatus?: boolean;
}

export function Navbar({
  isLoggedIn = false,
  userProfileImg,
  notificationCount = 0,
  showConnectionStatus = true
}: NavbarProps) {
  const router = useRouter();
  const pathname = usePathname();
  
  // Navigation state
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  
  // Handle search
  const handleSearch = (query: string) => {
    router.push(`/search?q=${encodeURIComponent(query)}`);
  };
  
  // Handle navigation
  const handleNavigate = (path: string) => {
    setIsMobileMenuOpen(false);
    router.push(path);
  };
}
```

### Navigation Guards

```typescript
// middleware.ts
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const token = request.cookies.get('token');
  
  // Protected routes
  if (
    !token &&
    !pathname.startsWith('/auth') &&
    !pathname.startsWith('/api')
  ) {
    return NextResponse.redirect(new URL('/auth', request.url));
  }
  
  return NextResponse.next();
}

export const config = {
  matcher: [
    '/((?!auth|api|_next/static|_next/image|favicon.ico).*)'
  ]
};
```

## Client-Side Navigation

### Watch Content Navigation

```typescript
function handleContentNavigation(contentId: string, type: 'movie' | 'show') {
  const watchUrl = `/watch/${contentId}?contentType=${type}`;
  
  // Add additional parameters for TV shows
  if (type === 'show') {
    watchUrl += `&season=${season}&episode=${episode}`;
  }
  
  router.push(watchUrl);
}
```

### Watch Party Navigation

```typescript
function handleWatchPartyNavigation(partyId: string, isHost: boolean) {
  const watchUrl = `/watch/${contentId}?mode=party&partyId=${partyId}&isHost=${isHost}`;
  
  if (content.type === 'show') {
    watchUrl += `&season=${season}&episode=${episode}`;
  }
  
  router.push(watchUrl);
}
```

## URL Management

### Query Parameters

```typescript
function useQueryParams<T extends Record<string, string>>() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  
  const setQueryParams = useCallback((params: Partial<T>) => {
    const current = new URLSearchParams(searchParams);
    
    Object.entries(params).forEach(([key, value]) => {
      if (value) {
        current.set(key, value);
      } else {
        current.delete(key);
      }
    });
    
    router.push(`${pathname}?${current.toString()}`);
  }, [pathname, router, searchParams]);
  
  return {
    queryParams: Object.fromEntries(searchParams.entries()) as T,
    setQueryParams
  };
}
```

### URL State Management

```typescript
function useURLState<T>(key: string, defaultValue: T) {
  const { queryParams, setQueryParams } = useQueryParams();
  
  const value = queryParams[key] 
    ? JSON.parse(queryParams[key]) 
    : defaultValue;
  
  const setValue = useCallback((newValue: T) => {
    setQueryParams({
      [key]: JSON.stringify(newValue)
    });
  }, [key, setQueryParams]);
  
  return [value, setValue] as const;
}
```

## Navigation Events

### Route Change Handlers

```typescript
function useRouteEvents() {
  const pathname = usePathname();
  const { setLoading } = useLoading();
  
  useEffect(() => {
    const handleStart = () => setLoading(true);
    const handleComplete = () => setLoading(false);
    
    router.events.on('routeChangeStart', handleStart);
    router.events.on('routeChangeComplete', handleComplete);
    router.events.on('routeChangeError', handleComplete);
    
    return () => {
      router.events.off('routeChangeStart', handleStart);
      router.events.off('routeChangeComplete', handleComplete);
      router.events.off('routeChangeError', handleComplete);
    };
  }, [setLoading]);
  
  // Scroll to top on route change
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);
}
```

## Best Practices

1. **Performance Optimization**
   - Use static generation for frequently accessed pages
   - Implement dynamic imports for large components
   - Cache API responses
   - Optimize images and assets

2. **SEO Considerations**
   - Generate proper metadata for each route
   - Implement canonical URLs
   - Use semantic HTML structure
   - Add structured data when applicable

3. **User Experience**
   - Maintain loading states during navigation
   - Preserve scroll position when appropriate
   - Implement smooth transitions
   - Handle back/forward navigation

4. **Error Handling**
   - Implement proper error boundaries
   - Show meaningful error messages
   - Provide recovery options
   - Handle offline scenarios

5. **Security**
   - Validate route parameters
   - Implement proper access controls
   - Sanitize URL parameters
   - Handle sensitive data appropriately

## Integration Guidelines

1. **New Routes**
   - Follow existing route structure
   - Implement proper metadata
   - Add necessary guards
   - Handle loading states

2. **Navigation Components**
   - Use Next.js Link component
   - Implement proper active states
   - Handle mobile responsiveness
   - Add loading indicators

3. **State Management**
   - Use URL state when appropriate
   - Implement proper caching
   - Handle route transitions
   - Manage loading states
``` 
/**
 * Simple in-memory cache implementation
 * 
 * This provides a basic caching mechanism to reduce database load
 * for frequently accessed data.
 */

interface CacheItem<T> {
  data: T;
  expiry: number;
}

class Cache {
  private cache: Map<string, CacheItem<any>> = new Map();
  
  /**
   * Get an item from the cache
   * @param key Cache key
   * @returns The cached data or null if not found or expired
   */
  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    
    // If item doesn't exist or has expired, return null
    if (!item || item.expiry < Date.now()) {
      if (item) {
        // Clean up expired item
        this.cache.delete(key);
      }
      return null;
    }
    
    return item.data as T;
  }
  
  /**
   * Set an item in the cache
   * @param key Cache key
   * @param data Data to cache
   * @param ttlSeconds Time to live in seconds
   */
  set<T>(key: string, data: T, ttlSeconds: number = 300): void {
    this.cache.set(key, {
      data,
      expiry: Date.now() + (ttlSeconds * 1000)
    });
  }
  
  /**
   * Remove an item from the cache
   * @param key Cache key
   */
  delete(key: string): void {
    this.cache.delete(key);
  }
  
  /**
   * Clear all items from the cache
   */
  clear(): void {
    this.cache.clear();
  }
  
  /**
   * Get the number of items in the cache
   */
  get size(): number {
    return this.cache.size;
  }
  
  /**
   * Clean up expired items
   * Call this periodically to prevent memory leaks
   */
  cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (item.expiry < now) {
        this.cache.delete(key);
      }
    }
  }
}

// Create a singleton instance
export const cache = new Cache();

// Set up periodic cleanup
setInterval(() => {
  cache.cleanup();
}, 60000); // Clean up every minute

/**
 * Wrapper function to get cached data or fetch it if not available
 * @param key Cache key
 * @param fetchFn Function to fetch the data if not in cache
 * @param ttlSeconds Time to live in seconds
 * @returns The data from cache or from the fetch function
 */
export async function getCachedData<T>(
  key: string,
  fetchFn: () => Promise<T>,
  ttlSeconds: number = 300
): Promise<T> {
  // Try to get from cache first
  const cachedData = cache.get<T>(key);
  if (cachedData !== null) {
    return cachedData;
  }
  
  // If not in cache, fetch the data
  const data = await fetchFn();
  
  // Store in cache for future use
  cache.set(key, data, ttlSeconds);
  
  return data;
}

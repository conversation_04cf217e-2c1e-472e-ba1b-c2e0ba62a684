import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { ensureMongooseConnection } from '@/lib/mongoose';
import User, { IUser } from '@/models/User';
import bcrypt from 'bcryptjs';
import { Document, Types } from 'mongoose';
import { logUserActivity } from '@/lib/activity-logger';

export const options: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          // Connect to MongoDB
          await ensureMongooseConnection();

          // Find user by email
          const user = await User.findOne({ email: credentials.email });

          if (!user) {
            return null;
          }

          // Check password
          const isPasswordValid = await bcrypt.compare(
            credentials.password,
            user.password
          );

          if (!isPasswordValid) {
            return null;
          }

          // Get the ID string from MongoDB's ObjectId
          const userId = (user._id as unknown as Types.ObjectId).toString();

          // Log successful login
          try {
            await logUserActivity(
              userId,
              'auth',
              'login',
              `User logged in: ${user.email}`,
              null,
              { method: 'credentials' }
            );
          } catch (logError) {
            console.error('Error logging login activity:', logError);
            // Continue even if logging fails
          }

          // Return user data that should be stored in the session
          return {
            id: userId,
            email: user.email,
            name: user.name,
            image: user.profileImage,
            isAdmin: user.role === 'admin' || user.role === 'superadmin'
          };
        } catch (error) {
          console.error('Auth error:', error);
          return null;
        }
      }
    })
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.isAdmin = user.isAdmin;
      }
      return token;
    },
    async session({ session, token }) {
      if (session.user) {
        session.user.id = token.id;
        session.user.isAdmin = token.isAdmin;
      }
      return session;
    }
  },
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error'
  },
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60 // 30 days
  },
  events: {
    // No longer tracking logout
    async createUser({ user }) {
      try {
        await logUserActivity(
          user.id,
          'auth',
          'signup',
          `New user registered: ${user.email}`,
          null,
          { method: 'credentials' }
        );
      } catch (error) {
        console.error('Error logging user registration activity:', error);
      }
    }
  },
  secret: process.env.NEXTAUTH_SECRET
};

export default options;
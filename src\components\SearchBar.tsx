"use client";

import { useState, useEffect, useRef, useCallback, ChangeEvent, FormEvent, MouseEvent, KeyboardEvent } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, X, Loader2, ImageIcon, Play, Plus, Star, Film, Tv, Info, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { IContent } from '@/data/content';
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge, badgeVariants } from "@/components/ui/badge";
import { useDebounce } from '@/hooks/useDebounce';

interface SearchBarProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function SearchBar({ isOpen, onClose }: SearchBarProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<IContent[]>([]);
  const [popularSearches, setPopularSearches] = useState<IContent[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showLoadingIndicator, setShowLoadingIndicator] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("all");
  const searchInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();
  const searchCacheRef = useRef<Map<string, IContent[]>>(new Map());
  const abortControllerRef = useRef<AbortController | null>(null);
  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const [watchlistItems, setWatchlistItems] = useState<Set<string>>(new Set());

  const getImageUrl = useCallback((path: string | undefined, title?: string): string => {
    if (!path || path === 'null' || path === 'undefined' || path.trim() === '') {
      return 'https://placehold.co/300x450/171717/CCCCCC?text=No+Image';
    }
    if (!path.startsWith('http')) {
      return `https://image.tmdb.org/t/p/w342${path.startsWith('/') ? path : `/${path}`}`;
    }
    return path;
  }, []);

  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      setTimeout(() => searchInputRef.current?.focus(), 100);
      // Prevent background scrolling
      document.body.style.overflow = 'hidden';
    }
    if (!isOpen) {
      setSearchQuery('');
      // Restore scrolling when search is closed
      document.body.style.overflow = '';
    }
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      // Ensure scrolling is restored on unmount
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  useEffect(() => {
    if (isOpen && popularSearches.length === 0) {
      const fetchPopularContent = async () => {
        if (searchCacheRef.current.has('popular')) {
          setPopularSearches(searchCacheRef.current.get('popular') || []);
          return;
        }
        const response = await fetch('/api/search?query=popular&limit=20');
        if (response.ok) {
          const data = await response.json();
          if (data.results && Array.isArray(data.results)) {
            setPopularSearches(data.results);
            searchCacheRef.current.set('popular', data.results);
          }
        }
      };
      fetchPopularContent();
    }
  }, [isOpen, popularSearches.length]);

  // Effect to manage loading indicator
  useEffect(() => {
    // Show loading indicator immediately when searching starts
    if (isSearching) {
      setShowLoadingIndicator(true);
    } else {
      // Always ensure loading indicator is removed when not searching
      // No delay needed here to avoid persistent spinner
      setShowLoadingIndicator(false);
    }

    // Cleanup function to ensure indicator is removed when component unmounts
    return () => {
      setShowLoadingIndicator(false);
    };
  }, [isSearching]);

  const performSearch = useCallback(
    async (query: string) => {
      if (!query.trim()) {
        setSearchResults([]);
        return;
      }

      const normalizedQuery = query.toLowerCase().trim().replace(/^the\s+/, '');
      const cacheKey = normalizedQuery;

      if (searchCacheRef.current.has(cacheKey)) {
        console.log(`[SearchBar] Using cached results for "${query}"`);
        setSearchResults(searchCacheRef.current.get(cacheKey) || []);
        return;
      }

      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      abortControllerRef.current = new AbortController();
      const signal = abortControllerRef.current.signal;

      setIsSearching(true);
      setHasSearched(false);

      try {
        const startTime = Date.now();

        // Ensure search takes at least 800ms to avoid flickering UI
        const minSearchTime = 800;

        // Try exact match first
        const exactResponse = await fetch(`/api/search?query="${encodeURIComponent(query)}"&limit=30`, { signal });
        if (exactResponse.ok) {
          const exactData = await exactResponse.json();
          if (exactData.results && exactData.results.length > 0) {
            const uniqueResults = deduplicateResults(exactData.results);
            searchCacheRef.current.set(cacheKey, uniqueResults);

            // Calculate how much time has passed
            const elapsedTime = Date.now() - startTime;

            // If search was too fast, wait a bit to avoid UI flickering
            if (elapsedTime < minSearchTime) {
              await new Promise(resolve => setTimeout(resolve, minSearchTime - elapsedTime));
            }

            setSearchResults(uniqueResults);
            console.log(`[SearchBar] Exact match took ${Date.now() - startTime}ms, found ${uniqueResults.length} results`);
            setIsSearching(false);
            setHasSearched(true);
            return;
          }
        }

        // Fallback to regular combined search
        const regularResponse = await fetch(`/api/search?query=${encodeURIComponent(normalizedQuery)}&limit=30`, { signal });
        if (regularResponse.ok) {
          const regularData = await regularResponse.json();
          if (regularData.results && regularData.results.length > 0) {
            const uniqueResults = deduplicateResults(regularData.results);
            searchCacheRef.current.set(cacheKey, uniqueResults);

            // Calculate how much time has passed
            const elapsedTime = Date.now() - startTime;

            // If search was too fast, wait a bit to avoid UI flickering
            if (elapsedTime < minSearchTime) {
              await new Promise(resolve => setTimeout(resolve, minSearchTime - elapsedTime));
            }

            setSearchResults(uniqueResults);
            console.log(`[SearchBar] Regular search took ${Date.now() - startTime}ms, found ${uniqueResults.length} results`);
            setIsSearching(false);
            setHasSearched(true);
            return;
          }
        }

        // Calculate how much time has passed
        const elapsedTime = Date.now() - startTime;

        // If search was too fast, wait a bit to avoid UI flickering
        if (elapsedTime < minSearchTime) {
          await new Promise(resolve => setTimeout(resolve, minSearchTime - elapsedTime));
        }

        setSearchResults([]);
        console.log(`[SearchBar] No results found for "${query}" in ${Date.now() - startTime}ms`);
      } catch (error) {
        if (error instanceof Error && error.name !== 'AbortError') {
          console.error('[SearchBar] Search error:', error);
        }
        setSearchResults([]);
      } finally {
        // First set hasSearched to true
        setHasSearched(true);
        // Then set isSearching to false which will trigger the effect to remove the loading indicator
        setIsSearching(false);
        // Force the loading indicator to be removed as a failsafe
        setShowLoadingIndicator(false);
      }
    },
    []
  );

  const deduplicateResults = (results: IContent[]): IContent[] => {
    const uniqueMap = new Map<string, IContent>();
    results.forEach((item: IContent) => {
      const key = `${item.id}-${item.type}`;
      if (!uniqueMap.has(key) || (!uniqueMap.get(key)?.posterPath && item.posterPath)) {
        uniqueMap.set(key, item);
      }
    });
    return Array.from(uniqueMap.values());
  };

  useEffect(() => {
    if (!isOpen) {
      // Reset all states when search is closed
      setIsSearching(false);
      setShowLoadingIndicator(false);
      setHasSearched(false);
      return;
    }

    // When search query changes, reset hasSearched flag
    setHasSearched(false);

    if (debouncedSearchQuery.trim().length === 0) {
      setSearchResults([]);
      // Ensure all states are reset when search query is empty
      setIsSearching(false);
      setShowLoadingIndicator(false);
      return;
    }

    if (debouncedSearchQuery.trim().length >= 2) {
      setIsSearching(true);
      performSearch(debouncedSearchQuery);
    } else {
      // For queries shorter than 2 characters, don't search but ensure we're not in loading state
      setIsSearching(false);
      setShowLoadingIndicator(false);
    }
  }, [debouncedSearchQuery, performSearch, isOpen]);

  const handleSearchSubmit = (e: FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      performSearch(searchQuery.trim());
      searchInputRef.current?.focus();
    }
  };

  const getFilteredResults = useCallback(() => {
    if (activeTab === "all") return searchResults;
    return searchResults.filter((item: IContent) => item.type === activeTab);
  }, [activeTab, searchResults]);

  const filteredResults = getFilteredResults();
  const hasSearchQuery = searchQuery.trim().length > 0;
  const showResults = hasSearchQuery && filteredResults.length > 0;
  const showEmptyState = !hasSearchQuery && !isSearching;

  // Only show "No results" when:
  // 1. We have a search query with at least 2 characters
  // 2. Search is complete (not searching)
  // 3. We have no results
  // 4. We have actually performed a search (hasSearched is true)
  const showNoResults = hasSearchQuery &&
                       searchQuery.trim().length >= 2 &&
                       filteredResults.length === 0 &&
                       !isSearching &&
                       hasSearched;

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.2 } }
  };

  const handleToggleWatchlist = (item: IContent) => {
    const itemKey = `${item.type}-${item.id}`;
    setWatchlistItems((prev: Set<string>) => {
      const newSet = new Set(prev);
      if (newSet.has(itemKey)) {
        newSet.delete(itemKey);
        console.log(`Removed "${item.title}" from simulated watchlist`);
      } else {
        newSet.add(itemKey);
        console.log(`Added "${item.title}" to simulated watchlist`);
      }
      return newSet;
    });
  };

  return (
    <AnimatePresence onExitComplete={() => {
      // Ensure all states are reset when the search modal is fully closed
      setIsSearching(false);
      setShowLoadingIndicator(false);
      setHasSearched(false);
    }}>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="fixed inset-0 z-50 bg-gradient-to-b from-black/98 to-black/95 backdrop-blur-md overflow-hidden"
        >
          <div className="container mx-auto px-3 xs:px-4 md:px-6 pt-6 xs:pt-8 md:pt-10 pb-4 xs:pb-6 h-full flex flex-col">
            <div className="flex justify-end items-center mb-1 xs:mb-2">
              <button
                className="text-white hover:bg-white/10 w-8 h-8 rounded-full flex items-center justify-center border border-white/10 hover:border-white/30 transition-all duration-200"
                onClick={onClose}
              >
                <X className="h-4 w-4" />
              </button>
            </div>

            <div className="mb-3 xs:mb-4 md:mb-6">
              <div className="relative group">
                <div className="absolute left-3 xs:left-4 top-1/2 -translate-y-1/2 flex items-center justify-center w-8 h-8 xs:w-10 xs:h-10 text-white/50 group-focus-within:text-white transition-colors duration-200">
                  <Search className="h-4 w-4 xs:h-5 xs:w-5" />
                </div>
                <Input
                  ref={searchInputRef}
                  type="text"
                  value={searchQuery}
                  onChange={(e: ChangeEvent<HTMLInputElement>) => setSearchQuery(e.target.value)}
                  placeholder="Search for shows, movies, genres..."
                  className="w-full bg-vista-dark/80 backdrop-blur-md border border-white/20 rounded-xl py-2 xs:py-3 pl-10 xs:pl-14 pr-10 xs:pr-14 text-sm xs:text-base text-white placeholder:text-white/70 focus-visible:ring-1 focus-visible:ring-white/40 focus-visible:ring-offset-0 focus-visible:border-white/40 transition-all duration-200 group-hover:border-white/30"
                  onKeyDown={(e: KeyboardEvent) => e.key === 'Enter' && handleSearchSubmit(e as FormEvent)}
                />
                <div className="absolute right-3 xs:right-4 top-1/2 -translate-y-1/2 flex items-center space-x-2 xs:space-x-3">
                  {searchQuery && (
                    <button
                      type="button"
                      onClick={(e: MouseEvent) => {
                        e.stopPropagation();
                        e.preventDefault();
                        setSearchQuery('');
                        // Ensure all states are reset when clearing search
                        setIsSearching(false);
                        setShowLoadingIndicator(false);
                        setHasSearched(false);
                      }}
                      className="p-1.5 xs:p-2 rounded-full hover:bg-vista-light/10 text-vista-light/50 hover:text-vista-light transition-colors"
                    >
                      <X className="h-3.5 w-3.5 xs:h-4 xs:w-4" />
                    </button>
                  )}
                  {isSearching && (
                    <div className="bg-white/10 p-1.5 xs:p-2 rounded-full">
                      <Loader2 className="h-3.5 w-3.5 xs:h-4 xs:w-4 text-white animate-spin" />
                    </div>
                  )}
                </div>
              </div>
            </div>

            {showResults && (
              <div className="mb-6">
                <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4 mb-4">
                  <div>
                    <h3 className="text-base font-medium text-vista-light mb-1">Search Results</h3>
                    <Badge className="bg-white/10 text-white border-0 rounded-lg py-1 px-3">
                      Found {filteredResults.length} results for "{searchQuery}"
                    </Badge>
                  </div>
                  <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full md:w-auto">
                    <TabsList className="bg-vista-dark-lighter/50 p-1 rounded-xl w-full md:w-auto">
                      <TabsTrigger
                        value="all"
                        className="flex-1 md:flex-none rounded-lg text-sm data-[state=active]:bg-white data-[state=active]:text-black"
                      >
                        All
                      </TabsTrigger>
                      <TabsTrigger
                        value="movie"
                        className="flex-1 md:flex-none rounded-lg text-sm data-[state=active]:bg-white data-[state=active]:text-black"
                      >
                        Movies
                      </TabsTrigger>
                      <TabsTrigger
                        value="show"
                        className="flex-1 md:flex-none rounded-lg text-sm data-[state=active]:bg-white data-[state=active]:text-black"
                      >
                        TV Shows
                      </TabsTrigger>
                    </TabsList>
                  </Tabs>
                </div>
              </div>
            )}

            <ScrollArea className="flex-1 overflow-y-auto pr-2">
              <AnimatePresence>
                {showLoadingIndicator && !showResults && (
                  <motion.div
                    key="loading-indicator"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    className="flex items-center justify-center py-4 mt-2"
                  >
                    <div className="flex items-center space-x-3 bg-vista-dark-lighter/30 px-4 py-2 rounded-full">
                      <div className="h-5 w-5 rounded-full border-2 border-white/20 border-t-white animate-spin"></div>
                      <p className="text-sm text-white/70">Searching...</p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>

              {showResults && (
                <div className="grid grid-cols-2 xs:grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-2 xs:gap-3 sm:gap-4 md:gap-6 pb-6">
                  {filteredResults.map((result: IContent, index: number) => {
                    const itemKey = `${result.type}-${result.id}`;
                    const isInWatchlist = watchlistItems.has(itemKey);

                    return (
                      <motion.div
                        key={`${result.id}-${result.type}-${index}`}
                        variants={itemVariants}
                        initial="hidden"
                        animate="visible"
                        className="group relative overflow-hidden rounded-xl bg-vista-dark-lighter/60 border border-vista-light/10 focus-within:ring-2 focus-within:ring-vista-blue hover:border-vista-light/30 transition-all duration-300 hover:shadow-xl hover:shadow-vista-blue/10 hover:-translate-y-1.5"
                      >
                        <Link
                          href={`/details/${result.type === 'show' ? 'shows' : 'movies'}/${result.id}`}
                          onClick={(e: MouseEvent) => {
                            e.stopPropagation();
                            e.preventDefault();
                            router.push(`/details/${result.type === 'show' ? 'shows' : 'movies'}/${result.id}`);
                            onClose();
                          }}
                          className="block"
                          aria-label={`View details for ${result.title}`}
                        >
                          <div className="aspect-[2/3] relative">
                            {result.posterPath ? (
                              <Image
                                src={getImageUrl(result.posterPath, result.title)}
                                alt={result.title}
                                fill
                                className="object-cover rounded-t-xl"
                                sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, 20vw"
                                loading="lazy"
                                onError={(e) => {
                                  (e.target as HTMLImageElement).src = 'https://placehold.co/300x450/171717/CCCCCC?text=N/A';
                                }}
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center bg-vista-dark rounded-t-xl">
                                <ImageIcon className="h-10 w-10 text-vista-light/30" />
                              </div>
                            )}
                            <div className="absolute inset-0 bg-gradient-to-b from-black/0 via-black/20 to-black/95 opacity-70 group-hover:opacity-100 transition-all duration-300 rounded-t-xl group-hover:via-black/40"></div>
                          </div>
                          <div className="absolute bottom-0 left-0 right-0 p-2 xs:p-3 text-vista-light z-10">
                            <h4 className="font-medium truncate text-xs xs:text-sm md:text-base mb-0.5 xs:mb-1">{result.title}</h4>
                            <div className="flex items-center text-[10px] xs:text-xs text-vista-light/70 mb-1 xs:mb-2">
                              <span className="bg-white/20 text-white px-1 py-0.5 rounded-sm text-[8px] xs:text-[10px] font-medium">
                                {result.type === 'movie' ? 'MOVIE' : 'TV SHOW'}
                              </span>
                              <span className="mx-1 xs:mx-1.5">•</span>
                              <span>{result.year}</span>
                              {result.rating && (
                                <>
                                  <span className="mx-1 xs:mx-1.5">•</span>
                                  <div className="flex items-center">
                                    <Star className="h-2.5 w-2.5 xs:h-3 xs:w-3 text-white mr-0.5 fill-white" />
                                    <span className="text-white font-medium">{result.rating.toFixed(1)}</span>
                                  </div>
                                </>
                              )}
                            </div>
                            <div className="flex space-x-1 xs:space-x-2 mt-2 xs:mt-3 opacity-100 sm:opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform sm:translate-y-2 group-hover:translate-y-0">
                              <Button
                                size="sm"
                                variant="default"
                                className="bg-white hover:bg-white/90 text-black gap-1 xs:gap-1.5 h-6 xs:h-8 px-2 xs:px-3 text-[10px] xs:text-xs rounded-full shadow-md"
                                onClick={(e: MouseEvent) => {
                                  e.stopPropagation();
                                  e.preventDefault();
                                  router.push(`/watch/${result.id}?forcePlay=true&contentType=${result.type === 'show' ? 'show' : 'movie'}`);
                                  onClose();
                                }}
                                aria-label={`Play ${result.title}`}
                              >
                                <Play className="h-3 w-3 xs:h-3.5 xs:w-3.5" /> Play
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                className={`border-white/30 hover:bg-white/10 gap-1 xs:gap-1.5 h-6 xs:h-8 px-2 xs:px-3 text-[10px] xs:text-xs rounded-full shadow-md ${isInWatchlist ? 'text-white border-white/50 hover:text-white' : 'text-white/90 hover:text-white'}`}
                                onClick={(e: MouseEvent) => {
                                  e.stopPropagation();
                                  e.preventDefault();
                                  handleToggleWatchlist(result);
                                }}
                                aria-label={isInWatchlist ? `Remove ${result.title} from watchlist` : `Add ${result.title} to watchlist`}
                              >
                                {isInWatchlist ? (
                                  <Check className="h-3 w-3 xs:h-3.5 xs:w-3.5" />
                                ) : (
                                  <Plus className="h-3 w-3 xs:h-3.5 xs:w-3.5" />
                                )}
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                className="text-white/90 border-white/30 hover:bg-white/10 hover:text-white gap-1 xs:gap-1.5 h-6 xs:h-8 px-2 xs:px-3 text-[10px] xs:text-xs rounded-full shadow-md"
                                onClick={(e: MouseEvent) => {
                                  e.stopPropagation();
                                  e.preventDefault();
                                  router.push(`/details/${result.type === 'show' ? 'shows' : 'movies'}/${result.id}`);
                                  onClose();
                                }}
                                aria-label={`View details for ${result.title}`}
                              >
                                <Info className="h-3 w-3 xs:h-3.5 xs:w-3.5" /> Details
                              </Button>
                            </div>
                          </div>
                        </Link>
                      </motion.div>
                    );
                  })}
                </div>
              )}

              {showNoResults && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className="text-center py-12 bg-vista-dark-lighter/20 rounded-2xl border border-vista-light/5 backdrop-blur-sm"
                >
                  <div className="bg-vista-dark-lighter/50 rounded-full p-6 inline-flex mb-4 shadow-inner">
                    <Search className="h-8 w-8 text-vista-light/40" />
                  </div>
                  <h3 className="text-xl font-medium text-vista-light mb-2">No results found</h3>
                  <p className="text-vista-light/70 max-w-md mx-auto">We couldn't find anything matching "{searchQuery}". Try different keywords or check the spelling.</p>
                  <div className="mt-6 mb-6">
                    <p className="text-vista-light/70 text-sm mb-3">Try searching for popular genres:</p>
                    <div className="flex flex-wrap gap-2 justify-center max-w-md mx-auto">
                      <div className="w-full text-xs text-vista-light/50 mb-2">Movies</div>
                      {['Action', 'Comedy', 'Drama', 'Thriller', 'Sci-Fi'].map(genre => (
                        <Badge
                          key={genre}
                          className="bg-gradient-to-br from-vista-dark-lighter to-vista-dark hover:from-vista-blue/20 hover:to-vista-blue/40 cursor-pointer text-vista-light/90 hover:text-white py-1.5 px-3 text-xs transition-all duration-200 hover:shadow-md hover:shadow-vista-blue/20 border border-white/10 hover:border-white/30 rounded-lg"
                          onClick={() => setSearchQuery(genre)}
                        >
                          {genre}
                        </Badge>
                      ))}
                      <div className="w-full text-xs text-vista-light/50 mt-3 mb-2">TV Shows</div>
                      {['Crime', 'Reality', 'Sitcom', 'Mystery', 'Anime'].map(genre => (
                        <Badge
                          key={genre}
                          className="bg-gradient-to-br from-vista-dark-lighter to-vista-dark hover:from-vista-blue/20 hover:to-vista-blue/40 cursor-pointer text-vista-light/90 hover:text-white py-1.5 px-3 text-xs transition-all duration-200 hover:shadow-md hover:shadow-vista-blue/20 border border-white/10 hover:border-white/30 rounded-lg"
                          onClick={() => setSearchQuery(genre)}
                        >
                          {genre}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div className="flex flex-col sm:flex-row items-center justify-center gap-3 mt-6">
                    <Button
                      variant="outline"
                      className="border-white/20 text-white hover:bg-white/10 hover:text-white w-full sm:w-auto"
                      onClick={() => {
                        setSearchQuery('');
                        // Ensure all states are reset when clearing search
                        setIsSearching(false);
                        setShowLoadingIndicator(false);
                        setHasSearched(false);
                      }}
                    >
                      <X className="h-4 w-4 mr-2" /> Clear Search
                    </Button>
                    <Button
                      variant="default"
                      className="bg-white hover:bg-white/90 text-black w-full sm:w-auto"
                      onClick={() => router.push('/details/movies')}
                    >
                      <Film className="h-4 w-4 mr-2" /> Browse Movies
                    </Button>
                    <Button
                      variant="default"
                      className="bg-white/90 hover:bg-white text-black w-full sm:w-auto"
                      onClick={() => router.push('/details/shows')}
                    >
                      <Tv className="h-4 w-4 mr-2" /> Browse TV Shows
                    </Button>
                  </div>
                </motion.div>
              )}

              {showEmptyState && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                  className="flex flex-col items-center justify-center text-center py-4 mt-2"
                >
                  <h3 className="text-xl md:text-2xl font-medium text-vista-light mb-3">Find your next favorite</h3>
                  <p className="text-vista-light/70 max-w-md mx-auto mb-6">
                    Search for movies, TV shows, actors, directors, or genres to discover amazing content.
                  </p>
                  <div className="flex flex-wrap gap-3 justify-center max-w-lg mx-auto">
                    {[
                      'Action', 'Comedy', 'Drama', 'Sci-Fi', 'Thriller', 'Animation',
                      'Adventure', 'Fantasy', 'Horror', 'Romance', 'Documentary', 'Family',
                      'Reality', 'Crime', 'Mystery', 'Talk Show', 'Sitcom', 'Anime'
                    ].map(genre => (
                      <Badge
                        key={genre}
                        className="bg-gradient-to-br from-vista-dark-lighter to-vista-dark hover:from-vista-blue/20 hover:to-vista-blue/40 cursor-pointer text-vista-light/90 hover:text-white py-2 px-4 text-sm transition-all duration-200 hover:shadow-md hover:shadow-vista-blue/20 border border-white/10 hover:border-white/30 rounded-lg"
                        onClick={() => setSearchQuery(genre)}
                      >
                        {genre}
                      </Badge>
                    ))}
                  </div>
                </motion.div>
              )}
            </ScrollArea>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
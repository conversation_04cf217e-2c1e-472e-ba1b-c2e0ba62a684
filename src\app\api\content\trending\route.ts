import { NextRequest, NextResponse } from 'next/server';
import { getTrendingDaily } from '@/lib/tmdb-api';
import { formatTMDbContentForCards } from '@/lib/content-utils';

export async function GET(request: NextRequest) {
  try {
    // Get page from query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const mediaType = searchParams.get('mediaType') || 'all';
    
    // Fetch trending content from TMDb
    const trendingContent = await getTrendingDaily(mediaType as 'movie' | 'tv' | 'all', page);
    
    // Format the data for content cards
    const formattedContent = formatTMDbContentForCards(trendingContent);
    
    // Return the formatted data
    return NextResponse.json({
      success: true,
      data: formattedContent,
      page,
      hasMore: page < 5 // Limit to 5 pages for now
    });
  } catch (error) {
    console.error('Error fetching trending content:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch trending content' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';

// Define an interface for the user document structure
interface IUser {
  _id: string;
  name: string;
  email: string;
  // Add other properties as needed
}

/**
 * POST /api/admin/activity/manage
 * Admin endpoint to manage user activity logs
 * Actions:
 * - purge: Delete logs older than X days
 * - clearUser: Clear all logs for a specific user
 * - clearType: Clear all logs of a specific type
 * - getStats: Get statistics about activity logs
 */
export async function POST(request: NextRequest) {
  try {
    // Use the admin-auth helper for consistent admin authentication
    const { getAdminUserId } = await import('@/lib/admin-auth');
    const auth = await getAdminUserId(request);

    if (!auth.isAuthorized) {
      // Return the error response from the auth helper
      return auth.error || NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = auth.userId;

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB using our helper
    await ensureMongooseConnection();

    // Import the UserActivity model
    const { getUserActivityModel } = await import('@/models/UserActivity');
    const UserActivity = getUserActivityModel();

    // Define proper types for request data
    interface ActivityManageRequest {
      action: 'purge' | 'clearUser' | 'clearType' | 'getStats';
      userId?: string;
      type?: string;
      days?: number;
    }

    // Parse request body
    const data = await request.json() as ActivityManageRequest;
    const { action, userId: targetUserId, type, days } = data;

    if (!action) {
      return NextResponse.json({ error: 'Missing required parameter: action' }, { status: 400 });
    }

    // Process based on action
    switch (action) {
      case 'purge': {
        // Validate days parameter
        if (!days || typeof days !== 'number' || days < 1) {
          return NextResponse.json(
            { error: 'Invalid days parameter. Must be a positive number.' },
            { status: 400 }
          );
        }

        // Calculate cutoff date
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - days);

        // Delete logs older than cutoff date
        const result = await UserActivity.deleteMany({
          timestamp: { $lt: cutoffDate }
        });

        // Log the admin action directly
        await UserActivity.create({
          userId: new mongoose.default.Types.ObjectId(userId),
          type: 'admin',
          action: 'purge_logs',
          details: `Purged ${result.deletedCount} activity logs older than ${days} days`,
          ipAddress: request.ip || request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown',
          timestamp: new Date()
        });

        return NextResponse.json({
          success: true,
          message: `Successfully purged ${result.deletedCount} activity logs older than ${days} days`,
          deletedCount: result.deletedCount
        });
      }

      case 'clearUser': {
        // Validate userId parameter
        if (!targetUserId) {
          return NextResponse.json(
            { error: 'Missing required parameter: userId' },
            { status: 400 }
          );
        }

        // Delete logs for the specified user
        const result = await UserActivity.deleteMany({ userId: targetUserId });

        // Log the admin action directly
        await UserActivity.create({
          userId: new mongoose.default.Types.ObjectId(userId),
          type: 'admin',
          action: 'clear_user_logs',
          details: `Cleared ${result.deletedCount} activity logs for user ${targetUserId}`,
          ipAddress: request.ip || request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown',
          timestamp: new Date()
        });

        return NextResponse.json({
          success: true,
          message: `Successfully cleared ${result.deletedCount} activity logs for user ${targetUserId}`,
          deletedCount: result.deletedCount
        });
      }

      case 'clearType': {
        // Validate type parameter
        if (!type) {
          return NextResponse.json(
            { error: 'Missing required parameter: type' },
            { status: 400 }
          );
        }

        // Delete logs of the specified type
        const result = await UserActivity.deleteMany({ type });

        // Log the admin action directly
        await UserActivity.create({
          userId: new mongoose.default.Types.ObjectId(userId),
          type: 'admin',
          action: 'clear_type_logs',
          details: `Cleared ${result.deletedCount} activity logs of type ${type}`,
          ipAddress: request.ip || request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown',
          timestamp: new Date()
        });

        return NextResponse.json({
          success: true,
          message: `Successfully cleared ${result.deletedCount} activity logs of type ${type}`,
          deletedCount: result.deletedCount
        });
      }

      case 'getStats': {
        // Get activity stats
        const stats = await UserActivity.aggregate([
          {
            $facet: {
              byType: [
                { $group: { _id: '$type', count: { $sum: 1 } } },
                { $sort: { count: -1 } }
              ],
              byAction: [
                { $group: { _id: '$action', count: { $sum: 1 } } },
                { $sort: { count: -1 } }
              ],
              timeDistribution: [
                {
                  $group: {
                    _id: {
                      year: { $year: '$timestamp' },
                      month: { $month: '$timestamp' },
                      day: { $dayOfMonth: '$timestamp' }
                    },
                    count: { $sum: 1 }
                  }
                },
                { $sort: { '_id.year': -1, '_id.month': -1, '_id.day': -1 } },
                { $limit: 30 }
              ],
              totalCount: [{ $count: 'count' }],
              oldestLog: [{ $sort: { timestamp: 1 } }, { $limit: 1 }]
            }
          }
        ]);

        // Format stats for response
        interface TimeDistributionItem {
          _id: {
            year: number;
            month: number;
            day: number;
          };
          count: number;
        }

        const formattedStats = {
          byType: stats[0].byType,
          byAction: stats[0].byAction,
          timeDistribution: stats[0].timeDistribution.map((item: TimeDistributionItem) => ({
            date: new Date(item._id.year, item._id.month - 1, item._id.day).toISOString().split('T')[0],
            count: item.count
          })),
          totalCount: stats[0].totalCount[0]?.count || 0,
          oldestLog: stats[0].oldestLog[0]?.timestamp || null
        };

        // Create a response with caching headers for getStats
        const response = NextResponse.json({
          success: true,
          stats: formattedStats
        });

        // Add cache control headers to allow client-side caching
        response.headers.set('Cache-Control', 'private, max-age=10');

        return response;
      }

      default:
        return NextResponse.json(
          { error: `Invalid action: ${action}. Valid actions are purge, clearUser, clearType, and getStats.` },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error managing activity logs:', error);
    return NextResponse.json(
      { error: 'Failed to manage activity logs', details: (error as Error).message },
      { status: 500 }
    );
  }
}
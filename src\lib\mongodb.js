// This file is a compatibility layer that re-exports from mongodb.ts
// It exists to maintain backward compatibility with code that imports from '@/lib/mongodb.js'

// Import directly from mongoose.ts to avoid circular dependencies
import mongoose from 'mongoose';
import { MongoClient } from 'mongodb';

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI;

// Only check for MONGODB_URI on the server side
// This prevents the error when importing this file on the client side
if (typeof window === 'undefined' && !MONGODB_URI) {
  throw new Error('Please define the MONGODB_URI environment variable');
}

// MongoDB client promise
let clientPromise;

if (typeof window === 'undefined') {
  // Only initialize connection on server side
  if (process.env.NODE_ENV === 'development') {
    // In development mode, use a global variable so that the value
    // is preserved across module reloads caused by HMR (Hot Module Replacement).
    let globalWithMongo = global;
    if (!globalWithMongo._mongoClientPromise) {
      const client = new MongoClient(MONGODB_URI);
      globalWithMongo._mongoClientPromise = client.connect();
    }
    clientPromise = globalWithMongo._mongoClientPromise;
  } else {
    // In production mode, it's best to not use a global variable.
    const client = new MongoClient(MONGODB_URI);
    clientPromise = client.connect();
  }
}

// Connect to MongoDB using mongoose
async function dbConnect() {
  if (typeof window !== 'undefined') {
    console.warn('Attempted to connect to MongoDB from client-side code. This is not allowed.');
    return null;
  }
  
  if (mongoose.connection.readyState >= 1) {
    return;
  }

  return mongoose.connect(MONGODB_URI);
}

// Ensure mongoose connection
async function ensureMongooseConnection() {
  if (typeof window !== 'undefined') {
    console.warn('Attempted to connect to MongoDB from client-side code. This is not allowed.');
    return null;
  }

  if (mongoose.connection.readyState === 1) {
    return mongoose.connection;
  }

  return mongoose.connect(MONGODB_URI);
}

// Connect to MongoDB using MongoClient
async function connectToDatabase() {
  if (typeof window !== 'undefined') {
    console.warn('Attempted to connect to MongoDB from client-side code. This is not allowed.');
    return null;
  }

  const client = await clientPromise;
  const db = client.db();
  return { client, db };
}

// Check MongoDB health
async function checkMongoHealth() {
  if (typeof window !== 'undefined') {
    console.warn('Attempted to connect to MongoDB from client-side code. This is not allowed.');
    return { ok: false, error: 'Client-side MongoDB connection not allowed' };
  }

  try {
    const startTime = Date.now();
    const client = await clientPromise;
    const adminDb = client.db().admin();
    const result = await adminDb.ping();
    const pingTime = Date.now() - startTime;

    return {
      ok: result.ok === 1,
      pingTime,
      version: (await adminDb.serverInfo()).version,
      connections: (await adminDb.serverStatus()).connections
    };
  } catch (error) {
    console.error('MongoDB health check failed:', error);
    return { ok: false, error: error.message };
  }
}

// Reset MongoDB connections
async function resetMongoConnections() {
  if (typeof window !== 'undefined') {
    console.warn('Attempted to connect to MongoDB from client-side code. This is not allowed.');
    return { success: false, error: 'Client-side MongoDB connection not allowed' };
  }

  try {
    if (mongoose.connection.readyState !== 0) {
      await mongoose.connection.close();
    }

    const client = await clientPromise;
    await client.close();

    // Reset the global promise
    if (process.env.NODE_ENV === 'development') {
      let globalWithMongo = global;
      delete globalWithMongo._mongoClientPromise;
    }

    return { success: true };
  } catch (error) {
    console.error('Error resetting MongoDB connections:', error);
    return { success: false, error: error.message };
  }
}

// Export everything
export {
  connectToDatabase,
  ensureMongooseConnection,
  checkMongoHealth,
  resetMongoConnections,
  clientPromise,
  dbConnect
};

// Create a named object before exporting as default
const mongodbExports = {
  connectToDatabase,
  ensureMongooseConnection,
  checkMongoHealth,
  resetMongoConnections,
  clientPromise,
  dbConnect
};

// Export default
export default mongodbExports;
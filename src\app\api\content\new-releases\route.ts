import { NextRequest, NextResponse } from 'next/server';
import dbConnect, { ensureMongooseConnection } from '@/lib/mongodb';
import User from '@/models/User';
import Notification from '@/models/Notification';
import { pusherServer } from '@/lib/pusher-server';

// This endpoint will be called by a cron job or admin to create notifications for new content
export async function POST(request: NextRequest) {
  try {
    // Simple API key validation for admin/system operations
    const apiKey = request.headers.get('x-api-key');
    const isSystemRequest = apiKey === process.env.SYSTEM_API_KEY;

    if (!isSystemRequest) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to the database
    await ensureMongooseConnection();

    // Get the request body
    const body = await request.json();

    // Validate required fields
    if (!body.contentId || !body.contentType || !body.title) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Find all users who have enabled new content notifications
    const users = await User.find({
      'preferences.notifications.newContent': true
    });

    if (users.length === 0) {
      return NextResponse.json({
        message: 'No users with new content notifications enabled',
        notificationsCreated: 0
      });
    }

    // Create notifications for each user
    const notifications = [];

    for (const user of users) {
      const notification = await Notification.create({
        userId: user._id,
        type: 'new_content',
        title: `New ${body.contentType}: ${body.title}`,
        message: body.message || `${body.title} is now available to stream on StreamVista.`,
        contentId: body.contentId,
        contentType: body.contentType,
        image: body.image,
        read: false
      });

      notifications.push(notification);

      // Notify the user via Pusher if available
      try {
        await pusherServer.trigger(
          `user-${user._id}`,
          'new-notification',
          {
            notification
          }
        );
      } catch (error) {
        console.error('Error sending Pusher notification:', error);
      }
    }

    return NextResponse.json({
      message: 'Notifications created successfully',
      notificationsCreated: notifications.length
    });
  } catch (error) {
    console.error('Error creating notifications:', error);
    return NextResponse.json(
      { error: 'Failed to create notifications' },
      { status: 500 }
    );
  }
}

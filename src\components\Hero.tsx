"use client";

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Play, Info, Volume2, VolumeX } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { showcaseContent } from '@/data/content';
import { useLanguage } from '@/lib/i18n/LanguageContext';

export default function Hero() {
  const [isMuted, setIsMuted] = useState(true);
  const { t } = useLanguage();

  return (
    <div className="relative w-full aspect-[21/9] overflow-hidden">
      {/* Background Image with Gradient Overlay */}
      <div className="absolute inset-0">
        <Image
          src={showcaseContent.imageUrl}
          alt={showcaseContent.title}
          fill
          priority
          className="object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-vista-dark via-vista-dark/50 to-transparent" />
        <div className="absolute inset-0 bg-gradient-to-t from-vista-dark via-transparent to-transparent" />
      </div>

      {/* Content */}
      <div className="relative h-full container mx-auto px-4 flex items-center">
        <div className="max-w-3xl space-y-6">
          {/* Release Info Badge */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Badge variant="outline" className="bg-vista-dark/40 text-vista-light border-vista-light/20">
              {showcaseContent.releaseInfo}
            </Badge>
          </motion.div>

          {/* Title */}
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-4xl md:text-5xl lg:text-6xl font-bold text-vista-light tracking-tight"
          >
            {showcaseContent.title}
          </motion.h1>

          {/* Description */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-lg text-vista-light/90 max-w-2xl"
          >
            {showcaseContent.description}
          </motion.p>

          {/* Highlights */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="flex flex-wrap gap-3"
          >
            {showcaseContent.highlights?.map((highlight, index) => (
              <Badge
                key={index}
                variant="secondary"
                className="bg-vista-blue/10 text-vista-blue border-vista-blue/20"
              >
                {highlight}
              </Badge>
            ))}
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="flex items-center gap-4"
          >
            <Link href={`/watch/horizon-line?forcePlay=true&contentType=show`}>
              <Button size="lg" className="bg-white text-vista-dark hover:bg-white/90 gap-2">
                <Play className="w-5 h-5" />
                Watch Now
              </Button>
            </Link>
            <Link href={`/shows/horizon-line`}>
              <Button
                size="lg"
                variant="outline"
                className="border-vista-light/20 text-vista-light hover:bg-vista-light/10 gap-2"
              >
                <Info className="w-5 h-5" />
                Details
              </Button>
            </Link>
            <Button
              size="icon"
              variant="ghost"
              className="text-vista-light hover:bg-vista-light/10"
              onClick={() => setIsMuted(!isMuted)}
            >
              {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
            </Button>
          </motion.div>
        </div>
      </div>
    </div>
  );
}

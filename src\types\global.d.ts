// Global type declarations for StreamVista

interface Window {
  google?: {
    accounts: {
      id: {
        initialize: (config: any) => void;
        prompt: (callback?: (notification: any) => void) => void;
        renderButton: (element: HTMLElement, options: any) => void;
        disableAutoSelect: () => void;
        cancel?: () => void;
      };
    };
  };

  // For watch party playback throttling
  updatePlaybackTimeout: NodeJS.Timeout | null;
}
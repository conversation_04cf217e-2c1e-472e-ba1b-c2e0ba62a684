import { NextResponse } from 'next/server';
import { checkMongoHealth, resetMongoConnections } from '@/lib/mongodb';
import mongoose from 'mongoose';

/**
 * GET /api/health
 *
 * Health check endpoint for the application
 * Returns the status of various services
 */
export async function GET(request: Request) {
  const startTime = Date.now();

  try {
    // Get URL parameters
    const { searchParams } = new URL(request.url);
    const reset = searchParams.get('reset') === 'true';
    const detailed = searchParams.get('detailed') === 'true';

    // Reset MongoDB connections if requested
    if (reset) {
      await resetMongoConnections();
    }

    // Check MongoDB health
    const mongoHealth = await checkMongoHealth();

    // Calculate response time
    const responseTime = Date.now() - startTime;

    // Determine overall status
    const isHealthy = mongoHealth.status === 'healthy';

    // Get system information
    const systemInfo = {
      nodeVersion: process.version,
      platform: process.platform,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      mongooseVersion: mongoose.version,
    };

    return NextResponse.json({
      status: isHealthy ? 'healthy' : 'degraded',
      timestamp: new Date().toISOString(),
      responseTime: `${responseTime}ms`,
      environment: process.env.NODE_ENV || 'development',
      isNetlify: process.env.NETLIFY === 'true',
      message: isHealthy ? 'API is operational' : 'API is operational but database connectivity issues detected',
      services: {
        mongodb: mongoHealth
      },
      system: detailed ? systemInfo : undefined,
      reset: reset ? true : undefined
    }, {
      status: isHealthy ? 200 : 503,
      headers: {
        'Cache-Control': 'no-store, max-age=0'
      }
    });
  } catch (error) {
    // Calculate response time even for errors
    const responseTime = Date.now() - startTime;

    console.error('Health check failed:', error);

    return NextResponse.json({
      status: 'error',
      timestamp: new Date().toISOString(),
      responseTime: `${responseTime}ms`,
      environment: process.env.NODE_ENV || 'development',
      message: 'Health check failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, {
      status: 500,
      headers: {
        'Cache-Control': 'no-store, max-age=0'
      }
    });
  }
}
'use client';

import { useState, useEffect } from 'react';
import { Navbar } from '@/components/Navbar';
import Footer from '@/components/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Cookie, Shield, BarChart3, Target, Settings, Info, CheckCircle, XCircle } from 'lucide-react';
import { toast } from 'sonner';

interface CookieCategory {
  id: string;
  name: string;
  description: string;
  icon: any;
  required: boolean;
  enabled: boolean;
  cookies: {
    name: string;
    purpose: string;
    duration: string;
    provider: string;
  }[];
}

const cookieCategories: CookieCategory[] = [
  {
    id: 'essential',
    name: 'Essential Cookies',
    description: 'These cookies are necessary for the website to function and cannot be disabled.',
    icon: Shield,
    required: true,
    enabled: true,
    cookies: [
      {
        name: 'session_id',
        purpose: 'Maintains your login session',
        duration: 'Session',
        provider: 'StreamVista'
      },
      {
        name: 'csrf_token',
        purpose: 'Security protection against cross-site attacks',
        duration: 'Session',
        provider: 'StreamVista'
      },
      {
        name: 'user_preferences',
        purpose: 'Stores your language and region settings',
        duration: '1 year',
        provider: 'StreamVista'
      }
    ]
  },
  {
    id: 'functional',
    name: 'Functional Cookies',
    description: 'These cookies enable enhanced functionality and personalization.',
    icon: Settings,
    required: false,
    enabled: true,
    cookies: [
      {
        name: 'video_quality',
        purpose: 'Remembers your preferred video quality settings',
        duration: '6 months',
        provider: 'StreamVista'
      },
      {
        name: 'subtitle_preferences',
        purpose: 'Stores your subtitle and audio preferences',
        duration: '1 year',
        provider: 'StreamVista'
      },
      {
        name: 'watchlist_cache',
        purpose: 'Caches your watchlist for faster loading',
        duration: '30 days',
        provider: 'StreamVista'
      }
    ]
  },
  {
    id: 'analytics',
    name: 'Analytics Cookies',
    description: 'These cookies help us understand how you use our service to improve it.',
    icon: BarChart3,
    required: false,
    enabled: false,
    cookies: [
      {
        name: '_ga',
        purpose: 'Tracks website usage and user behavior',
        duration: '2 years',
        provider: 'Google Analytics'
      },
      {
        name: '_gid',
        purpose: 'Distinguishes users for analytics',
        duration: '24 hours',
        provider: 'Google Analytics'
      },
      {
        name: 'mixpanel_token',
        purpose: 'Tracks user interactions and events',
        duration: '1 year',
        provider: 'Mixpanel'
      }
    ]
  },
  {
    id: 'marketing',
    name: 'Marketing Cookies',
    description: 'These cookies are used to deliver personalized advertisements.',
    icon: Target,
    required: false,
    enabled: false,
    cookies: [
      {
        name: 'facebook_pixel',
        purpose: 'Tracks conversions and builds audiences',
        duration: '90 days',
        provider: 'Facebook'
      },
      {
        name: 'google_ads',
        purpose: 'Delivers targeted advertisements',
        duration: '90 days',
        provider: 'Google Ads'
      },
      {
        name: 'twitter_pixel',
        purpose: 'Measures ad performance and targeting',
        duration: '30 days',
        provider: 'Twitter'
      }
    ]
  }
];

export default function CookiesPage() {
  const [categories, setCategories] = useState<CookieCategory[]>(cookieCategories);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    // Load saved preferences from localStorage
    const savedPreferences = localStorage.getItem('cookiePreferences');
    if (savedPreferences) {
      try {
        const preferences = JSON.parse(savedPreferences);
        setCategories(prev => prev.map(category => ({
          ...category,
          enabled: category.required || preferences[category.id] || false
        })));
      } catch (error) {
        console.error('Error loading cookie preferences:', error);
      }
    }
  }, []);

  const handleToggle = (categoryId: string, enabled: boolean) => {
    setCategories(prev => prev.map(category => 
      category.id === categoryId ? { ...category, enabled } : category
    ));
    setHasChanges(true);
  };

  const savePreferences = () => {
    const preferences = categories.reduce((acc, category) => {
      if (!category.required) {
        acc[category.id] = category.enabled;
      }
      return acc;
    }, {} as Record<string, boolean>);

    localStorage.setItem('cookiePreferences', JSON.stringify(preferences));
    setHasChanges(false);
    toast.success('Cookie preferences saved successfully!');
  };

  const acceptAll = () => {
    setCategories(prev => prev.map(category => ({ ...category, enabled: true })));
    setHasChanges(true);
  };

  const rejectAll = () => {
    setCategories(prev => prev.map(category => ({ 
      ...category, 
      enabled: category.required 
    })));
    setHasChanges(true);
  };

  return (
    <div className="min-h-screen bg-vista-dark text-vista-light">
      <Navbar />
      
      {/* Hero Section */}
      <section className="relative py-20 px-4 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-vista-blue/10 to-transparent" />
        <div className="container mx-auto text-center relative z-10">
          <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-vista-light to-vista-blue bg-clip-text text-transparent">
            Cookie Preferences
          </h1>
          <p className="text-xl md:text-2xl text-vista-light/80 max-w-3xl mx-auto mb-8">
            Manage your cookie preferences and learn how we use cookies to improve your experience.
          </p>
          <Badge variant="secondary" className="bg-vista-blue/20 text-vista-blue">
            GDPR Compliant Cookie Management
          </Badge>
        </div>
      </section>

      {/* Cookie Overview */}
      <section className="py-16 px-4 bg-vista-card/30">
        <div className="container mx-auto max-w-4xl">
          <div className="grid md:grid-cols-3 gap-6 mb-8">
            <Card className="bg-vista-card border-vista-light/10 text-center">
              <CardContent className="p-6">
                <Cookie className="w-8 h-8 mx-auto mb-3 text-vista-blue" />
                <h3 className="font-semibold text-vista-light mb-2">What are Cookies?</h3>
                <p className="text-vista-light/70 text-sm">Small text files stored on your device to enhance your browsing experience.</p>
              </CardContent>
            </Card>
            <Card className="bg-vista-card border-vista-light/10 text-center">
              <CardContent className="p-6">
                <Shield className="w-8 h-8 mx-auto mb-3 text-vista-blue" />
                <h3 className="font-semibold text-vista-light mb-2">Your Control</h3>
                <p className="text-vista-light/70 text-sm">You can choose which cookies to accept and manage your preferences at any time.</p>
              </CardContent>
            </Card>
            <Card className="bg-vista-card border-vista-light/10 text-center">
              <CardContent className="p-6">
                <Info className="w-8 h-8 mx-auto mb-3 text-vista-blue" />
                <h3 className="font-semibold text-vista-light mb-2">Transparency</h3>
                <p className="text-vista-light/70 text-sm">We're transparent about what cookies we use and why we use them.</p>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <Button onClick={acceptAll} className="bg-vista-blue hover:bg-vista-blue/90">
              <CheckCircle className="w-4 h-4 mr-2" />
              Accept All Cookies
            </Button>
            <Button onClick={rejectAll} variant="outline" className="border-vista-light/20 text-vista-light hover:bg-vista-light/10">
              <XCircle className="w-4 h-4 mr-2" />
              Reject Non-Essential
            </Button>
          </div>
        </div>
      </section>

      {/* Cookie Categories */}
      <section className="py-20 px-4">
        <div className="container mx-auto max-w-4xl">
          <h2 className="text-3xl font-bold text-center mb-12 text-vista-light">Cookie Categories</h2>
          
          <Tabs defaultValue="preferences" className="w-full">
            <TabsList className="grid w-full grid-cols-2 bg-vista-card border-vista-light/10 mb-8">
              <TabsTrigger value="preferences" className="data-[state=active]:bg-vista-blue data-[state=active]:text-white">
                Manage Preferences
              </TabsTrigger>
              <TabsTrigger value="details" className="data-[state=active]:bg-vista-blue data-[state=active]:text-white">
                Cookie Details
              </TabsTrigger>
            </TabsList>

            <TabsContent value="preferences">
              <div className="space-y-6">
                {categories.map((category) => (
                  <Card key={category.id} className="bg-vista-card border-vista-light/10">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-vista-blue/20 rounded-full flex items-center justify-center">
                            <category.icon className="w-5 h-5 text-vista-blue" />
                          </div>
                          <div>
                            <CardTitle className="text-vista-light">{category.name}</CardTitle>
                            <p className="text-vista-light/70 text-sm mt-1">{category.description}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {category.required && (
                            <Badge variant="secondary" className="bg-orange-500/20 text-orange-400">
                              Required
                            </Badge>
                          )}
                          <Switch
                            checked={category.enabled}
                            onCheckedChange={(enabled) => handleToggle(category.id, enabled)}
                            disabled={category.required}
                          />
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-vista-light/60 text-sm">
                        {category.cookies.length} cookie{category.cookies.length !== 1 ? 's' : ''} in this category
                      </p>
                    </CardContent>
                  </Card>
                ))}

                {hasChanges && (
                  <div className="flex justify-center pt-6">
                    <Button onClick={savePreferences} size="lg" className="bg-vista-blue hover:bg-vista-blue/90">
                      Save Preferences
                    </Button>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="details">
              <div className="space-y-8">
                {categories.map((category) => (
                  <Card key={category.id} className="bg-vista-card border-vista-light/10">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-3 text-vista-light">
                        <category.icon className="w-5 h-5 text-vista-blue" />
                        {category.name}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead>
                            <tr className="border-b border-vista-light/10">
                              <th className="text-left p-2 text-vista-light font-semibold text-sm">Cookie Name</th>
                              <th className="text-left p-2 text-vista-light font-semibold text-sm">Purpose</th>
                              <th className="text-left p-2 text-vista-light font-semibold text-sm">Duration</th>
                              <th className="text-left p-2 text-vista-light font-semibold text-sm">Provider</th>
                            </tr>
                          </thead>
                          <tbody>
                            {category.cookies.map((cookie, index) => (
                              <tr key={index} className="border-b border-vista-light/5">
                                <td className="p-2 text-vista-light/80 font-mono text-sm">{cookie.name}</td>
                                <td className="p-2 text-vista-light/70 text-sm">{cookie.purpose}</td>
                                <td className="p-2 text-vista-light/70 text-sm">{cookie.duration}</td>
                                <td className="p-2 text-vista-light/70 text-sm">{cookie.provider}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </section>

      <Separator className="bg-vista-light/10" />

      {/* Additional Information */}
      <section className="py-16 px-4 bg-vista-card/30">
        <div className="container mx-auto max-w-4xl">
          <h2 className="text-3xl font-bold text-center mb-8 text-vista-light">Additional Information</h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            <Card className="bg-vista-card border-vista-light/10">
              <CardHeader>
                <CardTitle className="text-vista-light">Browser Settings</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-vista-light/80 text-sm mb-4">
                  You can also manage cookies through your browser settings. Most browsers allow you to:
                </p>
                <ul className="space-y-1 text-vista-light/70 text-sm">
                  <li>• Block all cookies</li>
                  <li>• Block third-party cookies</li>
                  <li>• Delete existing cookies</li>
                  <li>• Set cookie preferences per website</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="bg-vista-card border-vista-light/10">
              <CardHeader>
                <CardTitle className="text-vista-light">Third-Party Cookies</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-vista-light/80 text-sm mb-4">
                  Some cookies are set by third-party services we use:
                </p>
                <ul className="space-y-1 text-vista-light/70 text-sm">
                  <li>• Analytics providers (Google Analytics)</li>
                  <li>• Advertising networks</li>
                  <li>• Social media platforms</li>
                  <li>• Payment processors</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 px-4">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl font-bold mb-6 text-vista-light">Questions About Cookies?</h2>
          <p className="text-vista-light/80 max-w-2xl mx-auto mb-8">
            If you have questions about our use of cookies or need help managing your preferences, please contact us.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button className="bg-vista-blue hover:bg-vista-blue/90">
              Contact Support
            </Button>
            <Button variant="outline" className="border-vista-light/20 text-vista-light hover:bg-vista-light/10">
              Privacy Policy
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}

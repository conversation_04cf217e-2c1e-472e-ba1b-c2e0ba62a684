import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { toast } from '@/components/ui/use-toast';

/**
 * Custom hook to handle admin authentication
 * This hook will check if the user is an admin and redirect if not
 */
export function useAdminAuth() {
  const { user, isAdmin } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user is admin and redirect if not
    const checkAdminStatus = async () => {
      // If user is not loaded yet, wait
      if (!user) {
        setIsLoading(false);
        setIsAuthorized(false);
        router.push('/');
        toast({
          title: 'Access Denied',
          description: 'You must be signed in to access this page.',
          variant: 'destructive'
        });
        return;
      }

      // Check if user is admin
      if (!isAdmin()) {
        setIsLoading(false);
        setIsAuthorized(false);
        router.push('/');
        toast({
          title: 'Access Denied',
          description: 'You do not have permission to access this page.',
          variant: 'destructive'
        });
        return;
      }

      // User is admin
      setIsAuthorized(true);
      setIsLoading(false);
    };

    checkAdminStatus();
  }, [user, isAdmin, router]);

  return { isAuthorized, isLoading };
}

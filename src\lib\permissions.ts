import { ensureMongooseConnection } from './mongoose';
import UserPermission from '@/models/UserPermission';
import DefaultPermission from '@/models/DefaultPermission';
import mongoose from 'mongoose';

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined';

// Define default permissions for different user roles
// This object is safe to import in client components
export const DEFAULT_PERMISSIONS = {
  user: {
    // Content access
    'content.view': true,
    'content.premium': false,
    'content.early_access': false,

    // Social features
    'social.comment': true,
    'social.rate': true,
    'social.share': true,

    // Account management
    'account.profile': true,
    'account.subscription': true,
    'account.payment': true
  },
  moderator: {
    // Content access
    'content.view': true,
    'content.premium': true,
    'content.early_access': true,

    // Social features
    'social.comment': true,
    'social.rate': true,
    'social.share': true,

    // Account management
    'account.profile': true,
    'account.subscription': true,
    'account.payment': true,

    // Moderator permissions
    'moderator.review_comments': true,
    'moderator.approve_content': true
  },
  admin: {
    // All permissions enabled for admin
    'content.view': true,
    'content.premium': true,
    'content.early_access': true,

    'social.comment': true,
    'social.rate': true,
    'social.share': true,

    'account.profile': true,
    'account.subscription': true,
    'account.payment': true,

    'moderator.review_comments': true,
    'moderator.approve_content': true,

    'admin.manage_users': true,
    'admin.manage_content': true,
    'admin.manage_settings': true,
    'admin.view_analytics': true
  }
};

// Define permission types
type PermissionSet = Record<string, boolean>;
type RolePermissions = Record<string, PermissionSet>;
type UserPermissionResult = {
  userId: string;
  permissions: PermissionSet;
  isDefault?: boolean;
};

// Define empty stub functions for client-side environment
let getDefaultPermissionsImpl: (role?: string) => Promise<RolePermissions | PermissionSet> = async () => DEFAULT_PERMISSIONS;
let setDefaultPermissionsImpl: (permissions: RolePermissions) => Promise<boolean> = async () => false;
let resetAllDefaultPermissionsImpl: () => Promise<boolean> = async () => false;
let getUserPermissionsImpl: (userId: string) => Promise<UserPermissionResult> = async () => ({ userId: '', permissions: {} });
let updateUserPermissionsImpl: (userId: string, permissions: PermissionSet) => Promise<UserPermissionResult> = async () => ({ userId: '', permissions: {} });
let resetUserPermissionsImpl: (userId: string, role: 'user' | 'moderator' | 'admin') => Promise<UserPermissionResult> = async () => ({ userId: '', permissions: {} });
let hasPermissionImpl: (userId: string, permission: string) => Promise<boolean> = async () => false;

// Server-side only implementations
if (!isBrowser) {
  // Actual implementation for server-side
  getDefaultPermissionsImpl = async (role?: string) => {
    try {
      // Connect to MongoDB
      await ensureMongooseConnection();

      // DefaultPermission is not null on server side
      const model = DefaultPermission as NonNullable<typeof DefaultPermission>;
      
      // Find default permissions
      const defaultPermissions = await model.findOne({ isActive: true });

      // If no custom defaults found, return system defaults
      if (!defaultPermissions) {
        return role ? DEFAULT_PERMISSIONS[role as keyof typeof DEFAULT_PERMISSIONS] : DEFAULT_PERMISSIONS;
      }

      // Return permissions for specific role or all roles
      return role ? defaultPermissions.permissions[role] : defaultPermissions.permissions;
    } catch (error) {
      console.error('Error getting default permissions:', error);
      // Return system defaults as fallback
      return role ? DEFAULT_PERMISSIONS[role as keyof typeof DEFAULT_PERMISSIONS] : DEFAULT_PERMISSIONS;
    }
  };

  setDefaultPermissionsImpl = async (permissions: RolePermissions) => {
    try {
      // Connect to MongoDB
      await ensureMongooseConnection();

      // Validate permissions object
      if (!permissions || typeof permissions !== 'object') {
        throw new Error('Invalid permissions object');
      }

      // DefaultPermission is not null on server side
      const model = DefaultPermission as NonNullable<typeof DefaultPermission>;

      // Update or create default permissions
      await model.updateOne(
        { isActive: true },
        {
          permissions,
          updatedAt: new Date()
        },
        { upsert: true }
      );

      return true;
    } catch (error) {
      console.error('Error setting default permissions:', error);
      throw error;
    }
  };

  resetAllDefaultPermissionsImpl = async () => {
    try {
      // Connect to MongoDB
      await ensureMongooseConnection();

      // DefaultPermission is not null on server side
      const model = DefaultPermission as NonNullable<typeof DefaultPermission>;

      // Delete all custom default permissions
      await model.deleteMany({});

      // Create a new document with system defaults
      await model.create({
        permissions: DEFAULT_PERMISSIONS,
        isActive: true
      });

      return true;
    } catch (error) {
      console.error('Error resetting default permissions:', error);
      throw error;
    }
  };

  getUserPermissionsImpl = async (userId: string) => {
    try {
      // Connect to MongoDB
      await ensureMongooseConnection();

      // UserPermission is not null on server side
      const model = UserPermission as NonNullable<typeof UserPermission>;

      // Find user permissions
      const userPermissions = await model.findOne({
        userId: new mongoose.Types.ObjectId(userId)
      });

      // If no permissions found, get default permissions for user role
      if (!userPermissions) {
        // Get user from database to determine role
        const User = mongoose.models.User;
        const user = await User.findById(userId).select('role');
        const role = user?.role || 'user';

        // Get custom default permissions for this role
        const defaultPerms = await getDefaultPermissionsImpl(role);

        return {
          userId,
          permissions: defaultPerms as PermissionSet || DEFAULT_PERMISSIONS[role as keyof typeof DEFAULT_PERMISSIONS] || DEFAULT_PERMISSIONS.user,
          isDefault: true
        };
      }

      return {
        userId,
        permissions: userPermissions.permissions,
        isDefault: false
      };
    } catch (error) {
      console.error('Error getting user permissions:', error);
      throw error;
    }
  };

  updateUserPermissionsImpl = async (userId: string, permissions: PermissionSet) => {
    try {
      // Connect to MongoDB
      await ensureMongooseConnection();

      // UserPermission is not null on server side
      const model = UserPermission as NonNullable<typeof UserPermission>;

      // Update or create user permissions
      const result = await model.findOneAndUpdate(
        { userId: new mongoose.Types.ObjectId(userId) },
        { permissions },
        { upsert: true, new: true }
      );

      return {
        userId,
        permissions: result.permissions
      };
    } catch (error) {
      console.error('Error updating user permissions:', error);
      throw error;
    }
  };

  resetUserPermissionsImpl = async (userId: string, role: 'user' | 'moderator' | 'admin') => {
    try {
      // Connect to MongoDB
      await ensureMongooseConnection();

      // Get custom default permissions for role
      const customDefaults = await getDefaultPermissionsImpl(role);

      // Use custom defaults if available, otherwise use system defaults
      const defaultPermissions = customDefaults as PermissionSet || DEFAULT_PERMISSIONS[role] || DEFAULT_PERMISSIONS.user;

      // UserPermission is not null on server side
      const model = UserPermission as NonNullable<typeof UserPermission>;

      // Update user permissions
      const result = await model.findOneAndUpdate(
        { userId: new mongoose.Types.ObjectId(userId) },
        { permissions: defaultPermissions },
        { upsert: true, new: true }
      );

      return {
        userId,
        permissions: result.permissions
      };
    } catch (error) {
      console.error('Error resetting user permissions:', error);
      throw error;
    }
  };

  hasPermissionImpl = async (userId: string, permission: string) => {
    try {
      const { permissions } = await getUserPermissionsImpl(userId);
      return !!permissions[permission];
    } catch (error) {
      console.error('Error checking user permission:', error);
      return false;
    }
  };
}

// Export the functions using their implementations
export const getDefaultPermissions = getDefaultPermissionsImpl;
export const setDefaultPermissions = setDefaultPermissionsImpl;
export const resetAllDefaultPermissions = resetAllDefaultPermissionsImpl;
export const getUserPermissions = getUserPermissionsImpl;
export const updateUserPermissions = updateUserPermissionsImpl;
export const resetUserPermissions = resetUserPermissionsImpl;
export const hasPermission = hasPermissionImpl;

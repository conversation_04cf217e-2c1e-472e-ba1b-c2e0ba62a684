'use client';

import Link from 'next/link';
import Image from 'next/image';
import { Navbar } from '@/components/Navbar';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-vista-dark text-vista-light flex flex-col">
      <Navbar />
      
      <div className="flex-1 flex items-center justify-center px-4">
        <div className="max-w-md text-center">
          <h1 className="text-6xl font-bold text-vista-blue mb-4">404</h1>
          <h2 className="text-2xl font-semibold text-vista-light mb-4">Page Not Found</h2>
          <p className="text-vista-light/80 mb-6">
            Sorry, the page you are looking for does not exist or has been moved.
          </p>
          <Link href="/">
            <Button className="bg-vista-blue hover:bg-vista-blue/90">
              Return Home
            </Button>
          </Link>
        </div>
      </div>
      
      <Footer />
    </div>
  )
}

'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import DetailBanner from '@/components/DetailBanner';
import EpisodeList from '@/components/EpisodeList';
import RelatedContent from '@/components/RelatedContent';
import { Button } from '@/components/ui/button';
import { Navbar } from '@/components/Navbar';
import Footer from '@/components/Footer';
import { ContentCardType } from '@/lib/content-utils';
import { Play, Info, Users, Clapperboard, Star } from 'lucide-react';
import { getTVShowDetails, getTVSeasonDetails } from '@/lib/tmdb-api';
import { Episode } from '@/types/index';
import { CastCrewSection } from '@/components/cast-crew/CastCrewSection';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/Tab';

interface TmdbEpisode {
  id: number;
  name: string;
  episode_number: number;
  season_number: number;
  overview: string;
  still_path: string | null;
  runtime: number;
  air_date: string;
}

// Sample detail data
const detailData = {
  id: 'stranger-things',
  title: 'Stranger Things',
  type: 'shows',
  description: 'When a young boy vanishes, a small town uncovers a mystery involving secret experiments, terrifying supernatural forces and one strange little girl.',
  releaseYear: 2016,
  ageRating: 'TV-14',
  creators: 'The Duffer Brothers',
  starring: ['Winona Ryder', 'David Harbour', 'Finn Wolfhard'],
  genres: ['Sci-Fi', 'Horror', 'Drama'],
  seasons: 4,
  imagePath: 'https://ext.same-assets.com/787489437/5677834534.jpg',
  bannerImage: 'https://ext.same-assets.com/349857/3458973451.jpg',
  duration: '45-60 min',
  imdbId: 'tt4574334',
  director: '',
  actors: [],
  awards: '',
  rated: '',
  released: '',
  metascore: 0,
  dataSource: 'tmdb',
  tmdbId: '66732',
  seasonData: {
    totalSeasons: 4,
    currentSeason: 1,
    currentEpisode: 1,
    episodeCount: 32
  }
};

// Mock data for related content
const relatedContent: ContentCardType[] = [
    { id: 'dark', title: 'Dark', imagePath: 'https://images.unsplash.com/photo-1536440136628-849c177e76a1?q=80&w=2125', type: 'shows', year: '2017', ageRating: 'TV-MA' },
    { id: 'mind-hunter', title: 'Mindhunter', imagePath: 'https://images.unsplash.com/photo-1536440136628-849c177e76a1?q=80&w=2125', type: 'shows', year: '2017', ageRating: 'TV-MA' },
    { id: 'the-oa', title: 'The OA', imagePath: 'https://images.unsplash.com/photo-1536440136628-849c177e76a1?q=80&w=2125', type: 'shows', year: '2016', ageRating: 'TV-MA' }
];

interface DetailClientProps {
  params: {
    type: string;
    id: string;
  }
}

export default function DetailClient({ params }: DetailClientProps) {
  const { type, id } = params;
  const router = useRouter();
  const [data, setData] = useState<typeof detailData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [seasons, setSeasons] = useState<{ seasonNumber: number; episodes: Episode[] }[]>([]);
  const [isLoadingSeasons, setIsLoadingSeasons] = useState(true);
  const [seasonCount, setSeasonCount] = useState(0);

  useEffect(() => {
    const fetchContentDetails = async () => {
      try {
        setLoading(true);
        if (!id || id === 'undefined' || id === 'null' || id === '[object Object]') {
          throw new Error(`Invalid content ID: ${id}`);
        }
        const contentType = type === 'shows' ? 'show' : 'movie';
        const response = await fetch(`/api/content?id=${id}&type=${contentType}`);
        if (!response.ok) {
          throw new Error(`Failed to fetch content details: ${response.status}`);
        }
        const contentData = await response.json();
        setData({
          id: contentData.id.toString(),
          title: contentData.title,
          type: type,
          description: contentData.overview || '',
          releaseYear: contentData.year ? parseInt(contentData.year) : 0,
          ageRating: contentData.rated || (contentType === 'movie' ? 'PG-13' : 'TV-14'),
          creators: contentData.director || contentData.creator || '',
          starring: contentData.actors || [],
          genres: contentData.genres || [],
          seasons: contentData.seasons || 1,
          imagePath: contentData.posterPath || '',
          bannerImage: contentData.backdropPath || contentData.posterPath || '',
          duration: contentData.runtime ? `${contentData.runtime} min` : '45-60 min',
          imdbId: contentData.imdbId || '',
          tmdbId: contentData.tmdbId || '',
          director: contentData.director || '',
          actors: contentData.actors || [],
          awards: contentData.awards || '',
          rated: contentData.rated || '',
          released: contentData.released || '',
          metascore: contentData.metascore || 0,
          dataSource: contentData.dataSource || 'tmdb',
          seasonData: {
            totalSeasons: contentData.seasons || 1,
            currentSeason: 1,
            currentEpisode: 1,
            episodeCount: 0
          }
        });
      } catch (error) {
        console.error('Error fetching content details:', error);
        setError(error instanceof Error ? error.message : 'Failed to load content details');
        setData({ ...detailData, id, type });
      } finally {
        setLoading(false);
      }
    };
    fetchContentDetails();
  }, [id, type]);

  useEffect(() => {
    const fetchSeasonData = async () => {
      if (!data || data.type !== 'shows' || !data.tmdbId) return;
      setIsLoadingSeasons(true);
      try {
        const showDetails = await getTVShowDetails(data.tmdbId);
        setSeasonCount(showDetails.number_of_seasons || 0);
        const season1Data = await getTVSeasonDetails(data.tmdbId, 1);
        const formattedSeason = {
          seasonNumber: 1,
          episodes: season1Data.episodes.map((ep: TmdbEpisode) => ({
            id: `${ep.id}`,
            title: ep.name,
            episodeNumber: ep.episode_number,
            seasonNumber: ep.season_number,
            description: ep.overview,
            thumbnail: ep.still_path ? `https://image.tmdb.org/t/p/w780${ep.still_path}` : '',
            runtime: ep.runtime || 0,
            airDate: ep.air_date
          }))
        };
        setSeasons([formattedSeason]);
      } catch (error) {
        console.error('Error fetching TV show seasons:', error);
      } finally {
        setIsLoadingSeasons(false);
      }
    };
    fetchSeasonData();
  }, [data]);

  useEffect(() => {
    const handleSeasonChange = async (event: CustomEvent) => {
      const { season } = event.detail;
      if (!data?.tmdbId || !season) return;
      const existingSeason = seasons.find(s => s.seasonNumber === season);
      if (existingSeason) return;
      setIsLoadingSeasons(true);
      try {
        const seasonData = await getTVSeasonDetails(data.tmdbId, season);
        const formattedSeason = {
          seasonNumber: season,
          episodes: seasonData.episodes.map((ep: TmdbEpisode) => ({
            id: `${ep.id}`,
            title: ep.name,
            episodeNumber: ep.episode_number,
            seasonNumber: ep.season_number,
            description: ep.overview,
            thumbnail: ep.still_path ? `https://image.tmdb.org/t/p/w780${ep.still_path}` : '',
            runtime: ep.runtime || 0,
            airDate: ep.air_date
          }))
        };
        setSeasons(prev => [...prev, formattedSeason].sort((a, b) => a.seasonNumber - b.seasonNumber));
      } catch (error) {
        console.error(`Error fetching season ${season}:`, error);
      } finally {
        setIsLoadingSeasons(false);
      }
    };
    window.addEventListener('seasonChange', handleSeasonChange as unknown as EventListener);
    return () => window.removeEventListener('seasonChange', handleSeasonChange as unknown as EventListener);
  }, [data?.tmdbId, seasons]);

  if (loading) {
    return (
      <div className="min-h-screen bg-vista-dark flex items-center justify-center">
        <div className="w-16 h-16 border-4 border-vista-blue/20 border-t-vista-blue rounded-full animate-spin"></div>
      </div>
    );
  }

  if (error || !data || !id || id === 'undefined' || id === 'null' || id === '[object Object]') {
    return (
      <div className="min-h-screen bg-vista-dark flex items-center justify-center">
        <div className="text-center p-8 bg-vista-dark-lighter rounded-lg max-w-md">
          <Info className="h-16 w-16 text-vista-light/30 mx-auto mb-4" />
          <h2 className="text-2xl text-vista-light font-bold">Content not found</h2>
          <p className="text-vista-light/70 mt-2">{error || `Unable to load content details. Invalid ID: ${id}`}</p>
          <Button className="mt-6 bg-vista-blue hover:bg-vista-blue/90 text-white" onClick={() => router.push('/')}>
            Return Home
          </Button>
        </div>
      </div>
    );
  }

  const isShow = type === 'shows';

  const handlePlay = () => {
    const contentType = type === 'shows' ? 'show' : 'movie';
    const idType = data.imdbId ? 'imdb' : 'tmdb';
    const watchId = data.imdbId || data.tmdbId;
    if (watchId) {
      router.push(`/watch/${watchId}?forcePlay=true&contentType=${contentType}`);
    } else {
      router.push(`/watch/${data.id}?forcePlay=true&contentType=${contentType}`);
    }
  };

  return (
    <div className="min-h-screen bg-vista-dark text-vista-light">
      <Navbar />
      <DetailBanner
        title={data.title}
        description={data.description}
        year={data.releaseYear}
        ageRating={data.ageRating}
        duration={data.duration}
        genres={data.genres}
        starring={data.starring}
        creator={data.creators}
        imagePath={data.bannerImage || data.imagePath}
        director={data.director}
        awards={data.awards}
        rated={data.rated}
        released={data.released}
        metascore={data.metascore}
      />

      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-wrap gap-3 mb-8">
          <Button className="bg-vista-blue hover:bg-vista-blue/90 text-white gap-2 rounded-full px-6" onClick={handlePlay}>
            <Play className="h-4 w-4" />
            {isShow ? 'Watch S1:E1' : 'Watch Now'}
          </Button>
          <Button className="bg-vista-accent hover:bg-vista-accent-dim text-white gap-2 rounded-full px-6" onClick={() => router.push(`/watch-party/create?content=${data.id}`)}>
            Watch Party
          </Button>
          <Button variant="outline" className="border-vista-light/30 text-vista-light hover:bg-vista-light/10 gap-2 rounded-full">Add to My List</Button>
          <Button variant="outline" className="border-vista-light/30 text-vista-light hover:bg-vista-light/10 gap-2 rounded-full">Share</Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12">
          <div className="lg:col-span-2">
            <Tabs defaultValue="overview" className="w-full">
              <TabsList className="grid w-full grid-cols-3 bg-vista-dark-lighter">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="cast">Cast & Crew</TabsTrigger>
                <TabsTrigger value="details">Details</TabsTrigger>
              </TabsList>
              <TabsContent value="overview" className="mt-6">
                <p className="text-vista-light/80 leading-relaxed">{data.description}</p>
              </TabsContent>
              <TabsContent value="cast" className="mt-6">
                {(() => {
                  console.log('Cast Data:', Array.isArray(data.actors) && data.actors.length > 0
                      ? (data.actors as Array<{ id?: string | number; name?: string; role?: string; image?: string }>)
                          .map((actor, i) => ({
                            id: (actor.id || i).toString(),
                            name: actor.name || (typeof actor === 'string' ? actor : ''),
                            character: actor.role || '',
                            profile_path: actor.image || '',
                          }))
                      : (data.starring || []).map((name, i) => ({ id: i.toString(), name })));
                  return null;
                })()}
                <CastCrewSection
                  cast={Array.isArray(data.actors) && data.actors.length > 0
                    ? (data.actors as Array<{ id?: string | number; name?: string; role?: string; image?: string }>)
                        .map((actor, i) => ({
                          id: (actor.id || i).toString(),
                          name: actor.name || (typeof actor === 'string' ? actor : ''),
                          character: actor.role || '',
                          profile_path: actor.image || '',
                        }))
                    : (data.starring || []).map((name, i) => ({ id: i.toString(), name }))}
                  crew={[]}
                />
              </TabsContent>
              <TabsContent value="details" className="mt-6">
                <div className="space-y-3 text-sm">
                  <p><span className="font-semibold text-vista-light/60 w-24 inline-block">Creators:</span> {data.creators}</p>
                  {data.director && <p><span className="font-semibold text-vista-light/60 w-24 inline-block">Director:</span> {data.director}</p>}
                  <p><span className="font-semibold text-vista-light/60 w-24 inline-block">Genres:</span> {data.genres?.join(', ')}</p>
                  {data.released && <p><span className="font-semibold text-vista-light/60 w-24 inline-block">Released:</span> {data.released}</p>}
                  {data.rated && <p><span className="font-semibold text-vista-light/60 w-24 inline-block">Rated:</span> {data.rated}</p>}
                  {data.awards && <p><span className="font-semibold text-vista-light/60 w-24 inline-block">Awards:</span> {data.awards}</p>}
                  {data.metascore > 0 && <p><span className="font-semibold text-vista-light/60 w-24 inline-block">Metascore:</span> {data.metascore}</p>}
                  {data.imdbId && <p><span className="font-semibold text-vista-light/60 w-24 inline-block">IMDb:</span> <a href={`https://www.imdb.com/title/${data.imdbId}`} target="_blank" rel="noopener noreferrer" className="text-vista-blue hover:underline">{data.imdbId}</a></p>}
                  {data.tmdbId && <p><span className="font-semibold text-vista-light/60 w-24 inline-block">TMDb ID:</span> {data.tmdbId}</p>}
                  {data.dataSource && <p><span className="font-semibold text-vista-light/60 w-24 inline-block">Data Source:</span> {data.dataSource}</p>}
                </div>
              </TabsContent>
            </Tabs>
          </div>

          <div className="lg:col-span-1">
            {isShow && (
              <div className="mb-10">
                <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                  <Clapperboard className="w-5 h-5 text-vista-blue" />
                  Episodes
                </h2>
                <EpisodeList
                  seasons={seasons}
                  showTitle={data.title}
                  imdbId={data.imdbId}
                  tmdbId={data.tmdbId}
                  contentId={data.id}
                  seasonCount={seasonCount}
                  isLoading={isLoadingSeasons}
                />
              </div>
            )}
            <div>
              <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                <Star className="w-5 h-5 text-vista-blue" />
                More Like This
              </h2>
              <RelatedContent title="More Like This" contents={relatedContent} />
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}

import mongoose, { Document, Schema } from 'mongoose';

export interface IContentAnalytics extends Document {
  contentId: mongoose.Types.ObjectId;
  views: number;
  uniqueViewers: number;
  totalWatchTime: number; // in seconds
  averageWatchTime: number; // in seconds
  completionRate: number; // percentage
  likes: number;
  dislikes: number;
  favorites: number;
  shares: number;
  lastUpdated: Date;
}

const ContentAnalyticsSchema = new Schema<IContentAnalytics>(
  {
    contentId: {
      type: Schema.Types.ObjectId,
      ref: 'Content',
      required: true,
      unique: true,
      index: true
    },
    views: {
      type: Number,
      default: 0
    },
    uniqueViewers: {
      type: Number,
      default: 0
    },
    totalWatchTime: {
      type: Number,
      default: 0
    },
    averageWatchTime: {
      type: Number,
      default: 0
    },
    completionRate: {
      type: Number,
      default: 0
    },
    likes: {
      type: Number,
      default: 0
    },
    dislikes: {
      type: Number,
      default: 0
    },
    favorites: {
      type: Number,
      default: 0
    },
    shares: {
      type: Number,
      default: 0
    },
    lastUpdated: {
      type: Date,
      default: Date.now
    }
  },
  {
    timestamps: true
  }
);

// Create the model if it doesn't exist already
const ContentAnalytics = mongoose.models.ContentAnalytics || mongoose.model<IContentAnalytics>('ContentAnalytics', ContentAnalyticsSchema);

export default ContentAnalytics;

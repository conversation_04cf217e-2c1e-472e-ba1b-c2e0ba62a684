import { useState, useEffect } from 'react';
import Image from 'next/image';
import { getPersonImage } from '@/lib/image-utils';
import { getTMDbImageUrl } from '@/lib/tmdb-api';

// Helper function to get initials from a name
const getInitials = (name: string): string => {
  if (!name || typeof name !== 'string') return '?';

  // Clean the name: remove extra spaces, special characters that might cause issues
  const cleanName = name.trim().replace(/[^\w\s\u00C0-\u017F\u0100-\u024F]/g, '');
  
  if (!cleanName) return '?';

  // Split the name by spaces and get the first letter of each part
  const parts = cleanName.split(/\s+/).filter(part => part.length > 0);

  if (parts.length === 0) return '?';

  if (parts.length === 1) {
    // If only one part, return the first two letters or just the first if it's a single letter
    const firstPart = parts[0];
    return firstPart.substring(0, Math.min(2, firstPart.length)).toUpperCase();
  }

  // Return the first letter of the first part and the first letter of the last part
  // Handle cases where parts might be empty after cleaning
  const firstLetter = parts[0][0] || '?';
  const lastLetter = parts[parts.length - 1][0] || '';
  
  return (firstLetter + lastLetter).toUpperCase();
};

interface PersonImageProps {
  id: string;
  name: string;
  profilePath: string | null;
  gender?: number;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  priority?: boolean;
}

/**
 * Enhanced component for displaying actor/crew member images with advanced fallback
 * Uses multiple sources to ensure high image availability
 */
export default function PersonImage({
  id,
  name,
  profilePath,
  gender,
  size = 'md',
  className = '',
  priority = false
}: PersonImageProps) {
  // Early safety checks to prevent issues with invalid props
  const safeName = name || 'Unknown';
  const safeId = id || 'unknown';
  
  // State for the image URL
  const [imageUrl, setImageUrl] = useState<string | null>(
    profilePath ? getTMDbImageUrl(profilePath, 'w342') : null // Increased size from w185 to w342
  );
  const [isLoading, setIsLoading] = useState(!imageUrl);
  const [hasError, setHasError] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 2; // Allow up to 2 retry attempts

  // Fixed size mappings (no dynamic classes)
  const sizeMap = {
    sm: { width: 32, height: 32, textSize: 'text-xs', className: 'w-8 h-8' },
    md: { width: 40, height: 40, textSize: 'text-sm', className: 'w-10 h-10' },
    lg: { width: 56, height: 56, textSize: 'text-base', className: 'w-14 h-14' }
  };

  const { width, height, textSize, className: sizeClassName } = sizeMap[size];

  // Log props on mount
  useEffect(() => {
    console.log(`[PersonImage] Component mounted for ${safeName} (ID: ${safeId})`, {
      id: safeId,
      name: safeName,
      profilePath,
      gender,
      size,
      priority
    });
  }, [safeId, safeName, profilePath, gender, size, priority]);

  // Fetch enhanced image if needed
  useEffect(() => {
    // Skip if we already have a TMDb image
    if (imageUrl) {
      console.log(`[PersonImage] Already have image URL for ${safeName}: ${imageUrl.substring(0, 50)}...`);
      return;
    }

    // Skip if we've already tried and failed for this person
    if (hasError) {
      console.log(`[PersonImage] Already tried and failed for ${safeName}`);
      return;
    }

    // Skip if no valid ID
    if (!safeId || safeId === 'unknown') {
      console.warn(`[PersonImage] Missing or invalid ID for ${safeName}`);
      setDebugInfo("Missing ID");
      setHasError(true);
      setIsLoading(false);
      return;
    }

    // Attempt to fetch enhanced image
    const fetchEnhancedImage = async () => {
      try {
        setIsLoading(true);
        console.log(`[PersonImage] Fetching image for ${safeName} (ID: ${safeId})`);
        setDebugInfo(`Fetching image for ${safeName} (ID: ${safeId})`);

        // Try to get image directly from TMDb first if we have a profile path
        if (profilePath) {
          console.log(`[PersonImage] Using TMDb image for ${safeName}: ${profilePath}`);
          const tmdbUrl = getTMDbImageUrl(profilePath, 'w342');
          setImageUrl(tmdbUrl);
          setDebugInfo(`Using TMDb image: ${profilePath}`);
          return;
        }

        // If no profile path, try enhanced sources
        console.log(`[PersonImage] No profile path for ${safeName}, trying enhanced sources`);
        const enhancedImageUrl = await getPersonImage(safeId, safeName, profilePath, gender);

        if (enhancedImageUrl) {
          console.log(`[PersonImage] Found enhanced image for ${safeName}: ${enhancedImageUrl.substring(0, 50)}...`);
          setImageUrl(enhancedImageUrl);
          setDebugInfo(`Found enhanced image from: ${
            enhancedImageUrl.includes('themoviedb.org') ? 'TMDb' :
            enhancedImageUrl.includes('wikimedia.org') ? 'Wikipedia' :
            enhancedImageUrl.startsWith('data:') ? 'Generated' : 'Other'
          }`);
        } else {
          console.warn(`[PersonImage] No image found for ${safeName}`);
          setHasError(true);
          setDebugInfo(`No image found for ${safeName}`);
        }
      } catch (error) {
        console.warn(`[PersonImage] Error fetching image for ${safeName} (ID: ${safeId}):`, error);
        setHasError(true);
        setDebugInfo(`Error: ${error instanceof Error ? error.message : String(error)}`);
      } finally {
        setIsLoading(false);
      }
    };

    fetchEnhancedImage();
  }, [safeId, safeName, profilePath, gender, hasError, imageUrl]);

  // If we have an image URL, display the image
  if (imageUrl && !hasError) {
    // Sanitize the image URL to ensure it's valid
    let sanitizedImageUrl = imageUrl;

    // Handle Wikipedia/Wikimedia URLs that might have issues
    if (imageUrl.includes('wikimedia.org') || imageUrl.includes('wikipedia.org')) {
      try {
        // For Wikimedia URLs, use a direct approach with error handling
        if (profilePath) {
          // If we have a TMDb profile path, prefer that as fallback
          sanitizedImageUrl = getTMDbImageUrl(profilePath, 'w185');
        } else {
          // Use the original URL but with better error handling
          sanitizedImageUrl = imageUrl;
        }
      } catch (e) {
        console.warn(`[PersonImage] URL sanitization failed for ${safeName}:`, e);
        // Fall back to initials on sanitization error
        setHasError(true);
        setImageUrl(null);
        setDebugInfo(`URL sanitization failed`);
        return null; // This will cause a re-render to show initials
      }
    }

    return (
      <div
        className={`${sizeClassName} rounded-full overflow-hidden flex-shrink-0 border-2 border-vista-dark-lighter shadow-sm ${className}`}
        title={debugInfo || safeName}
      >
        <Image
          src={sanitizedImageUrl}
          alt={safeName}
          width={width}
          height={height}
          className="object-cover w-full h-full"
          priority={priority || size === 'lg'} // Prioritize loading larger images
          unoptimized={sanitizedImageUrl.startsWith('data:') || sanitizedImageUrl.includes('/api/wiki-image')} // Skip optimization for data URLs and proxied URLs
          onError={(e) => {
            // Prevent the error from bubbling up as an unhandled error
            e.preventDefault();
            e.stopPropagation();
            
            // Log the error for debugging but don't throw
            if (process.env.NODE_ENV === 'development') {
              console.warn(`[PersonImage] Image load failed for ${safeName} (ID: ${safeId}): "${sanitizedImageUrl}"`);
            }

            // Try different fallback strategies based on current retry count
            const currentRetry = retryCount;
            
            if (currentRetry < maxRetries) {
              const nextRetry = currentRetry + 1;
              setRetryCount(nextRetry);
              
              if (currentRetry === 0 && profilePath && !sanitizedImageUrl.includes('tmdb.org')) {
                // First retry: Try TMDb image if we have profile path and weren't already using it
                const tmdbFallback = getTMDbImageUrl(profilePath, 'w185');
                console.warn(`[PersonImage] Retry ${nextRetry} for ${safeName}: Trying TMDb fallback`);
                setImageUrl(tmdbFallback);
                setDebugInfo(`Retry ${nextRetry}: TMDb fallback`);
                return;
              } else if (currentRetry === 1 && !sanitizedImageUrl.startsWith('data:')) {
                // Second retry: Try generating a placeholder
                console.warn(`[PersonImage] Retry ${nextRetry} for ${safeName}: Generating placeholder`);
                try {
                  const colorIndex = safeName.charCodeAt(0) % 360;
                  // Generate a simple SVG placeholder
                  const hue = colorIndex;
                  const saturation = gender === 1 ? 80 : gender === 2 ? 70 : 75;
                  const lightness = gender === 1 ? 65 : gender === 2 ? 60 : 62;
                  const bgColor = `hsl(${hue}, ${saturation}%, ${lightness}%)`;
                  
                  // Use a try-catch for btoa in case it's not available
                  let placeholderUrl;
                  try {
                    const svg = `<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200"><rect width="200" height="200" fill="${bgColor}" /><circle cx="100" cy="70" r="40" fill="#ffffff" opacity="0.9" /><rect x="60" y="120" width="80" height="60" rx="10" fill="#ffffff" opacity="0.9" /></svg>`;
                    const base64 = btoa(svg);
                    placeholderUrl = `data:image/svg+xml;base64,${base64}`;
                  } catch (encodeError) {
                    // If btoa fails, create a simple data URL with URL encoding
                    const svg = `<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200"><rect width="200" height="200" fill="${bgColor}" /><circle cx="100" cy="70" r="40" fill="#ffffff" opacity="0.9" /><rect x="60" y="120" width="80" height="60" rx="10" fill="#ffffff" opacity="0.9" /></svg>`;
                    placeholderUrl = `data:image/svg+xml,${encodeURIComponent(svg)}`;
                  }
                  
                  setImageUrl(placeholderUrl);
                  setDebugInfo(`Retry ${nextRetry}: Generated placeholder`);
                  return;
                } catch (placeholderError) {
                  console.warn(`[PersonImage] Placeholder generation failed for ${safeName}:`, placeholderError);
                }
              }
            }

            // All retries exhausted or no more fallback options - show initials
            setImageUrl(null);
            setHasError(true);
            setDebugInfo(`Image load failed after ${currentRetry + 1} attempts`);
          }}
          onLoad={() => {
            // Reset retry count on successful load
            setRetryCount(0);
            setDebugInfo(debugInfo || `Loaded successfully`);
          }}
        />
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <div
        className={`${sizeClassName} rounded-full flex-shrink-0 border-2 border-vista-dark-lighter shadow-sm bg-vista-dark/60 flex items-center justify-center animate-pulse ${className}`}
        title={`Loading image for ${safeName}...`}
      />
    );
  }

  // Fallback to initials
  return (
    <div
      className={`${sizeClassName} rounded-full bg-vista-blue/20 flex items-center justify-center flex-shrink-0 border-2 border-vista-dark-lighter shadow-sm ${className}`}
      title={debugInfo || `No image available for ${safeName}`}
    >
      <span className={`${textSize} text-vista-light font-medium`}>
        {getInitials(safeName)}
      </span>
    </div>
  );
}
import { NextRequest, NextResponse } from 'next/server';

/**
 * GET /api/admin/users/[id]/subscription
 * Get subscription details for a specific user
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the user ID from the cookie directly
    const userId = request.cookies.get('userId')?.value;
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const UserSchema = new mongoose.default.Schema({
      name: String,
      email: String,
      profileImage: String,
      role: String,
      status: String,
      emailVerified: Date,
      lastLogin: Date,
      subscription: String,
      subscriptionStatus: String,
      subscriptionRenewal: Date
    }, {
      timestamps: true
    });

    const User = mongoose.default.models.User ||
                mongoose.default.model('User', UserSchema);

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the Subscription schema directly
    const SubscriptionSchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      plan: String,
      status: String,
      startDate: Date,
      renewalDate: Date,
      endDate: Date,
      canceledAt: Date,
      price: Number,
      interval: String
    }, {
      timestamps: true
    });

    // Get the Subscription model
    const Subscription = mongoose.default.models.Subscription ||
                        mongoose.default.model('Subscription', SubscriptionSchema);

    // Define the Transaction schema directly
    const TransactionSchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      subscriptionId: mongoose.default.Schema.Types.ObjectId,
      amount: Number,
      currency: String,
      status: String,
      type: String,
      description: String,
      transactionDate: Date
    }, {
      timestamps: true
    });

    // Get the Transaction model
    const Transaction = mongoose.default.models.Transaction ||
                       mongoose.default.model('Transaction', TransactionSchema);

    // Validate user ID
    if (!mongoose.default.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
    }

    // Check if target user exists
    const targetUser = await User.findById(params.id);
    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get active subscription
    const activeSubscription = await Subscription.findOne({
      userId: params.id,
      status: { $in: ['active', 'pending'] }
    }).sort({ createdAt: -1 });

    // Get subscription history
    const subscriptionHistory = await Subscription.find({
      userId: params.id
    }).sort({ createdAt: -1 }).limit(10);

    // Get recent transactions
    const recentTransactions = await Transaction.find({
      userId: params.id
    }).sort({ transactionDate: -1 }).limit(5);

    // Format response
    const response = {
      currentSubscription: activeSubscription ? {
        id: activeSubscription._id.toString(),
        plan: activeSubscription.plan,
        status: activeSubscription.status,
        startDate: activeSubscription.startDate,
        renewalDate: activeSubscription.renewalDate,
        price: activeSubscription.price,
        interval: activeSubscription.interval
      } : {
        plan: targetUser.subscription || 'free',
        status: targetUser.subscriptionStatus || 'active',
        renewalDate: targetUser.subscriptionRenewal
      },
      history: subscriptionHistory.map((sub: any) => ({
        id: sub._id.toString(),
        plan: sub.plan,
        status: sub.status,
        startDate: sub.startDate,
        endDate: sub.endDate,
        price: sub.price,
        interval: sub.interval
      })),
      transactions: recentTransactions.map((tx: any) => ({
        id: tx._id.toString(),
        amount: tx.amount,
        currency: tx.currency,
        status: tx.status,
        type: tx.type,
        description: tx.description,
        date: tx.transactionDate
      }))
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching user subscription:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user subscription', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/users/[id]/subscription
 * Update subscription for a specific user
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the user ID from the cookie directly
    const userId = request.cookies.get('userId')?.value;
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const UserSchema = new mongoose.default.Schema({
      name: String,
      email: String,
      profileImage: String,
      role: String,
      status: String,
      emailVerified: Date,
      lastLogin: Date,
      subscription: String,
      subscriptionStatus: String,
      subscriptionRenewal: Date
    }, {
      timestamps: true
    });

    const User = mongoose.default.models.User ||
                mongoose.default.model('User', UserSchema);

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the Subscription schema directly
    const SubscriptionSchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      plan: String,
      status: String,
      startDate: Date,
      renewalDate: Date,
      endDate: Date,
      canceledAt: Date,
      price: Number,
      interval: String
    }, {
      timestamps: true
    });

    // Get the Subscription model
    const Subscription = mongoose.default.models.Subscription ||
                        mongoose.default.model('Subscription', SubscriptionSchema);

    // Define the Transaction schema directly
    const TransactionSchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      subscriptionId: mongoose.default.Schema.Types.ObjectId,
      amount: Number,
      currency: String,
      status: String,
      type: String,
      description: String,
      transactionDate: Date
    }, {
      timestamps: true
    });

    // Get the Transaction model
    const Transaction = mongoose.default.models.Transaction ||
                       mongoose.default.model('Transaction', TransactionSchema);

    // Define the UserActivity schema directly
    const UserActivitySchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      action: String,
      details: String,
      ipAddress: String,
      userAgent: String,
      timestamp: { type: Date, default: Date.now },
      metadata: mongoose.default.Schema.Types.Mixed
    }, {
      timestamps: true
    });

    // Get the UserActivity model
    const UserActivity = mongoose.default.models.UserActivity ||
                        mongoose.default.model('UserActivity', UserActivitySchema);

    // Validate user ID
    if (!mongoose.default.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
    }

    // Check if target user exists
    const targetUser = await User.findById(params.id);
    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get request data
    const data = await request.json();

    // Validate subscription data
    if (!data.plan || !['free', 'basic', 'premium', 'family'].includes(data.plan)) {
      return NextResponse.json(
        { error: 'Invalid subscription plan' },
        { status: 400 }
      );
    }

    if (!data.status || !['active', 'canceled', 'expired', 'pending'].includes(data.status)) {
      return NextResponse.json(
        { error: 'Invalid subscription status' },
        { status: 400 }
      );
    }

    // Get current active subscription
    const currentSubscription = await Subscription.findOne({
      userId: params.id,
      status: { $in: ['active', 'pending'] }
    });

    // If changing from free to paid plan, create a new subscription
    if (data.plan !== 'free' && (!currentSubscription || currentSubscription.plan === 'free')) {
      // Create new subscription
      const newSubscription = await Subscription.create({
        userId: params.id,
        plan: data.plan,
        status: data.status,
        startDate: new Date(),
        renewalDate: data.renewalDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        price: getPlanPrice(data.plan, data.interval || 'monthly'),
        interval: data.interval || 'monthly'
      });

      // Create transaction record
      await Transaction.create({
        userId: params.id,
        subscriptionId: newSubscription._id,
        amount: newSubscription.price,
        currency: 'USD',
        status: 'completed',
        type: 'subscription',
        description: `Subscription to ${data.plan} plan (${data.interval || 'monthly'})`,
        transactionDate: new Date()
      });

      // Update user record
      await User.findByIdAndUpdate(params.id, {
        subscription: data.plan,
        subscriptionStatus: data.status,
        subscriptionRenewal: data.renewalDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
      });
    }
    // If changing existing subscription
    else if (currentSubscription) {
      // If canceling subscription
      if (data.status === 'canceled' && currentSubscription.status === 'active') {
        await Subscription.findByIdAndUpdate(currentSubscription._id, {
          status: 'canceled',
          canceledAt: new Date(),
          endDate: data.endDate || currentSubscription.renewalDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
        });
      }
      // If changing plan
      else if (data.plan !== currentSubscription.plan) {
        // End current subscription
        await Subscription.findByIdAndUpdate(currentSubscription._id, {
          status: 'expired',
          endDate: new Date()
        });

        // Create new subscription
        const newSubscription = await Subscription.create({
          userId: params.id,
          plan: data.plan,
          status: data.status,
          startDate: new Date(),
          renewalDate: data.renewalDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          price: getPlanPrice(data.plan, data.interval || currentSubscription.interval),
          interval: data.interval || currentSubscription.interval
        });

        // Create transaction record
        await Transaction.create({
          userId: params.id,
          subscriptionId: newSubscription._id,
          amount: newSubscription.price,
          currency: 'USD',
          status: 'completed',
          type: 'subscription',
          description: `Changed subscription to ${data.plan} plan (${data.interval || currentSubscription.interval})`,
          transactionDate: new Date()
        });
      }
      // Just update status or other details
      else {
        await Subscription.findByIdAndUpdate(currentSubscription._id, {
          status: data.status,
          renewalDate: data.renewalDate || currentSubscription.renewalDate,
          interval: data.interval || currentSubscription.interval,
          price: data.interval !== currentSubscription.interval ?
            getPlanPrice(data.plan, data.interval) :
            currentSubscription.price
        });
      }

      // Update user record
      await User.findByIdAndUpdate(params.id, {
        subscription: data.plan,
        subscriptionStatus: data.status,
        subscriptionRenewal: data.renewalDate ||
          (currentSubscription.renewalDate ? new Date(currentSubscription.renewalDate) :
          new Date(Date.now() + 30 * 24 * 60 * 60 * 1000))
      });
    }
    // Just updating the free plan status
    else {
      // Update user record
      await User.findByIdAndUpdate(params.id, {
        subscription: data.plan,
        subscriptionStatus: data.status,
        subscriptionRenewal: data.renewalDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
      });
    }

    // Log admin activity directly
    await UserActivity.create({
      userId: new mongoose.default.Types.ObjectId(userId),
      type: 'admin',
      action: 'update_user_subscription',
      details: `Admin updated subscription for user: ${targetUser.name} (${targetUser.email}) to ${data.plan} plan`,
      ipAddress: request.ip || request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      timestamp: new Date(),
      metadata: {
        userId: params.id,
        plan: data.plan,
        status: data.status
      }
    });

    // Get updated subscription
    const updatedSubscription = await Subscription.findOne({
      userId: params.id,
      status: { $in: ['active', 'pending', 'canceled'] }
    }).sort({ createdAt: -1 });

    // Return updated subscription
    return NextResponse.json({
      success: true,
      subscription: updatedSubscription ? {
        id: updatedSubscription._id.toString(),
        plan: updatedSubscription.plan,
        status: updatedSubscription.status,
        startDate: updatedSubscription.startDate,
        renewalDate: updatedSubscription.renewalDate,
        price: updatedSubscription.price,
        interval: updatedSubscription.interval
      } : {
        plan: data.plan,
        status: data.status,
        renewalDate: data.renewalDate
      }
    });
  } catch (error) {
    console.error('Error updating user subscription:', error);
    return NextResponse.json(
      { error: 'Failed to update user subscription', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// Helper function to get plan price
function getPlanPrice(plan: string, interval: string): number {
  const prices = {
    free: 0,
    basic: interval === 'monthly' ? 9.99 : 99.99,
    premium: interval === 'monthly' ? 14.99 : 149.99,
    family: interval === 'monthly' ? 19.99 : 199.99
  };

  return prices[plan as keyof typeof prices] || 0;
}

import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';

/**
 * POST /api/admin/users/import
 * Import users from JSON data
 */
export async function POST(request: NextRequest) {
  try {
    // Get the user ID from the cookie directly
    const userId = request.cookies.get('userId')?.value;
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const UserSchema = new mongoose.default.Schema({
      name: String,
      email: { type: String, required: true, unique: true },
      password: {
        type: String,
        required: function(this: { googleId?: string }) {
          // Password is required only if googleId is not present
          return !this.googleId;
        }
      },
      profileImage: String,
      googleId: String,
      role: { type: String, default: 'user' },
      status: { type: String, default: 'active' },
      emailVerified: Date,
      lastLogin: Date
    }, {
      timestamps: true
    });

    const User = mongoose.default.models.User ||
                mongoose.default.model('User', UserSchema);

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as { role?: string }).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Get users data from request
    const { users } = await request.json();

    if (!users || !Array.isArray(users) || users.length === 0) {
      return NextResponse.json(
        { error: 'No valid users data provided' },
        { status: 400 }
      );
    }

    // Validate and process each user
    const results = {
      success: 0,
      failed: 0,
      errors: [] as string[]
    };

    // Process users in batches to avoid overwhelming the database
    const batchSize = 50;
    const batches = Math.ceil(users.length / batchSize);

    for (let i = 0; i < batches; i++) {
      const start = i * batchSize;
      const end = Math.min(start + batchSize, users.length);
      const batch = users.slice(start, end);

      // Define the user data interface
      interface ImportUserData {
        email: string;
        name: string;
        password?: string;
        profileImage?: string;
        role?: string;
        emailVerified?: Date | null;
      }

      // Process each user in the batch
      const batchPromises = batch.map(async (userData: ImportUserData, index: number) => {
        try {
          // Validate required fields
          if (!userData.email || !userData.name) {
            results.failed++;
            results.errors.push(`User at index ${start + index}: Missing required fields (email, name)`);
            return;
          }

          // Check if email already exists
          const existingUser = await User.findOne({ email: userData.email });
          if (existingUser) {
            results.failed++;
            results.errors.push(`User at index ${start + index}: Email already in use (${userData.email})`);
            return;
          }

          // Generate a random password if not provided
          let password = userData.password;
          if (!password) {
            // Generate a random password (8 characters)
            password = Math.random().toString(36).slice(-8);
          }

          // Hash password
          const hashedPassword = await bcrypt.hash(password, 10);

          // Create new user
          await User.create({
            name: userData.name,
            email: userData.email,
            password: hashedPassword,
            profileImage: userData.profileImage || '',
            role: userData.role || 'user',
            emailVerified: userData.emailVerified || null,
          });

          results.success++;
        } catch (error) {
          results.failed++;
          results.errors.push(`User at index ${start + index}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      });

      // Wait for all users in this batch to be processed
      await Promise.all(batchPromises);
    }

    // Return results
    return NextResponse.json({
      message: `Import completed. ${results.success} users imported successfully, ${results.failed} failed.`,
      results
    });
  } catch (error) {
    console.error('Error importing users:', error);
    return NextResponse.json(
      { error: 'Failed to import users', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

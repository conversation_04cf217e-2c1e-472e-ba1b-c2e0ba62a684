import { NextRequest, NextResponse } from 'next/server';
import mongoose from 'mongoose';
import User from '@/models/User';
import bcrypt from 'bcryptjs';
import { ensureMongooseConnection } from '@/lib/mongoose';

/**
 * PUT /api/user/password
 * Update user password
 */
export async function PUT(req: NextRequest) {
  try {
    // Parse request body
    const { userId, newPassword } = await req.json();

    // Validate inputs
    if (!userId || !newPassword) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate password strength
    if (newPassword.length < 8) {
      return NextResponse.json(
        { success: false, error: 'Password must be at least 8 characters long' },
        { status: 400 }
      );
    }

    // Connect to the database
    await ensureMongooseConnection();

    // Find user
    const user = await User.findById(userId);
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if user has a password (Google users might not)
    if (user.googleId) {
      return NextResponse.json(
        { success: false, error: 'Cannot update password for social login accounts' },
        { status: 400 }
      );
    }

    // Hash the new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // Update password directly using updateOne to bypass the pre-save middleware
    // This prevents double-hashing of the password
    await User.updateOne(
      { _id: userId },
      { $set: { password: hashedPassword } }
    );

    return NextResponse.json({
      success: true,
      message: 'Password updated successfully'
    });
  } catch (error) {
    console.error('Error updating password:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update password' },
      { status: 500 }
    );
  }
} 
'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Users, MessageCircle, Settings, Share2, ArrowLeft, Crown, Mic, MicOff } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import VidSrcPlayer from '@/components/VidSrcPlayer';
import { Navbar } from '@/components/Navbar';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface WatchPartyMember {
  id: string;
  name: string;
  avatar?: string;
  isHost: boolean;
  isReady: boolean;
  joinedAt: string;
}

interface ChatMessage {
  id: string;
  sender: string;
  content: string;
  timestamp: string;
  type: 'text' | 'system' | 'reaction';
}

interface WatchPartyData {
  id: string;
  title: string;
  hostId: string;
  members: WatchPartyMember[];
  messages: ChatMessage[];
  currentTime: number;
  isPlaying: boolean;
  contentId: string;
  contentType: 'movie' | 'show';
  season?: number;
  episode?: number;
  isPrivate: boolean;
  maxMembers: number;
  allowChat: boolean;
  allowReactions: boolean;
}

export default function WatchPartyViewPage({ params }: { params: Promise<{ partyId: string }> }) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [partyData, setPartyData] = useState<WatchPartyData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const [isHost, setIsHost] = useState(false);
  const [showMembers, setShowMembers] = useState(false);
  const [activeTab, setActiveTab] = useState('chat');

  // Extract parameters
  const contentId = searchParams.get('contentId');
  const title = searchParams.get('title') || 'Unknown Title';
  const contentType = searchParams.get('contentType') as 'movie' | 'show' || 'movie';
  const season = searchParams.get('season') ? parseInt(searchParams.get('season')!) : undefined;
  const episode = searchParams.get('episode') ? parseInt(searchParams.get('episode')!) : undefined;

  useEffect(() => {
    const loadPartyData = async () => {
      try {
        setLoading(true);
        
        // Mock party data - in real implementation, fetch from API
        const mockPartyData: WatchPartyData = {
          id: (await params).partyId,
          title: decodeURIComponent(title),
          hostId: 'user-1',
          members: [
            {
              id: 'user-1',
              name: 'John Doe',
              avatar: '/avatars/avatar-1.png',
              isHost: true,
              isReady: true,
              joinedAt: new Date(Date.now() - 1000 * 60 * 5).toISOString()
            },
            {
              id: 'user-2',
              name: 'Jane Smith',
              avatar: '/avatars/avatar-2.png',
              isHost: false,
              isReady: true,
              joinedAt: new Date(Date.now() - 1000 * 60 * 2).toISOString()
            }
          ],
          messages: [
            {
              id: '1',
              sender: 'System',
              content: 'John Doe created the watch party',
              timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString(),
              type: 'system'
            },
            {
              id: '2',
              sender: 'Jane Smith',
              content: 'Hey everyone! 👋',
              timestamp: new Date(Date.now() - 1000 * 60 * 2).toISOString(),
              type: 'text'
            }
          ],
          currentTime: 0,
          isPlaying: false,
          contentId: contentId || 'tt4154796',
          contentType,
          season,
          episode,
          isPrivate: false,
          maxMembers: 10,
          allowChat: true,
          allowReactions: true
        };

        setPartyData(mockPartyData);
        setIsHost(mockPartyData.hostId === 'user-1'); // Mock user ID
      } catch (err) {
        setError('Failed to load watch party');
        console.error('Error loading party data:', err);
      } finally {
        setLoading(false);
      }
    };

    loadPartyData();
  }, [params, contentId, title, contentType, season, episode]);

  const handleBack = () => {
    router.push('/watch-party');
  };

  const handleShare = () => {
    const shareUrl = `${window.location.origin}/watch-party/${partyData?.id}`;
    
    if (navigator.share) {
      navigator.share({
        title: `Join my watch party: ${partyData?.title}`,
        url: shareUrl
      });
    } else {
      navigator.clipboard.writeText(shareUrl);
      toast.success('Party link copied to clipboard!');
    }
  };

  const handleSendMessage = () => {
    if (!newMessage.trim() || !partyData) return;

    const message: ChatMessage = {
      id: Date.now().toString(),
      sender: 'You',
      content: newMessage,
      timestamp: new Date().toISOString(),
      type: 'text'
    };

    setPartyData(prev => prev ? {
      ...prev,
      messages: [...prev.messages, message]
    } : null);

    setNewMessage('');
  };

  const handleReaction = (reaction: string) => {
    if (!partyData) return;

    const message: ChatMessage = {
      id: Date.now().toString(),
      sender: 'You',
      content: reaction,
      timestamp: new Date().toISOString(),
      type: 'reaction'
    };

    setPartyData(prev => prev ? {
      ...prev,
      messages: [...prev.messages, message]
    } : null);
  };

  const handlePlayerError = (error: string) => {
    console.error('VidSrc Player Error:', error);
    toast.error('Failed to load video player');
  };

  const handlePlayerLoad = () => {
    console.log('VidSrc Player loaded successfully');
    toast.success('Video player ready!');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-vista-dark">
        <Navbar />
        <div className="flex items-center justify-center min-h-[calc(100vh-80px)]">
          <div className="text-vista-light text-lg">Loading watch party...</div>
        </div>
      </div>
    );
  }

  if (error || !partyData) {
    return (
      <div className="min-h-screen bg-vista-dark">
        <Navbar />
        <div className="flex items-center justify-center min-h-[calc(100vh-80px)]">
          <div className="text-center space-y-4">
            <div className="text-red-500 text-6xl">⚠️</div>
            <h2 className="text-2xl font-bold text-vista-light">Watch Party Not Found</h2>
            <p className="text-vista-light/70">{error || 'The requested watch party could not be found.'}</p>
            <Button onClick={handleBack} variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Watch Parties
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-vista-dark">
      <Navbar />
      
      <div className="pt-20 pb-8">
        <div className="container mx-auto px-4">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={handleBack}
                className="text-vista-light hover:text-vista-blue"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              
              <div>
                <h1 className="text-2xl font-bold text-vista-light">
                  {partyData.title}
                </h1>
                <div className="flex items-center space-x-2 text-sm text-vista-light/70">
                  <Users className="h-4 w-4" />
                  <span>{partyData.members.length}/{partyData.maxMembers} members</span>
                  {partyData.isPrivate && (
                    <Badge variant="outline" className="border-vista-light/30 text-vista-light">
                      Private
                    </Badge>
                  )}
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowMembers(!showMembers)}
                      className="text-vista-light hover:bg-vista-dark-lighter"
                    >
                      <Users className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Members ({partyData.members.length})</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleShare}
                className="text-vista-light hover:bg-vista-dark-lighter"
              >
                <Share2 className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Video Player */}
            <div className="lg:col-span-3">
              <VidSrcPlayer
                imdbId={partyData.contentId.startsWith('tt') ? partyData.contentId : undefined}
                tmdbId={!partyData.contentId.startsWith('tt') ? partyData.contentId : undefined}
                type={partyData.contentType}
                season={partyData.season}
                episode={partyData.episode}
                className="w-full rounded-lg overflow-hidden shadow-2xl"
                height="56.25vw"
                maxHeight="70vh"
                autoplay={false}
                onError={handlePlayerError}
                onLoad={handlePlayerLoad}
              />
            </div>

            {/* Chat and Members Sidebar */}
            <div className="space-y-4">
              {/* Members List */}
              {showMembers && (
                <Card className="bg-vista-dark-lighter border-vista-light/10">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-vista-light text-lg">Members</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {partyData.members.map((member) => (
                      <div key={member.id} className="flex items-center space-x-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={member.avatar} />
                          <AvatarFallback className="bg-vista-blue text-white text-xs">
                            {member.name.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2">
                            <p className="text-sm font-medium text-vista-light truncate">
                              {member.name}
                            </p>
                            {member.isHost && (
                              <Crown className="h-3 w-3 text-yellow-500" />
                            )}
                          </div>
                          <p className="text-xs text-vista-light/50">
                            {member.isReady ? 'Ready' : 'Not ready'}
                          </p>
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              )}

              {/* Chat */}
              <Card className="bg-vista-dark-lighter border-vista-light/10 h-[600px] flex flex-col">
                <CardHeader className="pb-3">
                  <CardTitle className="text-vista-light text-lg">Chat</CardTitle>
                </CardHeader>
                <CardContent className="flex-1 flex flex-col p-0">
                  <ScrollArea className="flex-1 px-4">
                    <div className="space-y-3 pb-4">
                      {partyData.messages.map((message) => (
                        <div
                          key={message.id}
                          className={cn(
                            "flex flex-col",
                            message.type === 'system' && "items-center",
                            message.sender === 'You' && "items-end"
                          )}
                        >
                          {message.type === 'system' ? (
                            <div className="text-xs text-vista-light/50 bg-vista-dark/50 px-2 py-1 rounded">
                              {message.content}
                            </div>
                          ) : (
                            <div className={cn(
                              "max-w-[80%]",
                              message.sender === 'You' && "ml-auto"
                            )}>
                              <div className="flex items-center space-x-2 mb-1">
                                <span className="text-xs font-medium text-vista-light">
                                  {message.sender}
                                </span>
                                <span className="text-xs text-vista-light/50">
                                  {new Date(message.timestamp).toLocaleTimeString([], { 
                                    hour: '2-digit', 
                                    minute: '2-digit' 
                                  })}
                                </span>
                              </div>
                              <div className={cn(
                                "px-3 py-2 rounded-lg text-sm",
                                message.type === 'reaction'
                                  ? "bg-vista-dark/50 text-center text-2xl"
                                  : message.sender === 'You'
                                    ? "bg-vista-blue text-white"
                                    : "bg-vista-dark text-vista-light"
                              )}>
                                {message.content}
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                  
                  <Separator className="my-3" />
                  
                  {/* Message Input */}
                  <div className="px-4 pb-4 space-y-3">
                    {partyData.allowChat && (
                      <div className="flex space-x-2">
                        <Input
                          value={newMessage}
                          onChange={(e) => setNewMessage(e.target.value)}
                          onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                          placeholder="Type a message..."
                          className="flex-1 bg-vista-dark border-vista-light/20 text-vista-light placeholder:text-vista-light/50"
                        />
                        <Button
                          onClick={handleSendMessage}
                          disabled={!newMessage.trim()}
                          size="sm"
                          className="bg-vista-blue hover:bg-vista-blue/90 text-white"
                        >
                          Send
                        </Button>
                      </div>
                    )}
                    
                    {partyData.allowReactions && (
                      <div className="flex flex-wrap gap-2">
                        {['👍', '❤️', '😂', '😮', '😢', '😡'].map((reaction) => (
                          <Button
                            key={reaction}
                            variant="ghost"
                            size="sm"
                            onClick={() => handleReaction(reaction)}
                            className="text-lg hover:bg-vista-dark"
                          >
                            {reaction}
                          </Button>
                        ))}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 
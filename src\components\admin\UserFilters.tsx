'use client';

import { useState } from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>,
  CardFooter
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Checkbox } from '@/components/ui/checkbox';
import { DatePicker } from '@/components/ui/date-picker';
import {
  Filter,
  X,
  Search,
  Calendar,
  User,
  Shield,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle
} from 'lucide-react';

interface UserFiltersProps {
  onFilterChange: (filters: UserFilterOptions) => void;
  onReset: () => void;
}

export interface UserFilterOptions {
  search: string;
  role: string[];
  status: string[];
  verified: boolean | null;
  dateRange: {
    from: Date | null;
    to: Date | null;
  };
  subscription: string[];
}

export default function UserFilters({ onFilterChange, onReset }: UserFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [filters, setFilters] = useState<UserFilterOptions>({
    search: '',
    role: [],
    status: [],
    verified: null,
    dateRange: {
      from: null,
      to: null
    },
    subscription: []
  });

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newFilters = {
      ...filters,
      search: e.target.value
    };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  // Handle role selection change
  const handleRoleChange = (role: string) => {
    const newRoles = filters.role.includes(role)
      ? filters.role.filter(r => r !== role)
      : [...filters.role, role];

    const newFilters = {
      ...filters,
      role: newRoles
    };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  // Handle verification status change
  const handleVerificationChange = (status: boolean | null) => {
    const newFilters = {
      ...filters,
      verified: status
    };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  // Handle date range change
  const handleDateChange = (type: 'from' | 'to', date: Date | null) => {
    const newFilters = {
      ...filters,
      dateRange: {
        ...filters.dateRange,
        [type]: date
      }
    };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  // Handle subscription change
  const handleSubscriptionChange = (subscription: string) => {
    const newSubscriptions = filters.subscription.includes(subscription)
      ? filters.subscription.filter(s => s !== subscription)
      : [...filters.subscription, subscription];

    const newFilters = {
      ...filters,
      subscription: newSubscriptions
    };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  // Reset all filters
  const handleReset = () => {
    const resetFilters = {
      search: '',
      role: [],
      verified: null,
      dateRange: {
        from: null,
        to: null
      },
      subscription: []
    };
    setFilters(resetFilters);
    onReset();
  };

  // Count active filters (excluding search)
  const activeFilterCount =
    filters.role.length +
    filters.status.length +
    (filters.verified !== null ? 1 : 0) +
    (filters.dateRange.from || filters.dateRange.to ? 1 : 0) +
    filters.subscription.length;

  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center">
            <Filter className="h-5 w-5 mr-2 text-vista-blue" />
            User Filters
          </CardTitle>
          {activeFilterCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleReset}
              className="h-8 text-vista-light/70 hover:text-vista-light"
            >
              <X className="h-4 w-4 mr-1" />
              Clear Filters ({activeFilterCount})
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Search Input */}
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-vista-light/50" />
            <Input
              type="search"
              placeholder="Search users by name or email..."
              className="pl-9"
              value={filters.search}
              onChange={handleSearchChange}
            />
          </div>

          {/* Advanced Filters Accordion */}
          <Accordion
            type="single"
            collapsible
            className="w-full"
            value={isExpanded ? 'filters' : ''}
            onValueChange={(value) => setIsExpanded(value === 'filters')}
          >
            <AccordionItem value="filters" className="border-b-0">
              <AccordionTrigger className="py-2 text-vista-light/80 hover:text-vista-light">
                Advanced Filters
              </AccordionTrigger>
              <AccordionContent className="pt-4 pb-2">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {/* Role Filter */}
                  <div className="space-y-3">
                    <Label className="text-vista-light flex items-center">
                      <User className="h-4 w-4 mr-2 text-vista-blue" />
                      User Role
                    </Label>
                    <div className="space-y-2">
                      {['user', 'admin', 'moderator'].map((role) => (
                        <div key={role} className="flex items-center space-x-2">
                          <Checkbox
                            id={`role-${role}`}
                            checked={filters.role.includes(role)}
                            onCheckedChange={() => handleRoleChange(role)}
                          />
                          <label
                            htmlFor={`role-${role}`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 capitalize"
                          >
                            {role}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* User Status */}
                  <div className="space-y-3">
                    <Label className="text-vista-light flex items-center">
                      <Shield className="h-4 w-4 mr-2 text-vista-blue" />
                      User Status
                    </Label>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="status-active"
                          checked={filters.status.includes('active')}
                          onCheckedChange={(checked) => {
                            const newStatus = [...filters.status];
                            if (checked) {
                              newStatus.push('active');
                            } else {
                              const index = newStatus.indexOf('active');
                              if (index !== -1) newStatus.splice(index, 1);
                            }
                            setFilters({ ...filters, status: newStatus });
                            onFilterChange({ ...filters, status: newStatus });
                          }}
                        />
                        <label
                          htmlFor="status-active"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center"
                        >
                          <CheckCircle className="h-3.5 w-3.5 mr-1.5 text-green-500" />
                          Active
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="status-inactive"
                          checked={filters.status.includes('inactive')}
                          onCheckedChange={(checked) => {
                            const newStatus = [...filters.status];
                            if (checked) {
                              newStatus.push('inactive');
                            } else {
                              const index = newStatus.indexOf('inactive');
                              if (index !== -1) newStatus.splice(index, 1);
                            }
                            setFilters({ ...filters, status: newStatus });
                            onFilterChange({ ...filters, status: newStatus });
                          }}
                        />
                        <label
                          htmlFor="status-inactive"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center"
                        >
                          <XCircle className="h-3.5 w-3.5 mr-1.5 text-red-500" />
                          Inactive
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="status-pending"
                          checked={filters.status.includes('pending')}
                          onCheckedChange={(checked) => {
                            const newStatus = [...filters.status];
                            if (checked) {
                              newStatus.push('pending');
                            } else {
                              const index = newStatus.indexOf('pending');
                              if (index !== -1) newStatus.splice(index, 1);
                            }
                            setFilters({ ...filters, status: newStatus });
                            onFilterChange({ ...filters, status: newStatus });
                          }}
                        />
                        <label
                          htmlFor="status-pending"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center"
                        >
                          <Clock className="h-3.5 w-3.5 mr-1.5 text-yellow-500" />
                          Pending
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="status-suspended"
                          checked={filters.status.includes('suspended')}
                          onCheckedChange={(checked) => {
                            const newStatus = [...filters.status];
                            if (checked) {
                              newStatus.push('suspended');
                            } else {
                              const index = newStatus.indexOf('suspended');
                              if (index !== -1) newStatus.splice(index, 1);
                            }
                            setFilters({ ...filters, status: newStatus });
                            onFilterChange({ ...filters, status: newStatus });
                          }}
                        />
                        <label
                          htmlFor="status-suspended"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center"
                        >
                          <AlertCircle className="h-3.5 w-3.5 mr-1.5 text-orange-500" />
                          Suspended
                        </label>
                      </div>
                    </div>
                  </div>

                  {/* Verification Status */}
                  <div className="space-y-3">
                    <Label className="text-vista-light flex items-center">
                      <Shield className="h-4 w-4 mr-2 text-vista-blue" />
                      Verification Status
                    </Label>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="verified-yes"
                          checked={filters.verified === true}
                          onCheckedChange={() => handleVerificationChange(
                            filters.verified === true ? null : true
                          )}
                        />
                        <label
                          htmlFor="verified-yes"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center"
                        >
                          <CheckCircle className="h-3.5 w-3.5 mr-1.5 text-green-500" />
                          Verified
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="verified-no"
                          checked={filters.verified === false}
                          onCheckedChange={() => handleVerificationChange(
                            filters.verified === false ? null : false
                          )}
                        />
                        <label
                          htmlFor="verified-no"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center"
                        >
                          <XCircle className="h-3.5 w-3.5 mr-1.5 text-red-500" />
                          Not Verified
                        </label>
                      </div>
                    </div>
                  </div>

                  {/* Date Range */}
                  <div className="space-y-3">
                    <Label className="text-vista-light flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-vista-blue" />
                      Registration Date
                    </Label>
                    <div className="space-y-2">
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <Label className="text-xs mb-1 block">From</Label>
                          <DatePicker
                            date={filters.dateRange.from}
                            setDate={(date) => handleDateChange('from', date)}
                            className="w-full"
                          />
                        </div>
                        <div>
                          <Label className="text-xs mb-1 block">To</Label>
                          <DatePicker
                            date={filters.dateRange.to}
                            setDate={(date) => handleDateChange('to', date)}
                            className="w-full"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Subscription Type */}
                  <div className="space-y-3">
                    <Label className="text-vista-light flex items-center">
                      <Shield className="h-4 w-4 mr-2 text-vista-blue" />
                      Subscription Plan
                    </Label>
                    <div className="space-y-2">
                      {['free', 'basic', 'premium', 'family'].map((plan) => (
                        <div key={plan} className="flex items-center space-x-2">
                          <Checkbox
                            id={`plan-${plan}`}
                            checked={filters.subscription.includes(plan)}
                            onCheckedChange={() => handleSubscriptionChange(plan)}
                          />
                          <label
                            htmlFor={`plan-${plan}`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 capitalize"
                          >
                            {plan}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </CardContent>
      {isExpanded && (
        <CardFooter className="border-t border-vista-blue/10 pt-4 flex justify-between">
          <Button variant="outline" onClick={handleReset}>
            Reset Filters
          </Button>
          <Button onClick={() => setIsExpanded(false)}>
            Apply Filters
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}

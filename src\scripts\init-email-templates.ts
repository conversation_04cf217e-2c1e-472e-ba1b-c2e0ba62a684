import { ensureMongooseConnection } from '@/lib/mongodb';
import { createDefaultEmailTemplates } from '@/lib/email';
import User from '@/models/User';

/**
 * <PERSON>ript to initialize default email templates
 * Run with: npx ts-node -r tsconfig-paths/register src/scripts/init-email-templates.ts
 */
async function initEmailTemplates() {
  try {
    console.log('Connecting to MongoDB...');
    await ensureMongooseConnection();

    // Find an admin user
    console.log('Finding admin user...');
    const adminUser = await User.findOne({ role: 'admin' });

    if (!adminUser) {
      console.error('No admin user found. Please create an admin user first.');
      process.exit(1);
    }

    console.log(`Found admin user: ${adminUser.name} (${adminUser.email})`);

    // Create default email templates
    console.log('Creating default email templates...');
    await createDefaultEmailTemplates(adminUser._id.toString());

    console.log('Default email templates created successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error initializing email templates:', error);
    process.exit(1);
  }
}

// Run the script
initEmailTemplates();

import { NextRequest, NextResponse } from 'next/server';
import {
  getPopularMovies,
  getPopularTVShows,
  discoverMovies,
  discoverTVShows,
  TMDB_GENRE_MAP,
  getMoviesByCategory,
  TMDbItem
} from '@/lib/tmdb-api';

// Map category names to genre IDs
const CATEGORY_TO_GENRE_ID: Record<string, number> = {
  'Action': 28,
  'Adventure': 12,
  'Animation': 16,
  'Comedy': 35,
  'Crime': 80,
  'Documentary': 99,
  'Drama': 18,
  'Family': 10751,
  'Fantasy': 14,
  'History': 36,
  'Horror': 27,
  'Music': 10402,
  'Mystery': 9648,
  'Romance': 10749,
  'Science Fiction': 878,
  'Sci-Fi': 878, // Alias for Science Fiction
  'TV Movie': 10770,
  'Thriller': 53,
  'War': 10752,
  'Western': 37
};

// Add server-side cache
const CACHE_TTL = 15 * 60 * 1000; // 15 minutes in milliseconds
type CachedResult = {
  data: { images: CategoryImageData[] };
  timestamp: number;
};
const imageCache = new Map<string, CachedResult>();

// Interface for the expected structure of category image data
interface CategoryImageData {
    id: number;
    title: string | undefined;
    posterPath: string | null;
    backdropPath: string | null;
}

// Interface for the API response structure
interface CategoryApiResponse {
  [category: string]: {
    images: CategoryImageData[];
  };
}

/**
 * API endpoint to fetch backdrop images for multiple categories
 */
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    // Expect categories as a comma-separated string
    const categoriesParam = url.searchParams.get('category');
    const count = parseInt(url.searchParams.get('count') || '5', 10);

    if (!categoriesParam) {
      return NextResponse.json({ error: 'Category parameter is required' }, { status: 400 });
    }

    const requestedCategories = categoriesParam.split(',').map(cat => cat.trim()).filter(Boolean);
    if (requestedCategories.length === 0) {
       return NextResponse.json({ error: 'At least one category must be specified' }, { status: 400 });
    }

    const results: CategoryApiResponse = {};
    const now = Date.now();
    const fetchPromises: Promise<void>[] = []; // To fetch uncached categories concurrently

    for (const category of requestedCategories) {
      const cacheKey = `${category}_${count}`;
      const cachedResult = imageCache.get(cacheKey);

      if (cachedResult && (now - cachedResult.timestamp) < CACHE_TTL) {
        results[category] = cachedResult.data;
      } else {
        // Fetch fresh data if not in cache or expired
        fetchPromises.push(
          (async () => {
            try {
                const movies = await getMoviesByCategory(category, count);
                if (!movies || movies.length === 0) {
                   console.warn(`No movies found for category: ${category}`);
                   // Provide empty array or specific structure for consistency
                   results[category] = { images: [] };
                   // Optionally cache the 'not found' state to avoid repeated external calls for a short period
                   imageCache.set(cacheKey, { data: { images: [] }, timestamp: now });
                   return;
                }

                const categoryData = {
                  images: movies.map((movie: TMDbItem) => ({
                    id: movie.id,
                    title: movie.title || movie.name,
                    posterPath: movie.poster_path,
                    backdropPath: movie.backdrop_path,
                  }))
                };

                results[category] = categoryData;
                // Store in cache
                imageCache.set(cacheKey, {
                  data: categoryData,
                  timestamp: now
                });
            } catch (error) {
                 console.error(`Error fetching images for category ${category}:`, error);
                 // Handle error for this specific category, maybe return empty or error indicator
                 results[category] = { images: [] }; // Default to empty on error
            }
          })()
        );
      }
    }

    // Wait for all fetches to complete
    await Promise.all(fetchPromises);

    // Check if any results were successfully found or retrieved from cache
    if (Object.keys(results).length === 0 && fetchPromises.length === requestedCategories.length) {
        // This condition means all fetches resulted in errors or no data, and nothing was cached
        return NextResponse.json({ error: 'Failed to fetch images for all requested categories' }, { status: 500 });
    }


    return NextResponse.json(results);
  } catch (error) {
    // Catch unexpected errors in the overall process
    console.error('Error processing category image request:', error);
    return NextResponse.json({ error: 'Failed to process image request' }, { status: 500 });
  }
}

'use client';

import { CreditCard, Calendar, CheckCircle, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { SettingsCard } from './SettingsCard';
import { cn } from '@/lib/utils';
import { TooltipElement } from '@/components/ui/tooltip';

interface SubscriptionCardProps {
  plan: string;
  status: string;
  nextBillingDate?: string;
  price?: string;
  features?: string[];
  className?: string;
}

export function SubscriptionCard({
  plan,
  status,
  nextBillingDate,
  price,
  features = [],
  className
}: SubscriptionCardProps) {
  const isPremium = plan.toLowerCase() !== 'free';
  const isActive = status.toLowerCase() === 'active';
  
  // Define features if not provided
  const defaultFeatures = isPremium 
    ? [
        'Ad-free streaming',
        'Ultra HD available',
        'Watch on any device',
        'Cancel anytime'
      ]
    : [
        'Limited content library',
        'Standard definition only',
        'Mobile streaming only',
        'Ad-supported'
      ];
  
  const displayFeatures = features.length > 0 ? features : defaultFeatures;
  
  return (
    <SettingsCard
      title="Subscription"
      description="Your current plan and billing"
      icon={CreditCard}
      accentColor={isPremium ? "emerald" : "slate"}
      className={className}
    >
      <div className="h-full flex flex-col">
        {/* Plan Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 mb-5">
          <div>
            <div className="flex items-center gap-2">
              <h3 className="font-semibold text-xl capitalize text-white">{plan} Plan</h3>
              <Badge
                variant={isPremium ? "success" : "outline"}
                className="capitalize font-medium px-2 py-0.5 text-xs"
              >
                {isPremium ? 'Premium' : 'Basic'}
              </Badge>
            </div>
            <div className="flex items-center mt-1">
              <div className={cn(
                "w-2 h-2 rounded-full mr-2",
                isActive ? "bg-green-500" : "bg-yellow-500"
              )}></div>
              <p className="text-sm text-vista-light/70 capitalize">{status}</p>
            </div>
          </div>
          
          {isPremium && price && (
            <div className="bg-black/30 px-4 py-2 rounded-lg border border-white/5">
              <p className="text-sm text-vista-light/70">Monthly Price</p>
              <p className="text-xl font-semibold text-white">{price}</p>
            </div>
          )}
        </div>
        
        {/* Plan Features */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-vista-light/60 mb-3">Plan Features</h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {displayFeatures.map((feature, index) => (
              <div 
                key={index} 
                className="flex items-center gap-2 p-2 rounded-lg"
              >
                {isPremium ? (
                  <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                ) : (
                  index < 2 ? (
                    <CheckCircle className="h-4 w-4 text-vista-light/50 flex-shrink-0" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-vista-light/30 flex-shrink-0" />
                  )
                )}
                <span className={cn(
                  "text-sm",
                  isPremium ? "text-vista-light" : "text-vista-light/70"
                )}>
                  {feature}
                </span>
              </div>
            ))}
          </div>
        </div>
        
        {/* Next Billing Date for Premium */}
        {isPremium && nextBillingDate && (
          <div className="flex items-center gap-3 p-3 rounded-lg bg-black/20 border border-white/5 mb-5">
            <div className="w-10 h-10 rounded-md bg-emerald-500/10 flex items-center justify-center">
              <Calendar className="h-5 w-5 text-emerald-500" />
            </div>
            <div>
              <p className="text-sm text-vista-light/70">Next Billing Date</p>
              <p className="font-medium text-vista-light">{nextBillingDate}</p>
            </div>
          </div>
        )}
        
        {/* Action Button */}
        <div className="mt-auto pt-4">
          {isPremium ? (
            <div className="flex flex-col sm:flex-row gap-2">
              <Button
                variant="outline"
                className="bg-black/20 hover:bg-black/40 border-white/5 hover:border-white/10 flex-1"
              >
                Manage Plan
              </Button>
              <TooltipElement content="Cancel your subscription">
                <Button
                  variant="outline"
                  className="border-red-500/20 text-red-400 hover:bg-red-500/10 hover:text-red-300 flex-1"
                >
                  Cancel Subscription
                </Button>
              </TooltipElement>
            </div>
          ) : (
            <Button
              className="w-full bg-emerald-500 hover:bg-emerald-600 text-white"
            >
              Upgrade to Premium
            </Button>
          )}
        </div>
      </div>
    </SettingsCard>
  );
}

import mongoose, { Schema, Document } from 'mongoose';

export interface IContent extends Document {
  title: string;
  type: 'movie' | 'show';
  tmdbId: string;
  imdbId?: string;
  posterPath: string;
  backdropPath?: string;
  overview?: string;
  tagline?: string;
  year?: string;
  genres: string[];
  runtime?: number;
  rating?: number;
  seasons?: number;
  episodes?: number;
  status: 'published' | 'draft';
  featured: boolean;
  trending: boolean;
  views: number;
  watchTime: number;
  completionRate: number;
  createdAt: Date;
  updatedAt: Date;
}

// Define the Content schema
const ContentSchema = new Schema<IContent>({
  title: { type: String, required: true },
  type: { type: String, enum: ['movie', 'show'], required: true },
  tmdbId: { type: String, required: true },
  imdbId: { type: String },
  posterPath: { type: String, required: true },
  backdropPath: { type: String },
  overview: { type: String },
  tagline: { type: String },
  year: { type: String },
  genres: { type: [String], default: [] },
  runtime: { type: Number },
  rating: { type: Number },
  seasons: { type: Number },
  episodes: { type: Number },
  status: { 
    type: String, 
    enum: ['published', 'draft'], 
    default: 'draft' 
  },
  featured: { type: Boolean, default: false },
  trending: { type: Boolean, default: false },
  views: { type: Number, default: 0 },
  watchTime: { type: Number, default: 0 },
  completionRate: { type: Number, default: 0 },
}, {
  timestamps: true
});

// Add indexes for common queries
ContentSchema.index({ title: 'text', overview: 'text' });
ContentSchema.index({ type: 1 });
ContentSchema.index({ tmdbId: 1 }, { unique: true });
ContentSchema.index({ status: 1 });
ContentSchema.index({ featured: 1 });
ContentSchema.index({ trending: 1 });

// Use mongoose.models.Content if it exists, otherwise create a new model
const Content = mongoose.models.Content as mongoose.Model<IContent> || 
                mongoose.model<IContent>('Content', ContentSchema);

export default Content;

import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import BannerAd from '@/models/BannerAd';
import mongoose, { Types } from 'mongoose';

interface BatchAnalyticsEvent {
  bannerId: string;
  views: number;
  clicks: number;
}

interface BatchAnalyticsRequest {
  events: BatchAnalyticsEvent[];
}

/**
 * POST /api/banner-ads/analytics/batch
 * Public endpoint to track banner ad analytics in batches
 * This endpoint is public and doesn't require authentication
 */
export async function POST(request: NextRequest) {
  try {
    await ensureMongooseConnection();

    // Check if request has a body before trying to parse it
    const contentType = request.headers.get('content-type');
    let body: BatchAnalyticsRequest;
    
    if (contentType && contentType.includes('application/json')) {
      try {
        body = await request.json();
      } catch (parseError) {
        console.error('Error parsing batch analytics request body:', parseError);
        return NextResponse.json(
          { error: 'Invalid JSON in request body' },
          { status: 400 }
        );
      }
    } else {
      return NextResponse.json(
        { error: 'Content-Type must be application/json' },
        { status: 400 }
      );
    }
    
    const { events } = body;

    // Validate request
    if (!events || !Array.isArray(events) || events.length === 0) {
      return NextResponse.json(
        { error: 'Invalid events array' },
        { status: 400 }
      );
    }

    // Validate and process events
    const validEvents: BatchAnalyticsEvent[] = [];
    const processedBannerIds = new Set<string>();

    for (const event of events) {
      // Validate banner ID
      if (!event.bannerId || !mongoose.Types.ObjectId.isValid(event.bannerId)) {
        console.warn('Invalid banner ID in batch analytics:', event.bannerId);
        continue;
      }

      // Validate counts
      const views = Math.max(0, Math.floor(event.views || 0));
      const clicks = Math.max(0, Math.floor(event.clicks || 0));

      if (views === 0 && clicks === 0) {
        continue;
      }

      // Avoid duplicate banner IDs in the same batch
      if (processedBannerIds.has(event.bannerId)) {
        continue;
      }

      validEvents.push({
        bannerId: event.bannerId,
        views,
        clicks
      });

      processedBannerIds.add(event.bannerId);
    }

    if (validEvents.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No valid events to process',
        processed: 0
      });
    }

    // Check which banners exist and are active
    const bannerIds = validEvents.map(event => event.bannerId);
    const existingBanners = await BannerAd.find({
      _id: { $in: bannerIds },
      isActive: true
    }).select('_id').lean();

    const existingBannerIds = new Set(
      existingBanners.map(banner => (banner._id as Types.ObjectId).toString())
    );

    // Process analytics updates in bulk
    const bulkOps = [];
    let processedCount = 0;

    for (const event of validEvents) {
      if (!existingBannerIds.has(event.bannerId)) {
        continue;
      }

      const updateDoc: Record<string, number> = {};
      
      if (event.views > 0) {
        updateDoc['analytics.views'] = event.views;
      }
      
      if (event.clicks > 0) {
        updateDoc['analytics.clicks'] = event.clicks;
      }

      if (Object.keys(updateDoc).length > 0) {
        bulkOps.push({
          updateOne: {
            filter: { _id: event.bannerId },
            update: { $inc: updateDoc }
          }
        });
        processedCount++;
      }
    }

    // Execute bulk operations
    if (bulkOps.length > 0) {
      await BannerAd.bulkWrite(bulkOps);
    }

    return NextResponse.json({
      success: true,
      message: `Processed ${processedCount} analytics events`,
      processed: processedCount,
      total: events.length
    });

  } catch (error) {
    console.error('Error processing batch analytics:', error);
    return NextResponse.json(
      { error: 'Failed to process analytics batch' },
      { status: 500 }
    );
  }
} 
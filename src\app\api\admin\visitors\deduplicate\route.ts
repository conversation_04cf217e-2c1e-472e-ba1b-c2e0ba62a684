import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import User from '@/models/User';

interface VisitorDoc {
  id: string;
  visitorId: string;
  visitCount: number;
  pagesViewed: number;
  firstVisit: Date | string;
  lastVisit?: Date | string;
  city?: string;
  region?: string;
  country?: string;
  ipAddress?: string;
  browser?: string;
  os?: string;
}

/**
 * Checks if a group of visitors share the same location information
 */
function areSameLocation(docs: VisitorDoc[]): boolean {
  if (docs.length < 2) return false;
  
  // City and region should be present and same across records
  const uniqueLocations = new Set<string>();
  
  for (const doc of docs) {
    if (!doc.city || !doc.region) {
      return false; // Missing location data
    }
    
    const locationKey = `${doc.city.toLowerCase()}|${doc.region.toLowerCase()}`;
    uniqueLocations.add(locationKey);
  }
  
  // All docs should have the same location
  return uniqueLocations.size === 1;
}

/**
 * Checks if a group of visitors have overlapping time patterns
 * suggesting they might be the same person
 */
function checkTimePatterns(docs: VisitorDoc[]): boolean {
  if (docs.length < 2) return false;
  
  // Check if both first and last visit dates are available
  const hasDateInfo = docs.every(doc => doc.firstVisit && doc.lastVisit);
  if (!hasDateInfo) return false;
  
  // Check for sequential visits or overlapping date ranges
  const sortedByFirstVisit = [...docs].sort((a, b) => {
    return new Date(a.firstVisit).getTime() - new Date(b.firstVisit).getTime();
  });
  
  for (let i = 0; i < sortedByFirstVisit.length - 1; i++) {
    const currentDoc = sortedByFirstVisit[i];
    const nextDoc = sortedByFirstVisit[i + 1];
    
    const currentLastVisit = new Date(currentDoc.lastVisit!).getTime();
    const nextFirstVisit = new Date(nextDoc.firstVisit).getTime();
    
    // Allow for up to 24 hours between visits to consider them related
    const timeDifference = nextFirstVisit - currentLastVisit;
    if (timeDifference <= 24 * 60 * 60 * 1000) {
      return true; // Found overlapping or sequential visit patterns
    }
  }
  
  // Check for similar visit times of day pattern
  // This can indicate the same person visiting at similar times on different days
  const timePatterns = docs.map(doc => {
    const firstVisitHour = new Date(doc.firstVisit).getHours();
    const lastVisitHour = doc.lastVisit ? new Date(doc.lastVisit).getHours() : -1;
    return { firstVisitHour, lastVisitHour };
  });
  
  const uniquePatterns = new Set<string>();
  timePatterns.forEach(pattern => {
    // Group by 3-hour periods to allow for small variations
    const firstHourGroup = Math.floor(pattern.firstVisitHour / 3);
    const lastHourGroup = pattern.lastVisitHour >= 0 ? Math.floor(pattern.lastVisitHour / 3) : -1;
    uniquePatterns.add(`${firstHourGroup}|${lastHourGroup}`);
  });
  
  // If the visitors have similar time patterns, they might be the same person
  return uniquePatterns.size <= 2;
}

/**
 * POST /api/admin/visitors/deduplicate
 * Remove duplicate visitor records
 *
 * This endpoint is only accessible to admin users.
 * It identifies and removes duplicate visitor records based on:
 * 1. Fingerprint (most reliable)
 * 2. IP address and user agent combination (fallback)
 * 3. Other browser identifiers
 */
export async function POST(request: NextRequest) {
  try {
    // Get the userId from cookies or query string
    const { searchParams } = new URL(request.url);
    let userId = request.cookies.get('userId')?.value;

    // If no userId in cookies, try query string (for client-side admin verification)
    if (!userId) {
      const userIdParam = searchParams.get('userId');
      if (userIdParam) userId = userIdParam;
    }

    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Connect to database
    await ensureMongooseConnection();

    // Find the user by ID
    const user = await User.findById(userId).lean();

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if user has admin role
    const isUserAdmin = user.role === 'admin' || user.role === 'superadmin';

    if (!isUserAdmin) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Import the AnonymousVisitor model
    const AnonymousVisitor = (await import('@/models/AnonymousVisitor')).default;

    // Track deduplication results
    let removedCount = 0;
    let mergedCount = 0;

    // Step 1: First deduplicate by fingerprint (most reliable method)
    const fingerprintDuplicates = await AnonymousVisitor.aggregate([
      {
        $match: {
          fingerprint: { $type: "string", $not: { $in: ["", null] } }
        }
      },
      {
        $group: {
          _id: "$fingerprint",
          count: { $sum: 1 },
          docs: { $push: { id: "$_id", visitorId: "$visitorId", visitCount: "$visitCount", pagesViewed: "$pagesViewed", firstVisit: "$firstVisit" } }
        }
      },
      {
        $match: {
          count: { $gt: 1 }
        }
      }
    ]);

    // Process fingerprint duplicates
    for (const group of fingerprintDuplicates) {
      // Sort by firstVisit (oldest first) and visitCount (highest first)
      const sortedDocs = group.docs.sort((a: VisitorDoc, b: VisitorDoc) => {
        // First sort by firstVisit (oldest first)
        const dateA = new Date(a.firstVisit).getTime();
        const dateB = new Date(b.firstVisit).getTime();
        if (dateA !== dateB) return dateA - dateB;

        // If same date, sort by visitCount (highest first)
        return b.visitCount - a.visitCount;
      });

      // Keep the oldest record (or the one with the most visits if same date)
      const keepDoc = sortedDocs[0];
      const docsToRemove = sortedDocs.slice(1);

      // Calculate total visits and pages viewed across all duplicates
      const totalVisits = sortedDocs.reduce((sum: number, doc: VisitorDoc) => sum + (doc.visitCount || 0), 0);
      const totalPagesViewed = sortedDocs.reduce((sum: number, doc: VisitorDoc) => sum + (doc.pagesViewed || 0), 0);

      // Update the kept document with combined stats
      await AnonymousVisitor.updateOne(
        { _id: keepDoc.id },
        {
          $set: {
            visitCount: totalVisits,
            pagesViewed: totalPagesViewed,
            isDeduplicated: true,
            deduplicatedAt: new Date(),
            deduplicatedMethod: 'fingerprint',
            mergedIds: docsToRemove.map((d: VisitorDoc) => d.visitorId)
          }
        }
      );
      mergedCount++;

      // Remove the duplicate documents
      for (const doc of docsToRemove) {
        await AnonymousVisitor.deleteOne({ _id: doc.id });
        removedCount++;
      }
    }

    // Step 2: Find potential duplicates based on IP address and device type (more reliable than user agent)
    const ipDeviceDuplicates = await AnonymousVisitor.aggregate([
      {
        $match: {
          ipAddress: { $type: "string", $not: { $in: ["", null] } },
          device: { $type: "string", $not: { $in: ["", null] } },
          // Skip records with fingerprint or that have already been deduplicated
          $and: [
            {
              $or: [
                { fingerprint: { $exists: false } },
                { fingerprint: null },
                { fingerprint: "" }
              ]
            },
            {
              $or: [
                { isDeduplicated: { $exists: false } },
                { isDeduplicated: false }
              ]
            }
          ]
        }
      },
      {
        $group: {
          _id: { ipAddress: "$ipAddress", device: "$device" },
          count: { $sum: 1 },
          docs: { $push: { id: "$_id", visitorId: "$visitorId", visitCount: "$visitCount", pagesViewed: "$pagesViewed", firstVisit: "$firstVisit" } }
        }
      },
      {
        $match: {
          count: { $gt: 1 }
        }
      }
    ]);

    // Process IP+Device duplicates
    for (const group of ipDeviceDuplicates) {
      // Sort by firstVisit (oldest first) and visitCount (highest first)
      const sortedDocs = group.docs.sort((a: VisitorDoc, b: VisitorDoc) => {
        // First sort by firstVisit (oldest first)
        const dateA = new Date(a.firstVisit).getTime();
        const dateB = new Date(b.firstVisit).getTime();
        if (dateA !== dateB) return dateA - dateB;

        // If same date, sort by visitCount (highest first)
        return b.visitCount - a.visitCount;
      });

      // Keep the oldest record (or the one with the most visits if same date)
      const keepDoc = sortedDocs[0];
      const docsToRemove = sortedDocs.slice(1);

      // Calculate total visits and pages viewed
      const totalVisits = sortedDocs.reduce((sum: number, doc: VisitorDoc) => sum + (doc.visitCount || 0), 0);
      const totalPagesViewed = sortedDocs.reduce((sum: number, doc: VisitorDoc) => sum + (doc.pagesViewed || 0), 0);

      // Update the kept document with combined stats
      await AnonymousVisitor.updateOne(
        { _id: keepDoc.id },
        {
          $set: {
            visitCount: totalVisits,
            pagesViewed: totalPagesViewed,
            isDeduplicated: true,
            deduplicatedAt: new Date(),
            deduplicatedMethod: 'ip-device',
            mergedIds: docsToRemove.map((d: VisitorDoc) => d.visitorId)
          }
        }
      );
      mergedCount++;

      // Remove the duplicate documents
      for (const doc of docsToRemove) {
        await AnonymousVisitor.deleteOne({ _id: doc.id });
        removedCount++;
      }
    }

    // Step 3: Find potential duplicates based on IP address + browser + OS
    const ipBrowserOsDuplicates = await AnonymousVisitor.aggregate([
      {
        $match: {
          ipAddress: { $type: "string", $not: { $in: ["", null] } },
          browser: { $type: "string", $not: { $in: ["", null] } },
          os: { $type: "string", $not: { $in: ["", null] } },
          // Skip records that have already been deduplicated
          isDeduplicated: { $in: [false, null] }
        }
      },
      {
        $group: {
          _id: { ipAddress: "$ipAddress", browser: "$browser", os: "$os" },
          count: { $sum: 1 },
          docs: { $push: {
            id: "$_id",
            visitorId: "$visitorId",
            visitCount: "$visitCount",
            pagesViewed: "$pagesViewed",
            firstVisit: "$firstVisit",
            lastVisit: "$lastVisit",
            city: "$city",
            region: "$region",
            country: "$country"
          }}
        }
      },
      {
        $match: {
          count: { $gt: 1 }
        }
      }
    ]);

    // Process IP+Browser+OS duplicates
    for (const group of ipBrowserOsDuplicates) {
      // First, check if these are actually likely to be the same visitor
      // by analyzing location and time patterns
      const isSameLocation = areSameLocation(group.docs);
      const hasOverlappingTimePatterns = checkTimePatterns(group.docs);

      // Only merge if we have high confidence these are the same visitor
      if (isSameLocation || hasOverlappingTimePatterns) {
        // Sort by firstVisit (oldest first) and visitCount (highest first)
        const sortedDocs = group.docs.sort((a: VisitorDoc, b: VisitorDoc) => {
          // First sort by firstVisit (oldest first)
          const dateA = new Date(a.firstVisit).getTime();
          const dateB = new Date(b.firstVisit).getTime();
          if (dateA !== dateB) return dateA - dateB;

          // If same date, sort by visitCount (highest first)
          return b.visitCount - a.visitCount;
        });

        // Keep the oldest record (or the one with the most visits if same date)
        const keepDoc = sortedDocs[0];
        const docsToRemove = sortedDocs.slice(1);

        // Calculate total visits and pages viewed
        const totalVisits = sortedDocs.reduce((sum: number, doc: VisitorDoc) => sum + (doc.visitCount || 0), 0);
        const totalPagesViewed = sortedDocs.reduce((sum: number, doc: VisitorDoc) => sum + (doc.pagesViewed || 0), 0);

        // Update the kept document with combined stats
        await AnonymousVisitor.updateOne(
          { _id: keepDoc.id },
          {
            $set: {
              visitCount: totalVisits,
              pagesViewed: totalPagesViewed,
              isDeduplicated: true,
              deduplicatedAt: new Date(),
              deduplicatedMethod: isSameLocation ? 'ip-browser-os-location' : 'ip-browser-os-timepattern',
              mergedIds: docsToRemove.map((d: VisitorDoc) => d.visitorId)
            }
          }
        );
        mergedCount++;

        // Remove the duplicate documents
        for (const doc of docsToRemove) {
          await AnonymousVisitor.deleteOne({ _id: doc.id });
          removedCount++;
        }
      }
    }

    // Step 4: Find potential duplicates based on location + device + time patterns
    // This can catch visitors who use different browsers but are likely the same person
    const locationDeviceDuplicates = await AnonymousVisitor.aggregate([
      {
        $match: {
          city: { $type: "string", $not: { $in: ["", null] } },
          region: { $type: "string", $not: { $in: ["", null] } },
          device: { $type: "string", $not: { $in: ["", null] } },
          // Skip records that have already been deduplicated
          isDeduplicated: { $in: [false, null] }
        }
      },
      {
        $group: {
          _id: { city: "$city", region: "$region", device: "$device" },
          count: { $sum: 1 },
          docs: { $push: {
            id: "$_id",
            visitorId: "$visitorId",
            visitCount: "$visitCount",
            pagesViewed: "$pagesViewed",
            firstVisit: "$firstVisit",
            lastVisit: "$lastVisit",
            ipAddress: "$ipAddress",
            browser: "$browser",
            os: "$os"
          }}
        }
      },
      {
        $match: {
          count: { $gt: 1 }
        }
      }
    ]);

    // Process location+device duplicates
    for (const group of locationDeviceDuplicates) {
      // Check for overlapping time patterns which would suggest same user
      if (checkTimePatterns(group.docs)) {
        // Sort by firstVisit (oldest first) and visitCount (highest first)
        const sortedDocs = group.docs.sort((a: VisitorDoc, b: VisitorDoc) => {
          // First sort by firstVisit (oldest first)
          const dateA = new Date(a.firstVisit).getTime();
          const dateB = new Date(b.firstVisit).getTime();
          if (dateA !== dateB) return dateA - dateB;

          // If same date, sort by visitCount (highest first)
          return b.visitCount - a.visitCount;
        });

        // Keep the oldest record (or the one with the most visits if same date)
        const keepDoc = sortedDocs[0];
        const docsToRemove = sortedDocs.slice(1);

        // Calculate total visits and pages viewed
        const totalVisits = sortedDocs.reduce((sum: number, doc: VisitorDoc) => sum + (doc.visitCount || 0), 0);
        const totalPagesViewed = sortedDocs.reduce((sum: number, doc: VisitorDoc) => sum + (doc.pagesViewed || 0), 0);

        // Update the kept document with combined stats
        await AnonymousVisitor.updateOne(
          { _id: keepDoc.id },
          {
            $set: {
              visitCount: totalVisits,
              pagesViewed: totalPagesViewed,
              isDeduplicated: true,
              deduplicatedAt: new Date(),
              deduplicatedMethod: 'location-device-timepattern',
              mergedIds: docsToRemove.map((d: VisitorDoc) => d.visitorId)
            }
          }
        );
        mergedCount++;

        // Remove the duplicate documents
        for (const doc of docsToRemove) {
          await AnonymousVisitor.deleteOne({ _id: doc.id });
          removedCount++;
        }
      }
    }

    // Return success response
    return NextResponse.json({
      success: true,
      message: `Removed ${removedCount} duplicate records and merged ${mergedCount} visitor records`,
      removedCount,
      mergedCount
    });
  } catch (error) {
    console.error('Error deduplicating visitors:', error);
    return NextResponse.json(
      { error: 'Failed to deduplicate visitors', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

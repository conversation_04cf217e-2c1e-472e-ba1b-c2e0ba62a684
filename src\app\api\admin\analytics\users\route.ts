import { NextRequest, NextResponse } from 'next/server';
import type { Model } from 'mongoose'; // Import Model
import type { IUser } from '@/models/User';
import type { IUserActivity } from '@/models/UserActivity';
import { ensureMongooseConnection } from '@/lib/mongoose';
import { isAdmin } from '@/lib/middleware';

// Helper Functions
async function _getDailySignups(User: Model<IUser>, days: Date[]) {
  return Promise.all(
    days.map(async (date) => {
      const nextDay = new Date(date);
      nextDay.setDate(nextDay.getDate() + 1);
      const count = await User.countDocuments({ createdAt: { $gte: date, $lt: nextDay } });
      return { date: date.toISOString().split('T')[0], count };
    })
  );
}

async function _getSubscriptionDistribution(User: Model<IUser>) {
  const subscriptionCounts = await User.aggregate([
    { $group: { _id: '$subscription.plan', count: { $sum: 1 } } }
  ]);
  // Use Record<string, number> for better type safety
  return subscriptionCounts.reduce<Record<string, number>>((acc, item) => {
    const plan = item._id || 'free'; // Assuming item._id is string or null/undefined
    acc[plan] = item.count; // Assuming item.count is number
    return acc;
  }, { free: 0, basic: 0, premium: 0, family: 0 });
}

async function _getActiveUsers(UserActivity: Model<IUserActivity>, last7DaysStrings: string[]) {
   const activeUsersByDay = await UserActivity.aggregate([
      { $match: { timestamp: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }, type: 'auth', action: 'login' } },
      { $group: { _id: { userId: '$userId', day: { $dateToString: { format: '%Y-%m-%d', date: '$timestamp' } } } } },
      { $group: { _id: '$_id.day', count: { $sum: 1 } } }, // Group by day string
      { $sort: { _id: 1 } } // Sort by day string
    ]);

    // Create a map for efficient lookup
    const activeUsersMap = new Map(activeUsersByDay.map((d: { _id: string; count: number }) => [d._id, d.count]));

    return last7DaysStrings.map(day => activeUsersMap.get(day) || 0); // Lookup in map, default to 0
}


// --- GET Handler ---

// GET handler to fetch user analytics
export async function GET(request: NextRequest) {
  try {
    // Check if user is admin (calls ensureMongooseConnection internally)
    const adminCheck = await isAdmin(request);
    if (!adminCheck.isAuthorized) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Explicitly ensure connection (optional, as isAdmin does it, but safe)
    await ensureMongooseConnection();

    // Dynamically import models ONCE
    // Correctly assert the type using Model<IUser> and Model<IUserActivity>
    const User = (await import('@/models/User')).default as Model<IUser>;
    const UserActivity = (await import('@/models/UserActivity')).default as Model<IUserActivity>;

    // Prepare date ranges
    const now = new Date();
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date(now); date.setDate(date.getDate() - i); date.setHours(0, 0, 0, 0); return date;
    }).reverse();
    const last7DaysStrings = last7Days.map(d => d.toISOString().split('T')[0]); // Use the reversed dates

    // Call helper functions using Promise.all for concurrency
    const [dailySignupsData, subscriptionDistributionData, totalUsers, dailyActiveUsersData] = await Promise.all([
        _getDailySignups(User, last7Days),
        _getSubscriptionDistribution(User),
        User.countDocuments(), // Keep simple counts inline or move to helper
        _getActiveUsers(UserActivity, last7DaysStrings)
    ]);

    // Format results: Calculate subscription percentages
    const subscriptionPercentages = Object.entries(subscriptionDistributionData).reduce<Record<string, number>>((acc, [plan, count]) => {
        // Ensure totalUsers is greater than 0 to avoid division by zero
        acc[plan] = totalUsers > 0 ? Math.round((count / totalUsers) * 100) : 0;
        return acc;
      }, {});

    // Return real data
    return NextResponse.json({
      dailySignups: dailySignupsData.map(d => d.count),
      dates: last7DaysStrings,
      subscriptionDistribution: subscriptionPercentages,
      dailyActiveUsers: dailyActiveUsersData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in simplified GET handler:', error);
    // Ensure error is an instance of Error before accessing message
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      // Adjust error message for clarity
      { error: 'Failed during simplified handler', details: errorMessage },
      { status: 500 }
    );
  }
}

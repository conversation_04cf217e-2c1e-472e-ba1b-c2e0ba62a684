import { NextRequest, NextResponse } from 'next/server';
import mongoose from 'mongoose';
import User from '@/models/User';
import Profile from '@/models/Profile';
import { ensureMongooseConnection } from '@/lib/mongodb';

/**
 * POST /api/admin/fix-profile-images
 * Fix profile images for all users in the database
 */
export async function POST(request: NextRequest) {
  try {
    // Try to get the user ID from the cookie directly
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    if (!userId) {
      console.error('Admin fix-profile-images API: No user ID found in request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Check if user is admin
    const adminUser = await User.findById(userId).select('role').lean();
    if (!adminUser || (adminUser as { role?: string }).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Get all users
    const users = await User.find({});

    // Log all users with their profile images for debugging
    console.log('All users with profile images:');
    users.forEach(user => {
      console.log(`User ${user.name} (${user.email}):`, {
        id: user._id.toString(),
        profileImage: user.profileImage,
        picture: user.picture,
        hasProfileImage: !!user.profileImage,
        hasPicture: !!user.picture,
        isGoogleUser: !!user.googleId
      });
    });

    // Process each user
    const results = [];
    let updatedCount = 0;

    for (const user of users) {
      let needsUpdate = false;
      let updates = {};

      // Process Cloudinary URLs to ensure they use the correct cloud name
      const processCloudinaryUrl = (url?: string) => {
        if (!url) return null;

        // If it's a Cloudinary URL, ensure it has the correct cloud name (lowercase)
        if (url.includes('cloudinary.com')) {
          // Check if the URL contains the correct cloud name (should be lowercase)
          if (!url.includes('cloudinary.com/streamvista/')) {
            // Fix the URL by replacing the cloud name with the lowercase version
            return url.replace(/cloudinary\.com\/([^\/]+)\//, 'cloudinary.com/streamvista/');
          }
        }
        return url;
      };

      // Check and fix profileImage
      if (user.profileImage) {
        const fixedProfileImage = processCloudinaryUrl(user.profileImage);
        if (fixedProfileImage && fixedProfileImage !== user.profileImage) {
          updates = { ...updates, profileImage: fixedProfileImage };
          needsUpdate = true;
        }
      }

      // Check and fix picture
      if (user.picture) {
        const fixedPicture = processCloudinaryUrl(user.picture);
        if (fixedPicture && fixedPicture !== user.picture) {
          updates = { ...updates, picture: fixedPicture };
          needsUpdate = true;
        }
      }

      // If user has no profile image but has a picture, set profileImage = picture
      if (!user.profileImage && user.picture) {
        updates = { ...updates, profileImage: user.picture };
        needsUpdate = true;
      }

      // If user has no picture but has a profileImage, set picture = profileImage
      if (!user.picture && user.profileImage) {
        updates = { ...updates, picture: user.profileImage };
        needsUpdate = true;
      }

      // If user has neither profileImage nor picture, set default avatar
      if (!user.profileImage && !user.picture) {
        // Add cache busting parameter to the default avatar URL
        const timestamp = Date.now();
        const defaultAvatarUrl = `https://res.cloudinary.com/streamvista/image/upload/v1743812698/defaults/default_avatar.jpg?t=${timestamp}`;
        updates = { ...updates, profileImage: defaultAvatarUrl, picture: defaultAvatarUrl };
        needsUpdate = true;
      }

      // Add cache busting to any Cloudinary URLs in the updates
      if (updates.profileImage && typeof updates.profileImage === 'string' &&
          updates.profileImage.includes('cloudinary.com') && !updates.profileImage.includes('t=')) {
        const separator = updates.profileImage.includes('?') ? '&' : '?';
        updates.profileImage = `${updates.profileImage}${separator}t=${Date.now()}`;
      }

      if (updates.picture && typeof updates.picture === 'string' &&
          updates.picture.includes('cloudinary.com') && !updates.picture.includes('t=')) {
        const separator = updates.picture.includes('?') ? '&' : '?';
        updates.picture = `${updates.picture}${separator}t=${Date.now()}`;
      }

      // Update user if needed
      if (needsUpdate) {
        console.log(`Updating user ${user.name} (${user.email}):`, {
          before: {
            profileImage: user.profileImage,
            picture: user.picture
          },
          updates
        });

        await User.updateOne({ _id: user._id }, { $set: updates });

        // Verify the update was successful
        const updatedUser = await User.findById(user._id);
        console.log(`User ${user.name} after update:`, {
          profileImage: updatedUser?.profileImage,
          picture: updatedUser?.picture
        });

        // Update only the primary profile associated with this user
        // Each profile has its own avatar, but the user's profile image should match the primary profile
        const primaryProfile = await Profile.findOne({
          userId: user._id,
          isPrimary: true
        });

        if (primaryProfile) {
          // Update only the primary profile with the same image as the user's profile image
          const profileUpdates = { avatar: updatedUser?.profileImage || updatedUser?.picture };
          await Profile.findByIdAndUpdate(primaryProfile._id, { $set: profileUpdates });
          console.log(`Updated primary profile for user ${user.name} with new avatar`);
        } else {
          console.log(`No primary profile found for user ${user.name}`);
        }

        updatedCount++;
        results.push({
          id: user._id.toString(),
          name: user.name,
          email: user.email,
          updates,
          profileUpdated: !!primaryProfile
        });
      } else {
        console.log(`No updates needed for user ${user.name} (${user.email})`);
      }
    }

    // Return results
    return NextResponse.json({
      success: true,
      message: `Fixed profile images for ${updatedCount} users`,
      updatedUsers: updatedCount,
      totalUsers: users.length,
      details: results
    });
  } catch (error) {
    console.error('Error fixing profile images:', error);
    return NextResponse.json(
      { error: 'Failed to fix profile images', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

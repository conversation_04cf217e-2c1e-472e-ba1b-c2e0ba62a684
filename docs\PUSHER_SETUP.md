# Pusher Configuration Guide

This guide will help you set up Pusher for real-time WebSocket communication in your StreamVista application. StreamVista now exclusively uses <PERSON>usher for all real-time features, having completely migrated away from Socket.io.

## Overview

Pusher is a hosted service that provides WebSocket communication with fallbacks to other real-time technologies. It offers:

- Cross-platform real-time WebSocket communication
- Automatic reconnection logic
- Authentication and private channels
- Presence channels for user status
- Support for client-side and server-side implementation

## Requirements

- Pusher account (free tier available at [pusher.com](https://pusher.com))
- Your Pusher app credentials:
  - App ID
  - App Key
  - App Secret
  - App Cluster

## Environment Variables Setup

Add the following variables to your `.env.local` file:

```
# Pusher Configuration
PUSHER_APP_ID=your-app-id
PUSHER_APP_KEY=your-app-key
PUSHER_APP_SECRET=your-app-secret
PUSHER_APP_CLUSTER=your-app-cluster

# Make these available to the client
NEXT_PUBLIC_PUSHER_KEY=your-app-key
NEXT_PUBLIC_PUSHER_CLUSTER=your-app-cluster
```

## Client-Side Implementation

StreamVista implements connection monitoring through the `usePusher` hook, which provides real-time connection status and reconnection logic.

The `ConnectionStatusIndicator` component displays this connection state in the UI.

## Key Implementation Files

- `src/lib/pusher-client.ts` - Client-side pusher implementation
- `src/lib/pusher-server.ts` - Server-side pusher implementation
- `src/hooks/usePusher.ts` - Connection monitoring hook
- `src/components/ConnectionStatusIndicator.tsx` - UI component for connection status

## Watch Party Implementation

Watch parties now use Pusher for all real-time communication:

- Channel naming format: `watch-party-{partyId}`
- Event types defined in `WATCH_PARTY_EVENTS` enum
- Implementation in `useWatchPartyWithPusher` hook

## Troubleshooting

1. **Connection Issues**
   - Check that Pusher credentials are correctly configured
   - Verify cluster matches your region
   - Ensure client and server keys are properly separated

2. **Event Not Received**
   - Ensure channel name matches exactly
   - Check event naming consistency 
   - Verify channel subscription is active

3. **Rate Limiting**
   - Free tier has limits on connections and messages
   - Consider upgrading for production use

## Best Practices

1. **Channel Management**
   - Use channel prefixes for organization (e.g., `chat-`, `watch-party-`)
   - Clean up subscriptions when components unmount

2. **Event Naming**
   - Use descriptive, consistent event names
   - Define event types in a shared location

3. **Server-Side Triggering**
   - Use server-side triggering for security
   - Apply validation before broadcasting events 
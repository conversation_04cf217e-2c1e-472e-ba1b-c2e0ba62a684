import { useState } from 'react';
import Image from 'next/image';
import { Play, X, AlertCircle } from 'lucide-react';
import { ContentCardType } from "@/lib/content-utils";
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { useRouter } from 'next/navigation';

interface SearchResultsProps {
  query: string;
  results: ContentCardType[];
  isLoading?: boolean;
  error?: string | null;
  onClear?: () => void;
}

export default function SearchResults({ query, results, isLoading = false, error = null, onClear }: SearchResultsProps) {
  const [filter, setFilter] = useState<'all' | 'shows' | 'movies'>('all');
  const router = useRouter();

  const filteredResults = filter === 'all'
    ? results
    : results.filter(item => item.type === filter);

  const totalResults = results.length;
  const showsCount = results.filter(item => item.type === 'shows').length;
  const moviesCount = results.filter(item => item.type === 'movies').length;

  const handleNavigateToContent = (result: ContentCardType) => {
    const contentId = result.tmdbId || result.id;
    const contentType = result.type === 'shows' ? 'show' : 'movie';

    // Navigate directly to the watch route with forcePlay parameter
    router.push(`/watch/${contentId}?forcePlay=true&contentType=${contentType}`);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[calc(100vh-16rem)]">
        <div className="w-10 h-10 border-4 border-vista-blue/30 border-t-vista-blue rounded-full animate-spin"></div>
      </div>
    );
  }

  if (error && results.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-16rem)] text-center px-4">
        <AlertCircle className="w-12 h-12 text-red-500 mb-4" />
        <h2 className="text-xl font-semibold mb-2">Search Error</h2>
        <p className="text-vista-light/70 mb-6 max-w-md">{error}</p>
        <p className="text-vista-light/70 text-sm max-w-md">
          We're having trouble connecting to our search service. Please try again later or try a different search term.
        </p>
      </div>
    );
  }

  if (!query) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-16rem)] text-center px-4">
        <h1 className="text-3xl font-bold mb-4">Search StreamVista</h1>
        <p className="text-vista-light/70 max-w-lg mx-auto">
          Use the search bar above to find your favorite shows, movies, or browse by genre.
        </p>
      </div>
    );
  }

  return (
    <div className="min-h-[calc(100vh-16rem)] bg-vista-dark text-vista-light">
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">Search Results</h1>
            <p className="text-vista-light/70 mt-1">
              Found {totalResults} {totalResults === 1 ? 'result' : 'results'} for "{query}"
            </p>
            {error && (
              <p className="text-yellow-400 text-sm mt-1 flex items-center">
                <AlertCircle className="w-4 h-4 mr-1" />
                Note: Search had some issues. Showing limited results.
              </p>
            )}
          </div>
          {onClear && (
            <Button
              variant="outline"
              onClick={onClear}
              className="border-vista-light/20 text-vista-light hover:bg-vista-light/10"
            >
              <X className="h-4 w-4 mr-2" />
              Clear
            </Button>
          )}
        </div>

        <div className="mb-8">
          <div className="flex space-x-4 overflow-x-auto pb-2">
            <Button
              variant={filter === 'all' ? "default" : "outline"}
              className={filter === 'all'
                ? "bg-vista-blue text-white hover:bg-vista-blue/90 rounded-full"
                : "bg-transparent border-vista-light/20 text-vista-light hover:bg-vista-light/10 rounded-full"
              }
              onClick={() => setFilter('all')}
            >
              All ({totalResults})
            </Button>
            <Button
              variant={filter === 'shows' ? "default" : "outline"}
              className={filter === 'shows'
                ? "bg-vista-blue text-white hover:bg-vista-blue/90 rounded-full"
                : "bg-transparent border-vista-light/20 text-vista-light hover:bg-vista-light/10 rounded-full"
              }
              onClick={() => setFilter('shows')}
            >
              Shows ({showsCount})
            </Button>
            <Button
              variant={filter === 'movies' ? "default" : "outline"}
              className={filter === 'movies'
                ? "bg-vista-blue text-white hover:bg-vista-blue/90 rounded-full"
                : "bg-transparent border-vista-light/20 text-vista-light hover:bg-vista-light/10 rounded-full"
              }
              onClick={() => setFilter('movies')}
            >
              Movies ({moviesCount})
            </Button>
          </div>
        </div>

        {filteredResults.length === 0 ? (
          <div className="flex items-center justify-center py-20 text-center min-h-[40vh]">
            <div className="text-center">
              <p className="text-xl text-vista-light/60 mb-2">No results found for this filter.</p>
              <p className="text-sm text-vista-light/40">Try changing your filter or search for something else.</p>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8 animate-fade-in">
            {filteredResults.map((result, index) => (
              <div
                key={result.id}
                className="group cursor-pointer transition-all duration-300 hover:translate-y-[-4px]"
                onClick={() => handleNavigateToContent(result)}
                style={{ animationDelay: `${index * 50}ms` }}
              >
                <div className="relative w-full aspect-[2/3] rounded-lg overflow-hidden mb-3">
                  <Image
                    src={result.imagePath}
                    alt={result.title}
                    fill
                    className="object-cover transition-transform group-hover:scale-105"
                  />
                  {result.dataSource === 'both' && (
                    <div className="absolute top-2 right-2 bg-vista-blue/90 rounded-full p-1.5 transform transition-transform hover:scale-110">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                        <path d="M8 16H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v2"></path>
                        <path d="M12 8h8a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-8a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2z"></path>
                      </svg>
                    </div>
                  )}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity flex items-end justify-center p-4">
                    <Button className="bg-white text-vista-dark hover:bg-white/90 w-full">
                      <Play className="h-4 w-4 mr-2" /> Play
                    </Button>
                  </div>
                </div>
                <h3 className="font-medium text-vista-light">{result.title}</h3>
                <div className="flex items-center text-sm text-vista-light/70 mt-1">
                  <span className="capitalize">{result.type.slice(0, -1)}</span>
                  <span className="mx-1.5">•</span>
                  <span>{result.year}</span>
                  {result.dataSource && (
                    <>
                      <span className="mx-1.5">•</span>
                      {result.dataSource === 'both' ? (
                        <span className="text-vista-blue font-medium flex items-center">
                          <span className="mr-1">Combined</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M8 16H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v2"></path>
                            <path d="M12 8h8a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-8a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2z"></path>
                          </svg>
                        </span>
                      ) : (
                        <span className="capitalize">{result.dataSource}</span>
                      )}
                    </>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {filteredResults.length > 0 && (
          <Separator className="bg-vista-light/10 my-12" />
        )}
      </div>
    </div>
  );
}

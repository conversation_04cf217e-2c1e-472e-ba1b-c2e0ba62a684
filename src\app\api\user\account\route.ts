import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongoose';
import User from '@/models/User';
import mongoose from 'mongoose';

/**
 * PUT /api/user/account
 * Update user account information
 */
export async function PUT(req: NextRequest) {
  try {
    const { userId, name } = await req.json();

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'UserId is required' },
        { status: 400 }
      );
    }

    // Validate name if provided
    if (name !== undefined) {
      if (!name || name.trim() === '') {
        return NextResponse.json(
          { success: false, error: 'Name cannot be empty' },
          { status: 400 }
        );
      }

      if (name.length > 50) {
        return NextResponse.json(
          { success: false, error: 'Name cannot exceed 50 characters' },
          { status: 400 }
        );
      }
    }

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Prepare update object
    const updateData: any = {};
    if (name !== undefined) {
      updateData.name = name.trim();
    }

    // Only proceed if there's something to update
    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { success: false, error: 'No valid fields to update' },
        { status: 400 }
      );
    }

    // Update user in the database
    const updatedUser = await User.findByIdAndUpdate(
      userId,
      updateData,
      { new: true, select: '-password' }
    );

    if (!updatedUser) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Also update the primary profile name if it exists
    try {
      const Profile = mongoose.models.Profile;
      if (Profile && name !== undefined) {
        // Find the primary profile for this user
        const primaryProfile = await Profile.findOne({
          userId: userId,
          isPrimary: true
        });

        if (primaryProfile) {
          // Update the primary profile name to match the user's name
          await Profile.updateOne(
            { _id: primaryProfile._id },
            { name: name.trim() }
          );
        }
      }
    } catch (profileError) {
      console.warn('Error updating primary profile name:', profileError);
      // Continue even if profile update fails
    }

    // Return success with updated user data
    return NextResponse.json({
      success: true,
      user: {
        id: updatedUser._id.toString(),
        name: updatedUser.name,
        email: updatedUser.email,
        picture: updatedUser.picture || updatedUser.profileImage,
        role: updatedUser.role || 'user',
        createdAt: updatedUser.createdAt.toISOString()
      }
    });
  } catch (error) {
    console.error('Error updating user account:', error);
    let errorMessage = 'An unexpected error occurred';

    if (error instanceof Error) {
      errorMessage = error.message;
    }

    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getMovieRecommendations, getTVShowRecommendations } from '@/lib/tmdb-api';
import { formatTMDbContentForCards } from '@/lib/content-utils';

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  const { id } = params;
  const { searchParams } = new URL(request.url);
  const type = searchParams.get('type') as 'movie' | 'show' | null;

  if (!id || !type) {
    return NextResponse.json({ success: false, error: 'Content ID and type are required' }, { status: 400 });
  }

  try {
    let recommendations;
    if (type === 'movie') {
      recommendations = await getMovieRecommendations(id);
    } else if (type === 'show') {
      recommendations = await getTVShowRecommendations(id);
    } else {
      return NextResponse.json({ success: false, error: 'Invalid content type' }, { status: 400 });
    }

    const formattedRecommendations = formatTMDbContentForCards(recommendations.slice(0, 12));

    return NextResponse.json({ success: true, data: formattedRecommendations });
  } catch (error) {
    console.error(`Error fetching recommendations for ${type} ${id}:`, error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch recommendations' },
      { status: 500 }
    );
  }
}

'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { getTMDbImageUrl } from '@/lib/tmdb-api';

interface ImageItem {
  id: number;
  title: string;
  posterPath: string | null;
  backdropPath: string | null;
}

interface Category {
  name: string;
  images: ImageItem[];
  link: string;
  description: string;
  currentImageIndex: number;
}

// Initial categories structure
const initialCategoriesData: Omit<Category, 'images' | 'currentImageIndex'>[] = [
  {
    name: 'Movies',
    link: '/movies',
    description: 'Browse our extensive movie collection'
  },
  {
    name: 'TV Shows',
    link: '/shows',
    description: 'Discover popular and trending TV series'
  },
  {
    name: 'Action',
    link: '/movies?genre=action',
    description: 'High-octane action and adventure films'
  },
  {
    name: 'Sci-Fi',
    link: '/movies?genre=sci-fi',
    description: 'Futuristic and mind-bending sci-fi content'
  },
  {
    name: 'Horror',
    link: '/movies?genre=horror',
    description: 'Spine-chilling horror and thriller films'
  },
  {
    name: 'Documentary',
    link: '/shows?genre=documentary',
    description: 'Fascinating real-world documentary series'
  },
  {
    name: 'Animation',
    link: '/movies?genre=animation',
    description: 'Animated features for viewers of all ages'
  },
  {
    name: 'Drama',
    link: '/shows?genre=drama',
    description: 'Compelling dramatic series and films'
  }
];

// Fallback images for each category
const fallbackImages = {
  'Movies': 'https://images.unsplash.com/photo-1489599849927-2ee91cede3ba?q=80&w=2070',
  'TV Shows': 'https://images.unsplash.com/photo-1522869635100-9f4c5e86aa37?q=80&w=2070',
  'Action': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?q=80&w=2070',
  'Sci-Fi': 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?q=80&w=2070',
  'Horror': 'https://images.unsplash.com/photo-1520637736862-4d197d17c90a?q=80&w=2070',
  'Documentary': 'https://images.unsplash.com/photo-1682687220742-aba13b6e50ba?q=80&w=2070',
  'Animation': 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?q=80&w=2070',
  'Drama': 'https://images.unsplash.com/photo-1440404653325-ab127d49abc1?q=80&w=2070'
};

const DEFAULT_FALLBACK_IMAGE = 'https://images.unsplash.com/photo-1489599849927-2ee91cede3ba?q=80&w=2070';

const containerVariants = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  show: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5
    }
  }
};

export default function CategoryExplorer() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Helper function to get proper image URL
  const getImageUrl = (path: string | null): string => {
    if (!path || path === 'null' || path === 'undefined' || path.trim() === '') {
      return DEFAULT_FALLBACK_IMAGE;
    }

    // If it's already a full URL (like our fallback images), return it directly
    if (path.startsWith('http')) {
      return path;
    }

    // Otherwise, use the TMDB image URL helper
    const cleanedPath = path.startsWith('//') ? path.substring(1) : path;
    return getTMDbImageUrl(cleanedPath);
  };

  // Function to get next unique image for a category
  const getNextUniqueImage = (category: Category, currentIndex: number): number => {
    const currentlyDisplayed = new Set(
      categories.map(cat => cat.images[cat.currentImageIndex]?.id).filter(Boolean)
    );

    // Try to find an image that's not currently displayed
    for (let i = 1; i < category.images.length; i++) {
      const nextIndex = (currentIndex + i) % category.images.length;
      const imageId = category.images[nextIndex]?.id;

      if (imageId && !currentlyDisplayed.has(imageId)) {
        return nextIndex;
      }
    }

    // If all images are in use, just cycle to next (shouldn't happen with enough images)
    return (currentIndex + 1) % category.images.length;
  };

  // Handle hover to change image
  const handleCategoryHover = (categoryIndex: number) => {
    setCategories(prev => {
      const updated = [...prev];
      const category = updated[categoryIndex];

      if (category && category.images.length > 1) {
        const nextImageIndex = getNextUniqueImage(category, category.currentImageIndex);
        updated[categoryIndex] = {
          ...category,
          currentImageIndex: nextImageIndex
        };
      }

      return updated;
    });
  };

  // Fetch images for all categories and ensure unique initial display
  useEffect(() => {
    const fetchAllCategoryImages = async () => {
      setIsLoading(true);
      const categoryNames = initialCategoriesData.map(cat => cat.name).join(',');
      try {
        const response = await fetch(`/api/category-images?category=${encodeURIComponent(categoryNames)}&count=25`);
        if (!response.ok) throw new Error(`API error: ${response.statusText}`);

        const apiData = await response.json();
        const usedIds = new Set<number>();

        const categoriesWithImages = initialCategoriesData.map((initialCat, categoryIndex) => {
          const categoryData = apiData[initialCat.name];
          let images: ImageItem[] = [];

          if (categoryData && Array.isArray(categoryData.images) && categoryData.images.length > 0) {
            images = categoryData.images;
          } else {
            console.warn(`No valid images found for ${initialCat.name}, using fallback.`);
            const fallbackUrl = fallbackImages[initialCat.name as keyof typeof fallbackImages] || DEFAULT_FALLBACK_IMAGE;
            images = [{
              id: Date.now() + categoryIndex, // Unique ID for fallback
              title: 'Fallback Image',
              posterPath: fallbackUrl,
              backdropPath: fallbackUrl
            }];
          }

          // Find a unique image for initial display
          let initialImageIndex = 0;
          for (let i = 0; i < images.length; i++) {
            const imageId = images[i].id;
            if (!usedIds.has(imageId)) {
              usedIds.add(imageId);
              initialImageIndex = i;
              break;
            }
          }

          return {
            ...initialCat,
            images: images,
            currentImageIndex: initialImageIndex
          };
        });

        setCategories(categoriesWithImages);
      } catch (error) {
        console.error('Error fetching category images:', error);
        // Fallback: Populate categories with static fallback images if API fails
        const categoriesWithFallbacks = initialCategoriesData.map((category, index) => ({
          ...category,
          images: [{
            id: Date.now() + index,
            title: category.name,
            posterPath: fallbackImages[category.name as keyof typeof fallbackImages] || DEFAULT_FALLBACK_IMAGE,
            backdropPath: fallbackImages[category.name as keyof typeof fallbackImages] || DEFAULT_FALLBACK_IMAGE
          }],
          currentImageIndex: 0
        }));
        setCategories(categoriesWithFallbacks);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAllCategoryImages();
  }, []);

  // No auto-rotation - images only change on hover

  if (isLoading) {
    return (
      <section className="py-8">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl md:text-3xl font-bold text-white mb-6">
            Explore Content
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Array.from({ length: 8 }).map((_, index) => (
              <div
                key={index}
                className="relative overflow-hidden rounded-lg aspect-[16/9] bg-gray-800 animate-pulse"
              />
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-8">
      <div className="container mx-auto px-4">
        <motion.h2
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-2xl md:text-3xl font-bold text-white mb-6"
        >
          Explore Content
        </motion.h2>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="show"
          className="grid grid-cols-2 md:grid-cols-4 gap-4"
        >
          {categories.map((category, index) => (
            <Link href={category.link} key={category.name}>
              <motion.div
                variants={itemVariants}
                className="relative block overflow-hidden rounded-xl aspect-[16/9] group shadow-xl transition-all duration-500 ease-out hover:scale-[1.02] hover:shadow-2xl cursor-pointer"
                onMouseEnter={() => handleCategoryHover(index)}
              >
                {/* Background Image Container with Enhanced Transitions */}
                <div className="absolute inset-0 bg-gray-800 overflow-hidden">
                  {category.images.map((image, imageIndex) => (
                    <div
                      key={`${category.name}-${imageIndex}`}
                      className={`absolute inset-0 transition-all ease-in-out ${
                        imageIndex === category.currentImageIndex
                          ? 'opacity-100 scale-100'
                          : 'opacity-0 scale-105'
                      }`}
                      style={{ transitionDuration: '1500ms' }}
                    >
                      <Image
                        src={getImageUrl(image.backdropPath || image.posterPath)}
                        alt={`${image.title || category.name} background`}
                        fill
                        sizes="(max-width: 768px) 50vw, 25vw"
                        quality={85}
                        priority={index < 4 && imageIndex === 0}
                        className="object-cover w-full h-full"
                        onError={(e) => {
                          e.currentTarget.src = DEFAULT_FALLBACK_IMAGE;
                        }}
                      />
                      {/* Subtle overlay for better text readability */}
                      <div className="absolute inset-0 bg-black/10" />
                    </div>
                  ))}
                </div>

                {/* Enhanced Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/30 to-transparent opacity-80 group-hover:opacity-70 transition-opacity duration-500" />

                {/* Text Content with Better Typography */}
                <div className="absolute inset-0 flex flex-col justify-end p-4 md:p-5">
                  <h3 className="text-lg md:text-xl font-bold text-white mb-1 md:mb-2 drop-shadow-lg group-hover:text-vista-blue transition-colors duration-300">
                    {category.name}
                  </h3>
                  <p className="text-xs md:text-sm text-gray-100 line-clamp-2 opacity-95 group-hover:opacity-100 transition-opacity duration-300">
                    {category.description}
                  </p>
                </div>

                {/* Enhanced Hover Border with Glow */}
                <div className="absolute inset-0 border-2 border-transparent rounded-xl group-hover:border-vista-blue/60 group-hover:shadow-[0_0_20px_rgba(59,130,246,0.3)] transition-all duration-500" />
              </motion.div>
            </Link>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
import { Suspense } from 'react';
import { Metadata } from 'next';
import DetailWrapper from './DetailWrapper';

// Static export configuration
export const preferredRegion = 'auto'
export const dynamicParams = true // Changed from 'dynamic' to avoid naming conflict
export const runtime = 'nodejs'

// Define the metadata for the page
export async function generateMetadata({ params }: { params: { type: string; id: string } }): Promise<Metadata> {
  const { type, id } = await Promise.resolve(params);

  return {
    title: `${id.charAt(0).toUpperCase() + id.slice(1).replace(/-/g, ' ')} Details | StreamVista`,
    description: `Explore details and watch the trailer for ${id.replace(/-/g, ' ')} on StreamVista`,
  };
}

// Server component
export default async function DetailedPage({ params }: { params: { type: string; id: string } }) {
  // First await the params before using them
  const resolvedParams = await Promise.resolve(params);

  // Now it's safe to log them
  console.log('DetailedPage received params:', resolvedParams);
  console.log('DetailedPage params type:', typeof resolvedParams);
  console.log('DetailedPage params stringified:', JSON.stringify(resolvedParams));

  // Defensive extraction to ensure we always have string values
  const type = typeof resolvedParams?.type === 'string' ? resolvedParams.type : String(resolvedParams?.type || '');
  const id = typeof resolvedParams?.id === 'string' ? resolvedParams.id : String(resolvedParams?.id || '');

  console.log('DetailedPage extracted and sanitized params:', { type, id });

  // Ensure we have valid params
  const validType = type && (type === 'shows' || type === 'movies');
  const validId = id &&
                 typeof id === 'string' &&
                 id !== 'undefined' &&
                 id !== 'null' &&
                 id !== '[object Object]' &&
                 id !== 'unknown' &&
                 id !== '';

  // Log validation results
  console.log('DetailedPage validation:', { validType, validId, type, id });

  // If params are invalid, show error
  if (!validType || !validId) {
    return (
      <div className="min-h-screen bg-vista-dark flex items-center justify-center">
        <div className="text-center p-8 bg-vista-dark-lighter rounded-lg max-w-md">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-vista-light/30 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h2 className="text-2xl text-vista-light font-bold">Content not found</h2>
          <p className="text-vista-light/70 mt-2">
            {!validType ? 'Invalid content type' : 'Invalid content ID'}
          </p>
          <a
            href="/"
            className="mt-6 inline-block px-4 py-2 bg-vista-blue hover:bg-vista-blue/90 text-white rounded-md"
          >
            Return Home
          </a>
        </div>
      </div>
    );
  }

  // Create a sanitized version of params to pass to the client
  const sanitizedParams = {
    type: type,
    id: id
  };

  console.log('DetailedPage passing sanitized params to client:', sanitizedParams);

  return (
    <Suspense fallback={
      <div className="min-h-screen bg-vista-dark flex items-center justify-center">
        <div className="w-16 h-16 border-4 border-vista-blue/20 border-t-vista-blue rounded-full animate-spin"></div>
      </div>
    }>
      <DetailWrapper params={sanitizedParams} />
    </Suspense>
  );
}

/**
 * Authentication utilities for StreamVista
 */

import { NextRequest } from 'next/server';
// Remove direct imports that are only needed on the server
// import { ensureMongooseConnection } from '@/lib/mongoose';
// import User from '@/models/User';

// Environment variables
const GOOGLE_CLIENT_ID = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;

/**
 * Interface for the Google user profile
 */
export interface GoogleUserProfile {
  id: string;
  email: string;
  name: string;
  given_name: string;
  family_name: string;
  picture: string;
}

/**
 * Get the authenticated user from the request
 * This function should only be used on the server
 */
export async function getUserFromRequest(request: NextRequest) {
  // Ensure this code only runs on the server
  if (typeof window !== 'undefined') {
    console.error('getUserFromRequest should only be called on the server');
    return null;
  }

  try {
    // Get the session token from cookies
    const sessionToken = request.cookies.get('session')?.value;

    if (!sessionToken) {
      return null;
    }

    // Dynamically import server-only dependencies
    const { ensureMongooseConnection } = await import('@/lib/mongoose');
    const UserModel = (await import('@/models/User')).default;

    // Connect to the database
    await ensureMongooseConnection();

    // Find the user by session token
    const user = await UserModel.findOne({ 'sessions.token': sessionToken });

    // Return null if no user found
    if (!user) {
      return null;
    }

    // Return user data with proper typing
    return {
      id: String(user._id), // Convert to string safely
      email: String(user.email),
      name: String(user.name),
      role: String(user.role || 'user') // Default to 'user' if not defined
    };
  } catch (error) {
    console.error('Error getting user from request:', error);
    return null;
  }
}

/**
 * Load the Google API script with improved reliability
 */
export function loadGoogleScript(timeoutMs = 5000): Promise<void> {
  return new Promise((resolve, reject) => {
    // Safety check for server-side rendering
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      reject(new Error('Cannot load Google script in server environment'));
      return;
    }

    // Global state variable to prevent multiple simultaneous loads
    if ((window as any).__GOOGLE_SCRIPT_LOADING__) {
      console.log('Google script is already being loaded by another component');
      // Wait for existing load to complete
      const checkInterval = setInterval(() => {
        if ((window as any).__GOOGLE_SCRIPT_LOADING_COMPLETE__) {
          clearInterval(checkInterval);
          resolve();
        }
      }, 100);
      return;
    }

    // Check if the script is already loaded AND the API is available
    if (document.getElementById('google-auth-script') &&
        window.google && window.google.accounts &&
        (window as any).__GOOGLE_SCRIPT_LOADING_COMPLETE__) {
      console.log('Google auth script already loaded and API is available');
      resolve();
      return;
    }

    // Set loading state
    (window as any).__GOOGLE_SCRIPT_LOADING__ = true;

    // If script tag exists but API isn't available, remove it to try again
    const existingScript = document.getElementById('google-auth-script');
    if (existingScript) {
      console.log('Script tag exists but API is not available, removing to retry');
      existingScript.remove();
    }

    const script = document.createElement('script');
    script.src = 'https://accounts.google.com/gsi/client';
    script.id = 'google-auth-script';
    script.async = true;
    script.defer = true;

    // Set up polling to check for API availability
    let startTime = Date.now();
    const checkApiAvailable = () => {
      if (window.google && window.google.accounts) {
        console.log(`Google auth API available after ${Date.now() - startTime}ms`);
        // Mark as complete and resolve
        (window as any).__GOOGLE_SCRIPT_LOADING_COMPLETE__ = true;
        (window as any).__GOOGLE_SCRIPT_LOADING__ = false;
        resolve();
        return;
      }

      if (Date.now() - startTime > timeoutMs) {
        const error = new Error(`Google auth API not available after ${timeoutMs}ms timeout`);
        console.error(error);
        // Reset loading state on error
        (window as any).__GOOGLE_SCRIPT_LOADING__ = false;
        reject(error);
        return;
      }

      // Continue polling
      setTimeout(checkApiAvailable, 100);
    };

    script.onload = () => {
      console.log(`Script onload event fired after ${Date.now() - startTime}ms`);
      // Start checking for API availability
      checkApiAvailable();
    };

    script.onerror = (error) => {
      console.error('Error loading Google auth script:', error);
      // Reset loading state on error
      (window as any).__GOOGLE_SCRIPT_LOADING__ = false;
      reject(new Error('Failed to load Google authentication script'));
    };

    document.body.appendChild(script);
  });
}

/**
 * Check if the browser supports FedCM
 */
export function isFedCMSupported(): boolean {
  if (typeof window === 'undefined') return false;

  // Chrome 117+ supports FedCM
  const userAgent = window.navigator.userAgent;
  const chromeMatch = userAgent.match(/Chrome\/(\d+)/);

  if (chromeMatch && parseInt(chromeMatch[1], 10) >= 117) {
    console.log('FedCM is supported in this browser (Chrome 117+)');
    return true;
  }

  console.log('FedCM may not be supported in this browser - using fallback authentication');
  return false;
}

/**
 * Initialize Google One Tap and Google Sign-In
 */
export function initGoogleAuth(
  onSuccess: (response: any) => void,
  buttonId?: string
): void {
  // Safety check for server-side rendering
  if (typeof window === 'undefined' || typeof document === 'undefined') {
    console.error('Cannot initialize Google auth in server environment');
    return;
  }

  // Get client ID from environment variable
  const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
  if (!clientId) {
    console.error('Google Client ID is not defined in environment variables');
    return;
  }

  const usesFedCM = isFedCMSupported();
  console.log('Initializing Google auth with FedCM support:', usesFedCM);
  console.log('Using client ID:', clientId.substring(0, 8) + '...');

  // Wait for the google object to be available
  if (window.google && window.google.accounts) {
    // Initialize One Tap with FedCM support
    const config = {
      client_id: clientId,
      callback: onSuccess,
      auto_select: false,
      cancel_on_tap_outside: true,
      use_fedcm_for_prompt: usesFedCM, // Enable FedCM based on browser support
    };

    console.log('Google auth configuration:', {
      ...config,
      client_id: config.client_id.substring(0, 8) + '...' // Log partial client ID for security
    });

    try {
      window.google.accounts.id.initialize(config);
      console.log('Google auth initialized successfully');

      // Display the One Tap UI if not specifying a button
      if (!buttonId) {
        console.log('Prompting Google One Tap');
        window.google.accounts.id.prompt();

        // Since we can't use the callback directly with prompt anymore,
        // we can add listeners for the One Tap UI events if needed
        // For example, the UI may be skipped or not displayed for various reasons
        console.log('One Tap UI prompted - check console for Google Sign-In debug info');
      }

      // Render the sign-in button if buttonId is provided
      if (buttonId) {
        const buttonElement = document.getElementById(buttonId);
        if (buttonElement) {
          console.log('Rendering Google Sign-In button');
          window.google.accounts.id.renderButton(
            buttonElement,
            {
              type: 'standard',
              theme: 'outline',
              size: 'large',
              text: 'continue_with',
              shape: 'rectangular',
              logo_alignment: 'center',
              width: '100%',
            }
          );
        } else {
          console.error(`Button element with ID '${buttonId}' not found`);
        }
      }
    } catch (error) {
      console.error('Error initializing Google auth:', error);
    }
  } else {
    console.error('Google accounts API not available');
  }
}

/**
 * Handle Google Sign-In response
 */
export function handleGoogleSignIn(response: any): GoogleUserProfile | null {
  try {
    // Validate the response
    if (!response || !response.credential) {
      console.error('Invalid Google Sign-In response:', response);
      return null;
    }

    // Decode the JWT token
    const token = response.credential;
    const base64Url = token.split('.')[1];
    if (!base64Url) {
      console.error('Invalid JWT token format');
      return null;
    }

    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');

    // Safely decode the base64 string
    let jsonPayload;
    try {
      const decodedString = atob(base64);
      jsonPayload = decodeURIComponent(
        decodedString
          .split('')
          .map((c) => {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          })
          .join('')
      );
    } catch (decodeError) {
      console.error('Error decoding JWT payload:', decodeError);
      return null;
    }

    // Parse the JWT payload
    const payload = JSON.parse(jsonPayload);

    // Validate required fields
    if (!payload.sub || !payload.email) {
      console.error('Missing required fields in Google profile:', payload);
      return null;
    }

    // Return user profile information
    return {
      id: payload.sub,
      email: payload.email,
      name: payload.name || '',
      given_name: payload.given_name || '',
      family_name: payload.family_name || '',
      picture: payload.picture || '',
    };
  } catch (error) {
    console.error('Error handling Google Sign-In:', error);
    return null;
  }
}

/**
 * Sign out from Google
 */
export function signOutGoogle(): void {
  if (typeof window !== 'undefined' && window.google && window.google.accounts) {
    window.google.accounts.id.disableAutoSelect();
  }
}

/**
 * Check if the user is an admin
 * This function should only be used on the server
 */
export async function isAdmin(request: NextRequest) {
  // Ensure this code only runs on the server
  if (typeof window !== 'undefined') {
    console.error('isAdmin should only be called on the server');
    return {
      isAuthorized: false,
      message: 'Server-side function called on client',
      status: 500
    };
  }

  try {
    // Get the user ID from the cookie
    const userId = request.cookies.get('userId')?.value;

    if (!userId) {
      return {
        isAuthorized: false,
        message: 'Authentication required',
        status: 401
      };
    }

    // Dynamically import server-only dependencies
    const { ensureMongooseConnection } = await import('@/lib/mongoose');
    const mongoose = await import('mongoose');

    // Connect to the database
    await ensureMongooseConnection();

    // Get the User model
    const User = mongoose.default.models.User ||
                mongoose.default.model('User', new mongoose.default.Schema({
                  role: String
                }));

    // Find the user and check if they're an admin
    const user = await User.findById(userId).select('role').lean();

    if (!user) {
      return {
        isAuthorized: false,
        message: 'User not found',
        status: 401,
        userId
      };
    }

    if ((user as any).role !== 'admin') {
      return {
        isAuthorized: false,
        message: 'Forbidden: You do not have permission to access this resource',
        status: 403,
        userId
      };
    }

    // User is an admin
    return {
      isAuthorized: true,
      userId
    };
  } catch (error) {
    console.error('Error checking admin status:', error);
    return {
      isAuthorized: false,
      message: 'Error checking admin status',
      status: 500
    };
  }
}
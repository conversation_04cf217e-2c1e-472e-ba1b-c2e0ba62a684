# Testing Documentation

## Overview

StreamVista implements a comprehensive testing strategy that covers unit tests, integration tests, and end-to-end testing. The testing setup is designed to ensure code quality, catch bugs early, and maintain a stable application.

## Testing Stack

### Core Testing Tools
```json
{
  "devDependencies": {
    "@testing-library/react": "^14.0.0",
    "@testing-library/jest-dom": "^6.0.0",
    "@testing-library/user-event": "^14.0.0",
    "jest": "^29.0.0",
    "jest-environment-jsdom": "^29.0.0",
    "cypress": "^13.0.0",
    "msw": "^2.0.0"
  }
}
```

## Unit Testing

### Component Testing
```typescript
// Example of a component test
import { render, screen } from '@testing-library/react'
import { Button } from '@/components/ui/button'

describe('Button', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>)
    expect(screen.getByText('Click me')).toBeInTheDocument()
  })

  it('handles click events', () => {
    const handleClick = jest.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    screen.getByText('Click me').click()
    expect(handleClick).toHaveBeenCalled()
  })
})
```

### Utility Function Testing
```typescript
// Example of utility function tests
import { isValidApiKey } from '@/config/api'

describe('API Utilities', () => {
  it('validates API keys correctly', () => {
    expect(isValidApiKey('valid_key_12345678901234567890')).toBe(true)
    expect(isValidApiKey('short_key')).toBe(false)
    expect(isValidApiKey('YOUR_API_KEY_HERE')).toBe(false)
  })
})
```

### Mock Data Testing
```typescript
// Example of mock data testing
import { MOCK_MOVIES, MOCK_SHOWS } from '@/lib/content-api'

describe('Mock Content Data', () => {
  it('has required properties for movies', () => {
    MOCK_MOVIES.forEach(movie => {
      expect(movie).toHaveProperty('id')
      expect(movie).toHaveProperty('title')
      expect(movie).toHaveProperty('year')
      expect(movie).toHaveProperty('poster')
    })
  })
})
```

## Integration Testing

### API Integration Tests
```typescript
// Example of API integration test
import { getTMDbContent } from '@/lib/tmdb-api'

describe('TMDb API Integration', () => {
  it('fetches movie content correctly', async () => {
    const content = await getTMDbContent('movie', '550')
    expect(content).toHaveProperty('title')
    expect(content).toHaveProperty('overview')
    expect(content).toHaveProperty('posterPath')
  })
})
```

### Socket Integration Tests
```typescript
// Example of socket integration test
import { SocketManager } from '@/lib/socket-manager'

describe('Socket Manager', () => {
  let socket: SocketManager

  beforeEach(() => {
    socket = new SocketManager('ws://localhost:3001')
  })

  afterEach(() => {
    socket.disconnect()
  })

  it('connects and handles events', done => {
    socket.connect()
    socket.on('connect', () => {
      expect(socket.isConnected).toBe(true)
      done()
    })
  })
})
```

### Component Integration Tests
```typescript
// Example of component integration test
import { render, screen, fireEvent } from '@testing-library/react'
import { WatchParty } from '@/components/WatchParty'
import { SocketProvider } from '@/contexts/SocketContext'

describe('WatchParty Integration', () => {
  it('integrates with socket context', () => {
    render(
      <SocketProvider>
        <WatchParty contentId="123" />
      </SocketProvider>
    )
    
    expect(screen.getByText('Join Watch Party')).toBeInTheDocument()
  })
})
```

## End-to-End Testing

### Cypress Tests
```typescript
// Example of Cypress test
describe('Watch Party Flow', () => {
  it('completes full watch party flow', () => {
    cy.visit('/')
    cy.login('<EMAIL>', 'password')
    cy.get('[data-testid="content-card"]').first().click()
    cy.get('[data-testid="watch-party-button"]').click()
    cy.get('[data-testid="create-party"]').click()
    cy.url().should('include', '/watch-party/')
  })
})
```

### User Flow Testing
```typescript
// Example of user flow test
describe('Authentication Flow', () => {
  it('handles login and logout', () => {
    cy.visit('/auth')
    cy.get('input[name="email"]').type('<EMAIL>')
    cy.get('input[name="password"]').type('password123')
    cy.get('button[type="submit"]').click()
    cy.url().should('eq', Cypress.config().baseUrl + '/')
    cy.get('[data-testid="user-menu"]').click()
    cy.get('[data-testid="logout-button"]').click()
    cy.url().should('include', '/auth')
  })
})
```

## Test Utilities

### Test Helpers
```typescript
// Example of test helpers
export const mockTMDBResponse = <T>(data: T): Promise<T> => {
  return Promise.resolve(data)
}

export const createTestContent = (overrides = {}): IContent => ({
  id: 'test-id',
  title: 'Test Content',
  type: 'movie',
  year: '2024',
  ...overrides
})
```

### Mock Socket Manager
```typescript
// Example of mock socket manager
export class MockSocketManager {
  isConnected = false
  events: Record<string, Function[]> = {}

  connect() {
    this.isConnected = true
    this.emit('connect')
  }

  disconnect() {
    this.isConnected = false
    this.emit('disconnect')
  }

  on(event: string, callback: Function) {
    if (!this.events[event]) {
      this.events[event] = []
    }
    this.events[event].push(callback)
  }

  emit(event: string, data?: any) {
    if (this.events[event]) {
      this.events[event].forEach(callback => callback(data))
    }
  }
}
```

## Testing Best Practices

### 1. Component Testing
- Test component rendering
- Test user interactions
- Test state changes
- Test error states
- Test loading states

### 2. Integration Testing
- Test component interactions
- Test context providers
- Test data flow
- Test error handling
- Test real-time features

### 3. End-to-End Testing
- Test critical user flows
- Test error scenarios
- Test responsive design
- Test performance
- Test accessibility

### 4. Test Organization
- Group related tests
- Use descriptive test names
- Maintain test isolation
- Clean up after tests
- Use appropriate assertions

### 5. Mock Data
- Use realistic test data
- Maintain mock consistency
- Document mock behavior
- Update mocks with schema changes
- Test edge cases

## Test Configuration

### Jest Configuration
```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy'
  },
  testPathIgnorePatterns: ['<rootDir>/.next/', '<rootDir>/node_modules/'],
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': ['babel-jest', { presets: ['next/babel'] }]
  }
}
```

### Cypress Configuration
```javascript
// cypress.config.js
module.exports = {
  e2e: {
    baseUrl: 'http://localhost:3000',
    supportFile: 'cypress/support/e2e.ts',
    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',
    viewportWidth: 1280,
    viewportHeight: 720
  }
}
```

## Continuous Integration

### GitHub Actions Workflow
```yaml
name: Test
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test
      - run: npm run test:e2e
```

## Test Coverage

### Coverage Configuration
```javascript
// jest.config.js
module.exports = {
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov'],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
}
```

### Coverage Reports
- HTML reports in `/coverage`
- CI integration with coverage badges
- Threshold enforcement
- Branch coverage tracking
- Function coverage tracking
``` 
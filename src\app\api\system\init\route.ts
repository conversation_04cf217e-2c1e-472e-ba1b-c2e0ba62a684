import { NextRequest, NextResponse } from 'next/server';
import { initializeApplication, checkInitializationStatus } from '@/lib/initialization';

/**
 * GET /api/system/init
 * Check initialization status
 */
export async function GET(request: NextRequest) {
  try {
    const isInitialized = await checkInitializationStatus();
    
    return NextResponse.json({
      initialized: isInitialized,
      message: isInitialized ? 'Application is initialized' : 'Application needs initialization'
    });
  } catch (error) {
    console.error('Error checking initialization status:', error);
    return NextResponse.json(
      { error: 'Failed to check initialization status' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/system/init
 * Initialize the application
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Manual initialization requested via API');
    
    const result = await initializeApplication();
    
    if (result.success) {
      return NextResponse.json({
        success: true,
        message: result.message,
        details: result.details
      });
    } else {
      return NextResponse.json({
        success: false,
        message: result.message,
        details: result.details,
        errors: result.errors
      }, { status: 207 }); // Multi-status - partial success
    }
  } catch (error) {
    console.error('Error during manual initialization:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to initialize application',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

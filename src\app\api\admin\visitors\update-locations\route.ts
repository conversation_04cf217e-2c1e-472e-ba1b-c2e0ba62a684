import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import { getLocationFromIp } from '@/lib/geo-utils';
import User from '@/models/User';

/**
 * POST /api/admin/visitors/update-locations
 * Update location data for existing visitors
 *
 * This endpoint is only accessible to admin users.
 * It updates location information for visitors that have IP addresses but missing location data.
 */
export async function POST(request: NextRequest) {
  try {
    // Get the userId from cookies or query string
    const { searchParams } = new URL(request.url);
    let userId = request.cookies.get('userId')?.value;

    // If no userId in cookies, try query string (for client-side admin verification)
    if (!userId) {
      const userIdParam = searchParams.get('userId');
      if (userIdParam) userId = userIdParam;
    }

    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Connect to database
    await ensureMongooseConnection();

    // Find the user by ID
    const user = await User.findById(userId).lean();

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if user has admin role
    const isUserAdmin = user.role === 'admin' || user.role === 'superadmin';

    if (!isUserAdmin) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Import the AnonymousVisitor model
    const AnonymousVisitor = (await import('@/models/AnonymousVisitor')).default;

    // Get the batch size from query params or use default
    const batchSize = parseInt(searchParams.get('batchSize') || '25', 10);

    // Get the force update flag from query params
    const forceUpdate = searchParams.get('force') === 'true';

    // Build the query for finding visitors that need location updates
    let query: any = {
      ipAddress: { $exists: true, $ne: null, $ne: '' }
    };

    // If not forcing updates, only get visitors with missing location data
    if (!forceUpdate) {
      query.$or = [
        { country: { $exists: false } },
        { country: null },
        { country: '' },
        { country: 'Error' },
        { country: 'Unknown (Rate Limited)' }
      ];
    }

    console.log('Finding visitors with query:', JSON.stringify(query));

    // Find visitors that need location updates
    const visitors = await AnonymousVisitor.find(query)
      .sort({ lastVisit: -1 }) // Process most recent visitors first
      .limit(batchSize);

    console.log(`Found ${visitors.length} visitors that need location updates`);

    // Update each visitor with location data
    let updatedCount = 0;
    let errorCount = 0;
    let skippedCount = 0;

    for (const visitor of visitors) {
      try {
        if (!visitor.ipAddress) {
          console.log(`Skipping visitor ${visitor._id} - no IP address`);
          skippedCount++;
          continue;
        }

        // Skip local IPs unless force update is enabled
        const isLocalIp = visitor.ipAddress === 'localhost' ||
                         visitor.ipAddress === '127.0.0.1' ||
                         visitor.ipAddress === '::1' ||
                         visitor.ipAddress.startsWith('192.168.') ||
                         visitor.ipAddress.startsWith('10.') ||
                         visitor.ipAddress.startsWith('172.16.');

        if (isLocalIp && !forceUpdate) {
          console.log(`Skipping local IP ${visitor.ipAddress} for visitor ${visitor._id}`);
          skippedCount++;
          continue;
        }

        console.log(`Processing visitor ${visitor._id} with IP ${visitor.ipAddress}`);

        // Get location information
        const locationInfo = await getLocationFromIp(visitor.ipAddress);

        // Only update if we got valid location data
        if (locationInfo && (locationInfo.country || locationInfo.city)) {
          console.log(`Updating visitor ${visitor._id} with location:`, locationInfo);

          // Update visitor with location data
          await AnonymousVisitor.updateOne(
            { _id: visitor._id },
            {
              $set: {
                country: locationInfo.country,
                countryCode: locationInfo.countryCode,
                region: locationInfo.region,
                city: locationInfo.city,
                timezone: locationInfo.timezone,
                latitude: locationInfo.latitude,
                longitude: locationInfo.longitude
              }
            }
          );

          updatedCount++;
        } else {
          console.log(`No location data found for visitor ${visitor._id}`);
          errorCount++;
        }
      } catch (error) {
        console.error(`Error updating location for visitor ${visitor._id}:`, error);
        errorCount++;
      }

      // Add a small delay to avoid rate limits
      await new Promise(resolve => setTimeout(resolve, 300));
    }

    // Count remaining visitors that need updates
    const remainingCount = await AnonymousVisitor.countDocuments({
      ipAddress: { $exists: true, $ne: null, $ne: '' },
      $or: [
        { country: { $exists: false } },
        { country: null },
        { country: '' },
        { country: 'Error' },
        { country: 'Unknown (Rate Limited)' }
      ]
    });

    // Return detailed success response
    return NextResponse.json({
      success: true,
      message: `Updated location data for ${updatedCount} visitors. Skipped ${skippedCount}, errors: ${errorCount}. ${remainingCount} visitors still need updates.`,
      updatedCount,
      skippedCount,
      errorCount,
      remainingCount,
      batchSize,
      forceUpdate
    });
  } catch (error) {
    console.error('Error updating visitor locations:', error);
    return NextResponse.json(
      { error: 'Failed to update visitor locations', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { 
  ArrowRight, 
  TrendingUp, 
  Star, 
  Film, 
  Tv, 
  Heart, 
  Flame, 
  CircleUser, 
  ThumbsUp,
  <PERSON>fresh<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Loader2
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Navbar } from '@/components/Navbar';
import Footer from '@/components/Footer';
import ContentCard from '@/components/ContentCard';
import ContentRow from '@/components/ContentRow';
import RecommendedSection from '@/components/RecommendedSection';
import { ContentCardType } from '@/lib/content-utils';

import { 
  getTrendingDaily, 
  getTrendingWeekly,
  getPopularMovies,
  getTopRatedMovies,
  getPopularTVShows,
  getTopRatedTVShows,
  getMovieDetails,
  getTVShowDetails,
  MappedContent 
} from '@/lib/tmdb-api';

export default function RecommendationsPage() {
  // State management
  const [loading, setLoading] = useState(true);
  const [trendingContent, setTrendingContent] = useState<MappedContent[]>([]);
  const [popularMovies, setPopularMovies] = useState<MappedContent[]>([]);
  const [topRatedMovies, setTopRatedMovies] = useState<MappedContent[]>([]);
  const [popularShows, setPopularShows] = useState<MappedContent[]>([]);
  const [topRatedShows, setTopRatedShows] = useState<MappedContent[]>([]);
  const [personalizedRecommendations, setPersonalizedRecommendations] = useState<ContentCardType[]>([]);
  const [featuredContent, setFeaturedContent] = useState<MappedContent | null>(null);
  const [similarContent, setSimilarContent] = useState<MappedContent[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  // Generate featured content ID (would be based on user preferences in a real app)
  const getFeaturedContentId = () => {
    // This would be more sophisticated in a production app
    const contentTypes = ['movie', 'tv'];
    const randomType = contentTypes[Math.floor(Math.random() * contentTypes.length)];
    
    // Popular movie/show IDs that would provide good recommendations
    const popularIds = {
      movie: ['299054', '753342', '569094', '609681', '438631', '843794', '866398', '693134'],
      tv: ['203911', '71712', '84958', '76479', '63174', '60735', '114410', '204255']
    };
    
    const randomId = popularIds[randomType as 'movie' | 'tv'][Math.floor(Math.random() * popularIds[randomType as 'movie' | 'tv'].length)];
    return { type: randomType, id: randomId };
  };

  // Fetch data
  useEffect(() => {
    const fetchRecommendationsData = async () => {
      try {
        setLoading(true);
        
        // Fetch trending content
        const trending = await getTrendingDaily('all');
        setTrendingContent(trending.filter(item => item.posterUrl));
        
        // Fetch popular and top-rated content
        const [movies, topMovies, shows, topShows] = await Promise.all([
          getPopularMovies(),
          getTopRatedMovies(),
          getPopularTVShows(),
          getTopRatedTVShows()
        ]);
        
        setPopularMovies(movies.filter(item => item.posterUrl));
        setTopRatedMovies(topMovies.filter(item => item.posterUrl));
        setPopularShows(shows.filter(item => item.posterUrl));
        setTopRatedShows(topShows.filter(item => item.posterUrl));
        
        // Generate featured content and similar recommendations
        const featuredContentInfo = getFeaturedContentId();
        let featuredDetails;
        let recommendationsData;
        
        if (featuredContentInfo.type === 'movie') {
          featuredDetails = await getMovieDetails(featuredContentInfo.id);
          recommendationsData = featuredDetails.recommendations?.results || [];
        } else {
          featuredDetails = await getTVShowDetails(featuredContentInfo.id);
          recommendationsData = featuredDetails.recommendations?.results || [];
        }
        
        // Create mapped content for featured content
        const mappedFeatured: MappedContent = {
          id: featuredDetails.id.toString(),
          title: featuredDetails.title || featuredDetails.name,
          overview: featuredDetails.overview,
          posterUrl: featuredDetails.poster_path 
            ? `https://image.tmdb.org/t/p/w500${featuredDetails.poster_path}` 
            : null,
          backdropUrl: featuredDetails.backdrop_path 
            ? `https://image.tmdb.org/t/p/original${featuredDetails.backdrop_path}` 
            : null,
          voteAverage: featuredDetails.vote_average,
          releaseDate: featuredDetails.release_date || featuredDetails.first_air_date,
          tmdbId: featuredDetails.id.toString(),
          idType: 'tmdb',
          year: new Date(featuredDetails.release_date || featuredDetails.first_air_date).getFullYear()
        };
        
        setFeaturedContent(mappedFeatured);
        
        // Map recommendations to our content format
        const mappedRecommendations = recommendationsData.map((item: any) => ({
          id: item.id.toString(),
          title: item.title || item.name,
          overview: item.overview,
          posterUrl: item.poster_path 
            ? `https://image.tmdb.org/t/p/w500${item.poster_path}` 
            : null,
          backdropUrl: item.backdrop_path 
            ? `https://image.tmdb.org/t/p/original${item.backdrop_path}` 
            : null,
          voteAverage: item.vote_average,
          releaseDate: item.release_date || item.first_air_date,
          tmdbId: item.id.toString(),
          idType: 'tmdb',
          year: item.release_date || item.first_air_date 
            ? new Date(item.release_date || item.first_air_date).getFullYear() 
            : undefined
        }));
        
        setSimilarContent(mappedRecommendations.filter((item: any) => item.posterUrl));
        
        // Generate personalized recs (would be based on user watch history in a real app)
        const combined = [...movies, ...shows, ...topMovies, ...topShows];
        const randomSelection = [...combined]
          .sort(() => Math.random() - 0.5)
          .slice(0, 6)
          .map(item => ({
            id: item.id,
            title: item.title,
            imagePath: item.posterUrl || '/images/placeholder-poster.jpg',
            type: item.id.includes('tv') ? 'shows' : 'movies',
            year: item.year?.toString() || '',
            userRating: item.voteAverage,
            isNew: Math.random() > 0.7,
            isTrending: item.voteAverage > 7.5
          }));
        
        setPersonalizedRecommendations(randomSelection as ContentCardType[]);
      } catch (error) {
        console.error('Error fetching recommendations data:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchRecommendationsData();
  }, [refreshing]);
  
  // Handle refresh
  const handleRefresh = () => {
    setRefreshing(prev => !prev);
  };

  return (
    <div className="min-h-screen bg-vista-dark text-vista-light">
      <Navbar />

      <main className="container mx-auto px-4 pt-24 pb-12">
        {loading ? (
          <div className="flex flex-col items-center justify-center min-h-[50vh]">
            <Loader2 className="h-12 w-12 animate-spin text-vista-blue mb-4" />
            <p className="text-vista-light/70 text-lg">Curating recommendations just for you...</p>
          </div>
        ) : (
          <>
            {/* Page Header */}
            <div className="flex items-center justify-between mb-8">
              <div>
                <h1 className="text-3xl md:text-4xl font-bold text-vista-light">
                  Recommendations
                </h1>
                <p className="text-vista-light/70 mt-2">
                  Personalized content suggestions based on your viewing history and preferences
                </p>
              </div>
              
              <Button
                variant="outline"
                className="border-vista-light/10 bg-vista-light/5 text-vista-light hover:bg-vista-light/10"
                onClick={handleRefresh}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
            
            {/* Featured Recommendation */}
            {featuredContent && (
              <section className="mb-12">
                <div className="relative overflow-hidden rounded-xl border border-vista-light/10">
                  <div className="relative aspect-[21/9] w-full">
                    {/* Background Image */}
                    {featuredContent.backdropUrl && (
                      <>
                        <Image
                          src={featuredContent.backdropUrl}
                          alt={featuredContent.title}
                          fill
                          className="object-cover"
                          priority
                        />
                        <div className="absolute inset-0 bg-gradient-to-r from-vista-dark via-vista-dark/70 to-transparent" />
                        <div className="absolute inset-0 bg-gradient-to-t from-vista-dark via-vista-dark/50 to-transparent" />
                      </>
                    )}
                    
                    {/* Content */}
                    <div className="absolute inset-0 flex items-center p-6 md:p-10 z-10">
                      <div className="flex flex-col md:flex-row items-start md:items-center gap-6 md:gap-10 w-full">
                        {/* Poster */}
                        <div className="relative h-40 md:h-64 aspect-[2/3] rounded-lg overflow-hidden shadow-2xl border border-vista-light/10 flex-shrink-0">
                          <Image
                            src={featuredContent.posterUrl || '/images/placeholder-poster.jpg'}
                            alt={featuredContent.title}
                            fill
                            className="object-cover"
                            priority
                          />
                        </div>
                        
                        {/* Info */}
                        <div className="flex-1 max-w-3xl">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge className="bg-vista-blue text-white">Top Pick</Badge>
                            <Badge variant="outline" className="border-vista-light/20">
                              {featuredContent.id.includes('tv') ? 'TV Series' : 'Movie'}
                            </Badge>
                          </div>
                          
                          <h2 className="text-2xl md:text-4xl font-bold text-vista-light mb-3">
                            {featuredContent.title}
                          </h2>
                          
                          <div className="flex items-center gap-4 text-sm text-vista-light/70 mb-4">
                            {featuredContent.year && (
                              <div className="flex items-center">
                                <Clock className="h-4 w-4 mr-1" />
                                <span>{featuredContent.year}</span>
                              </div>
                            )}
                            {featuredContent.voteAverage > 0 && (
                              <div className="flex items-center">
                                <Star className="h-4 w-4 mr-1 text-yellow-400" />
                                <span>{featuredContent.voteAverage.toFixed(1)}</span>
                              </div>
                            )}
                          </div>
                          
                          <p className="text-vista-light/90 line-clamp-3 mb-6">
                            {featuredContent.overview}
                          </p>
                          
                          <div className="flex flex-wrap gap-3">
                            <Link href={`/watch/${featuredContent.id}?forcePlay=true&contentType=${featuredContent.id.includes('tv') ? 'show' : 'movie'}`}>
                              <Button className="bg-vista-blue hover:bg-vista-blue/90 text-white">
                                Watch Now
                              </Button>
                            </Link>
                            <Button
                              variant="outline"
                              className="border-vista-light/10 bg-vista-light/5 text-vista-light hover:bg-vista-light/10"
                            >
                              <Heart className="h-4 w-4 mr-2" />
                              Add to List
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </section>
            )}
            
            {/* Because You Watched Section */}
            {similarContent.length > 0 && featuredContent && (
              <section className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h2 className="text-xl font-semibold text-vista-light flex items-center">
                      <ThumbsUp className="h-5 w-5 mr-2 text-vista-blue" />
                      Because You Watched: {featuredContent.title}
                    </h2>
                    <p className="text-sm text-vista-light/70 mt-1">
                      Similar content you might enjoy
                    </p>
                  </div>
                  <Link href="/movies">
                    <Button variant="link" className="text-vista-blue hover:text-vista-blue/90 -mr-4">
                      View More <ArrowRight className="h-4 w-4 ml-1" />
                    </Button>
                  </Link>
                </div>
                
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                  {similarContent.slice(0, 6).map((content, index) => (
                    <ContentCard
                      key={content.id}
                      id={content.id}
                      title={content.title}
                      imagePath={content.posterUrl || '/images/placeholder-poster.jpg'}
                      type={content.id.includes('tv') ? 'shows' : 'movies'}
                      year={content.year?.toString() || ''}
                      userRating={content.voteAverage}
                      index={index}
                      link={`/watch/${content.id}?forcePlay=true&contentType=${content.id.includes('tv') ? 'show' : 'movie'}`}
                    />
                  ))}
                </div>
              </section>
            )}
            
            {/* For You Section (personalized) */}
            <section className="mb-10">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h2 className="text-xl font-semibold text-vista-light flex items-center">
                    <CircleUser className="h-5 w-5 mr-2 text-vista-blue" />
                    For You
                  </h2>
                  <p className="text-sm text-vista-light/70 mt-1">
                    Personalized selections based on your viewing history
                  </p>
                </div>
              </div>
              
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {personalizedRecommendations.map((content, index) => (
                  <ContentCard
                    key={content.id}
                    id={content.id}
                    title={content.title}
                    imagePath={content.imagePath}
                    type={content.type}
                    year={content.year}
                    userRating={content.userRating}
                    index={index}
                    link={`/watch/${content.id}?forcePlay=true&contentType=${content.type === 'shows' ? 'show' : 'movie'}`}
                  />
                ))}
              </div>
            </section>
            
            {/* Trending Now Section */}
            <RecommendedSection 
              title="Trending Now" 
              className="mb-10"
            />
            
            {/* Category Rows */}
            <section className="space-y-12 mb-10">
              {/* Top Rated Movies */}
              <ContentRow
                title="Critically Acclaimed Movies"
                subtitle="Top-rated films that critics love"
                seeAllLink="/movies"
                contents={topRatedMovies.slice(0, 10).map(movie => ({
                  id: movie.id,
                  title: movie.title,
                  imagePath: movie.posterUrl || '/images/placeholder-poster.jpg',
                  type: 'movies',
                  year: movie.year?.toString() || '',
                  userRating: movie.voteAverage
                }))}
              />
              
              {/* Popular TV Shows */}
              <ContentRow
                title="Must-Watch TV Shows"
                subtitle="Popular series everyone's talking about"
                seeAllLink="/shows"
                contents={popularShows.slice(0, 10).map(show => ({
                  id: show.id,
                  title: show.title,
                  imagePath: show.posterUrl || '/images/placeholder-poster.jpg',
                  type: 'shows',
                  year: show.year?.toString() || '',
                  userRating: show.voteAverage
                }))}
              />
              
              {/* Movies You Might Like */}
              <ContentRow
                title="Movies You Might Like"
                subtitle="Selected for you based on your preferences"
                seeAllLink="/movies"
                contents={popularMovies.slice(0, 10).map(movie => ({
                  id: movie.id,
                  title: movie.title,
                  imagePath: movie.posterUrl || '/images/placeholder-poster.jpg',
                  type: 'movies',
                  year: movie.year?.toString() || '',
                  userRating: movie.voteAverage
                }))}
              />
            </section>
            
            {/* Genres You Like Section */}
            <section className="mb-12">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-xl font-semibold text-vista-light">
                    Genres You'll Love
                  </h2>
                  <p className="text-sm text-vista-light/70 mt-1">
                    Explore categories based on your interests
                  </p>
                </div>
              </div>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {['Action', 'Sci-Fi', 'Drama', 'Comedy'].map((genre, index) => (
                  <Link href={`/movies?genre=${genre.toLowerCase()}`} key={genre}>
                    <motion.div 
                      className="relative overflow-hidden rounded-xl aspect-video group cursor-pointer"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <div className="absolute inset-0 bg-gradient-to-br from-vista-blue/50 to-vista-dark/80 group-hover:from-vista-blue/70 transition-all z-10" />
                      <Image
                        src={`https://images.unsplash.com/photo-${1550000000 + index * 100000}?w=500&auto=format&fit=crop`}
                        alt={genre}
                        fill
                        className="object-cover group-hover:scale-110 transition-transform duration-700"
                      />
                      <div className="absolute inset-0 flex items-center justify-center z-20">
                        <h3 className="text-xl font-bold text-white">{genre}</h3>
                      </div>
                    </motion.div>
                  </Link>
                ))}
              </div>
            </section>
          </>
        )}
      </main>

      <Footer />
    </div>
  );
} 
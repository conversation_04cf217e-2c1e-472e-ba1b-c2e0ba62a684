import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

/**
 * POST /api/admin/email-templates/test
 * Test an email template by sending a test email
 */
export async function POST(request: NextRequest) {
  try {
    // Get the user ID from the cookie directly
    const userId = request.cookies.get('userId')?.value;
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const User = mongoose.default.models.User ||
                mongoose.default.model('User', new mongoose.default.Schema({
                  role: String
                }));

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the EmailTemplate schema directly
    const EmailTemplateSchema = new mongoose.default.Schema({
      name: {
        type: String,
        required: true,
        unique: true,
        trim: true
      },
      subject: {
        type: String,
        required: true,
        trim: true
      },
      body: {
        type: String,
        required: true
      },
      description: {
        type: String,
        required: true
      },
      variables: [{
        type: String,
        trim: true
      }],
      isDefault: {
        type: Boolean,
        default: false
      },
      createdBy: {
        type: mongoose.default.Schema.Types.ObjectId,
        ref: 'User',
        required: true
      },
      updatedBy: {
        type: mongoose.default.Schema.Types.ObjectId,
        ref: 'User'
      },
      active: {
        type: Boolean,
        default: true
      }
    }, {
      timestamps: true
    });

    // Get the EmailTemplate model
    const EmailTemplate = mongoose.default.models.EmailTemplate ||
                         mongoose.default.model('EmailTemplate', EmailTemplateSchema);

    // Get data from request
    const data = await request.json();
    const { templateId, testEmail, testData } = data;

    // Validate required fields
    if (!templateId || !testEmail) {
      return NextResponse.json(
        { error: 'Template ID and test email are required' },
        { status: 400 }
      );
    }

    // Find the template
    const template = await EmailTemplate.findById(templateId);
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Replace variables in subject and body
    let subject = template.subject;
    let body = template.body;

    // If test data is provided, replace variables
    if (testData) {
      for (const [key, value] of Object.entries(testData)) {
        const regex = new RegExp(`{{${key}}}`, 'g');
        subject = subject.replace(regex, value as string);
        body = body.replace(regex, value as string);
      }
    }

    // Create a test account using Ethereal
    const testAccount = await nodemailer.createTestAccount();

    // Create a transporter
    const transporter = nodemailer.createTransport({
      host: process.env.EMAIL_SERVER_HOST || 'smtp.ethereal.email',
      port: parseInt(process.env.EMAIL_SERVER_PORT || '587'),
      secure: process.env.EMAIL_SERVER_SECURE === 'true',
      auth: {
        user: process.env.EMAIL_SERVER_USER || testAccount.user,
        pass: process.env.EMAIL_SERVER_PASSWORD || testAccount.pass
      }
    });

    // Send the test email
    const info = await transporter.sendMail({
      from: process.env.EMAIL_FROM || `"StreamVista" <${testAccount.user}>`,
      to: testEmail,
      subject,
      html: body
    });

    // Get the preview URL (for Ethereal emails)
    const previewUrl = nodemailer.getTestMessageUrl(info);

    return NextResponse.json({
      success: true,
      messageId: info.messageId,
      previewUrl
    });
  } catch (error) {
    console.error('Error sending test email:', error);
    return NextResponse.json(
      { error: 'Failed to send test email', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

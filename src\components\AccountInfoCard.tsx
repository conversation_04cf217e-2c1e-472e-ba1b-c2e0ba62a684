'use client';

import { useState } from 'react';
import { User, Mail, Key, LogOut, Shield, Pencil, Check, X, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { SettingsCard } from './SettingsCard';
import { cn } from '@/lib/utils';
import { TooltipElement } from '@/components/ui/tooltip';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

interface AccountInfoCardProps {
  email: string;
  name: string;
  onPasswordChange: () => void;
  onSignOut: () => void;
  className?: string;
}

export function AccountInfoCard({
  email,
  name,
  onPasswordChange,
  onSignOut,
  className
}: AccountInfoCardProps) {
  const { updateUserName } = useAuth();
  const [isEditingName, setIsEditingName] = useState(false);
  const [nameInput, setNameInput] = useState(name);
  const [isUpdating, setIsUpdating] = useState(false);
  const [nameBeforeEdit, setNameBeforeEdit] = useState('');

  // Start editing name
  const handleStartEditingName = () => {
    setNameBeforeEdit(name);
    setNameInput(name);
    setIsEditingName(true);
  };

  // Cancel editing name
  const handleCancelEditingName = () => {
    setIsEditingName(false);
    setNameInput(nameBeforeEdit);
  };

  // Save name changes
  const handleSaveName = async () => {
    // Validate name
    if (!nameInput || nameInput.trim() === '') {
      toast.error('Name cannot be empty');
      return;
    }

    // Don't update if name hasn't changed
    if (nameInput.trim() === name) {
      setIsEditingName(false);
      return;
    }

    setIsUpdating(true);

    try {
      const result = await updateUserName(nameInput.trim());

      if (result.success) {
        toast.success('Name updated successfully');
        setIsEditingName(false);
      } else {
        toast.error(result.error || 'Failed to update name');
        setNameInput(nameBeforeEdit);
      }
    } catch (error) {
      console.error('Error updating name:', error);
      toast.error('An unexpected error occurred');
      setNameInput(nameBeforeEdit);
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <SettingsCard
      title="Account Information"
      description="Your basic account details"
      icon={User}
      accentColor="purple"
      className={className}
    >
      <div className="space-y-5">
        {/* Email Field */}
        <div className="flex flex-col md:flex-row md:items-center gap-2 md:gap-6 p-3 rounded-lg bg-black/20 border border-white/5">
          <div className="md:w-1/3 flex items-center gap-2">
            <Mail className="h-4 w-4 text-purple-400" />
            <span className="text-sm text-vista-light/70 font-medium">Email</span>
          </div>
          <div className="md:w-2/3 font-medium break-all text-vista-light">{email || 'Not available'}</div>
        </div>

        {/* Name Field */}
        <div className="flex flex-col md:flex-row md:items-center gap-2 md:gap-6 p-3 rounded-lg bg-black/20 border border-white/5">
          <div className="md:w-1/3 flex items-center gap-2">
            <User className="h-4 w-4 text-purple-400" />
            <span className="text-sm text-vista-light/70 font-medium">Name</span>
          </div>
          <div className="md:w-2/3 flex items-center justify-between gap-2">
            {isEditingName ? (
              <div className="flex-1 flex items-center gap-2">
                <Input
                  value={nameInput}
                  onChange={(e) => setNameInput(e.target.value)}
                  className="h-8 bg-black/30 border-white/10 text-vista-light"
                  placeholder="Enter your name"
                  disabled={isUpdating}
                  autoFocus
                />
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 rounded-full bg-green-500/10 hover:bg-green-500/20 text-green-500"
                    onClick={handleSaveName}
                    disabled={isUpdating}
                  >
                    {isUpdating ? <Loader2 className="h-3.5 w-3.5 animate-spin" /> : <Check className="h-3.5 w-3.5" />}
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 rounded-full bg-red-500/10 hover:bg-red-500/20 text-red-500"
                    onClick={handleCancelEditingName}
                    disabled={isUpdating}
                  >
                    <X className="h-3.5 w-3.5" />
                  </Button>
                </div>
              </div>
            ) : (
              <>
                <div className="font-medium text-vista-light">{name || 'Not available'}</div>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 rounded-full bg-purple-500/10 hover:bg-purple-500/20 text-purple-400"
                  onClick={handleStartEditingName}
                >
                  <Pencil className="h-3.5 w-3.5" />
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Security Actions */}
        <div className="pt-3">
          <h3 className="text-sm font-medium text-vista-light/60 mb-3">Security Actions</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <Button
              variant="outline"
              size="sm"
              className="bg-black/20 hover:bg-black/40 border-white/5 hover:border-white/10 w-full flex items-center justify-center"
              onClick={onPasswordChange}
            >
              <Key className="h-4 w-4 mr-2 text-purple-400" />
              Change Password
            </Button>

            <TooltipElement content="Sign out from all devices">
              <Button
                variant="outline"
                size="sm"
                className="bg-black/20 hover:bg-black/40 border-white/5 hover:border-white/10 w-full flex items-center justify-center"
                onClick={onSignOut}
              >
                <LogOut className="h-4 w-4 mr-2 text-purple-400" />
                Sign Out
              </Button>
            </TooltipElement>
          </div>
        </div>

        {/* Two-Factor Authentication */}
        <div className="flex items-center gap-3 p-3 rounded-lg bg-black/20 border border-white/5 mt-4">
          <div className="w-10 h-10 rounded-md bg-purple-500/10 flex items-center justify-center">
            <Shield className="h-5 w-5 text-purple-500" />
          </div>
          <div className="flex-1">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-vista-light">Two-Factor Authentication</p>
                <p className="text-sm text-vista-light/70">Not enabled</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="bg-black/20 hover:bg-black/40 border-white/5 hover:border-white/10"
              >
                Enable
              </Button>
            </div>
          </div>
        </div>
      </div>
    </SettingsCard>
  );
}

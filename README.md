# Stream Vista

A modern streaming platform with collaborative watch party features.

## Features

### Current Features
- ✅ Content browsing and playback
- ✅ Custom video player with advanced controls
- ✅ Watch party system with Pusher integration
- ✅ Chat and reactions
- ✅ Playback synchronization
- ✅ Member ready states
- ✅ Toast notifications
- ✅ Error handling system
- ✅ Mock content data for development
- ✅ Responsive design with Tailwind CSS
- ✅ Multi-language support
- ✅ Enhanced UI components with consistent styling
- ✅ Unified navigation system
- ✅ Newsletter subscription component
- ✅ Mobile app showcase
- ✅ High-quality trailer presentations
- ✅ TypeScript strict mode compliance

### Upcoming Features

#### Phase 1: Real-time Communication (Q2 2024)
- 🔄 Enhanced Pusher integration
- 🔄 Automatic reconnection
- 🔄 Enhanced state synchronization
- 🔄 Rich presence indicators
- 🔄 Reaction animations

#### Phase 2: User Experience (Q3 2024)
- 📝 User profiles and preferences
- 😀 Personalized recommendations
- 👥 Advanced content filtering
- ⚙️ Watchlist management
- 🎥 Continue watching with progress tracking

#### Phase 3: Content Management (Q4 2024)
- 🧙‍♂️ Content upload wizard
- 👥 Analytics dashboard
- 💬 Content moderation tools
- 📊 Custom playlists and collections
- ⚙️ Creator monetization options

#### Phase 4: Mobile & Cross-platform (Q1 2025)
- ⚡ Progressive Web App (PWA) implementation
- 🛡️ Offline viewing capabilities
- ✨ Cross-device synchronization
- ♿ Mobile push notifications
- 📚 Touch-optimized controls

See our [detailed roadmap](docs/ROADMAP.md) for the complete development plan.

## Development

### Prerequisites
- Node.js 18+
- npm or yarn
- Modern web browser
- Pusher account (for watch party features)

### Setup
1. Clone the repository
```bash
git clone https://github.com/yourusername/stream-vista.git
cd stream-vista
```

2. Install dependencies
```bash
npm install
```

3. Start development server
```bash
npm run dev
```

### Pusher Configuration
For watch party functionality, you'll need to set up Pusher:

1. Create a Pusher account at [pusher.com](https://pusher.com)
2. Create a Channels app in your dashboard
3. Add your Pusher credentials to your `.env.local` file:

```
PUSHER_APP_ID=your_app_id
PUSHER_KEY=your_public_key
PUSHER_SECRET=your_secret_key
PUSHER_CLUSTER=your_cluster_region
NEXT_PUBLIC_PUSHER_KEY=your_public_key
NEXT_PUBLIC_PUSHER_CLUSTER=your_cluster_region
```

4. Enable client events in your Pusher app settings

See [Pusher Configuration](docs/PUSHER_CONFIGURATION.md) for more details.

### Command Line Notes
When running commands on Windows PowerShell, use semicolons instead of && for command chaining:
```powershell
cd C:/Users/<USER>/stream-vista; npm run dev
```

### Architecture

The watch party system is built on these core components:

- `WatchPartyContext`: Global state and party management
- `WatchParty`: Core party logic and state handling
- `PusherClient`: Real-time communication
- `UI Components`: Modular interface components

### Documentation
- [Project Overview](docs/PROJECT_OVERVIEW.md)
- [Technical Architecture](docs/TECHNICAL_ARCHITECTURE.md)
- [Development Workflow](docs/DEVELOPMENT_WORKFLOW.md)
- [Roadmap](docs/ROADMAP.md)
- [Pusher Configuration](docs/PUSHER_CONFIGURATION.md)

### Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

MIT License - See LICENSE file for details

## Environment Variables

Create a `.env.local` file in the root directory with the following variables:

```
# TMDb API Configuration
NEXT_PUBLIC_TMDB_API_KEY=your_tmdb_api_key
NEXT_PUBLIC_TMDB_ACCESS_TOKEN=your_tmdb_access_token
NEXT_PUBLIC_TMDB_BASE_URL=https://api.themoviedb.org/3
NEXT_PUBLIC_TMDB_IMAGE_BASE_URL=https://image.tmdb.org/t/p

# Pusher Configuration
PUSHER_APP_ID=your_app_id
PUSHER_KEY=your_public_key
PUSHER_SECRET=your_secret_key
PUSHER_CLUSTER=your_cluster_region
NEXT_PUBLIC_PUSHER_KEY=your_public_key
NEXT_PUBLIC_PUSHER_CLUSTER=your_cluster_region
```

You need to sign up for a TMDb API key at [https://www.themoviedb.org/settings/api](https://www.themoviedb.org/settings/api).

## Getting Started

## MongoDB Deployment Optimizations for Netlify

This application includes several optimizations to ensure MongoDB works reliably in Netlify's serverless environment:

### Connection Management

- **Intelligent Connection Pooling**: Dynamically adjusts connection pool size based on whether running in development or Netlify environment.
- **Connection Caching**: Uses global caching mechanism for MongoDB connections to prevent repeated connection attempts.
- **Circuit Breaker Pattern**: Implements exponential backoff to prevent connection storms during MongoDB outages.
- **Health Monitoring**: Added `/api/health` endpoint to verify MongoDB connectivity.

### Serverless-Specific Optimizations

- **Timeout Handling**: All database operations use a race pattern with timeouts to prevent function hanging.
- **Error Resilience**: Session validation gracefully handles network failures to prevent sudden logouts.
- **Connection Parameters**: Reduced connection timeouts specifically for serverless environment (5s connect, 15s socket).
- **Connection Cleanup**: Automatically closes connections after use to prevent resource exhaustion.

### Netlify Configuration

The `netlify.toml` file includes:
- Extended function timeouts for authentication endpoints
- Specific environment flags for serverless detection
- Proper caching headers for Next.js assets
- Next.js plugin configuration for serverless functions

## Auth System Features

- Email/password authentication with secure password hashing
- Google OAuth integration
- Session management with local storage
- Graceful handling of network issues

## Environment Setup

1. Clone the repository
2. Create a `.env.local` file with:
```
MONGODB_URI=your_mongodb_connection_string
MONGODB_DB=streamvista
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_google_client_id
```

3. Install dependencies:
```bash
npm install
```

4. Run the development server:
```bash
npm run dev
```

## Deploying to Netlify

Ensure you set the following environment variables in Netlify:
- `MONGODB_URI`: Your MongoDB connection string
- `MONGODB_DB`: Database name (default is "streamvista")
- `NEXT_PUBLIC_GOOGLE_CLIENT_ID`: For Google authentication
- `NETLIFY`: Set to "true" to enable serverless optimizations
- `PUSHER_APP_ID`: Your Pusher app ID
- `PUSHER_KEY`: Your Pusher key
- `PUSHER_SECRET`: Your Pusher secret
- `PUSHER_CLUSTER`: Your Pusher cluster
- `NEXT_PUBLIC_PUSHER_KEY`: Your Pusher key (client-side)
- `NEXT_PUBLIC_PUSHER_CLUSTER`: Your Pusher cluster (client-side)

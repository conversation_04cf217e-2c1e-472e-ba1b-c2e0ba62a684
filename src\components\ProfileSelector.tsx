"use client";

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Check<PERSON><PERSON>, Pencil, PlusCircle } from 'lucide-react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';

export interface Profile {
  id: string;
  name: string;
  avatar: string;
  isKids?: boolean;
}

interface ProfileSelectorProps {
  profiles: Profile[];
  onProfileSelect: (profileId: string) => void;
  onAddProfile?: () => void;
  onEditProfiles?: () => void;
}

export default function ProfileSelector({
  profiles,
  onProfileSelect,
  onAddProfile,
  onEditProfiles
}: ProfileSelectorProps) {
  const [selectedProfileId, setSelectedProfileId] = useState<string | null>(null);
  const [hoveredProfileId, setHoveredProfileId] = useState<string | null>(null);
  const [isClient, setIsClient] = useState(false);

  // Handle client-side rendering
  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleProfileClick = (profileId: string) => {
    setSelectedProfileId(profileId);
    // Add a slight delay to show the selection animation before navigating
    setTimeout(() => {
      onProfileSelect(profileId);
    }, 800);
  };

  // Variants for profile animation
  const profileVariants = {
    initial: { opacity: 0, y: 20 },
    animate: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.5,
        ease: [0.2, 0.65, 0.3, 0.9]
      }
    }),
    selected: {
      scale: 1.1,
      y: -10,
      transition: {
        duration: 0.4,
        ease: [0.2, 0.65, 0.3, 0.9]
      }
    },
    exit: {
      opacity: 0,
      scale: 0.9,
      transition: {
        duration: 0.3
      }
    }
  };

  const defaultAvatar = '/favicon.svg';

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-vista-dark text-vista-light px-4">
      <div className="w-full max-w-4xl">
        <h1 className="text-3xl md:text-4xl font-bold text-center mb-14">Who's Watching?</h1>

        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 md:gap-6 justify-center mb-16">
          {profiles.map((profile, index) => (
            <motion.div
              key={profile.id}
              custom={index}
              initial="initial"
              animate={selectedProfileId === profile.id ? "selected" : "animate"}
              exit="exit"
              variants={profileVariants}
              className="flex flex-col items-center"
              onMouseEnter={() => setHoveredProfileId(profile.id)}
              onMouseLeave={() => setHoveredProfileId(null)}
            >
              <button
                onClick={() => handleProfileClick(profile.id)}
                className="focus:outline-none group"
                disabled={selectedProfileId !== null}
                aria-label={`Select ${profile.name}'s profile`}
              >
                <div className="relative mb-3">
                  {/* Profile Image */}
                  <div
                    className={`relative w-24 h-24 md:w-28 md:h-28 rounded-lg overflow-hidden border-2 transition-all duration-300 ${
                      hoveredProfileId === profile.id && selectedProfileId === null
                        ? 'border-white scale-105 shadow-glow'
                        : 'border-transparent'
                    }`}
                  >
                    {isClient && (
                      <Image
                        src={profile.avatar || defaultAvatar}
                        alt={profile.name}
                        fill
                        sizes="(max-width: 768px) 96px, 112px"
                        className="object-cover"
                      />
                    )}

                    {/* Selection indicator */}
                    {selectedProfileId === profile.id && (
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="absolute inset-0 bg-vista-blue/30 flex items-center justify-center"
                      >
                        <div className="bg-vista-blue rounded-full p-2">
                          <CheckIcon className="h-6 w-6 text-white" />
                        </div>
                      </motion.div>
                    )}

                    {/* Kids badge */}
                    {profile.isKids && (
                      <div className="absolute bottom-0 left-0 right-0 bg-amber-500 text-[10px] text-center text-black py-0.5 font-medium">
                        KIDS
                      </div>
                    )}
                  </div>
                </div>

                <p className="text-center font-medium transition-colors duration-300">
                  {profile.name}
                </p>
              </button>
            </motion.div>
          ))}

          {/* Add Profile Button */}
          {onAddProfile && (
            <motion.div
              custom={profiles.length}
              initial="initial"
              animate="animate"
              variants={profileVariants}
              className="flex flex-col items-center"
            >
              <button
                onClick={onAddProfile}
                className="focus:outline-none group"
                disabled={selectedProfileId !== null}
                aria-label="Add new profile"
              >
                <div className="relative mb-3">
                  <div className="w-24 h-24 md:w-28 md:h-28 rounded-lg bg-vista-dark-lighter/30 border-2 border-dashed border-vista-light/30 flex items-center justify-center transition-all duration-300 group-hover:border-vista-light/50 group-hover:bg-vista-dark-lighter/50">
                    <PlusCircle className="h-10 w-10 text-vista-light/50 group-hover:text-vista-light/70 transition-colors duration-300" />
                  </div>
                </div>

                <p className="text-center font-medium text-vista-light/70 group-hover:text-vista-light transition-colors duration-300">
                  Add Profile
                </p>
              </button>
            </motion.div>
          )}
        </div>

        {/* Edit Profiles Button */}
        {onEditProfiles && (
          <div className="flex justify-center">
            <Button
              variant="outline"
              onClick={onEditProfiles}
              disabled={selectedProfileId !== null}
              className="border-vista-light/20 bg-transparent hover:bg-vista-light/10 text-vista-light/80 hover:text-vista-light"
            >
              <Pencil className="mr-2 h-4 w-4" /> Edit Profiles
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}

---
description: 
globs: 
alwaysApply: false
---
# TypeScript Strict Typing Rules

## Core Principles
- **Zero Tolerance for `any`**: Never use the `any` type. It defeats the purpose of TypeScript's type system and opens the door to runtime errors.
- **Explicit Type Definitions**: Always define explicit types for all variables, parameters, return types, and object structures.
- **Type Inference Limitation**: Only rely on type inference for simple, obvious cases where the type is immediately clear from the assignment.
- **Comprehensive Type Coverage**: Ensure 100% type coverage across the codebase with no implicit `any` types.
- **Discriminated Unions**: Use discriminated unions instead of type casting or type assertions.

## Variable and Function Declarations

### Variables
```typescript
// ❌ AVOID
const userData = fetchUserData();
let count = 0;
const items = [];

// ✅ CORRECT
const userData: UserProfile = fetchUserData();
let count: number = 0;
const items: Item[] = [];
```

### Function Parameters and Return Types
```typescript
// ❌ AVOID
function processUser(user) {
  return { ...user, lastActive: new Date() };
}

// ✅ CORRECT
function processUser(user: UserProfile): UserWithActivity {
  return { ...user, lastActive: new Date() };
}
```

### Arrow Functions
```typescript
// ❌ AVOID
const getFullName = (user) => `${user.firstName} ${user.lastName}`;

// ✅ CORRECT
const getFullName = (user: UserProfile): string => `${user.firstName} ${user.lastName}`;
```

### Generic Functions
```typescript
// ❌ AVOID
function getFirstItem(items) {
  return items[0];
}

// ✅ CORRECT
function getFirstItem<T>(items: T[]): T | undefined {
  return items[0];
}
```

## Object and Interface Definitions

### Object Literals
```typescript
// ❌ AVOID
const config = {
  apiUrl: 'https://api.example.com',
  timeout: 3000
};

// ✅ CORRECT
const config: ApiConfig = {
  apiUrl: 'https://api.example.com',
  timeout: 3000
};
```

### Interface Definitions
```typescript
// ❌ AVOID
interface User {
  name;
  email;
  role?;
}

// ✅ CORRECT
interface User {
  name: string;
  email: string;
  role?: UserRole;
  createdAt: Date;
}
```

### Interface Extensions
```typescript
// ✅ RECOMMENDED
interface BaseUser {
  id: string;
  email: string;
}

interface AdminUser extends BaseUser {
  permissions: Permission[];
  role: 'admin';
}

interface RegularUser extends BaseUser {
  preferences: UserPreferences;
  role: 'user';
}

type User = AdminUser | RegularUser;
```

## React Component Types

### Props Definitions
```typescript
// ❌ AVOID
function UserCard(props) {
  return <div>{props.name}</div>;
}

// ✅ CORRECT
interface UserCardProps {
  name: string;
  email: string;
  isActive: boolean;
  lastLogin?: Date;
  onProfileClick: (userId: string) => void;
}

function UserCard({ name, email, isActive, lastLogin, onProfileClick }: UserCardProps): JSX.Element {
  return <div>{name}</div>;
}
```

### State Types
```typescript
// ❌ AVOID
const [users, setUsers] = useState([]);
const [loading, setLoading] = useState(false);

// ✅ CORRECT
const [users, setUsers] = useState<User[]>([]);
const [loading, setLoading] = useState<boolean>(false);
```

### Event Handlers
```typescript
// ❌ AVOID
const handleClick = (e) => {
  console.log(e.target.value);
};

// ✅ CORRECT
const handleClick = (e: React.MouseEvent<HTMLButtonElement>): void => {
  console.log(e.currentTarget.value);
};
```

## Advanced Type Features

### Type Guards
```typescript
// ✅ RECOMMENDED
function isAdminUser(user: User): user is AdminUser {
  return user.role === 'admin';
}

function handleUser(user: User): void {
  if (isAdminUser(user)) {
    // TypeScript knows user is AdminUser here
    console.log(user.permissions);
  }
}
```

### Mapped Types
```typescript
// ✅ RECOMMENDED
interface UserFields {
  name: string;
  email: string;
  age: number;
}

type ReadonlyUser = {
  readonly [K in keyof UserFields]: UserFields[K];
};

type OptionalUser = {
  [K in keyof UserFields]?: UserFields[K];
};
```

### Conditional Types
```typescript
// ✅ RECOMMENDED
type ArrayElementType<T> = T extends (infer U)[] ? U : never;

type StringOrNumber<T> = T extends string ? string : T extends number ? number : never;
```

## Error Handling with Types

### Result Types
```typescript
// ✅ RECOMMENDED
interface Success<T> {
  success: true;
  data: T;
}

interface Failure {
  success: false;
  error: {
    code: string;
    message: string;
  };
}

type Result<T> = Success<T> | Failure;

function fetchData(): Promise<Result<User[]>> {
  // Implementation
}

// Usage
const result = await fetchData();
if (result.success) {
  // TypeScript knows result.data is User[]
  const users = result.data;
} else {
  // TypeScript knows result.error exists
  console.error(result.error.message);
}
```

## API and External Data Types

### API Response Types
```typescript
// ✅ RECOMMENDED
interface ApiResponse<T> {
  data: T;
  meta: {
    page: number;
    totalPages: number;
    count: number;
  };
}

// When consuming the API
async function getUsers(): Promise<User[]> {
  const response = await fetch('/api/users');
  const json: ApiResponse<User[]> = await response.json();
  return json.data;
}
```

### Form Data Types
```typescript
// ✅ RECOMMENDED
interface LoginFormData {
  email: string;
  password: string;
  rememberMe: boolean;
}

function submitForm(data: LoginFormData): Promise<Result<UserSession>> {
  // Implementation
}
```

## Configuration and Environment Types

### Environment Variables
```typescript
// ✅ RECOMMENDED
interface Env {
  API_URL: string;
  DEBUG: boolean;
  PUSHER_APP_ID: string;
  PUSHER_KEY: string;
  PUSHER_CLUSTER: string;
}

// Access with typing
const apiUrl = process.env.API_URL as string;
```

## Type Declaration Files

### Third-Party Libraries
```typescript
// ✅ RECOMMENDED
// declarations.d.ts
declare module 'untyped-library' {
  export interface LibraryOptions {
    timeout: number;
    retries: number;
  }
  
  export default function init(options: LibraryOptions): void;
}
```

## Compiler Configuration

### Strict Mode Settings
```json
// tsconfig.json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "noImplicitThis": true,
    "alwaysStrict": true,
    "exactOptionalPropertyTypes": true,
    "noUncheckedIndexedAccess": true
  }
}
```

## Enforcement and Tools

### ESLint Rules
- Configure `@typescript-eslint/no-explicit-any` to error
- Enable `@typescript-eslint/explicit-function-return-type`
- Enable `@typescript-eslint/explicit-module-boundary-types`
- Use `@typescript-eslint/no-unsafe-assignment` and related rules

### Code Review Checklist
- Zero instances of `any` type
- All function parameters and return types explicitly typed
- No type assertions without justification
- Interface properties have explicit types
- Discriminated unions for complex type relationships
- Proper null and undefined handling with `strictNullChecks`

### Type Coverage
- Run type coverage reports as part of CI/CD
- Maintain a minimum of 95% type coverage across the codebase
- Document justifications for any exceptions to typing rules

## Common Anti-patterns to Avoid

### Type Assertions
```typescript
// ❌ AVOID
const user = data as User;

// ✅ CORRECT
// Use type guards or validation
function isUser(data: unknown): data is User {
  return (
    typeof data === 'object' && 
    data !== null && 
    'name' in data && 
    'email' in data
  );
}

if (isUser(data)) {
  const user: User = data;
}
```

### Non-null Assertions
```typescript
// ❌ AVOID
const element = document.getElementById('root')!;

// ✅ CORRECT
const element = document.getElementById('root');
if (element) {
  // Use element safely here
}
```

### Unused Generic Types
```typescript
// ❌ AVOID
function getData<T>(): any {
  return fetch('/api/data');
}

// ✅ CORRECT
function getData<T>(): Promise<T> {
  return fetch('/api/data').then(res => res.json());
}
```

### Loose Function Typing
```typescript
// ❌ AVOID
type Callback = Function;

// ✅ CORRECT
type Callback<T, R> = (arg: T) => R;
```

## Best Practices

### Type Narrowing
```typescript
// ✅ RECOMMENDED
type Status = 'pending' | 'fulfilled' | 'rejected';

function processStatus(status: Status): void {
  switch (status) {
    case 'pending':
      // Handle pending
      break;
    case 'fulfilled':
      // Handle fulfilled
      break;
    case 'rejected':
      // Handle rejected
      break;
    default:
      // Exhaustiveness check - should never reach here
      const _exhaustiveCheck: never = status;
      throw new Error(`Unhandled status: ${_exhaustiveCheck}`);
  }
}
```

### Type Documentation
```typescript
/**
 * Represents a user in the system
 * @property {string} id - Unique identifier
 * @property {string} email - User's email address
 * @property {UserRole} role - User's role in the system
 */
interface User {
  id: string;
  email: string;
  role: UserRole;
}
```

### Performance Considerations
- Avoid excessive type complexity that may impact IDE performance
- Use type aliases for commonly reused complex types
- Break down large interfaces into smaller, composed interfaces
- Use interface merging for extensible types rather than complex union types

## Testing TypeScript Code

### Unit Test Typing
```typescript
// ✅ RECOMMENDED
describe('UserService', () => {
  it('should return user by id', async () => {
    const user: User = await userService.getUserById('123');
    expect(user.id).toBe('123');
  });
});
```

### Mock Typing
```typescript
// ✅ RECOMMENDED
// Create properly typed mocks
const mockUser: User = {
  id: '123',
  email: '<EMAIL>',
  role: 'user'
};

const mockGetUser = jest.fn<Promise<User>, [string]>()
  .mockResolvedValue(mockUser);
```

## TypeScript and API Integration

### API Request Typing
```typescript
// ✅ RECOMMENDED
interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  order?: 'asc' | 'desc';
}

interface UserSearchParams extends PaginationParams {
  query?: string;
  role?: UserRole;
  status?: 'active' | 'inactive';
}

async function searchUsers(params: UserSearchParams): Promise<ApiResponse<User[]>> {
  // Implementation
}
```

### GraphQL and TypeScript
```typescript
// ✅ RECOMMENDED
// Use generated types from your GraphQL schema
import { GetUserQuery, GetUserQueryVariables } from './generated/graphql';

async function getUser(id: string): Promise<User> {
  const { data } = await client.query<GetUserQuery, GetUserQueryVariables>({
    query: GET_USER,
    variables: { id }
  });
  
  return data.user;
}
```


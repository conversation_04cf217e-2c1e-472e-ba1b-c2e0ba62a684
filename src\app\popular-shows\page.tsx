'use client';

import { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, Filter, Grid, List, Calendar, Flame, SortAsc, SortDesc, Monitor } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Navbar } from '@/components/Navbar';
import Footer from '@/components/Footer';
import ContentCard from '@/components/ContentCard';
import { getPopularTVShows, MappedContent } from '@/lib/tmdb-api';
import { formatTMDbContentForCards, ContentCardType } from '@/lib/content-utils';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2 } from 'lucide-react';

type SortOption = 'popularity' | 'first_air_date' | 'title' | 'rating';
type SortOrder = 'asc' | 'desc';
type ViewMode = 'grid' | 'list';

export default function PopularShowsPage() {
  const [content, setContent] = useState<ContentCardType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [sortBy, setSortBy] = useState<SortOption>('popularity');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [yearFilter, setYearFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Generate year options for filter
  const currentYear = new Date().getFullYear();
  const yearOptions = [
    { value: 'all', label: 'All Years' },
    { value: currentYear.toString(), label: currentYear.toString() },
    { value: (currentYear - 1).toString(), label: (currentYear - 1).toString() },
    { value: '2020s', label: '2020s' },
    { value: '2010s', label: '2010s' },
    { value: '2000s', label: '2000s' },
    { value: '1990s', label: '1990s' },
    { value: 'recent', label: 'Recent (Last 5 years)' }
  ];

  // Show status options
  const statusOptions = [
    { value: 'all', label: 'All Shows' },
    { value: 'ongoing', label: 'Currently Airing' },
    { value: 'ended', label: 'Ended' },
    { value: 'new', label: 'New This Year' }
  ];

  // Apply filters and sorting
  const applyFilters = useCallback((shows: ContentCardType[]): ContentCardType[] => {
    let filtered = [...shows];
    
    // Filter by year
    if (yearFilter !== 'all') {
      filtered = filtered.filter(show => {
        if (!show.year) return false;
        const showYear = parseInt(show.year);
        const currentYear = new Date().getFullYear();
        
        switch (yearFilter) {
          case '2020s':
            return showYear >= 2020;
          case '2010s':
            return showYear >= 2010 && showYear < 2020;
          case '2000s':
            return showYear >= 2000 && showYear < 2010;
          case '1990s':
            return showYear >= 1990 && showYear < 2000;
          case 'recent':
            return showYear >= (currentYear - 5);
          default:
            return showYear.toString() === yearFilter;
        }
      });
    }
    
    // Filter by status
    if (statusFilter === 'new') {
      const currentYear = new Date().getFullYear();
      filtered = filtered.filter(show => {
        if (!show.year) return false;
        const showYear = parseInt(show.year);
        return showYear === currentYear;
      });
    }
    
    // Sort shows
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'title':
          comparison = a.title.localeCompare(b.title);
          break;
        case 'first_air_date':
          const yearA = parseInt(a.year || '0');
          const yearB = parseInt(b.year || '0');
          comparison = yearA - yearB;
          break;
        case 'popularity':
        case 'rating':
        default:
          // For popularity and rating, maintain TMDb order (already sorted)
          return 0;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });
    
    return filtered;
  }, [sortBy, sortOrder, yearFilter, statusFilter]);

  // Fetch popular TV shows
  const fetchPopularShows = useCallback(async (resetPage = false) => {
    try {
      setLoading(true);
      setError(null);
      
      const currentPage = resetPage ? 1 : page;
      const data: MappedContent[] = await getPopularTVShows(currentPage);
      let formattedContent = formatTMDbContentForCards(data);
      
      // Apply filters
      formattedContent = applyFilters(formattedContent);
      
      if (resetPage) {
        setContent(formattedContent);
        setPage(2);
      } else {
        setContent(prev => [...prev, ...formattedContent]);
        setPage(prev => prev + 1);
      }
      
      // Check if we have more content
      setHasMore(formattedContent.length === 20);
      
    } catch (err) {
      console.error('Error fetching popular shows:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch popular shows');
    } finally {
      setLoading(false);
    }
  }, [page, applyFilters]);

  // Load more content
  const loadMore = () => {
    if (!loading && hasMore) {
      fetchPopularShows(false);
    }
  };

  // Reset and fetch when filters change
  useEffect(() => {
    setPage(1);
    fetchPopularShows(true);
  }, [fetchPopularShows]);

  return (
    <div className="min-h-screen bg-vista-dark">
      <Navbar />
      <main className="min-h-screen bg-vista-dark pt-16 md:pt-20">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-3">
              <TrendingUp className="h-8 w-8 text-vista-blue" />
              <div>
                <h1 className="text-3xl font-bold text-vista-light mb-2">Popular TV Shows</h1>
                <p className="text-vista-light/60">
                  Discover the most popular TV shows right now
                </p>
              </div>
            </div>
            
            {/* View Mode Toggle */}
            <div className="flex gap-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="icon"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="icon"
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Filters */}
          <div className="flex flex-wrap gap-4 mb-8 p-4 bg-vista-dark-card rounded-lg">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-vista-light/60" />
              <span className="text-sm text-vista-light/60">Filters:</span>
            </div>
            
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-vista-light/60" />
              <Select value={yearFilter} onValueChange={setYearFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {yearOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center gap-2">
              <Monitor className="h-4 w-4 text-vista-light/60" />
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center gap-2">
              <span className="text-sm text-vista-light/60">Sort by:</span>
              <Select value={sortBy} onValueChange={(value: SortOption) => setSortBy(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="popularity">Popularity</SelectItem>
                  <SelectItem value="first_air_date">First Air Date</SelectItem>
                  <SelectItem value="title">Title</SelectItem>
                  <SelectItem value="rating">Rating</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              className="gap-2"
            >
              {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
              {sortOrder === 'asc' ? 'Ascending' : 'Descending'}
            </Button>
            
            <Badge variant="secondary" className="gap-1">
                              <Flame className="h-3 w-3" />
              Popular
            </Badge>
          </div>

          {/* Error State */}
          {error && (
            <Alert className="mb-8">
              <AlertDescription>
                {error}
              </AlertDescription>
            </Alert>
          )}

          {/* Loading State */}
          {loading && content.length === 0 && (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
              {[...Array(20)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="bg-vista-dark-card rounded-lg h-64 mb-3"></div>
                  <div className="bg-vista-dark-card rounded h-4 mb-2"></div>
                  <div className="bg-vista-dark-card rounded h-3 w-3/4"></div>
                </div>
              ))}
            </div>
          )}

          {/* Content Grid */}
          {!loading || content.length > 0 ? (
            <>
              <div className={`grid gap-6 ${
                viewMode === 'grid' 
                  ? 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5' 
                  : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
              }`}>
                {content.map((item, index) => (
                  <motion.div
                    key={`${item.id}-${index}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                  >
                    <ContentCard
                      id={item.id}
                      title={item.title}
                      imagePath={item.imagePath}
                      type={item.type}
                      year={item.year}
                      ageRating={item.ageRating}
                      index={index}
                      link={`/watch/${item.id}?forcePlay=true&contentType=show`}
                      isAwardWinning={item.isAwardWinning}
                      dataSource={item.dataSource}
                    />
                  </motion.div>
                ))}
              </div>

              {/* Load More Button */}
              {hasMore && (
                <div className="flex justify-center mt-12">
                  <Button 
                    onClick={loadMore} 
                    disabled={loading}
                    className="gap-2"
                  >
                    {loading && <Loader2 className="h-4 w-4 animate-spin" />}
                    Load More
                  </Button>
                </div>
              )}

              {/* End Message */}
              {!hasMore && content.length > 0 && (
                <div className="text-center mt-12">
                  <p className="text-vista-light/60">
                    You've reached the end of popular shows
                  </p>
                </div>
              )}
            </>
          ) : null}

          {/* Empty State */}
          {!loading && !error && content.length === 0 && (
            <div className="text-center py-16">
              <TrendingUp className="h-16 w-16 text-vista-light/40 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-vista-light mb-2">
                No shows found
              </h2>
              <p className="text-vista-light/60">
                Try adjusting your filters
              </p>
            </div>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
}

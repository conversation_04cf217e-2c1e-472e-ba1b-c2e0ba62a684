# Theme and Styling Service

## Overview

StreamVista's theme and styling system is built on Tailwind CSS with a custom design system inspired by Apple TV+. It provides a dark-mode-first approach with consistent, reusable components and utilities. The system includes custom color schemes, animations, and responsive design patterns.

## Configuration

### Base Configuration

```typescript
// tailwind.config.ts
export default {
  darkMode: ["class"],
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/**/*.{ts,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // StreamVista custom colors
        vista: {
          dark: 'hsl(var(--vista-dark))',
          'dark-lighter': 'hsl(var(--vista-dark-lighter))',
          light: 'hsl(var(--vista-light))',
          blue: 'hsl(var(--vista-blue))',
          accent: 'hsl(var(--vista-accent))',
          'accent-dim': 'hsl(var(--vista-accent-dim))',
          'accent-light': 'hsl(var(--vista-accent-light))',
          card: 'hsl(var(--vista-card))',
          'card-hover': 'hsl(var(--vista-card-hover))',
          overlay: 'hsl(var(--vista-overlay))',
        }
      }
    }
  }
}
```

### CSS Variables

```css
:root {
  /* Base theme colors - dark mode by default */
  --vista-dark: 0 0% 0%;          /* Pure black background */
  --vista-dark-lighter: 220 10% 12%; /* Slightly lighter black */
  --vista-light: 0 0% 100%;       /* Pure white text */
  --vista-blue: 210 100% 50%;     /* Primary blue */
  --vista-accent: 210 90% 50%;    /* Accent color */
  --vista-accent-dim: 210 90% 40%; /* Dimmed accent */
  --vista-accent-light: 210 90% 60%; /* Light accent */
  --vista-card: 240 10% 10%;      /* Card background */
  --vista-card-hover: 240 10% 15%; /* Card hover state */
  --vista-overlay: 240 10% 5% 90%; /* Modal overlay */
}
```

## Components

### Theme Toggle

```typescript
interface ThemeToggleProps {
  className?: string;
}

export function ThemeToggle({ className }: ThemeToggleProps) {
  const [theme, setTheme] = useState<'light' | 'dark'>('dark');
  
  // Theme toggle implementation
  const toggleTheme = () => {
    setTheme(prev => prev === 'dark' ? 'light' : 'dark');
    document.documentElement.classList.toggle('dark');
  };
}
```

### Design System Components

#### Button Variants

```typescript
const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);
```

#### Alert Variants

```typescript
const alertVariants = cva(
  "relative w-full rounded-lg border px-4 py-3 text-sm",
  {
    variants: {
      variant: {
        default: "bg-vista-dark-lighter border-vista-light/10 text-vista-light",
        error: "border-red-600/30 text-red-400 bg-red-800/10",
        warning: "border-yellow-600/30 text-yellow-400 bg-yellow-800/10",
        success: "border-green-600/30 text-green-400 bg-green-800/10",
        info: "border-vista-blue/30 text-vista-blue bg-vista-blue/10",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);
```

## Utilities

### Class Name Utility

```typescript
import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}
```

### Custom Animations

```typescript
const animations = {
  'fade-in': 'fadeIn 0.5s ease-in-out forwards',
  'slide-up': 'slideUp 0.5s ease-out forwards',
  'accordion-down': 'accordion-down 0.2s ease-out',
  'accordion-up': 'accordion-up 0.2s ease-out',
};

const keyframes = {
  fadeIn: {
    '0%': { opacity: '0' },
    '100%': { opacity: '1' },
  },
  slideUp: {
    '0%': { transform: 'translateY(20px)', opacity: '0' },
    '100%': { transform: 'translateY(0)', opacity: '1' },
  },
};
```

## Custom Classes

### Hover Effects

```css
/* Card hover effect */
.hover-card-effect {
  @apply transition-all duration-300 hover:scale-[1.02] hover:shadow-lg hover:shadow-black/40;
}

/* Image hover effect */
.content-image-hover {
  @apply transition-transform duration-500;
}
.group:hover .content-image-hover {
  @apply scale-105;
}
```

### Custom Scrollbars

```css
/* Enhanced scrollbar */
.enhanced-scrollbar::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

.enhanced-scrollbar::-webkit-scrollbar-track {
  background: rgba(12, 15, 22, 0.6);
  border-radius: 10px;
}

.enhanced-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(59, 130, 246, 0.3);
  border-radius: 10px;
  border: 2px solid rgba(12, 15, 22, 0.6);
}
```

## Best Practices

1. **Theme Management**
   - Use CSS variables for theme values
   - Implement dark mode as the default theme
   - Support system preferences
   - Persist theme choice in localStorage

2. **Component Design**
   - Use consistent spacing and sizing
   - Implement proper hover and focus states
   - Ensure accessibility compliance
   - Support responsive design patterns

3. **Performance**
   - Minimize CSS bundle size
   - Use efficient selectors
   - Implement proper purging
   - Optimize animations

4. **Accessibility**
   - Maintain sufficient color contrast
   - Support reduced motion
   - Implement proper focus management
   - Provide ARIA labels

5. **Maintenance**
   - Document component variants
   - Use consistent naming conventions
   - Implement reusable patterns
   - Keep styles modular

## Usage Examples

### Theme Toggle Implementation

```typescript
function App() {
  return (
    <div className="bg-vista-dark text-vista-light">
      <ThemeToggle />
      <main className="container mx-auto">
        {/* Content */}
      </main>
    </div>
  );
}
```

### Component Styling

```typescript
function Card({ children, className }: { children: React.ReactNode; className?: string }) {
  return (
    <div className={cn(
      "bg-vista-card rounded-lg p-4 hover-card-effect",
      className
    )}>
      {children}
    </div>
  );
}
```

### Responsive Design

```typescript
function Layout({ children }: { children: React.ReactNode }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {children}
    </div>
  );
}
```

## Integration Guidelines

1. **New Components**
   - Follow existing design patterns
   - Use theme variables
   - Implement proper variants
   - Include hover/focus states

2. **Theme Extensions**
   - Add new colors to CSS variables
   - Update Tailwind config
   - Document new additions
   - Test dark/light modes

3. **Responsive Design**
   - Use mobile-first approach
   - Implement proper breakpoints
   - Test across devices
   - Optimize for performance 
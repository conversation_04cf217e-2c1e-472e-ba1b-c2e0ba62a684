import type { Config } from "tailwindcss";

const config = {
    darkMode: ["class"],
    content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/**/*.{ts,tsx}",
  ],
  theme: {
    screens: {
      'xs': '375px',
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
    },
    extend: {
      colors: {
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))'
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))'
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))'
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))'
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))'
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))'
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))'
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',

        // StreamVista custom colors
        vista: {
          dark: 'hsl(var(--vista-dark))',
          'dark-lighter': 'hsl(var(--vista-dark-lighter))',
          light: 'hsl(var(--vista-light))',
          blue: 'hsl(var(--vista-blue))',
          accent: 'hsl(var(--vista-accent))',
          'accent-dim': 'hsl(var(--vista-accent-dim))',
          'accent-light': 'hsl(var(--vista-accent-light))',
          card: 'hsl(var(--vista-card))',
          'card-hover': 'hsl(var(--vista-card-hover))',
          overlay: 'hsl(var(--vista-overlay))',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)'
      },
      container: {
      center: true,
      padding: {
        DEFAULT: '1rem',
        sm: '2rem',
        lg: '4rem',
        xl: '5rem',
        '2xl': '6rem',
      },
      screens: {
        sm: '640px',
        md: '768px',
        lg: '1024px',
        xl: '1280px',
        '2xl': '1536px',
      },
      },
      backgroundImage: {
        'hero-gradient': 'linear-gradient(to bottom, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 40%, rgba(0,0,0,0.8) 100%)',
        'card-gradient': 'linear-gradient(to top, rgba(0,0,0,0.9) 0%, rgba(0,0,0,0.3) 60%, rgba(0,0,0,0) 100%)',
      },
      boxShadow: {
        'vista': '0 4px 14px 0 rgba(0, 0, 0, 0.2)',
        'vista-hover': '0 6px 20px rgba(0, 0, 0, 0.3)'
      },
      fontSize: {
        '2xs': '0.625rem', // 10px
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out forwards',
        'slide-up': 'slideUp 0.5s ease-out forwards',
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        'blob': 'blob 7s infinite',
        'blob-mobile': 'blobMobile 4s infinite',
        'float': 'float 6s ease-in-out infinite',
        'card-entrance': 'cardEntrance 0.7s ease-out forwards',
        'field-entry': 'fieldEntry 0.5s ease-out forwards',
        'light-streak': 'lightStreak 8s ease-in-out infinite',
        'twinkle': 'twinkle 4s ease-in-out infinite',
        'pulse': 'pulse 5s ease-in-out infinite',
        'fadeout': 'fadeout 8s ease-in-out infinite',
        'blink': 'blink 3s ease-in-out infinite',
        'shimmer': 'shimmer 2s ease-in-out infinite',
        'shooting-star': 'shootingStar 12s cubic-bezier(0.05, 0.5, 0.25, 1) infinite',
        'shooting-star-trail': 'shootingStarTrail 12s cubic-bezier(0.05, 0.5, 0.25, 1) infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        'accordion-down': {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        'accordion-up': {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        blob: {
          '0%': {
            transform: 'translate(0px, 0px) scale(1)',
          },
          '33%': {
            transform: 'translate(30px, -50px) scale(1.1)',
          },
          '66%': {
            transform: 'translate(-20px, 20px) scale(0.9)',
          },
          '100%': {
            transform: 'translate(0px, 0px) scale(1)',
          },
        },
        blobMobile: {
          '0%': {
            transform: 'translate(0px, 0px) scale(1)',
          },
          '33%': {
            transform: 'translate(10px, -15px) scale(1.05)',
          },
          '66%': {
            transform: 'translate(-10px, 10px) scale(0.95)',
          },
          '100%': {
            transform: 'translate(0px, 0px) scale(1)',
          },
        },
        float: {
          '0%, 100%': {
            transform: 'translateY(0)',
          },
          '50%': {
            transform: 'translateY(-20px)',
          },
        },
        cardEntrance: {
          '0%': {
            opacity: '0',
            transform: 'scale(0.96)',
            filter: 'blur(3px)',
          },
          '100%': {
            opacity: '1',
            transform: 'scale(1)',
            filter: 'blur(0)',
          },
        },
        fieldEntry: {
          '0%': {
            opacity: '0',
            transform: 'translateY(8px)',
          },
          '100%': {
            opacity: '1',
            transform: 'translateY(0)',
          },
        },
        lightStreak: {
          '0%': {
            opacity: '0.3',
            transform: 'translateY(-5%) rotate(12deg)'
          },
          '50%': {
            opacity: '0.5',
            transform: 'translateY(-15%) rotate(12deg)'
          },
          '100%': {
            opacity: '0.3',
            transform: 'translateY(-5%) rotate(12deg)'
          },
        },
        floatParticle: {
          '0%': {
            transform: 'translateY(0) translateX(0)',
            opacity: '0'
          },
          '10%': {
            opacity: '0.8'
          },
          '90%': {
            opacity: '0.8'
          },
          '100%': {
            transform: 'translateY(-100vh) translateX(100px)',
            opacity: '0'
          }
        },
        twinkle: {
          '0%': {
            opacity: '0.3',
            transform: 'scale(0.8)',
            filter: 'blur(0.5px)'
          },
          '25%': {
            opacity: '0.7',
            transform: 'scale(1.05)',
            filter: 'blur(0px)'
          },
          '50%': {
            opacity: '1',
            transform: 'scale(1.1)',
            filter: 'blur(0px)'
          },
          '75%': {
            opacity: '0.7',
            transform: 'scale(1)',
            filter: 'blur(0px)'
          },
          '100%': {
            opacity: '0.3',
            transform: 'scale(0.8)',
            filter: 'blur(0.5px)'
          }
        },
        pulse: {
          '0%': {
            opacity: '0.3',
            transform: 'scale(1)',
            filter: 'blur(0.5px)'
          },
          '50%': {
            opacity: '1',
            transform: 'scale(1.2)',
            filter: 'blur(0px)'
          },
          '100%': {
            opacity: '0.3',
            transform: 'scale(1)',
            filter: 'blur(0.5px)'
          }
        },
        fadeout: {
          '0%': {
            opacity: '0.1',
            transform: 'scale(0.8)',
            filter: 'blur(1px)'
          },
          '20%': {
            opacity: '0.7',
            transform: 'scale(1.05)',
            filter: 'blur(0px)'
          },
          '50%': {
            opacity: '0.9',
            transform: 'scale(1.1)',
            filter: 'blur(0px)'
          },
          '70%': {
            opacity: '0.7',
            transform: 'scale(1.05)',
            filter: 'blur(0px)'
          },
          '85%': {
            opacity: '0.05',
            transform: 'scale(0.9)',
            filter: 'blur(1px)'
          },
          '100%': {
            opacity: '0.1',
            transform: 'scale(0.8)',
            filter: 'blur(1px)'
          }
        },
        blink: {
          '0%': {
            opacity: '0.3',
            transform: 'scale(0.9)',
            filter: 'blur(0.5px)'
          },
          '10%': {
            opacity: '1',
            transform: 'scale(1.1)',
            filter: 'blur(0px)'
          },
          '20%': {
            opacity: '0.2',
            transform: 'scale(0.9)',
            filter: 'blur(0.5px)'
          },
          '30%': {
            opacity: '0.8',
            transform: 'scale(1.05)',
            filter: 'blur(0px)'
          },
          '100%': {
            opacity: '0.3',
            transform: 'scale(0.9)',
            filter: 'blur(0.5px)'
          }
        },
        shimmer: {
          '0%': {
            transform: 'translateX(-100%)'
          },
          '100%': {
            transform: 'translateX(100%)'
          }
        },
        shootingStar: {
          '0%': {
            transform: 'translateX(0) translateY(0)',
            opacity: '0'
          },
          '1%': {
            opacity: '0'
          },
          '2%': {
            opacity: '1'
          },
          '60%': {
            opacity: '1'
          },
          '100%': {
            transform: 'translateX(1500px) translateY(-1500px)',
            opacity: '0'
          }
        },
        shootingStarTrail: {
          '0%': {
            transform: 'translateX(0) translateY(0)',
            opacity: '0',
            width: '0'
          },
          '1%': {
            opacity: '0',
            width: '0'
          },
          '2%': {
            opacity: '0'
          },
          '5%': {
            opacity: '0.7',
            width: '100px'
          },
          '15%': {
            width: 'var(--trail-length, 150px)'
          },
          '60%': {
            opacity: '0.7'
          },
          '100%': {
            transform: 'translateX(1500px) translateY(-1500px)',
            opacity: '0',
            width: 'var(--trail-length, 150px)'
          }
        }
      },
    }
  },
  plugins: [
    require("tailwindcss-animate"),
    function({ addUtilities, theme, e }: {
      addUtilities: (utilities: Record<string, Record<string, string>>) => void,
      theme: (path: string) => any,
      e: (selector: string) => string
    }) {
      const animationDelayUtilities: Record<string, Record<string, string>> = {};
      for (let i = 1; i <= 10; i++) {
        const delayClass = `animation-delay-${i * 1000}`;
        animationDelayUtilities[`.${e(delayClass)}`] = {
          'animation-delay': `${i}s`,
        };
      }

      // Add ms delays for sequenced animations
      for (let i = 1; i <= 20; i++) {
        const msDelayClass = `animation-delay-${i * 100}ms`;
        animationDelayUtilities[`.${e(msDelayClass)}`] = {
          'animation-delay': `${i * 100}ms`,
        };
      }

      // Add specific delay classes
      animationDelayUtilities[`.${e('delay-100')}`] = {
        'transition-delay': '100ms',
      };
      animationDelayUtilities[`.${e('delay-300')}`] = {
        'transition-delay': '300ms',
      };
      animationDelayUtilities[`.${e('delay-500')}`] = {
        'transition-delay': '500ms',
      };
      animationDelayUtilities[`.${e('delay-700')}`] = {
        'transition-delay': '700ms',
      };

      addUtilities(animationDelayUtilities);

      // Add custom animation utilities
      addUtilities({
        '.animate-particles': {
          'position': 'absolute',
          'top': '0',
          'left': '0',
          'width': '100%',
          'height': '100%',
          'overflow': 'hidden',
          'z-index': '0',
        }
      });
    }
  ],
} satisfies Config;

export default config;

'use client';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface FilterOption {
  id: string;
  name: string;
}

// Add sort options
interface SortOption {
  id: string; // e.g., 'popularity.desc'
  name: string; // e.g., 'Popularity Descending'
}

interface FilterPanelProps {
  availableGenres: string[];
  yearFilters: FilterOption[];
  ratingFilters: FilterOption[];
  sortOptions: SortOption[]; // Add sort options
  selectedGenres: string[];
  selectedYear: string;
  selectedRating: string;
  selectedSortBy: string; // Add selected sort
  onGenreToggle: (genre: string) => void;
  onYearChange: (yearId: string) => void;
  onRatingChange: (ratingId: string) => void;
  onSortChange: (sortId: string) => void; // Add sort handler
  filterType: 'movie' | 'show';
}

export function FilterPanel({
  availableGenres,
  yearFilters,
  ratingFilters,
  sortOptions, // Destructure new props
  selectedGenres,
  selectedYear,
  selectedRating,
  selectedSortBy,
  onGenreToggle,
  onYearChange,
  onRatingChange,
  onSortChange,
  filterType = 'movie',
}: FilterPanelProps) {

  const yearLabel = filterType === 'show' ? 'First Air Year' : 'Release Year';

  return (
    <Card className="w-full bg-vista-dark-lighter border-vista-light/10">
      <CardContent className="p-6">
        {/* Adjust grid layout for better responsiveness */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8"> {/* Changed grid classes and gap */}
          {/* Genres Section (Keep as badges) */}
          <div className="space-y-3"> {/* Reduced space-y */}
            <h3 className="font-semibold text-vista-light text-sm">Genres</h3> {/* Smaller heading */}
            <div className="flex flex-wrap gap-2">
              {availableGenres.map(genre => (
                <Badge
                  key={genre}
                  variant={selectedGenres.includes(genre) ? "default" : "outline"}
                  className={`cursor-pointer transition-colors text-xs px-2 py-0.5 rounded-sm ${ // Smaller text/padding/radius
                    selectedGenres.includes(genre)
                      ? "bg-vista-blue hover:bg-vista-blue/90 border-vista-blue text-white"
                      : "border-vista-light/30 bg-transparent text-vista-light/80 hover:bg-vista-light/10 hover:border-vista-light/50"
                  }`}
                  onClick={() => onGenreToggle(genre)}
                >
                  {genre}
                </Badge>
              ))}
            </div>
          </div>

          {/* Years Section (Change to Select) */}
          <div className="space-y-3"> {/* Reduced space-y */}
            <h3 className="font-semibold text-vista-light text-sm">{yearLabel}</h3> {/* Smaller heading */}
             <Select value={selectedYear} onValueChange={onYearChange}>
                <SelectTrigger className="w-full bg-vista-dark border-vista-light/30 text-vista-light focus:ring-vista-blue text-xs h-9"> {/* Smaller text/height */}
                  <SelectValue placeholder={`Select ${yearLabel}`} />
                </SelectTrigger>
                <SelectContent className="bg-vista-dark border-vista-light/30 text-vista-light">
                  {yearFilters.map(option => (
                    <SelectItem
                      key={option.id}
                      value={option.id}
                      className="focus:bg-vista-light/10 text-xs" /* Smaller text */
                     >
                      {option.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
          </div>

          {/* Ratings Section (Change to Select) */}
          <div className="space-y-3"> {/* Reduced space-y */}
            <h3 className="font-semibold text-vista-light text-sm">Rating</h3> {/* Smaller heading */}
             <Select value={selectedRating} onValueChange={onRatingChange}>
                <SelectTrigger className="w-full bg-vista-dark border-vista-light/30 text-vista-light focus:ring-vista-blue text-xs h-9"> {/* Smaller text/height */}
                  <SelectValue placeholder="Select Rating" />
                </SelectTrigger>
                <SelectContent className="bg-vista-dark border-vista-light/30 text-vista-light">
                  {ratingFilters.map(option => (
                    <SelectItem
                      key={option.id}
                      value={option.id}
                      className="focus:bg-vista-light/10 text-xs" /* Smaller text */
                     >
                      {option.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
          </div>

          {/* Sort By Section (Keep as Select) */}
          <div className="space-y-3"> {/* Reduced space-y */}
             <h3 className="font-semibold text-vista-light text-sm">Sort By</h3> {/* Smaller heading */}
             <Select value={selectedSortBy} onValueChange={onSortChange}>
                <SelectTrigger className="w-full bg-vista-dark border-vista-light/30 text-vista-light focus:ring-vista-blue text-xs h-9"> {/* Smaller text/height */}
                  <SelectValue placeholder="Select sorting" />
                </SelectTrigger>
                <SelectContent className="bg-vista-dark border-vista-light/30 text-vista-light">
                  {sortOptions.map(option => (
                    <SelectItem
                      key={option.id}
                      value={option.id}
                      className="focus:bg-vista-light/10 text-xs" /* Smaller text */
                     >
                      {option.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
          </div>

        </div>
      </CardContent>
    </Card>
  );
} 
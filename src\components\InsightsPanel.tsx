'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {
  Clock, History, Play, BarChart, Eye, TrendingUp, Award,
  Calendar, Timer, Flame, AlertCircle, Bookmark, Tv, Film, Popcorn,
  Hourglass, ChevronRight, ArrowUp, ArrowDown, SkipForward
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import SkeletonLoader from '@/components/SkeletonLoader';
import { Separator } from '@/components/ui/separator';
import { popularMovies, popularShows, continueWatching } from '@/data/content';
import {
  formatWatchTime,
  getWatchHistory,
  calculateWatchStats,
  generateSampleWatchHistory,
  WatchHistoryItem,
  WatchStats
} from '@/lib/insights-utils';

export default function InsightsPanel() {
  const [loading, setLoading] = useState(true);
  const [watchHistory, setWatchHistory] = useState<WatchHistoryItem[]>([]);
  const [stats, setStats] = useState<WatchStats | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'activity' | 'recommendations'>('overview');
  const [historyFilter, setHistoryFilter] = useState<'all' | 'shows' | 'movies'>('all');

  // Load watch history from localStorage (or generate sample data if empty)
  useEffect(() => {
    setTimeout(() => {
      let loadedHistory = getWatchHistory();

      // If no history exists, generate sample data for demonstration
      if (loadedHistory.length === 0) {
        const allContent = [...popularMovies, ...popularShows];
        loadedHistory = generateSampleWatchHistory(continueWatching, allContent);
        // Save the generated history
        if (typeof window !== 'undefined') {
          localStorage.setItem('vista-watch-history', JSON.stringify(loadedHistory));
        }
      }

      // Calculate stats based on the loaded history
      const allContent = [...popularMovies, ...popularShows];
      const calculatedStats = calculateWatchStats(loadedHistory, allContent);

      setWatchHistory(loadedHistory);
      setStats(calculatedStats);
      setLoading(false);
    }, 800); // Simulate loading delay
  }, []);

  // Filter history based on the selected type
  const filteredHistory = historyFilter === 'all'
    ? watchHistory
    : watchHistory.filter(item => item.type === (historyFilter === 'shows' ? 'show' : 'movie'));

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Group watch history by date
  const getGroupedHistory = (history: WatchHistoryItem[]) => {
    const grouped: { [key: string]: WatchHistoryItem[] } = {};

    history.forEach(item => {
      const date = new Date(item.watchedAt).toLocaleDateString();
      if (!grouped[date]) {
        grouped[date] = [];
      }
      grouped[date].push(item);
    });

    return Object.entries(grouped).map(([date, items]) => ({
      date: new Date(date),
      items
    })).sort((a, b) => b.date.getTime() - a.date.getTime());
  };

  const groupedHistory = getGroupedHistory(filteredHistory);

  // Generate content recommendations based on watch history
  const getRecommendations = () => {
    if (!stats || !stats.favoriteGenres.length) return [];

    const topGenres = stats.favoriteGenres.map(g => g.genre);
    const watchedIds = new Set(watchHistory.map(item => item.contentId));

    // Get content that matches top genres and hasn't been watched yet
    const allContent = [...popularMovies, ...popularShows];
    return allContent
      .filter(content =>
        !watchedIds.has(String(content.id)) &&
        content.genres?.some(genre => topGenres.includes(genre))
      )
      .slice(0, 6);
  };

  const recommendations = getRecommendations();

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-semibold text-vista-light">Insights</h2>
        </div>
        <SkeletonLoader count={4} layout="grid" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header section */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div>
          <h2 className="text-2xl md:text-3xl font-semibold text-vista-light">Viewing Insights</h2>
          <p className="text-vista-light/70">Discover your viewing habits and recommendations</p>
        </div>

        {/* Stats summary */}
        {stats && (
          <div className="flex items-center space-x-4 p-2">
            <div className="flex flex-col items-center px-4 py-2 bg-vista-dark-lighter rounded-lg">
              <span className="text-vista-light/70 text-xs mb-1">Watch Time</span>
              <span className="text-vista-light font-semibold">{formatWatchTime(stats.totalWatchTime)}</span>
            </div>

            <div className="flex flex-col items-center px-4 py-2 bg-vista-dark-lighter rounded-lg">
              <span className="text-vista-light/70 text-xs mb-1">Watch Streak</span>
              <span className="text-vista-light font-semibold flex items-center">
                <Flame className="w-3.5 h-3.5 text-vista-blue mr-1" />
                {stats.watchStreak} days
              </span>
            </div>

            <div className="flex flex-col items-center px-4 py-2 bg-vista-dark-lighter rounded-lg">
              <span className="text-vista-light/70 text-xs mb-1">Completion</span>
              <span className="text-vista-light font-semibold">
                {Math.round(stats.completionRate)}%
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Main tabs */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'overview' | 'activity' | 'recommendations')}>
        <TabsList className="w-full justify-start mb-6">
          <TabsTrigger value="overview" className="flex items-center">
            <BarChart className="w-4 h-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="activity" className="flex items-center">
            <History className="w-4 h-4 mr-2" />
            Watch History
          </TabsTrigger>
          <TabsTrigger value="recommendations" className="flex items-center">
            <Award className="w-4 h-4 mr-2" />
            For You
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab Content */}
        <TabsContent value="overview">
          {stats && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Watch Time Stats */}
              <div className="bg-vista-dark-lighter rounded-lg p-5">
                <h3 className="text-lg font-medium text-vista-light mb-4 flex items-center">
                  <Timer className="w-5 h-5 mr-2 text-vista-blue" />
                  Watch Time
                </h3>

                <div className="space-y-4">
                  <div>
                    <p className="text-vista-light/70 text-sm mb-1">Total</p>
                    <p className="text-2xl font-semibold text-vista-light">{formatWatchTime(stats.totalWatchTime)}</p>
                  </div>

                  <div>
                    <p className="text-vista-light/70 text-sm mb-1">Weekly Activity</p>
                    <div className="flex items-end h-32 space-x-1 mt-2">
                      {Object.entries(stats.weeklyWatchTime).map(([day, time], index) => {
                        const maxTime = Math.max(...Object.values(stats.weeklyWatchTime));
                        const percentage = maxTime > 0 ? (time / maxTime) * 100 : 0;
                        return (
                          <div key={day} className="flex flex-col items-center flex-1">
                            <div className="w-full bg-vista-dark rounded-t-sm overflow-hidden">
                              <div
                                className={`bg-vista-blue w-full ${percentage > 0 ? 'min-h-[4px]' : ''}`}
                                style={{ height: `${percentage}%` }}
                              ></div>
                            </div>
                            <span className="text-xs text-vista-light/70 mt-1">{day}</span>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  <div>
                    <p className="text-vista-light/70 text-sm mb-2">Peak Viewing Time</p>
                    <div className="grid grid-cols-4 gap-1">
                      {['Morning', 'Afternoon', 'Evening', 'Night'].map((time, index) => {
                        const key = time.toLowerCase() as keyof typeof stats.watchTimePerTimeOfDay;
                        const percentage = stats.watchTimePerTimeOfDay[key];
                        return (
                          <div key={time} className="flex flex-col items-center text-center">
                            <span className="text-xs font-medium text-vista-light mb-1">
                              {Math.round(percentage)}%
                            </span>
                            <div
                              className={`w-full ${percentage > 25 ? 'bg-vista-blue' : 'bg-vista-blue/30'} h-1 mb-1 rounded-full`}
                            ></div>
                            <span className="text-[10px] text-vista-light/70">{time}</span>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>

              {/* Content Preferences */}
              <div className="bg-vista-dark-lighter rounded-lg p-5">
                <h3 className="text-lg font-medium text-vista-light mb-4 flex items-center">
                  <Tv className="w-5 h-5 mr-2 text-vista-blue" />
                  Content Preferences
                </h3>

                <div className="space-y-4">
                  <div>
                    <p className="text-vista-light/70 text-sm mb-2">Content Type</p>
                    <div className="flex h-6 bg-vista-dark rounded-full overflow-hidden">
                      <div
                        className="bg-vista-blue h-full flex items-center justify-start pl-2"
                        style={{ width: `${stats.contentTypeDistribution.show}%` }}
                      >
                        {stats.contentTypeDistribution.show > 15 && (
                          <span className="text-xs font-medium">Shows</span>
                        )}
                      </div>
                      <div
                        className="bg-vista-accent h-full flex items-center justify-end pr-2"
                        style={{ width: `${stats.contentTypeDistribution.movie}%` }}
                      >
                        {stats.contentTypeDistribution.movie > 15 && (
                          <span className="text-xs font-medium">Movies</span>
                        )}
                      </div>
                    </div>
                    <div className="flex justify-between mt-1">
                      <span className="text-xs text-vista-light/70">
                        {Math.round(stats.contentTypeDistribution.show)}% Shows
                      </span>
                      <span className="text-xs text-vista-light/70">
                        {Math.round(stats.contentTypeDistribution.movie)}% Movies
                      </span>
                    </div>
                  </div>

                  <div>
                    <p className="text-vista-light/70 text-sm mb-2">Favorite Genres</p>
                    <div className="space-y-2">
                      {stats.favoriteGenres.slice(0, 5).map((genre, index) => (
                        <div key={genre.genre} className="flex items-center justify-between">
                          <span className="text-sm text-vista-light">{genre.genre}</span>
                          <div className="flex items-center gap-2">
                            <div className="flex w-24 h-1.5 bg-vista-dark rounded-full overflow-hidden">
                              <div
                                className="bg-vista-blue h-full"
                                style={{ width: `${100 - (index * 20)}%` }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <p className="text-vista-light/70 text-sm mb-2">Completion Rate</p>
                    <div className="flex items-center gap-3">
                      <div className="flex-1 h-8 bg-vista-dark rounded-full overflow-hidden">
                        <div
                          className="bg-green-600 h-full flex items-center justify-end pr-2"
                          style={{ width: `${Math.round(stats.completionRate)}%` }}
                        >
                          {stats.completionRate > 15 && (
                            <span className="text-xs font-medium">Completed</span>
                          )}
                        </div>
                      </div>
                      <span className="text-vista-light font-medium">
                        {Math.round(stats.completionRate)}%
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Trending & Recommendations */}
              <div className="bg-vista-dark-lighter rounded-lg p-5">
                <h3 className="text-lg font-medium text-vista-light mb-4 flex items-center">
                  <TrendingUp className="w-5 h-5 mr-2 text-vista-blue" />
                  Top Watched
                </h3>

                <div className="space-y-4">
                  {stats.topWatched.slice(0, 4).map((item, index) => (
                    <Link
                      key={item.id}
                      href={`/watch/${item.contentId}`}
                      className="flex items-center gap-3 hover:bg-vista-dark/40 p-2 rounded-md transition-colors"
                    >
                      <div className="relative h-12 w-8 flex-shrink-0 rounded overflow-hidden">
                        <Image
                          src={item.image}
                          alt={item.title}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <div className="min-w-0 flex-1">
                        <p className="text-sm text-vista-light font-medium truncate">{item.title}</p>
                        <p className="text-xs text-vista-light/70">
                          Watched {formatWatchTime(item.watchTime)}
                        </p>
                      </div>
                      <span className="flex-shrink-0 text-xl font-bold text-vista-light/20">
                        #{index + 1}
                      </span>
                    </Link>
                  ))}

                  <Separator className="bg-vista-light/10" />

                  <div>
                    <h4 className="text-sm font-medium text-vista-light mb-2">Continue Watching</h4>
                    {stats.unfinishedContent.length > 0 ? (
                      <div className="space-y-2">
                        {stats.unfinishedContent.slice(0, 2).map((item) => (
                          <div key={item.id} className="relative">
                            <Link
                              href={`/watch/${item.contentId}`}
                              className="flex items-center gap-3 hover:bg-vista-dark/40 p-2 rounded-md transition-colors"
                            >
                              <div className="relative h-10 w-7 flex-shrink-0 rounded overflow-hidden">
                                <Image
                                  src={item.image}
                                  alt={item.title}
                                  fill
                                  className="object-cover"
                                />
                              </div>
                              <div className="min-w-0 flex-1">
                                <p className="text-xs text-vista-light font-medium truncate">{item.title}</p>
                                <div className="w-full bg-vista-dark h-1 rounded-full mt-1 overflow-hidden">
                                  <div
                                    className="bg-vista-blue h-full"
                                    style={{ width: `${item.progress}%` }}
                                  ></div>
                                </div>
                              </div>
                              <Button
                                size="icon"
                                variant="ghost"
                                className="h-7 w-7 rounded-full bg-vista-blue/10 text-vista-blue hover:bg-vista-blue/20"
                              >
                                <Play className="h-3.5 w-3.5 fill-current" />
                              </Button>
                            </Link>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-xs text-vista-light/70">
                        All caught up! No unfinished content.
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </TabsContent>

        {/* Activity Tab Content */}
        <TabsContent value="activity">
          <div className="space-y-6">
            {/* Filter controls */}
            <div className="flex justify-between items-center">
              <div className="space-x-2">
                <Button
                  variant={historyFilter === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setHistoryFilter('all')}
                  className={historyFilter === 'all' ? 'bg-vista-blue text-white' : 'border-vista-light/20 hover:bg-vista-light/10'}
                >
                  All
                </Button>
                <Button
                  variant={historyFilter === 'shows' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setHistoryFilter('shows')}
                  className={historyFilter === 'shows' ? 'bg-vista-blue text-white' : 'border-vista-light/20 hover:bg-vista-light/10'}
                >
                  <Tv className="w-3.5 h-3.5 mr-1.5" />
                  Shows
                </Button>
                <Button
                  variant={historyFilter === 'movies' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setHistoryFilter('movies')}
                  className={historyFilter === 'movies' ? 'bg-vista-blue text-white' : 'border-vista-light/20 hover:bg-vista-light/10'}
                >
                  <Film className="w-3.5 h-3.5 mr-1.5" />
                  Movies
                </Button>
              </div>

              <span className="text-sm text-vista-light/70">
                {filteredHistory.length} {filteredHistory.length === 1 ? 'item' : 'items'}
              </span>
            </div>

            {/* History timeline */}
            {groupedHistory.length > 0 ? (
              <div className="space-y-8">
                {groupedHistory.map((group) => (
                  <div key={group.date.toISOString()} className="space-y-3">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-vista-blue"></div>
                      <h3 className="text-md font-medium text-vista-light">
                        {group.date.toLocaleDateString('en-US', {
                          weekday: 'long',
                          month: 'long',
                          day: 'numeric',
                          year: 'numeric'
                        })}
                      </h3>
                      <div className="flex-1 h-px bg-vista-light/10"></div>
                    </div>

                    <div className="space-y-2 pl-4">
                      {group.items.map((item) => (
                        <div
                          key={item.id}
                          className="flex items-center gap-4 p-3 bg-vista-dark-lighter hover:bg-vista-dark-lighter/70 rounded-lg transition-colors"
                        >
                          <Link href={`/watch/${item.contentId}`} className="relative h-16 w-12 flex-shrink-0 rounded overflow-hidden">
                            <Image
                              src={item.image}
                              alt={item.title}
                              fill
                              className="object-cover"
                            />
                          </Link>

                          <div className="flex-1 min-w-0">
                            <div className="flex flex-wrap items-center gap-x-2 mb-1">
                              <Link
                                href={`/watch/${item.contentId}`}
                                className="text-vista-light font-medium hover:text-vista-blue transition-colors"
                              >
                                {item.title}
                              </Link>

                              <Badge
                                className={`${item.type === 'show' ? 'bg-vista-blue/10 text-vista-blue' : 'bg-vista-accent-dim/10 text-vista-accent-dim'}`}
                              >
                                {item.type === 'show' ? 'Show' : 'Movie'}
                              </Badge>

                              {item.completed && (
                                <Badge className="bg-green-500/10 text-green-500">
                                  Completed
                                </Badge>
                              )}
                            </div>

                            <div className="flex flex-wrap items-center text-sm text-vista-light/70 gap-x-4">
                              <span className="flex items-center">
                                <Clock className="w-3.5 h-3.5 mr-1" />
                                {formatWatchTime(item.watchTime)}
                              </span>

                              <span className="flex items-center">
                                <Eye className="w-3.5 h-3.5 mr-1" />
                                {item.progress}% watched
                              </span>

                              {!item.completed && item.progress > 0 && (
                                <div className="w-24 bg-vista-dark h-1 rounded-full mt-1 overflow-hidden">
                                  <div
                                    className="bg-vista-blue h-full"
                                    style={{ width: `${item.progress}%` }}
                                  ></div>
                                </div>
                              )}
                            </div>
                          </div>

                          <div className="flex-shrink-0">
                            <Link href={`/watch/${item.contentId}?forcePlay=true`}>
                              <Button
                                size="sm"
                                className={`rounded-full ${item.completed ? 'bg-vista-dark border-vista-light/20 text-vista-light hover:bg-vista-light/10' : 'bg-vista-blue hover:bg-vista-blue/90 text-white'}`}
                              >
                                {item.completed ? (
                                  <span className="flex items-center">
                                    <SkipForward className="w-3.5 h-3.5 mr-1.5" />
                                    Rewatch
                                  </span>
                                ) : (
                                  <span className="flex items-center">
                                    <Play className="w-3.5 h-3.5 mr-1.5" />
                                    Resume
                                  </span>
                                )}
                              </Button>
                            </Link>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Hourglass className="w-12 h-12 text-vista-light/30 mx-auto mb-4" />
                <h3 className="text-xl font-medium text-vista-light mb-2">No watch history</h3>
                <p className="text-vista-light/70 max-w-md mx-auto">
                  You haven't watched any {historyFilter !== 'all' ? (historyFilter === 'shows' ? 'shows' : 'movies') : 'content'} yet.
                  Start watching to build your history.
                </p>
              </div>
            )}
          </div>
        </TabsContent>

        {/* Recommendations Tab Content */}
        <TabsContent value="recommendations">
          <div className="space-y-6">
            {stats && (
              <>
                {/* Why these recommendations */}
                <div className="bg-vista-dark-lighter rounded-lg p-5 mb-6">
                  <h3 className="text-lg font-medium text-vista-light mb-3">Personalized For You</h3>
                  <p className="text-vista-light/70 text-sm mb-4">
                    Based on your viewing history, we think you'll enjoy these titles.
                  </p>

                  {stats.favoriteGenres.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      <span className="text-vista-light/70 text-sm">Your top genres:</span>
                      {stats.favoriteGenres.slice(0, 3).map((genre) => (
                        <Badge key={genre.genre} className="bg-vista-blue/10 text-vista-blue">{genre.genre}</Badge>
                      ))}
                    </div>
                  )}
                </div>

                {/* Recommendations grid */}
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
                  {recommendations.length > 0 ? (
                    recommendations.map((content) => (
                      <div key={content.id} className="flex flex-col">
                        <div className="relative aspect-[2/3] rounded-lg overflow-hidden group">
                          <Image
                            src={content.posterPath}
                            alt={content.title}
                            fill
                            className="object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity flex items-end">
                            <div className="w-full p-3 flex justify-between items-center">
                              <Link href={`/watch/${content.id}?forcePlay=true`}>
                                <Button
                                  size="sm"
                                  className="rounded-full bg-vista-blue hover:bg-vista-blue/90 text-white gap-1"
                                >
                                  <Play className="h-3.5 w-3.5" />
                                  Watch
                                </Button>
                              </Link>
                              <Button
                                size="icon"
                                variant="ghost"
                                className="h-8 w-8 rounded-full bg-black/40 text-white hover:bg-black/60"
                              >
                                <Bookmark className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                        <div className="mt-2">
                          <Link href={`/details/${content.type === 'show' ? 'shows' : 'movies'}/${content.id}`}>
                            <h4 className="text-sm font-medium text-vista-light hover:text-vista-blue transition-colors">
                              {content.title}
                            </h4>
                          </Link>
                          <div className="flex items-center text-xs text-vista-light/70 gap-2 mt-1">
                            <span>{content.year}</span>
                            <span className="w-1 h-1 rounded-full bg-vista-light/40"></span>
                            <span>
                              {content.genres?.slice(0, 2).join(', ')}
                              {content.genres && content.genres.length > 2 && '...'}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="col-span-full text-center py-12">
                      <AlertCircle className="w-12 h-12 text-vista-light/30 mx-auto mb-4" />
                      <h3 className="text-xl font-medium text-vista-light mb-2">Not enough data</h3>
                      <p className="text-vista-light/70 max-w-md mx-auto">
                        Watch more content to get personalized recommendations based on your taste.
                      </p>
                    </div>
                  )}
                </div>
              </>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

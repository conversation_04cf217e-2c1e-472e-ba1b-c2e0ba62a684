// Import translation files
import en from './locales/en';
import es from './locales/es';
import fr from './locales/fr';
import de from './locales/de';
import ja from './locales/ja';

// Type definitions for the translations
export type Translation = typeof en;
export type TranslationKey = keyof Translation;
export type Language = 'en' | 'es' | 'fr' | 'de' | 'ja';

// Available languages with their display names
export const languages = {
  en: { name: 'English', nativeName: 'English' },
  es: { name: 'Spanish', nativeName: 'Español' },
  fr: { name: 'French', nativeName: 'Français' },
  de: { name: 'German', nativeName: 'Deutsch' },
  ja: { name: 'Japanese', nativeName: '日本語' }
};

// Translations mapping
const translations: Record<Language, Translation> = {
  en,
  es,
  fr,
  de,
  ja
};

// Default language
export const DEFAULT_LANGUAGE: Language = 'en';

// Get the user's language preference from localStorage or browser settings
export function getUserLanguage(): Language {
  if (typeof window === 'undefined') {
    return DEFAULT_LANGUAGE;
  }

  const storedLang = localStorage.getItem('vista-language') as Language | null;
  if (storedLang && translations[storedLang]) {
    return storedLang;
  }

  // Try to get from browser settings
  const browserLang = navigator.language.split('-')[0] as Language;
  if (browserLang && translations[browserLang]) {
    return browserLang;
  }

  return DEFAULT_LANGUAGE;
}

// Set the user's language preference
export function setUserLanguage(lang: Language): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem('vista-language', lang);
  }
}

// Translate a key
export function translate(key: TranslationKey, lang: Language = DEFAULT_LANGUAGE): string {
  const translation = translations[lang][key];
  if (!translation) {
    console.warn(`Translation missing for key: ${key} in language: ${lang}`);
    // Fall back to English if translation is missing
    return translations[DEFAULT_LANGUAGE][key] || key;
  }
  return translation;
}

// Helper function to format a translated string with variables
export function formatTranslation(
  key: TranslationKey,
  lang: Language = DEFAULT_LANGUAGE,
  variables: Record<string, string | number> = {}
): string {
  let translation = translate(key, lang);

  Object.entries(variables).forEach(([varName, value]) => {
    const regex = new RegExp(`{${varName}}`, 'g');
    translation = translation.replace(regex, String(value));
  });

  return translation;
}

// Export translations for direct access
export { translations };

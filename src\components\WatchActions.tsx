'use client';

import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { useWatchlist } from '@/contexts/WatchlistContext';
import { toast } from 'sonner';
import { Check, Plus, Share2, Users } from 'lucide-react';

interface ContentData {
  id: string;
  title: string;
  type: 'movie' | 'show';
  poster?: string;
  imagePath?: string;
  year?: string;
  releaseYear?: number;
}

interface WatchActionsProps {
  content: ContentData | null;
}

export default function WatchActions({ content }: WatchActionsProps) {
  const router = useRouter();
  const { addToWatchlist, removeFromWatchlist, isInWatchlist } = useWatchlist();

  if (!content || !content.id) {
    return null; // Don't render actions if content or content.id is not available
  }

  const isContentInWatchlist = isInWatchlist(content.id);

  const handleToggleWatchlist = () => {
    if (isContentInWatchlist) {
      removeFromWatchlist(content.id);
      toast.success(`Removed from My List`);
    } else {
      addToWatchlist({
        id: content.id,
        title: content.title,
        imagePath: content.poster || content.imagePath || '',
        type: content.type === 'show' ? 'shows' : 'movies',
        year: content.year || content.releaseYear?.toString() || '',
      });
      toast.success('Added to My List');
    }
  };

  const handleShare = () => {
    navigator.clipboard.writeText(window.location.href);
    toast.success('Link copied to clipboard!');
  };

  return (
    <div className="flex flex-wrap gap-2">
      <Button onClick={handleToggleWatchlist} variant={isContentInWatchlist ? 'default' : 'outline'}>
        {isContentInWatchlist ? <Check className="h-4 w-4 mr-2" /> : <Plus className="h-4 w-4 mr-2" />}
        My List
      </Button>
      <Button onClick={handleShare} variant="outline">
        <Share2 className="h-4 w-4 mr-2" /> Share
      </Button>
      <Button
        onClick={() => router.push(`/watch-party/create?contentId=${content.id}&title=${encodeURIComponent(content.title)}`)}
        className="bg-vista-accent hover:bg-vista-accent/90 text-white"
      >
        <Users className="h-4 w-4 mr-2" /> Create Watch Party
      </Button>
    </div>
  );
}

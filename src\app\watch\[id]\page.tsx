import { Suspense } from 'react';
import { notFound } from 'next/navigation';

interface PageProps {
  params: Promise<{ id: string }>;
}

export default async function WatchPage({ params }: PageProps) {
  // In Next.js 15, we need to await the params to prevent sync dynamic APIs error
  const { id } = await params;
  
  // Determine if this is a watch party (contentId starts with 'party-')
  const isWatchParty = id.startsWith('party-');
  
  // Render the appropriate component based on the context
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen bg-vista-dark">
        <div className="text-vista-light text-lg">Loading content...</div>
      </div>
    }>
      {isWatchParty ? (
        <WatchContentClient contentId={id.replace('party-', '')} />
      ) : (
        <DirectWatchClient contentId={id} />
      )}
    </Suspense>
  );
}

// Import the client components
import Watch<PERSON>ontentClient from './WatchContentClient';
import DirectWatchClient from './DirectWatchClient'; 
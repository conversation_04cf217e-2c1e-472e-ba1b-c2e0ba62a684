'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSystemHealth } from '@/hooks/useAdminData';
import {
  Server,
  Database,
  HardDrive,
  Cpu,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Clock,
  Activity,
  BarChart,
  Download,
  Upload,
  FileText,
  Info
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import SystemLogs from '@/components/admin/SystemLogs';

export default function SystemPage() {
  // Fetch system health data
  const {
    data: healthData,
    isLoading,
    error,
    refetch: refreshSystemHealth
  } = useSystemHealth();

  // Create a safe version of the health data with defaults
  const systemHealth = healthData || {
    status: 'unknown',
    timestamp: new Date().toISOString(),
    database: {
      status: 'unknown',
      details: {}
    },
    system: {
      platform: 'unknown',
      arch: 'unknown',
      cpus: 0,
      memoryUsage: {
        total: 0,
        free: 0,
        used: 0,
        usedPercentage: 0
      },
      uptime: 0,
      uptimeFormatted: '0d 0h 0m',
      cpuUsage: 0
    },
    process: {
      pid: 0,
      memoryUsage: {
        rss: 0,
        heapTotal: 0,
        heapUsed: 0,
        external: 0
      },
      uptime: 0,
      uptimeFormatted: '0d 0h 0m',
      version: ''
    }
  };

  // Alerts based on system health
  const [alerts, setAlerts] = useState<Array<{
    id: number;
    type: 'info' | 'warning' | 'error';
    message: string;
    timestamp: string;
  }>>([]);

  // Update alerts based on system health
  useEffect(() => {
    if (!healthData) return;

    const newAlerts: Array<{
      id: number;
      type: 'info' | 'warning' | 'error';
      message: string;
      timestamp: string;
    }> = [];
    const now = new Date().toISOString();

    // Check CPU usage
    if (healthData.system.cpuUsage > 80) {
      newAlerts.push({
        id: Date.now(),
        type: 'warning',
        message: `High CPU usage detected: ${healthData.system.cpuUsage}%`,
        timestamp: now
      });
    }

    // Check memory usage
    if (healthData.system.memoryUsage.usedPercentage > 80) {
      newAlerts.push({
        id: Date.now() + 1,
        type: 'warning',
        message: `High memory usage detected: ${healthData.system.memoryUsage.usedPercentage}%`,
        timestamp: now
      });
    }

    // Check database status
    if (healthData.database.status !== 'healthy') {
      newAlerts.push({
        id: Date.now() + 2,
        type: 'error',
        message: `Database issue detected: ${healthData.database.status}`,
        timestamp: now
      });
    }

    setAlerts(newAlerts);
  }, [healthData]);

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // Determine status color
  const getStatusColor = (value: number) => {
    if (value < 50) return 'text-green-500';
    if (value < 80) return 'text-yellow-500';
    return 'text-red-500';
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-vista-light">System Health</h1>
          <p className="text-vista-light/70">
            Monitor system performance and health metrics
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <Clock className="mr-2 h-4 w-4" />
            Last checked: {formatDate(systemHealth.timestamp)}
          </Button>
          <Button onClick={() => refreshSystemHealth()} disabled={isLoading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* System Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-vista-light text-lg">CPU Usage</CardTitle>
            <CardDescription>Current processor load</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Cpu className="h-5 w-5 text-vista-blue" />
                <span className={`text-2xl font-bold ${getStatusColor(systemHealth.system.cpuUsage)}`}>
                  {systemHealth.system.cpuUsage}%
                </span>
              </div>
              <Progress value={systemHealth.system.cpuUsage} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-vista-light text-lg">Memory Usage</CardTitle>
            <CardDescription>RAM utilization</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <HardDrive className="h-5 w-5 text-vista-blue" />
                <span className={`text-2xl font-bold ${getStatusColor(systemHealth.system.memoryUsage.usedPercentage)}`}>
                  {systemHealth.system.memoryUsage.usedPercentage}%
                </span>
              </div>
              <Progress value={systemHealth.system.memoryUsage.usedPercentage} className="h-2" />
              <p className="text-xs text-vista-light/70 text-right">
                {(systemHealth.system.memoryUsage.used).toFixed(2)} GB / {(systemHealth.system.memoryUsage.total).toFixed(2)} GB
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-vista-light text-lg">Disk Usage</CardTitle>
            <CardDescription>Storage utilization</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Database className="h-5 w-5 text-vista-blue" />
                <span className={`text-2xl font-bold ${getStatusColor(systemHealth.system.memoryUsage.usedPercentage)}`}>
                  {systemHealth.system.memoryUsage.usedPercentage}%
                </span>
              </div>
              <Progress value={systemHealth.system.memoryUsage.usedPercentage} className="h-2" />
              <p className="text-xs text-vista-light/70 text-right">
                Disk usage data not available
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-vista-light text-lg">System Status</CardTitle>
            <CardDescription>Overall health</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <Server className="h-5 w-5 text-vista-blue" />
              <div className="text-right">
                <Badge
                  variant={systemHealth.status === 'healthy' ? 'success' : 'destructive'}
                  className="mb-1"
                >
                  {systemHealth.status === 'healthy' ? 'Healthy' : 'Issues Detected'}
                </Badge>
                <p className="text-vista-light/70 text-sm">
                  Uptime: {systemHealth.system.uptimeFormatted}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs for different system views */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="database">Database</TabsTrigger>
          <TabsTrigger value="api">API Performance</TabsTrigger>
          <TabsTrigger value="logs">System Logs</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* System Alerts */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-vista-light">System Alerts</h2>

            {alerts.length > 0 ? alerts.map(alert => (
              <Alert
                key={alert.id}
                variant={alert.type === 'warning' ? 'destructive' : 'default'}
              >
                {alert.type === 'warning' ? (
                  <AlertTriangle className="h-4 w-4" />
                ) : (
                  <CheckCircle className="h-4 w-4" />
                )}
                <AlertTitle>
                  {alert.type === 'warning' ? 'Warning' : 'Information'}
                </AlertTitle>
                <AlertDescription className="flex justify-between items-center">
                  <span>{alert.message}</span>
                  <span className="text-sm text-vista-light/70">
                    {formatDate(alert.timestamp)}
                  </span>
                </AlertDescription>
              </Alert>
            )) : (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>No Alerts</AlertTitle>
                <AlertDescription>
                  No system alerts at this time. System is running normally.
                </AlertDescription>
              </Alert>
            )}
          </div>

          {/* Network Traffic */}
          <Card>
            <CardHeader>
              <CardTitle className="text-vista-light">Network Traffic</CardTitle>
              <CardDescription>Bandwidth usage over time</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[200px] flex items-end space-x-2">
                {Array.from({ length: 24 }).map((_, i) => {
                  const uploadHeight = Math.floor(Math.random() * 80) + 10;
                  const downloadHeight = Math.floor(Math.random() * 80) + 20;
                  return (
                    <div key={i} className="flex-1 flex flex-col items-center">
                      <div className="w-full flex flex-col items-center">
                        <div
                          className="w-full bg-green-500/70 rounded-t-sm"
                          style={{ height: `${downloadHeight}%` }}
                          title={`Download: ${downloadHeight} Mbps`}
                        ></div>
                        <div
                          className="w-full bg-blue-500/70 rounded-t-sm mt-px"
                          style={{ height: `${uploadHeight}%` }}
                          title={`Upload: ${uploadHeight} Mbps`}
                        ></div>
                      </div>
                      {i % 4 === 0 && (
                        <span className="text-vista-light/70 text-xs mt-2">
                          {i}h
                        </span>
                      )}
                    </div>
                  );
                })}
              </div>
              <div className="flex justify-center mt-4 space-x-4">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-500/70 rounded-sm mr-2"></div>
                  <span className="text-vista-light/70 text-sm">Download</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-blue-500/70 rounded-sm mr-2"></div>
                  <span className="text-vista-light/70 text-sm">Upload</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="database">
          <Card>
            <CardHeader>
              <CardTitle className="text-vista-light">Database Performance</CardTitle>
              <CardDescription>MongoDB connection and query metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium text-vista-light mb-4">Connection Status</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-vista-light/70">Status</span>
                      <Badge
                        variant={systemHealth.database.status === 'healthy' ? 'success' : 'destructive'}
                      >
                        {systemHealth.database.status}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-vista-light/70">Response Time</span>
                      <span className="text-vista-light">
                        {(() => {
                          if (systemHealth.database.details &&
                              typeof systemHealth.database.details === 'object' &&
                              'native' in systemHealth.database.details &&
                              systemHealth.database.details.native &&
                              typeof systemHealth.database.details.native === 'object' &&
                              'pingTime' in systemHealth.database.details.native) {
                            return String(systemHealth.database.details.native.pingTime);
                          }
                          return 'N/A';
                        })()}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-vista-light/70">Active Connections</span>
                      <span className="text-vista-light">
                        {(() => {
                          if (systemHealth.database.details &&
                              typeof systemHealth.database.details === 'object' &&
                              'connectionStats' in systemHealth.database.details &&
                              systemHealth.database.details.connectionStats &&
                              typeof systemHealth.database.details.connectionStats === 'object' &&
                              'failures' in systemHealth.database.details.connectionStats) {
                            return String(systemHealth.database.details.connectionStats.failures);
                          }
                          return '0';
                        })()}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-vista-light/70">Total Queries</span>
                      <span className="text-vista-light">N/A</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium text-vista-light mb-4">Query Performance</h3>
                  <div className="h-[200px] flex items-end space-x-2">
                    {Array.from({ length: 12 }).map((_, i) => {
                      const height = Math.floor(Math.random() * 80) + 20;
                      return (
                        <div key={i} className="flex-1 flex flex-col items-center">
                          <div
                            className="w-full bg-vista-blue rounded-t-sm"
                            style={{ height: `${height}%` }}
                          ></div>
                          {i % 2 === 0 && (
                            <span className="text-vista-light/70 text-xs mt-2">
                              {i * 5}m
                            </span>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="border-t border-vista-light/10 pt-4">
              <Button variant="outline" className="ml-auto">
                <Activity className="mr-2 h-4 w-4" />
                View Detailed Metrics
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="api">
          <Card>
            <CardHeader>
              <CardTitle className="text-vista-light">API Performance</CardTitle>
              <CardDescription>Request volume and response times</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-vista-light">API performance metrics will be displayed here.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          <SystemLogs limit={25} autoRefresh={false} refreshInterval={60000} height="450px" />
        </TabsContent>
      </Tabs>
    </div>
  );
}

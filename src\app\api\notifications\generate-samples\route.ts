import { NextRequest, NextResponse } from 'next/server';
import dbConnect, { ensureMongooseConnection } from '@/lib/mongodb';
import User from '@/models/User';
import Notification from '@/models/Notification';

// Sample content for generating notifications
const sampleContent = [
  {
    title: 'The Last Frontier',
    contentId: '12345',
    contentType: 'movie',
    image: '/images/content/movie-1.jpg',
    message: 'The epic sci-fi adventure "The Last Frontier" is now available to stream. Join the journey to the edge of the universe!'
  },
  {
    title: 'Midnight Chronicles',
    contentId: '23456',
    contentType: 'show',
    image: '/images/content/show-1.jpg',
    message: 'Season 2 of "Midnight Chronicles" has just been released. Dive back into the supernatural mystery!'
  },
  {
    title: 'Ocean\'s Depths',
    contentId: '34567',
    contentType: 'movie',
    image: '/images/content/movie-2.jpg',
    message: 'Explore the unknown in the new documentary "Ocean\'s Depths". Discover creatures never seen before!'
  },
  {
    title: 'City Lights',
    contentId: '45678',
    contentType: 'show',
    image: '/images/content/show-2.jpg',
    message: 'The final season of the award-winning drama "City Lights" is now streaming exclusively on StreamVista.'
  }
];

// Sample recommendations
const sampleRecommendations = [
  {
    title: 'Based on your watchlist: Stellar Journey',
    contentId: '56789',
    contentType: 'movie',
    image: '/images/content/movie-3.jpg',
    message: 'We think you\'ll enjoy "Stellar Journey" based on your interest in sci-fi adventures.'
  },
  {
    title: 'Popular in your area: Urban Tales',
    contentId: '67890',
    contentType: 'show',
    image: '/images/content/show-3.jpg',
    message: 'Many viewers in your region are enjoying "Urban Tales". Check it out now!'
  }
];

// Sample system notifications
const sampleSystemNotifications = [
  {
    title: 'StreamVista App Update',
    message: 'We\'ve updated our mobile app with new features and improvements. Download the latest version now!'
  },
  {
    title: 'Account Security Reminder',
    message: 'Keep your account secure by regularly updating your password and enabling two-factor authentication.'
  }
];

// This endpoint will generate sample notifications for testing
export async function POST(request: NextRequest) {
  try {
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    let userId = searchParams.get('userId');
    const clearExisting = searchParams.get('clear') === 'true';

    // If userId not in query params, try to get from body
    if (!userId) {
      try {
        const body = await request.json();
        userId = body.userId;
      } catch (e) {
        // No body or invalid JSON
      }
    }

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Connect to the database
    await ensureMongooseConnection();

    // Find the user by ID
    const user = await User.findById(userId);

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    if (clearExisting) {
      await Notification.deleteMany({ userId: user._id });
    }

    // Create sample notifications
    const notifications = [];

    // New content notifications
    for (const content of sampleContent) {
      const notification = await Notification.create({
        userId: user._id,
        type: 'new_content',
        title: `New ${content.contentType}: ${content.title}`,
        message: content.message,
        contentId: content.contentId,
        contentType: content.contentType,
        image: content.image,
        read: Math.random() > 0.7 // 30% chance of being unread
      });

      notifications.push(notification);
    }

    // Recommendation notifications
    for (const recommendation of sampleRecommendations) {
      const notification = await Notification.create({
        userId: user._id,
        type: 'recommendation',
        title: recommendation.title,
        message: recommendation.message,
        contentId: recommendation.contentId,
        contentType: recommendation.contentType,
        image: recommendation.image,
        read: Math.random() > 0.5 // 50% chance of being unread
      });

      notifications.push(notification);
    }

    // System notifications
    for (const systemNotification of sampleSystemNotifications) {
      const notification = await Notification.create({
        userId: user._id,
        type: 'system',
        title: systemNotification.title,
        message: systemNotification.message,
        read: Math.random() > 0.3 // 70% chance of being unread
      });

      notifications.push(notification);
    }

    return NextResponse.json({
      message: 'Sample notifications created successfully',
      notificationsCreated: notifications.length
    });
  } catch (error) {
    console.error('Error creating sample notifications:', error);
    return NextResponse.json(
      { error: 'Failed to create sample notifications' },
      { status: 500 }
    );
  }
}

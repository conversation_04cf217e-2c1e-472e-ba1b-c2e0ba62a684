'use client';

import { useState, useEffect } from 'react';
import ContentCard from '@/components/ContentCard';
import { formatTMDbContentForCards } from '@/lib/content-utils';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2 } from 'lucide-react';

interface DetailRelatedContentProps {
  contentId: string;
  type: 'shows' | 'movies';
  similar?: any[];
  recommendations?: any[];
}

export default function DetailRelatedContent({
  contentId,
  type,
  similar = [],
  recommendations = []
}: DetailRelatedContentProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'similar' | 'recommended'>(
    similar.length > 0 ? 'similar' : 'recommended'
  );
  const [formattedSimilar, setFormattedSimilar] = useState<any[]>([]);
  const [formattedRecommended, setFormattedRecommended] = useState<any[]>([]);

  // Format the content for display
  useEffect(() => {
    setIsLoading(true);

    // Format similar content
    if (!similar) {
      console.log('No similar content provided');
      setFormattedSimilar([]);
    } else if (!Array.isArray(similar)) {
      console.error('Similar content is not an array:', similar);
      setFormattedSimilar([]);
    } else if (similar.length === 0) {
      console.log('Similar content array is empty');
      setFormattedSimilar([]);
    } else {
      try {
        // Process and validate each item
        const processedSimilar = similar.map((item, index) => {
          try {
            if (!item) {
              console.warn(`Similar item at index ${index} is null or undefined`);
              return null;
            }

            // Create a new object to avoid mutating the original
            const processedItem = { ...item };

            // Ensure we have a valid poster_path
            if (processedItem.poster_path) {
              // If it's already a full URL, keep it as is
              if (typeof processedItem.poster_path === 'string' && processedItem.poster_path.startsWith('http')) {
                // No change needed
              }
              // If it's a TMDb path, use the TMDb image URL directly
              else if (typeof processedItem.poster_path === 'string' && processedItem.poster_path.startsWith('/')) {
                processedItem.posterUrl = `https://image.tmdb.org/t/p/w342${processedItem.poster_path}`;
              }
            }

            // Set the correct media type and ensure we have valid data
            // Ensure we have a valid title
            const title = type === 'shows'
              ? (processedItem.name || processedItem.title || 'Unknown Title')
              : (processedItem.title || processedItem.name || 'Unknown Title');

            return {
              ...processedItem,
              media_type: type === 'shows' ? 'tv' : 'movie',
              title: title
            };
          } catch (itemError) {
            console.error(`Error processing similar item at index ${index}:`, itemError);
            return null;
          }
        }).filter(Boolean); // Remove any null items

        try {
          console.log(`Processing ${processedSimilar.length} similar ${type} items`);
          const formatted = formatTMDbContentForCards(processedSimilar);
          setFormattedSimilar(Array.isArray(formatted) ? formatted : []);
        } catch (formatError) {
          console.error('Error formatting similar content cards:', formatError);
          setFormattedSimilar([]);
        }
      } catch (error) {
        console.error('Error processing similar content:', error);
        setFormattedSimilar([]);
      }
    }

    // Format recommended content
    if (!recommendations) {
      console.log('No recommended content provided');
      setFormattedRecommended([]);
    } else if (!Array.isArray(recommendations)) {
      console.error('Recommended content is not an array:', recommendations);
      setFormattedRecommended([]);
    } else if (recommendations.length === 0) {
      console.log('Recommended content array is empty');
      setFormattedRecommended([]);
    } else {
      try {
        // Process and validate each item
        const processedRecommendations = recommendations.map((item, index) => {
          try {
            if (!item) {
              console.warn(`Recommended item at index ${index} is null or undefined`);
              return null;
            }

            // Create a new object to avoid mutating the original
            const processedItem = { ...item };

            // Ensure we have a valid poster_path
            if (processedItem.poster_path) {
              // If it's already a full URL, keep it as is
              if (typeof processedItem.poster_path === 'string' && processedItem.poster_path.startsWith('http')) {
                // No change needed
              }
              // If it's a TMDb path, use the TMDb image URL directly
              else if (typeof processedItem.poster_path === 'string' && processedItem.poster_path.startsWith('/')) {
                processedItem.posterUrl = `https://image.tmdb.org/t/p/w342${processedItem.poster_path}`;
              }
            }

            // Set the correct media type and ensure we have valid data
            // Ensure we have a valid title
            const title = type === 'shows'
              ? (processedItem.name || processedItem.title || 'Unknown Title')
              : (processedItem.title || processedItem.name || 'Unknown Title');

            return {
              ...processedItem,
              media_type: type === 'shows' ? 'tv' : 'movie',
              title: title
            };
          } catch (itemError) {
            console.error(`Error processing recommended item at index ${index}:`, itemError);
            return null;
          }
        }).filter(Boolean); // Remove any null items

        try {
          console.log(`Processing ${processedRecommendations.length} recommended ${type} items`);
          const formatted = formatTMDbContentForCards(processedRecommendations);
          setFormattedRecommended(Array.isArray(formatted) ? formatted : []);
        } catch (formatError) {
          console.error('Error formatting recommended content cards:', formatError);
          setFormattedRecommended([]);
        }
      } catch (error) {
        console.error('Error processing recommended content:', error);
        setFormattedRecommended([]);
      }
    }

    setIsLoading(false);
  }, [similar, recommendations, type]);

  // If no related content is available
  if (
    (!similar || similar.length === 0) &&
    (!recommendations || recommendations.length === 0)
  ) {
    return null;
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="w-8 h-8 text-vista-blue animate-spin" />
      </div>
    );
  }

  return (
    <div className="overflow-x-hidden">
      <Tabs
        defaultValue={activeTab}
        value={activeTab}
        onValueChange={(value) => setActiveTab(value as 'similar' | 'recommended')}
        className="mb-6"
      >
        <TabsList className="bg-vista-dark-lighter w-full mb-4">
          {similar && similar.length > 0 && (
            <TabsTrigger value="similar" className="text-xs sm:text-sm flex-1">Similar {type === 'shows' ? 'Shows' : 'Movies'}</TabsTrigger>
          )}
          {recommendations && recommendations.length > 0 && (
            <TabsTrigger value="recommended" className="text-xs sm:text-sm flex-1">Recommended</TabsTrigger>
          )}
        </TabsList>

        {similar && similar.length > 0 && (
          <TabsContent value="similar" className="pt-4">
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3 sm:gap-4 mx-auto">
              {formattedSimilar.slice(0, 12).map((item, index) => (
                <ContentCard
                  key={`similar-${item.id}`}
                  {...item}
                  index={index}
                />
              ))}
            </div>
          </TabsContent>
        )}

        {recommendations && recommendations.length > 0 && (
          <TabsContent value="recommended" className="pt-4">
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3 sm:gap-4 mx-auto">
              {formattedRecommended.slice(0, 12).map((item, index) => (
                <ContentCard
                  key={`recommended-${item.id}`}
                  {...item}
                  index={index}
                />
              ))}
            </div>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}

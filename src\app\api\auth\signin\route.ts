import { NextRequest, NextResponse } from 'next/server';
import User from '@/models/User';
import { UserSession } from '@/lib/types';
import mongoose from 'mongoose';
import { withMongo } from '@/middleware/mongoMiddleware';
import bcrypt from 'bcryptjs';

// Define a type for the user document with password
interface UserWithPassword {
  _id: mongoose.Types.ObjectId;
  email: string;
  name: string;
  password: string;
  googleId?: string;
  picture?: string;
  role?: 'user' | 'admin' | 'superadmin';
  createdAt: Date;
  comparePassword?: (candidatePassword: string) => Promise<boolean>;
}

// Define the handler function
async function handler(request: NextRequest) {
  console.log('Starting signin process');

  try {
    // Extract user data from request
    let userData;
    try {
      userData = await request.json();
      console.log('Request parsed successfully');
    } catch (parseError) {
      console.error('Error parsing request body:', parseError);
      return NextResponse.json(
        { error: 'Invalid request data' },
        { status: 400 }
      );
    }

    const { email, password } = userData;

    // Basic validation
    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Find the user by email (without logging the email)
    console.log('Finding user by email...');
    const userDoc = await User.findOne({ email }).select('+password');

    // Check if user exists
    if (!userDoc) {
      console.log('User not found');
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      );
    }

    // Cast the user document to our type
    const user = userDoc.toObject() as UserWithPassword;

    console.log('User found');

    // Compare the password
    console.log('Comparing password...');

    // Check if comparePassword method exists
    let isPasswordValid = false;

    if (typeof userDoc.comparePassword !== 'function') {
      console.error('comparePassword method not found on user model');

      // Try to compare password manually as a fallback
      try {
        // Ensure password is defined before comparing
        if (!user.password) {
          console.error('Password is undefined for user');
          return NextResponse.json(
            { error: 'Authentication system error' },
            { status: 500 }
          );
        }

        // Use Promise.resolve to ensure we get a Promise<boolean>
        isPasswordValid = await Promise.resolve(bcrypt.compare(password, user.password));
        console.log('Manual password validation completed');
      } catch (bcryptError) {
        console.error('Error comparing passwords manually:', bcryptError);
        return NextResponse.json(
          { error: 'Authentication system error' },
          { status: 500 }
        );
      }
    } else {
      // Use the model's comparePassword method
      isPasswordValid = await Promise.resolve(userDoc.comparePassword(password));
      console.log('Password validation using model method completed');
    }

    if (!isPasswordValid) {
      console.log('Invalid password attempt');
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      );
    }

    // Create a user session object without sensitive data
    // Validate the role value to ensure it matches the expected union type
    let userRole: 'user' | 'admin' | 'superadmin' = 'user'; // Default to 'user'
    if (user.role === 'admin' || user.role === 'superadmin') {
      userRole = user.role;
    }

    const userSession: UserSession = {
      id: user._id.toString(),
      googleId: user.googleId, // Include googleId if present
      email: user.email,
      name: user.name,
      picture: user.picture,
      role: userRole,
      createdAt: user.createdAt.toISOString(),
    };

    console.log('Signin successful, returning user session');

    // Get host information for debugging
    const host = request.headers.get('host');
    console.log('Host:', host);
    console.log('Environment:', process.env.NODE_ENV);
    console.log('Is Netlify:', process.env.NETLIFY === 'true');

    // Create response with user data
    const response = NextResponse.json({
      success: true,
      user: userSession
    });

    // Set cookies for authentication with production-ready settings
    response.cookies.set('userId', userSession.id, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax', // 'lax' is more compatible across browsers and environments
      maxAge: 7 * 24 * 60 * 60, // 7 days
      path: '/'
    });

    // Update last login time and log user activity
    try {
      // Update last login timestamp using direct database access
      if (!mongoose.connection.db) {
        throw new Error('Database connection not established');
      }
      const db = mongoose.connection.db;
      const usersCollection = db.collection('users');

      // Convert string ID to ObjectId
      const objectId = new mongoose.Types.ObjectId(userSession.id);

      // Update the lastLogin field directly with the current timestamp
      const now = new Date();
      console.log('Updating lastLogin timestamp');

      // Try with ObjectId first
      let result = await usersCollection.updateOne(
        { _id: objectId },
        { $set: { lastLogin: now } }
      );

      // If no match with ObjectId, try alternative approaches
      if (result.matchedCount === 0) {
        console.log('No match with ObjectId, trying alternative approaches');

        // Try to find the user by other fields
        const user = await usersCollection.findOne({
          $or: [
            { userId: userSession.id },
            { externalId: userSession.id },
            { email: userSession.email } // Try by email since we have it
          ]
        });

        if (user) {
          // If we found the user, update using their actual _id
          result = await usersCollection.updateOne(
            { _id: user._id },
            { $set: { lastLogin: now } }
          );
          console.log(`Found user via alternative fields, updated lastLogin`);
        } else {
          console.log(`Could not find user with ID ${userSession.id} via alternative fields`);
          // Create an empty result object with the required properties
          result = {
            matchedCount: 0,
            modifiedCount: 0,
            acknowledged: true,
            upsertedCount: 0,
            upsertedId: null
          };
        }
      }

      if (result.matchedCount === 1) {
        console.log('Successfully updated lastLogin timestamp');
      } else {
        console.warn('Failed to update lastLogin timestamp');
      }

      // Log user activity
      const { logUserActivity } = await import('@/lib/activity-logger');
      await logUserActivity(
        userSession.id,
        'auth',
        'login',
        'User logged in successfully',
        request,
        { method: 'credentials' }
      );
    } catch (logError) {
      console.error('Error updating last login or logging user activity:', logError);
      // Continue even if logging fails
    }

    return response;

  } catch (error) {
    console.error('Signin error details:', error);

    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }

    // Handle specific MongoDB errors
    if (error instanceof mongoose.Error) {
      console.error('MongoDB error type:', error.constructor.name);
      return NextResponse.json(
        { error: `Database error during signin: ${error.message}` },
        { status: 500 }
      );
    }

    // Generic error response
    return NextResponse.json(
      { error: 'An unexpected error occurred during signin' },
      { status: 500 }
    );
  }
}

// Export the handler with MongoDB connection middleware
export const POST = withMongo(handler);
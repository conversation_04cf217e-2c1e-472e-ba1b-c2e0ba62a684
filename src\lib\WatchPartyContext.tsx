'use client';

import { createContext, useContext, useReducer, ReactNode, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

// Types for watch party functionality
interface WatchPartyMember {
  id: string;
  name: string;
  avatar?: string;
  isHost: boolean;
  isOnline: boolean;
  lastSeen: Date;
}

interface WatchPartyMessage {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  message: string;
  timestamp: Date;
  type: 'chat' | 'system' | 'reaction';
}

interface WatchPartyState {
  id: string | null;
  name: string;
  contentId: string | null;
  contentType: 'movie' | 'tv' | null;
  season?: number;
  episode?: number;
  isActive: boolean;
  isHost: boolean;
  members: WatchPartyMember[];
  messages: WatchPartyMessage[];
  currentTime: number;
  isPlaying: boolean;
  isPaused: boolean;
  isBuffering: boolean;
  error: string | null;
  settings: {
    allowChat: boolean;
    allowSkip: boolean;
    allowSeek: boolean;
    maxMembers: number;
  };
}

type WatchPartyAction =
  | { type: 'SET_PARTY'; payload: Partial<WatchPartyState> }
  | { type: 'JOIN_PARTY'; payload: { partyId: string; member: WatchPartyMember } }
  | { type: 'LEAVE_PARTY' }
  | { type: 'ADD_MESSAGE'; payload: WatchPartyMessage }
  | { type: 'UPDATE_MEMBERS'; payload: WatchPartyMember[] }
  | { type: 'UPDATE_PLAYBACK'; payload: { currentTime: number; isPlaying: boolean; isPaused: boolean; isBuffering: boolean } }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'RESET' };

const initialState: WatchPartyState = {
  id: null,
  name: '',
  contentId: null,
  contentType: null,
  isActive: false,
  isHost: false,
  members: [],
  messages: [],
  currentTime: 0,
  isPlaying: false,
  isPaused: false,
  isBuffering: false,
  error: null,
  settings: {
    allowChat: true,
    allowSkip: true,
    allowSeek: true,
    maxMembers: 10,
  },
};

function watchPartyReducer(state: WatchPartyState, action: WatchPartyAction): WatchPartyState {
  switch (action.type) {
    case 'SET_PARTY':
      return { ...state, ...action.payload };
    case 'JOIN_PARTY':
      return {
        ...state,
        id: action.payload.partyId,
        isActive: true,
        members: [...state.members, action.payload.member],
      };
    case 'LEAVE_PARTY':
      return initialState;
    case 'ADD_MESSAGE':
      return {
        ...state,
        messages: [...state.messages, action.payload],
      };
    case 'UPDATE_MEMBERS':
      return {
        ...state,
        members: action.payload,
      };
    case 'UPDATE_PLAYBACK':
      return {
        ...state,
        ...action.payload,
      };
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
      };
    case 'RESET':
      return initialState;
    default:
      return state;
  }
}

interface WatchPartyContextType {
  state: WatchPartyState;
  dispatch: React.Dispatch<WatchPartyAction>;
  joinParty: (partyId: string, contentId: string, contentType: 'movie' | 'tv', season?: number, episode?: number) => Promise<void>;
  leaveParty: () => void;
  sendMessage: (message: string) => void;
  updatePlayback: (currentTime: number, isPlaying: boolean, isPaused: boolean, isBuffering: boolean) => void;
  createParty: (name: string, contentId: string, contentType: 'movie' | 'tv', season?: number, episode?: number) => Promise<string>;
}

const WatchPartyContext = createContext<WatchPartyContextType | undefined>(undefined);

interface WatchPartyProviderProps {
  children: ReactNode;
}

export function WatchPartyProvider({ children }: WatchPartyProviderProps) {
  const [state, dispatch] = useReducer(watchPartyReducer, initialState);
  const { user } = useAuth();

  // Join a watch party
  const joinParty = async (partyId: string, contentId: string, contentType: 'movie' | 'tv', season?: number, episode?: number) => {
    try {
      if (!user) {
        throw new Error('You must be logged in to join a watch party');
      }

      const member: WatchPartyMember = {
        id: user.id || user.email || 'anonymous',
        name: user.name || user.email || 'Anonymous',
        avatar: user.picture || undefined,
        isHost: false,
        isOnline: true,
        lastSeen: new Date(),
      };

      dispatch({
        type: 'JOIN_PARTY',
        payload: { partyId, member },
      });

      dispatch({
        type: 'SET_PARTY',
        payload: {
          contentId,
          contentType,
          season,
          episode,
        },
      });

      // Here you would typically connect to your real-time service (Pusher, Socket.io, etc.)
      console.log(`Joined watch party: ${partyId}`);
    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: error instanceof Error ? error.message : 'Failed to join watch party',
      });
    }
  };

  // Leave the current watch party
  const leaveParty = () => {
    dispatch({ type: 'LEAVE_PARTY' });
    console.log('Left watch party');
  };

  // Send a chat message
  const sendMessage = (message: string) => {
    if (!user || !state.isActive) return;

    const newMessage: WatchPartyMessage = {
      id: Date.now().toString(),
      userId: user.id || user.email || 'anonymous',
      userName: user.name || user.email || 'Anonymous',
      userAvatar: user.picture || undefined,
      message,
      timestamp: new Date(),
      type: 'chat',
    };

    dispatch({ type: 'ADD_MESSAGE', payload: newMessage });
  };

  // Update playback state
  const updatePlayback = (currentTime: number, isPlaying: boolean, isPaused: boolean, isBuffering: boolean) => {
    dispatch({
      type: 'UPDATE_PLAYBACK',
      payload: { currentTime, isPlaying, isPaused, isBuffering },
    });
  };

  // Create a new watch party
  const createParty = async (name: string, contentId: string, contentType: 'movie' | 'tv', season?: number, episode?: number): Promise<string> => {
    try {
      if (!user) {
        throw new Error('You must be logged in to create a watch party');
      }

      const partyId = `party_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const member: WatchPartyMember = {
        id: user.id || user.email || 'anonymous',
        name: user.name || user.email || 'Anonymous',
        avatar: user.picture || undefined,
        isHost: true,
        isOnline: true,
        lastSeen: new Date(),
      };

      dispatch({
        type: 'SET_PARTY',
        payload: {
          id: partyId,
          name,
          contentId,
          contentType,
          season,
          episode,
          isActive: true,
          isHost: true,
          members: [member],
        },
      });

      // Here you would typically save the party to your database
      console.log(`Created watch party: ${partyId}`);
      
      return partyId;
    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: error instanceof Error ? error.message : 'Failed to create watch party',
      });
      throw error;
    }
  };

  const value: WatchPartyContextType = {
    state,
    dispatch,
    joinParty,
    leaveParty,
    sendMessage,
    updatePlayback,
    createParty,
  };

  return (
    <WatchPartyContext.Provider value={value}>
      {children}
    </WatchPartyContext.Provider>
  );
}

export function useWatchParty() {
  const context = useContext(WatchPartyContext);
  if (context === undefined) {
    throw new Error('useWatchParty must be used within a WatchPartyProvider');
  }
  return context;
} 
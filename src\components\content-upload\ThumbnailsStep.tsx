"use client"

import React, { useCallback, useState } from 'react'
import { motion } from 'framer-motion'
import { FileImage, Upload, Plus, Check, Trash } from 'lucide-react'
import { useDropzone } from 'react-dropzone'
import { Button } from '@/components/ui/button'
import { useContentUpload } from './ContentUploadProvider'

// Mock function to generate thumbnails - in a real implementation,
// this would extract frames from the video
const generateThumbnailsMock = (videoPreview: string | null) => {
  if (!videoPreview) return []

  // In a real implementation, we would generate frames from the video
  // Here we're just returning placeholder colors with the video URL
  return Array.from({ length: 6 }, (_, i) => ({
    id: `thumbnail-${i}`,
    url: videoPreview,
    timestamp: i * 10, // Seconds into the video
  }))
}

export function ThumbnailsStep() {
  const { uploadData, updateThumbnails } = useContentUpload()
  const { thumbnails, fileUpload } = uploadData
  const [error, setError] = useState<string | null>(null)

  // Mock thumbnails - in a real app these would be generated from the video
  const [generatedThumbnails, setGeneratedThumbnails] = useState<Array<{id: string, url: string, timestamp: number}>>([])

  // Handle custom thumbnail upload
  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return

    const file = acceptedFiles[0]
    setError(null)

    // Create a preview URL
    const previewUrl = URL.createObjectURL(file)

    // Update the thumbnails data
    updateThumbnails({
      mainThumbnail: previewUrl,
      selectedThumbnailIndex: -1 // -1 indicates a custom upload
    })
  }, [updateThumbnails])

  // Handle drop rejection
  const onDropRejected = useCallback(() => {
    setError('Please upload a valid image file (JPG, PNG, or WEBM)')
  }, [])

  // Configure dropzone
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    onDropRejected,
    accept: {
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
      'image/webp': ['.webp'],
    },
    maxSize: 5 * 1024 * 1024, // 5MB max
    multiple: false
  })

  // Generate thumbnails from the video
  const handleGenerateThumbnails = useCallback(() => {
    if (!fileUpload.preview) {
      setError('You need to upload a video first')
      return
    }

    const thumbnails = generateThumbnailsMock(fileUpload.preview)
    setGeneratedThumbnails(thumbnails)

    // If there's no main thumbnail selected yet, select the first one
    if (!uploadData.thumbnails.mainThumbnail && thumbnails.length > 0) {
      updateThumbnails({
        mainThumbnail: thumbnails[0].url,
        selectedThumbnailIndex: 0
      })
    }
  }, [fileUpload.preview, updateThumbnails, uploadData.thumbnails.mainThumbnail])

  // Select a generated thumbnail
  const selectThumbnail = (index: number, url: string) => {
    updateThumbnails({
      mainThumbnail: url,
      selectedThumbnailIndex: index
    })
  }

  // Remove the custom thumbnail
  const removeCustomThumbnail = () => {
    // If we have generated thumbnails, select the first one
    if (generatedThumbnails.length > 0) {
      updateThumbnails({
        mainThumbnail: generatedThumbnails[0].url,
        selectedThumbnailIndex: 0
      })
    } else {
      // Otherwise, clear the thumbnail
      updateThumbnails({
        mainThumbnail: null,
        selectedThumbnailIndex: -1
      })
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="space-y-2">
        <h2 className="text-xl font-semibold text-vista-light">Thumbnail</h2>
        <p className="text-vista-light/70">
          Choose a thumbnail that represents your content. You can upload a custom image or generate thumbnails from your video.
        </p>
      </div>

      {/* Preview of the selected thumbnail */}
      {thumbnails.mainThumbnail ? (
        <div className="space-y-4">
          <div className="aspect-video bg-vista-dark-lighter rounded-lg overflow-hidden relative group">
            <img
              src={thumbnails.mainThumbnail}
              alt="Selected thumbnail"
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
              <Button
                variant="destructive"
                size="sm"
                className="gap-2"
                onClick={removeCustomThumbnail}
              >
                <Trash className="h-4 w-4" />
                Remove
              </Button>
            </div>
          </div>

          <div className="text-sm text-vista-light/70">
            {thumbnails.selectedThumbnailIndex === -1
              ? 'Custom uploaded thumbnail'
              : `Generated thumbnail #${thumbnails.selectedThumbnailIndex + 1}`}
          </div>
        </div>
      ) : (
        <div
          {...getRootProps()}
          className={`
            border-2 border-dashed rounded-lg p-6
            aspect-video flex flex-col items-center justify-center
            cursor-pointer transition-colors
            ${isDragActive
              ? 'border-vista-blue bg-vista-blue/5'
              : 'border-vista-light/20 hover:border-vista-light/40'}
          `}
        >
          <input {...getInputProps()} />
          <FileImage className="h-12 w-12 text-vista-light/40 mb-4" />
          <div className="text-center">
            <p className="text-vista-light font-medium mb-1">
              {isDragActive
                ? 'Drop your thumbnail here'
                : 'Drag and drop your thumbnail here'}
            </p>
            <p className="text-vista-light/60 text-sm">
              or click to browse (JPG, PNG, WebP)
            </p>
          </div>
        </div>
      )}

      {error && (
        <div className="text-red-500 text-sm">
          {error}
        </div>
      )}

      {/* Divider */}
      <div className="relative flex items-center py-4">
        <div className="flex-grow border-t border-vista-light/10"></div>
        <span className="flex-shrink mx-4 text-vista-light/60 text-sm">OR</span>
        <div className="flex-grow border-t border-vista-light/10"></div>
      </div>

      {/* Generated thumbnails section */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium text-vista-light">Generate from Video</h3>
          <Button
            variant="outline"
            size="sm"
            onClick={handleGenerateThumbnails}
            disabled={!fileUpload.preview}
            className="gap-2"
          >
            <Upload className="h-4 w-4" />
            Generate Thumbnails
          </Button>
        </div>

        {generatedThumbnails.length > 0 ? (
          <div className="grid grid-cols-3 gap-3">
            {generatedThumbnails.map((thumb, index) => (
              <div
                key={thumb.id}
                className={`
                  aspect-video rounded-md overflow-hidden cursor-pointer relative group
                  ${thumbnails.selectedThumbnailIndex === index
                    ? 'ring-2 ring-vista-blue ring-offset-2 ring-offset-vista-dark'
                    : 'opacity-70 hover:opacity-100'}
                `}
                onClick={() => selectThumbnail(index, thumb.url)}
              >
                <img
                  src={thumb.url}
                  alt={`Thumbnail option ${index + 1}`}
                  className="w-full h-full object-cover"
                />
                {thumbnails.selectedThumbnailIndex === index && (
                  <div className="absolute top-2 right-2 bg-vista-blue rounded-full p-0.5">
                    <Check className="h-3 w-3 text-white" />
                  </div>
                )}
                <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded">
                  {Math.floor(thumb.timestamp / 60)}:{(thumb.timestamp % 60).toString().padStart(2, '0')}
                </div>
              </div>
            ))}

            {/* Add custom thumbnail option */}
            <div
              {...getRootProps()}
              className="aspect-video bg-vista-dark-lighter rounded-md flex flex-col items-center justify-center cursor-pointer border-2 border-dashed border-vista-light/20 hover:border-vista-light/40"
            >
              <input {...getInputProps()} />
              <Plus className="h-8 w-8 text-vista-light/50 mb-2" />
              <span className="text-vista-light/70 text-sm">Custom</span>
            </div>
          </div>
        ) : (
          <div className="text-vista-light/60 text-sm italic p-4 text-center bg-vista-dark-lighter rounded-md">
            {fileUpload.preview
              ? 'Click "Generate Thumbnails" to create options from your video'
              : 'Upload a video first to generate thumbnails'}
          </div>
        )}
      </div>
    </motion.div>
  )
}

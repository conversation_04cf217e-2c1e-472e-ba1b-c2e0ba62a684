'use client';

import { useState } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle, 
  CardFooter 
} from '@/components/ui/card';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Loader2, 
  RefreshCw, 
  Film, 
  Tv, 
  Eye, 
  Heart, 
  BarChart3, 
  Clock, 
  Calendar, 
  Download 
} from 'lucide-react';
import { useContentAnalytics } from '@/hooks/useAdminData';
import Image from 'next/image';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON><PERSON>, Pie<PERSON>hart } from '@/components/ui/charts';

export default function ContentAnalytics() {
  // Fetch content analytics
  const { 
    data: analyticsData, 
    isLoading, 
    error, 
    refetch 
  } = useContentAnalytics();
  
  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  // Format watch time
  const formatWatchTime = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes} min`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  };
  
  // Format completion rate
  const formatCompletionRate = (rate: number) => {
    return `${Math.round(rate * 100)}%`;
  };
  
  // Get color for completion rate
  const getCompletionRateColor = (rate: number) => {
    if (rate >= 0.8) return 'bg-green-500';
    if (rate >= 0.6) return 'bg-yellow-500';
    if (rate >= 0.4) return 'bg-orange-500';
    return 'bg-red-500';
  };
  
  // Export analytics data as CSV
  const exportAnalytics = () => {
    if (!analyticsData) return;
    
    // Prepare data for CSV
    const movieRows = analyticsData.topMovies.map(movie => [
      'Movie',
      movie.title,
      movie.views.toString(),
      formatCompletionRate(movie.completionRate),
      movie.likes?.toString() || '0',
      movie.favorites?.toString() || '0'
    ]);
    
    const showRows = analyticsData.topShows.map(show => [
      'TV Show',
      show.title,
      show.views.toString(),
      formatCompletionRate(show.completionRate),
      show.likes?.toString() || '0',
      show.favorites?.toString() || '0'
    ]);
    
    // Combine data
    const csvData = [
      ['Type', 'Title', 'Views', 'Completion Rate', 'Likes', 'Favorites'],
      ...movieRows,
      ...showRows
    ];
    
    // Convert to CSV string
    const csvContent = csvData.map(row => row.join(',')).join('\n');
    
    // Create download link
    const encodedUri = encodeURI('data:text/csv;charset=utf-8,' + csvContent);
    const link = document.createElement('a');
    link.setAttribute('href', encodedUri);
    link.setAttribute('download', `content-analytics-${new Date().toISOString().slice(0, 10)}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-vista-light">Content Performance</h2>
          <p className="text-vista-light/70">
            Analyze content engagement and viewing patterns
          </p>
        </div>
        <div className="flex items-center gap-2">
          {analyticsData && (
            <p className="text-sm text-vista-light/70">
              <Clock className="inline-block mr-1 h-4 w-4" />
              Last updated: {formatDate(analyticsData.timestamp)}
            </p>
          )}
          <Button variant="outline" onClick={() => refetch()} disabled={isLoading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" onClick={exportAnalytics} disabled={!analyticsData || isLoading}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>
      
      {error ? (
        <Card>
          <CardContent className="p-8 text-center">
            <div className="text-red-500 mb-4">
              <BarChart3 className="h-12 w-12 mx-auto mb-2" />
              <h3 className="text-lg font-medium">Error Loading Analytics</h3>
            </div>
            <p className="text-vista-light/70 mb-4">
              {error instanceof Error ? error.message : 'Failed to load content analytics'}
            </p>
            <Button onClick={() => refetch()}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          </CardContent>
        </Card>
      ) : isLoading ? (
        <Card>
          <CardContent className="p-8 flex justify-center items-center">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-vista-light/50" />
              <p className="text-vista-light/70">Loading analytics data...</p>
            </div>
          </CardContent>
        </Card>
      ) : analyticsData ? (
        <Tabs defaultValue="movies">
          <TabsList className="mb-4">
            <TabsTrigger value="movies" className="flex items-center">
              <Film className="mr-2 h-4 w-4" />
              Movies
            </TabsTrigger>
            <TabsTrigger value="shows" className="flex items-center">
              <Tv className="mr-2 h-4 w-4" />
              TV Shows
            </TabsTrigger>
            <TabsTrigger value="genres" className="flex items-center">
              <BarChart3 className="mr-2 h-4 w-4" />
              Genres
            </TabsTrigger>
            <TabsTrigger value="time" className="flex items-center">
              <Clock className="mr-2 h-4 w-4" />
              Watch Time
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="movies">
            <Card>
              <CardHeader>
                <CardTitle className="text-vista-light">Top Movies</CardTitle>
                <CardDescription>Most viewed movies on the platform</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border border-vista-light/10 overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Poster</TableHead>
                        <TableHead>Title</TableHead>
                        <TableHead>Views</TableHead>
                        <TableHead>Completion Rate</TableHead>
                        <TableHead>Engagement</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {analyticsData.topMovies.map((movie, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <div className="relative h-16 w-12 overflow-hidden rounded-sm">
                              {movie.posterPath ? (
                                <Image
                                  src={movie.posterPath.startsWith('/') 
                                    ? `https://image.tmdb.org/t/p/w92${movie.posterPath}` 
                                    : movie.posterPath}
                                  alt={movie.title}
                                  fill
                                  className="object-cover"
                                  onError={(e) => {
                                    e.currentTarget.src = 'https://via.placeholder.com/92x138?text=No+Image';
                                  }}
                                />
                              ) : (
                                <div className="h-full w-full bg-vista-dark-lighter flex items-center justify-center">
                                  <Film className="h-6 w-6 text-vista-light/50" />
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="font-medium">{movie.title}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Eye className="h-4 w-4 text-vista-light/70" />
                              <span>{movie.views.toLocaleString()}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="flex justify-between text-sm">
                                <span>Completion</span>
                                <span>{formatCompletionRate(movie.completionRate)}</span>
                              </div>
                              <Progress 
                                value={movie.completionRate * 100} 
                                className="h-2"
                                indicatorClassName={getCompletionRateColor(movie.completionRate)}
                              />
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-4">
                              <div className="flex items-center gap-1">
                                <Heart className="h-4 w-4 text-red-500" />
                                <span>{movie.likes?.toLocaleString() || '0'}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Badge variant="outline">
                                  {movie.favorites?.toLocaleString() || '0'} favorites
                                </Badge>
                              </div>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="shows">
            <Card>
              <CardHeader>
                <CardTitle className="text-vista-light">Top TV Shows</CardTitle>
                <CardDescription>Most viewed shows on the platform</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border border-vista-light/10 overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Poster</TableHead>
                        <TableHead>Title</TableHead>
                        <TableHead>Views</TableHead>
                        <TableHead>Completion Rate</TableHead>
                        <TableHead>Engagement</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {analyticsData.topShows.map((show, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <div className="relative h-16 w-12 overflow-hidden rounded-sm">
                              {show.posterPath ? (
                                <Image
                                  src={show.posterPath.startsWith('/') 
                                    ? `https://image.tmdb.org/t/p/w92${show.posterPath}` 
                                    : show.posterPath}
                                  alt={show.title}
                                  fill
                                  className="object-cover"
                                  onError={(e) => {
                                    e.currentTarget.src = 'https://via.placeholder.com/92x138?text=No+Image';
                                  }}
                                />
                              ) : (
                                <div className="h-full w-full bg-vista-dark-lighter flex items-center justify-center">
                                  <Tv className="h-6 w-6 text-vista-light/50" />
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="font-medium">{show.title}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Eye className="h-4 w-4 text-vista-light/70" />
                              <span>{show.views.toLocaleString()}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="flex justify-between text-sm">
                                <span>Completion</span>
                                <span>{formatCompletionRate(show.completionRate)}</span>
                              </div>
                              <Progress 
                                value={show.completionRate * 100} 
                                className="h-2"
                                indicatorClassName={getCompletionRateColor(show.completionRate)}
                              />
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-4">
                              <div className="flex items-center gap-1">
                                <Heart className="h-4 w-4 text-red-500" />
                                <span>{show.likes?.toLocaleString() || '0'}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Badge variant="outline">
                                  {show.favorites?.toLocaleString() || '0'} favorites
                                </Badge>
                              </div>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="genres">
            <Card>
              <CardHeader>
                <CardTitle className="text-vista-light">Genre Popularity</CardTitle>
                <CardDescription>Content distribution by genre</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <PieChart
                    data={Object.entries(analyticsData.genrePopularity)
                      .sort(([, a], [, b]) => (b as number) - (a as number))
                      .slice(0, 8)
                      .map(([genre, count]) => ({
                        name: genre,
                        value: count as number
                      }))
                    }
                    category="value"
                    index="name"
                    colors={['#3b82f6', '#8b5cf6', '#ec4899', '#f97316', '#10b981', '#06b6d4', '#84cc16', '#f59e0b']}
                    valueFormatter={(value) => `${value} titles`}
                    showAnimation={true}
                    showLegend={true}
                    innerRadius={40}
                    outerRadius={100}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="time">
            <Card>
              <CardHeader>
                <CardTitle className="text-vista-light">Watch Time Distribution</CardTitle>
                <CardDescription>Viewing patterns by hour of day</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <BarChart
                    data={analyticsData.watchTimeByHour.map((count, hour) => ({
                      hour: `${hour}:00`,
                      views: count
                    }))}
                    index="hour"
                    categories={['views']}
                    colors={['#3b82f6']}
                    valueFormatter={(value) => `${value.toLocaleString()} views`}
                    showLegend={false}
                    showGridLines={true}
                    showAnimation={true}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      ) : (
        <Card>
          <CardContent className="p-8 text-center">
            <p className="text-vista-light/70">No analytics data available</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

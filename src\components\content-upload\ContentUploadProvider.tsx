"use client"

import React, { createContext, useContext, useState, useCallback } from 'react'

// Define a type for upload status
export type UploadStatus = 'idle' | 'uploading' | 'processing' | 'success' | 'error'

// Define types for each step's data
export interface FileUploadData {
  file: File | null
  preview: string | null
  progress: number
}

export interface MetadataData {
  title: string
  description: string
  genres: string[]
  type: 'movie' | 'show'
  releaseYear: number
  duration?: string
  episodeNumber?: number
  seasonNumber?: number
}

export interface ThumbnailsData {
  mainThumbnail: string | null
  additionalThumbnails: string[]
  selectedThumbnailIndex: number
}

export interface SettingsData {
  visibility: 'public' | 'private' | 'unlisted'
  monetization: boolean
  releaseDate: Date | null
  ageRestriction: boolean
}

// Combined data structure for all steps
export interface ContentUploadData {
  fileUpload: FileUploadData
  metadata: MetadataData
  thumbnails: ThumbnailsData
  settings: SettingsData
  currentStep: number
  uploadStatus: UploadStatus
  errorMessage?: string
}

// Define the context type
interface ContentUploadContextType {
  uploadData: ContentUploadData
  setUploadData: React.Dispatch<React.SetStateAction<ContentUploadData>>
  updateFileUpload: (data: Partial<FileUploadData>) => void
  updateMetadata: (data: Partial<MetadataData>) => void
  updateThumbnails: (data: Partial<ThumbnailsData>) => void
  updateSettings: (data: Partial<SettingsData>) => void
  nextStep: () => void
  prevStep: () => void
  goToStep: (step: number) => void
  startUpload: () => Promise<void>
  resetUpload: () => void
}

// Default initial values for upload data
const initialUploadData: ContentUploadData = {
  fileUpload: {
    file: null,
    preview: null,
    progress: 0
  },
  metadata: {
    title: '',
    description: '',
    genres: [],
    type: 'movie',
    releaseYear: new Date().getFullYear(),
  },
  thumbnails: {
    mainThumbnail: null,
    additionalThumbnails: [],
    selectedThumbnailIndex: 0
  },
  settings: {
    visibility: 'private',
    monetization: false,
    releaseDate: null,
    ageRestriction: false
  },
  currentStep: 0,
  uploadStatus: 'idle'
}

// Create the context
const ContentUploadContext = createContext<ContentUploadContextType | undefined>(undefined)

// Provider component
export function ContentUploadProvider({ children }: { children: React.ReactNode }) {
  const [uploadData, setUploadData] = useState<ContentUploadData>(initialUploadData)

  // Helper functions for updating specific parts of the upload data
  const updateFileUpload = useCallback((data: Partial<FileUploadData>) => {
    setUploadData(prev => ({
      ...prev,
      fileUpload: { ...prev.fileUpload, ...data }
    }))
  }, [])

  const updateMetadata = useCallback((data: Partial<MetadataData>) => {
    setUploadData(prev => ({
      ...prev,
      metadata: { ...prev.metadata, ...data }
    }))
  }, [])

  const updateThumbnails = useCallback((data: Partial<ThumbnailsData>) => {
    setUploadData(prev => ({
      ...prev,
      thumbnails: { ...prev.thumbnails, ...data }
    }))
  }, [])

  const updateSettings = useCallback((data: Partial<SettingsData>) => {
    setUploadData(prev => ({
      ...prev,
      settings: { ...prev.settings, ...data }
    }))
  }, [])

  // Navigation functions
  const nextStep = useCallback(() => {
    setUploadData(prev => ({
      ...prev,
      currentStep: Math.min(prev.currentStep + 1, 3) // Maximum of 4 steps (0-3)
    }))
  }, [])

  const prevStep = useCallback(() => {
    setUploadData(prev => ({
      ...prev,
      currentStep: Math.max(prev.currentStep - 1, 0) // Minimum of 0
    }))
  }, [])

  const goToStep = useCallback((step: number) => {
    setUploadData(prev => ({
      ...prev,
      currentStep: Math.min(Math.max(step, 0), 3) // Keep between 0-3
    }))
  }, [])

  // Upload function that simulates uploading content
  const startUpload = useCallback(async () => {
    // Set status to uploading
    setUploadData(prev => ({
      ...prev,
      uploadStatus: 'uploading',
      errorMessage: undefined
    }))

    try {
      // Simulate file upload with progress
      for (let progress = 0; progress <= 100; progress += 5) {
        await new Promise(resolve => setTimeout(resolve, 200))

        updateFileUpload({ progress })
      }

      // Set status to processing
      setUploadData(prev => ({
        ...prev,
        uploadStatus: 'processing'
      }))

      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, 3000))

      // Set status to success
      setUploadData(prev => ({
        ...prev,
        uploadStatus: 'success'
      }))
    } catch (error) {
      // Handle upload errors
      setUploadData(prev => ({
        ...prev,
        uploadStatus: 'error',
        errorMessage: error instanceof Error ? error.message : 'An unknown error occurred'
      }))
    }
  }, [])

  // Reset function to clear all upload data
  const resetUpload = useCallback(() => {
    setUploadData(initialUploadData)
  }, [])

  // Create context value
  const contextValue: ContentUploadContextType = {
    uploadData,
    setUploadData,
    updateFileUpload,
    updateMetadata,
    updateThumbnails,
    updateSettings,
    nextStep,
    prevStep,
    goToStep,
    startUpload,
    resetUpload
  }

  return (
    <ContentUploadContext.Provider value={contextValue}>
      {children}
    </ContentUploadContext.Provider>
  )
}

// Custom hook for using the context
export function useContentUpload() {
  const context = useContext(ContentUploadContext)
  if (!context) {
    throw new Error('useContentUpload must be used within a ContentUploadProvider')
  }
  return context
}

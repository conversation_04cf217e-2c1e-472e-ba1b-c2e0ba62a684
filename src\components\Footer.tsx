"use client";

import Image from 'next/image';
import Link from 'next/link';
import { ChevronDown, Globe, MessageSquarePlus } from 'lucide-react';
import { useState, useEffect } from 'react';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

// Platform device icons for compatibility section
const platforms = [
  { name: "iOS", icon: "/icons/apple.svg" },
  { name: "Android", icon: "/icons/android.svg" },
  { name: "Smart TV", icon: "/icons/tv.svg" },
  { name: "Web", icon: "/icons/desktop.svg" },
  { name: "Game Console", icon: "/icons/console.svg" },
  { name: "Tablet", icon: "/icons/tablet.svg" },
];

const LOCAL_STORAGE_KEY = 'chatAssistantDismissed';

// Define a type for the window object with our custom property
interface WindowWithResetChat extends Window {
  resetChatAssistant?: () => void;
}

export default function Footer() {

  const [isChatDismissed, setIsChatDismissed] = useState(true);
  const [isMounted, setIsMounted] = useState(false);

  // --- Add effect for chat dismissal state ---
  useEffect(() => {
    setIsMounted(true);
    // Get the current value directly from localStorage
    const dismissed = localStorage.getItem(LOCAL_STORAGE_KEY) === 'true';
    console.log(`[Footer] Checked localStorage for ${LOCAL_STORAGE_KEY}:`, dismissed);
    setIsChatDismissed(dismissed);

    const handleStorageChange = (event: StorageEvent) => {
        if (event.key === LOCAL_STORAGE_KEY) {
            const dismissedFromStorage = localStorage.getItem(LOCAL_STORAGE_KEY) === 'true';
            console.log(`[Footer] Storage event for ${LOCAL_STORAGE_KEY}:`, dismissedFromStorage);
            setIsChatDismissed(dismissedFromStorage);
        }
    };
    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  const handleReEnableChat = () => {
    console.log("[Footer] handleReEnableChat called");

    try {
      // Use the global reset function if available
      if (typeof window !== 'undefined') {
        const customWindow = window as WindowWithResetChat;
        if (customWindow.resetChatAssistant) {
          customWindow.resetChatAssistant();
          return; // The page will reload
        }
      }

      // Fallback if the global function is not available
      // Clear any existing values
      localStorage.removeItem(LOCAL_STORAGE_KEY);

      // Set to default values - ALWAYS set to visible
      localStorage.setItem(LOCAL_STORAGE_KEY, 'false');

      // Force storage events to notify other components
      window.dispatchEvent(new StorageEvent('storage', {
        key: LOCAL_STORAGE_KEY,
        newValue: 'false'
      }));

      // Update local state
      setIsChatDismissed(false);

      // Reload the page to ensure the chat is visible
      window.location.reload();
    } catch (e) {
      console.error("[Footer] Error resetting chat:", e);
    }
  };
  // --- End chat dismissal state effect ---

  return (
    <footer className="bg-black pt-16 pb-8">
      <div className="container px-4 md:px-6 mx-auto">
        {/* Watch on devices section */}
        <div className="mb-16">
          <h2 className="text-2xl md:text-3xl font-bold text-vista-light text-center mb-8">
            Watch StreamVista Everywhere
          </h2>

          <div className="grid grid-cols-3 sm:grid-cols-6 gap-6 max-w-3xl mx-auto">
            {platforms.map((platform, index) => (
              <div key={index} className="flex flex-col items-center justify-center">
                <div className="w-12 h-12 md:w-16 md:h-16 relative mb-2">
                  <div className="w-full h-full rounded-full bg-vista-accent-alt/10 flex items-center justify-center">
                    <span className="text-vista-light text-xl">{platform.name.charAt(0)}</span>
                  </div>
                </div>
                <span className="text-xs md:text-sm text-vista-light/80 text-center">{platform.name}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
          <div className="md:col-span-1">
            <div className="flex items-center mb-4 group">
              <div className="relative w-14 h-14">
                <Image
                  src="/logo.webp"
                  alt="StreamVista"
                  width={64}
                  height={64}
                  quality={100}
                  className="object-contain transition-transform duration-300 group-hover:scale-105 drop-shadow-[0_2px_4px_rgba(0,0,0,0.3)]"
                />
              </div>
              <span className="ml-2 text-2xl font-bold text-gradient">StreamVista</span>
            </div>
            <p className="text-sm text-vista-light/60 mb-4">
              The premium streaming service for the best original content.
            </p>
          </div>

          <div>
            <h3 className="text-sm font-bold text-vista-light mb-4 uppercase tracking-wider">Company</h3>
            <ul className="space-y-2">
              <li><Link href="/about" className="text-sm text-vista-light/60 hover:text-vista-accent transition-colors">About Us</Link></li>
              <li><Link href="/careers" className="text-sm text-vista-light/60 hover:text-vista-accent transition-colors">Careers</Link></li>
              <li><Link href="/watch-party" className="text-sm text-vista-light/60 hover:text-vista-accent transition-colors">Watch Party</Link></li>
              <li><Link href="/contact" className="text-sm text-vista-light/60 hover:text-vista-accent transition-colors">Contact</Link></li>
            </ul>
          </div>

          <div>
            <h3 className="text-sm font-bold text-vista-light mb-4 uppercase tracking-wider">Support</h3>
            <ul className="space-y-2">
              <li><Link href="/help" className="text-sm text-vista-light/60 hover:text-vista-accent transition-colors">Help Center</Link></li>
              <li><Link href="/devices" className="text-sm text-vista-light/60 hover:text-vista-accent transition-colors">Supported Devices</Link></li>
              <li><Link href="/settings" className="text-sm text-vista-light/60 hover:text-vista-accent transition-colors">Account & Settings</Link></li>
              <li><Link href="/reset-chat" className="text-sm text-vista-light/60 hover:text-vista-accent transition-colors flex items-center gap-1"><MessageSquarePlus size={14} /> Reset Chat Assistant</Link></li>
              {isMounted && isChatDismissed && (
                <li>
                   <Button
                       variant="link"
                       className="text-sm text-vista-light/60 hover:text-vista-accent transition-colors p-0 h-auto flex items-center gap-1"
                       onClick={handleReEnableChat}
                   >
                       <MessageSquarePlus size={14} /> Show Chat
                   </Button>
                </li>
              )}
            </ul>
          </div>

          <div>
            <h3 className="text-sm font-bold text-vista-light mb-4 uppercase tracking-wider">Legal</h3>
            <ul className="space-y-2">
              <li><Link href="/terms" className="text-sm text-vista-light/60 hover:text-vista-accent transition-colors">Terms of Service</Link></li>
              <li><Link href="/privacy" className="text-sm text-vista-light/60 hover:text-vista-accent transition-colors">Privacy Policy</Link></li>
              <li><Link href="/cookies" className="text-sm text-vista-light/60 hover:text-vista-accent transition-colors">Cookie Preferences</Link></li>
            </ul>
          </div>
        </div>

        <Separator className="mb-6 bg-vista-light/10" />

        {/* Bottom Footer */}
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="flex items-center mb-4 md:mb-0">
            <button className="flex items-center gap-1 text-sm text-vista-light/60 hover:text-vista-light">
              <Globe className="h-4 w-4" />
              <span>English</span>
              <ChevronDown className="h-3 w-3" />
            </button>
          </div>

          <div className="text-xs text-vista-light/50">
            © {new Date().getFullYear()} StreamVista. All rights reserved.
          </div>
        </div>
      </div>
    </footer>
  );
}

'use client';

import { useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { CameraIcon } from 'lucide-react';
import { toast } from 'sonner';

interface CloudinaryUploadWidgetProps {
  onUploadSuccess: (url: string) => void;
  onUploadStart?: () => void;
  onUploadCancel?: () => void; // Add callback for when upload is cancelled
  buttonText?: string;
  className?: string;
}

declare global {
  interface Window {
    cloudinary: any;
  }
}

const CLOUDINARY_CLOUD_NAME = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || 'streamvista';
const CLOUDINARY_UPLOAD_PRESET = process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET || 'streamvista_profiles';

// Log Cloudinary configuration for debugging
console.log('Cloudinary configuration:', {
  cloudName: CLOUDINARY_CLOUD_NAME,
  uploadPreset: CLOUDINARY_UPLOAD_PRESET
});

export function CloudinaryUploadWidget({
  onUploadSuccess,
  onUploadStart,
  onUploadCancel,
  buttonText = 'Upload Image',
  className = '',
}: CloudinaryUploadWidgetProps) {
  const [loaded, setLoaded] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const cloudinaryRef = useRef<any>(null);
  const widgetRef = useRef<any>(null);

  useEffect(() => {
    // Load Cloudinary script dynamically
    if (!loaded) {
      const script = document.createElement('script');
      script.src = 'https://upload-widget.cloudinary.com/global/all.js';
      script.async = true;
      script.onload = () => setLoaded(true);
      document.body.appendChild(script);
      return () => {
        document.body.removeChild(script);
        // Reset uploading state when component unmounts
        if (isUploading && onUploadCancel) {
          onUploadCancel();
        }
      };
    }

    if (loaded && !widgetRef.current) {
      cloudinaryRef.current = window.cloudinary;
      widgetRef.current = cloudinaryRef.current.createUploadWidget(
        {
          cloudName: CLOUDINARY_CLOUD_NAME,
          uploadPreset: CLOUDINARY_UPLOAD_PRESET,
          folder: 'streamvista/profiles',
          // Add file name context using user ID and timestamp to help with organization
          publicId: `user_${localStorage.getItem('userId') || 'unknown'}_${Date.now()}`,
          sources: ['local', 'url', 'camera'],
          maxImageFileSize: 5000000, // 5MB
          multiple: false,
          cropping: true,
          croppingAspectRatio: 1,
          styles: {
            palette: {
              window: '#121212',
              sourceBg: '#1a1a1a',
              windowBorder: '#1a1a1a',
              tabIcon: '#007bff',
              inactiveTabIcon: '#6c757d',
              menuIcons: '#007bff',
              link: '#007bff',
              action: '#007bff',
              inProgress: '#007bff',
              complete: '#28a745',
              error: '#dc3545',
              textDark: '#f8f9fa',
              textLight: '#f8f9fa',
            },
          },
        },
        (error: any, result: any) => {
          // Handle widget close event
          if (result && result.event === 'close') {
            setIsUploading(false);
            // Call the onUploadCancel callback if provided
            if (onUploadCancel) {
              onUploadCancel();
            }
            return;
          }

          // Handle successful upload
          if (!error && result && result.event === 'success') {
            // Get secure URL from result and pass to onUploadSuccess callback
            const secureUrl = result.info.secure_url;
            onUploadSuccess(secureUrl);
            setIsUploading(false);
            // Don't show success toast here - let the parent component handle it
          }
          // Handle upload error
          else if (error) {
            setIsUploading(false);
            toast.error('Error uploading image');
            console.error('Cloudinary upload error:', error);
          }
        }
      );
    }
  }, [loaded, onUploadSuccess, onUploadCancel]);

  // Add a cleanup effect to ensure loading state is reset when component unmounts
  useEffect(() => {
    return () => {
      if (isUploading && onUploadCancel) {
        onUploadCancel();
      }
    };
  }, [isUploading, onUploadCancel]);

  const handleClick = () => {
    setIsUploading(true);
    if (onUploadStart) {
      onUploadStart();
    }
    if (widgetRef.current) {
      // Open the widget
      widgetRef.current.open();

      // Add a global event listener for the ESC key to detect manual cancellation
      const handleEscKey = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          setIsUploading(false);
          if (onUploadCancel) onUploadCancel();
          document.removeEventListener('keydown', handleEscKey);
        }
      };

      document.addEventListener('keydown', handleEscKey);

      // Remove the event listener after 2 minutes as a safety measure
      setTimeout(() => {
        document.removeEventListener('keydown', handleEscKey);
      }, 120000);
    }
  };

  return (
    <Button
      type="button"
      onClick={handleClick}
      className={`cloudinary-button ${className}`}
      variant="outline"
      disabled={!loaded || isUploading}
      size="sm"
    >
      {isUploading ? (
        <div className="flex items-center justify-center w-full">
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-1.5"></div>
          <span>Uploading...</span>
        </div>
      ) : (
        <div className="flex items-center justify-center w-full">
          <CameraIcon className="w-4 h-4 mr-1" />
          <span className="text-center">{buttonText}</span>
        </div>
      )}
    </Button>
  );
}
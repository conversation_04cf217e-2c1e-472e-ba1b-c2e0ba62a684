import { NextRequest, NextResponse } from 'next/server';
import { isAdmin } from '@/lib/middleware';
import { ensureMongooseConnection } from '@/lib/mongoose';
import Content, { IContent } from '@/models/Content';

// GET handler - fetch a single content item by ID
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check admin authorization
    const adminCheck = await isAdmin(req);
    if (!adminCheck.isAuthorized) {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    await ensureMongooseConnection();
    const { id } = params;

    if (!id) {
      return NextResponse.json({ error: "Content ID is required" }, { status: 400 });
    }

    const contentItem = await Content.findById(id);

    if (!contentItem) {
      return NextResponse.json({ error: "Content not found" }, { status: 404 });
    }

    return NextResponse.json(contentItem);
  } catch (error) {
    console.error("Error fetching content:", error);
    return NextResponse.json(
      { error: "Failed to fetch content" },
      { status: 500 }
    );
  }
}

// PUT handler - update a content item
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check admin authorization
    const adminCheck = await isAdmin(req);
    if (!adminCheck.isAuthorized) {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    await ensureMongooseConnection();
    const { id } = params;

    if (!id) {
      return NextResponse.json({ error: "Content ID is required" }, { status: 400 });
    }

    const contentItem = await Content.findById(id);

    if (!contentItem) {
      return NextResponse.json({ error: "Content not found" }, { status: 404 });
    }

    const data = await req.json();

    // Update fields
    const allowedFields = [
      'title', 'type', 'status', 'tmdbId', 'imdbId', 'posterPath',
      'backdropPath', 'overview', 'tagline', 'year', 'genres',
      'runtime', 'rating', 'featured', 'trending'
    ];

    // Create a typed content item
    const typedContentItem = contentItem as IContent;

    // Update each allowed field
    for (const field of allowedFields) {
      if (data[field] !== undefined) {
        // @ts-ignore - dynamically setting properties
        typedContentItem[field] = data[field];
      }
    }

    typedContentItem.updatedAt = new Date();
    await typedContentItem.save();

    return NextResponse.json({
      message: "Content updated successfully",
      content: typedContentItem
    });
  } catch (error) {
    console.error("Error updating content:", error);
    return NextResponse.json(
      { error: "Failed to update content" },
      { status: 500 }
    );
  }
}

// DELETE handler - delete a content item
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check admin authorization
    const adminCheck = await isAdmin(req);
    if (!adminCheck.isAuthorized) {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    await ensureMongooseConnection();
    const { id } = params;

    if (!id) {
      return NextResponse.json({ error: "Content ID is required" }, { status: 400 });
    }

    const contentItem = await Content.findByIdAndDelete(id);

    if (!contentItem) {
      return NextResponse.json({ error: "Content not found" }, { status: 404 });
    }

    return NextResponse.json({
      message: "Content deleted successfully"
    });
  } catch (error) {
    console.error("Error deleting content:", error);
    return NextResponse.json(
      { error: "Failed to delete content" },
      { status: 500 }
    );
  }
}

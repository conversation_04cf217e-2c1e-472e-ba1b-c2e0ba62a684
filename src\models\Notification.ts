import mongoose, { Schema, Document, Model, model } from 'mongoose';

// Define the notification types
export type NotificationType = 'new_content' | 'recommendation' | 'update' | 'system';

// Define the notification interface
export interface NotificationDocument extends Document {
  userId: mongoose.Types.ObjectId;
  type: NotificationType;
  title: string;
  message: string;
  contentId?: string;
  contentType?: 'movie' | 'show';
  image?: string;
  read: boolean;
  createdAt: Date;
  expiresAt?: Date;
  deletedBy?: mongoose.Types.ObjectId[]; // Array of user IDs who have deleted this notification
  isGlobal?: boolean; // Flag to indicate if this is a global notification (sent to all users)
}

// Define the notification schema
const NotificationSchema = new Schema<NotificationDocument>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true,
    },
    type: {
      type: String,
      enum: ['new_content', 'recommendation', 'update', 'system'],
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    message: {
      type: String,
      required: true,
    },
    contentId: {
      type: String,
      required: false,
    },
    contentType: {
      type: String,
      enum: ['movie', 'show'],
      required: false,
    },
    image: {
      type: String,
      required: false,
    },
    read: {
      type: Boolean,
      default: false,
    },
    expiresAt: {
      type: Date,
      required: false,
    },
    deletedBy: {
      type: [Schema.Types.ObjectId],
      ref: 'User',
      default: [],
    },
    isGlobal: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true, // Automatically create createdAt and updatedAt fields
  }
);

// Create indexes for performance
NotificationSchema.index({ userId: 1, read: 1 });
NotificationSchema.index({ userId: 1, createdAt: -1 });

// Create model only if it doesn't already exist (for Next.js hot reloading)
const Notification = mongoose.models.Notification as Model<NotificationDocument> ||
  model<NotificationDocument>('Notification', NotificationSchema);

export default Notification;

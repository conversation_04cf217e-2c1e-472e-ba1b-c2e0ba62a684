# Content Service

## Overview

StreamVista's content service manages movie and TV show data through integration with TMDb (The Movie Database) API. The service provides a unified interface for content discovery, search, and detailed information retrieval, with support for both movies and TV shows.

## Data Models

### Content Interface

```typescript
interface IContent {
  id: string | number;
  title: string;
  type: 'movie' | 'show';
  year: string;
  posterPath: string;
  backdropPath?: string;
  overview?: string;
  genres?: string[];
  runtime?: number;
  rating?: number;
  seasons?: number;
  episodes?: number;
  creator?: string;
  tmdbId?: string;
  imdbId?: string;
}
```

### TMDb Response Types

```typescript
interface TMDBMovieResult {
  id: number;
  title: string;
  poster_path: string | null;
  backdrop_path: string | null;
  overview: string;
  release_date: string;
  genre_ids: number[];
  vote_average: number;
  runtime?: number;
}

interface TMDBTVResult {
  id: number;
  name: string;
  poster_path: string | null;
  backdrop_path: string | null;
  overview: string;
  first_air_date: string;
  genre_ids: number[];
  vote_average: number;
  number_of_seasons?: number;
  number_of_episodes?: number;
}
```

## Configuration

```typescript
const TMDB_CONFIG = {
  API_KEY: process.env.NEXT_PUBLIC_TMDB_API_KEY,
  ACCESS_TOKEN: process.env.NEXT_PUBLIC_TMDB_ACCESS_TOKEN,
  BASE_URL: 'https://api.themoviedb.org/3',
  IMAGE_BASE_URL: 'https://image.tmdb.org/t/p',
  POSTER_SIZES: ['w92', 'w154', 'w185', 'w342', 'w500', 'w780', 'original'],
  BACKDROP_SIZES: ['w300', 'w780', 'w1280', 'original'],
  LANGUAGE: 'en-US'
};
```

## Core Functions

### Data Conversion

```typescript
// Convert TMDb movie to IContent
function convertMovieToContent(movie: TMDBMovieResult): IContent {
  return {
    id: movie.id.toString(),
    title: movie.title,
    type: 'movie',
    year: movie.release_date?.split('-')[0] || '',
    posterPath: movie.poster_path ? `${TMDB_IMAGE_BASE_URL}/w500${movie.poster_path}` : '',
    backdropPath: movie.backdrop_path ? `${TMDB_IMAGE_BASE_URL}/original${movie.backdrop_path}` : '',
    overview: movie.overview,
    genres: movie.genre_ids?.map(id => genreMap[id]).filter(Boolean) || [],
    rating: movie.vote_average,
    runtime: movie.runtime,
    tmdbId: movie.id.toString()
  };
}

// Convert TMDb TV show to IContent
function convertTVToContent(show: TMDBTVResult): IContent {
  return {
    id: show.id.toString(),
    title: show.name,
    type: 'show',
    year: show.first_air_date?.split('-')[0] || '',
    posterPath: show.poster_path ? `${TMDB_IMAGE_BASE_URL}/w500${show.poster_path}` : '',
    backdropPath: show.backdrop_path ? `${TMDB_IMAGE_BASE_URL}/original${show.backdrop_path}` : '',
    overview: show.overview,
    genres: show.genre_ids?.map(id => genreMap[id]).filter(Boolean) || [],
    rating: show.vote_average,
    seasons: show.number_of_seasons,
    episodes: show.number_of_episodes,
    tmdbId: show.id.toString()
  };
}
```

## API Functions

### Content Discovery

```typescript
// Get popular movies
async function getPopularMovies(page: number = 1): Promise<IContent[]>

// Get popular TV shows
async function getPopularShows(page: number = 1): Promise<IContent[]>

// Get top-rated movies
async function getTopRatedMovies(page: number = 1): Promise<IContent[]>

// Get top-rated TV shows
async function getTopRatedShows(page: number = 1): Promise<IContent[]>

// Get initial content data
async function getInitialContentData(): Promise<{
  popularMovies: IContent[];
  popularShows: IContent[];
  topRatedMovies: IContent[];
  topRatedShows: IContent[];
}>
```

### Content Search

```typescript
// Search for content
async function searchContent(query: string, page: number = 1): Promise<{
  movies: IContent[];
  shows: IContent[];
}>

// Search by IMDb ID
async function getMovieByImdbId(imdbId: string): Promise<IContent | null>
async function getTVByImdbId(imdbId: string): Promise<IContent | null>
```

### Content Details

```typescript
// Get movie details
async function getMovieDetails(id: string): Promise<IContent>

// Get TV show details
async function getTVDetails(id: string): Promise<IContent>

// Get recommendations
async function getMovieRecommendations(id: string, page: number = 1): Promise<IContent[]>
async function getTVRecommendations(id: string, page: number = 1): Promise<IContent[]>
```

## API Routes

### Content Details Route

```typescript
// GET /api/content?id={id}&type={movie|show}
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const id = searchParams.get('id');
  const type = searchParams.get('type') as 'movie' | 'show';
  
  // Fetch content based on type
  const content = type === 'movie' 
    ? await getMovieDetails(id)
    : await getTVDetails(id);
    
  return NextResponse.json(content);
}
```

## Content Utilities

### Format Content for Display

```typescript
function formatContentForCards(content: IContent[]): ContentCardType[] {
  return content.map(item => ({
    id: typeof item.id === 'number' ? item.id.toString() : item.id,
    title: item.title,
    imagePath: item.posterPath,
    type: item.type === 'show' ? 'shows' : 'movies',
    year: item.year.toString(),
    ageRating: item.rating ? item.rating.toString() : undefined,
    duration: item.type === 'movie' && item.runtime ? `${item.runtime} min` : undefined
  }));
}
```

## Error Handling

```typescript
interface TMDBError {
  status_message: string;
  status_code: number;
}

async function handleTMDBError(error: unknown): Promise<never> {
  if (error instanceof Response) {
    const data = await error.json() as TMDBError;
    throw new Error(`TMDb API error: ${data.status_code} - ${data.status_message}`);
  }
  throw error;
}
```

## Best Practices

1. **Performance**
   - Implement caching for frequently accessed content
   - Use appropriate image sizes for different contexts
   - Implement pagination for large result sets
   - Optimize API requests with proper query parameters

2. **Error Handling**
   - Graceful fallback for missing images
   - Comprehensive error messages
   - Retry logic for failed requests
   - Fallback content for API failures

3. **Data Management**
   - Consistent data transformation
   - Type safety throughout the application
   - Proper null handling
   - Data validation before display

4. **User Experience**
   - Progressive image loading
   - Placeholder content during loading
   - Smooth transitions between states
   - Clear error messages

5. **Maintenance**
   - Clear separation of concerns
   - Consistent naming conventions
   - Comprehensive logging
   - Documentation of API usage 
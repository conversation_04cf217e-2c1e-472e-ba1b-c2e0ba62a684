// Spanish translations
const translations = {
  // Common
  'common.appName': 'StreamVista',
  'common.loading': 'Cargando...',
  'common.error': 'Ha ocurrido un error',
  'common.search': 'Buscar',
  'common.cancel': 'Cancelar',
  'common.save': 'Guardar',
  'common.delete': 'Eliminar',
  'common.edit': 'Editar',
  'common.view': 'Ver',
  'common.back': 'Atrás',
  'common.next': 'Siguiente',
  'common.previous': 'Anterior',
  'common.submit': 'Enviar',
  'common.done': 'Hecho',
  'common.more': 'Más',
  'common.less': 'Menos',
  'common.yes': 'Sí',
  'common.no': 'No',
  'common.all': 'Todos',
  'common.add': 'Añadir',
  'common.remove': 'Eliminar',
  'common.create': 'Crear',
  'common.actions': 'Acciones',
  'common.close': 'Cerrar',
  'common.showMore': 'Mostrar más',
  'common.showLess': 'Mostrar menos',
  'common.seeAll': 'Ver todo',
  'common.readMore': 'Leer más',
  'common.continue': 'Continuar',
  'common.filter': 'Filtrar',
  'common.sort': 'Ordenar',
  'common.apply': 'Aplicar',
  'common.reset': 'Restablecer',
  'common.success': 'Éxito',
  'common.warning': 'Advertencia',
  'common.info': 'Información',

  // Navigation
  'nav.home': 'Inicio',
  'nav.shows': 'Series',
  'nav.movies': 'Películas',
  'nav.categories': 'Categorías',
  'nav.myList': 'Mi Lista',
  'nav.downloads': 'Descargas',
  'nav.insights': 'Estadísticas',
  'nav.account': 'Cuenta',
  'nav.settings': 'Configuración',
  'nav.profiles': 'Perfiles',
  'nav.help': 'Ayuda',
  'nav.signIn': 'Iniciar Sesión',
  'nav.signOut': 'Cerrar Sesión',

  // Home page
  'home.popularShows': 'Series Populares',
  'home.popularShowsSubtitle': 'Series de tendencia que todos están viendo',
  'home.popularMovies': 'Películas Populares',
  'home.popularMoviesSubtitle': 'Desde blockbusters hasta cine independiente',
  'home.continueWatching': 'Continuar Viendo',
  'home.featured': 'Destacado',
  'home.browseCategoryTitle': 'Navegar por Categoría',

  // Authentication
  'auth.welcomeBack': '¡Bienvenido de nuevo!',
  'auth.signInToContinue': 'Inicia sesión para continuar en StreamVista',
  'auth.email': 'Correo electrónico',
  'auth.password': 'Contraseña',
  'auth.forgotPassword': '¿Olvidaste tu contraseña?',
  'auth.signIn': 'Iniciar Sesión',
  'auth.signUp': 'Registrarse',
  'auth.noAccount': '¿No tienes una cuenta?',
  'auth.haveAccount': '¿Ya tienes una cuenta?',
  'auth.createAccount': 'Crear Cuenta',
  'auth.termsAgree': 'Al registrarte, aceptas nuestros Términos y Política de Privacidad',
  'auth.rememberMe': 'Recordarme',

  // Downloads
  'downloads.title': 'Descargas',
  'downloads.subtitle': 'Mira tus series y películas favoritas sin conexión',
  'downloads.noDownloads': 'No hay descargas disponibles',
  'downloads.noDownloadsMessage': 'Descarga tus series y películas favoritas para verlas sin conexión en cualquier momento y lugar.',
  'downloads.browseContent': 'Explorar Contenido',
  'downloads.storage': 'Almacenamiento',
  'downloads.storageUsed': 'usado',
  'downloads.storageAvailable': 'disponible',
  'downloads.totalDownloads': 'Descargas Totales',
  'downloads.completed': 'completadas',
  'downloads.downloading': 'Descargando',
  'downloads.paused': 'pausadas',
  'downloads.allDownloads': 'Todas las Descargas',
  'downloads.completedFilter': 'Completadas',
  'downloads.downloadingFilter': 'Descargando',
  'downloads.pausedFilter': 'Pausadas',
  'downloads.deleteConfirmTitle': 'Eliminar Descarga',
  'downloads.deleteConfirmMessage': '¿Estás seguro de que quieres eliminar esta descarga? Esta acción no se puede deshacer.',
  'downloads.remaining': 'restante',
  'downloads.downloadSettings': 'Ajustes de Descarga',
  'downloads.wifiOnly': 'Solo Wi-Fi',
  'downloads.standardQuality': 'Calidad Estándar',
  'downloads.changeSettings': 'Cambiar ajustes',

  // Insights
  'insights.title': 'Estadísticas de Visualización',
  'insights.subtitle': 'Descubre tus hábitos de visualización y recomendaciones',
  'insights.watchTime': 'Tiempo de Visualización',
  'insights.watchStreak': 'días',
  'insights.completion': 'Finalización',
  'insights.overview': 'Resumen',
  'insights.watchHistory': 'Historial',
  'insights.forYou': 'Para Ti',
  'insights.total': 'Total',
  'insights.weeklyActivity': 'Actividad Semanal',
  'insights.peakViewingTime': 'Horario de Máxima Visualización',
  'insights.morning': 'Mañana',
  'insights.afternoon': 'Tarde',
  'insights.evening': 'Noche',
  'insights.night': 'Madrugada',
  'insights.contentPreferences': 'Preferencias de Contenido',
  'insights.contentType': 'Tipo de Contenido',
  'insights.favoriteGenres': 'Géneros Favoritos',
  'insights.completionRate': 'Tasa de Finalización',
  'insights.completed': 'Completado',
  'insights.topWatched': 'Más Vistos',
  'insights.continueWatching': 'Continuar Viendo',
  'insights.caughtUp': '¡Al día! No hay contenido sin terminar.',
  'insights.watched': 'Visto',
  'insights.noHistory': 'Sin historial de visualización',
  'insights.noHistoryMessage': 'No has visto ningún contenido todavía. Comienza a ver para crear tu historial.',
  'insights.shows': 'Series',
  'insights.movies': 'Películas',
  'insights.items': 'elementos',
  'insights.item': 'elemento',
  'insights.personalizedTitle': 'Personalizado Para Ti',
  'insights.personalizedMessage': 'Basado en tu historial de visualización, creemos que disfrutarás estos títulos.',
  'insights.topGenres': 'Tus géneros favoritos:',
  'insights.notEnoughData': 'Datos insuficientes',
  'insights.notEnoughDataMessage': 'Ve más contenido para obtener recomendaciones personalizadas basadas en tus gustos.',
  'insights.rewatch': 'Volver a ver',
  'insights.resume': 'Continuar',
  'insights.watched': 'visto',

  // Content details
  'content.episodes': 'Episodios',
  'content.season': 'Temporada',
  'content.details': 'Detalles',
  'content.starring': 'Protagonistas:',
  'content.creators': 'Creadores:',
  'content.genres': 'Géneros:',
  'content.relatedContent': 'Contenido Similar',
  'content.watchNow': 'Ver Ahora',
  'content.addToList': 'Añadir a Mi Lista',
  'content.share': 'Compartir',

  // Video player
  'player.pause': 'Pausar',
  'player.play': 'Reproducir',
  'player.mute': 'Silenciar',
  'player.unmute': 'Activar sonido',
  'player.fullscreen': 'Pantalla completa',
  'player.exitFullscreen': 'Salir de pantalla completa',
  'player.settings': 'Ajustes',
  'player.subtitles': 'Subtítulos',
  'player.quality': 'Calidad',
  'player.playbackSpeed': 'Velocidad de reproducción',
  'player.normal': 'Normal',
  'player.pictureInPicture': 'Imagen en imagen',
  'player.exitPictureInPicture': 'Salir de imagen en imagen',
  'player.theaterMode': 'Modo teatro',
  'player.autoNext': 'Reproducir siguiente episodio',
  'player.downloading': 'Descargando',
  'player.downloaded': 'Descargado',
  'player.watchOffline': 'Ver sin conexión',

  // Settings
  'settings.title': 'Configuración',
  'settings.account': 'Cuenta',
  'settings.preferences': 'Preferencias',
  'settings.playback': 'Reproducción y Calidad',
  'settings.notifications': 'Notificaciones',
  'settings.privacy': 'Privacidad y Seguridad',
  'settings.billing': 'Facturación y Suscripción',
  'settings.language': 'Idioma',
  'settings.appearance': 'Apariencia',
  'settings.theme': 'Tema',
  'settings.darkMode': 'Modo oscuro',
  'settings.lightMode': 'Modo claro',
  'settings.displayLanguage': 'Idioma de visualización',
  'settings.autoplayNext': 'Reproducir automáticamente el siguiente episodio',
  'settings.autoplayMessage': 'Reproducir automáticamente el siguiente episodio de una serie',
  'settings.videoQuality': 'Calidad de video',
  'settings.downloadQuality': 'Calidad de descarga',
  'settings.subtitlesDefault': 'Activar subtítulos por defecto',
  'settings.saveChanges': 'Guardar Cambios',
  'settings.saving': 'Guardando...',

  // Toast notifications
  'toast.downloadComplete': 'Descarga Completa',
  'toast.downloadCompleteMessage': '{title} está listo para ver sin conexión',
  'toast.downloadPaused': 'Descarga Pausada',
  'toast.downloadPausedMessage': '{title} ha sido pausado',
  'toast.downloadResumed': 'Descarga Reanudada',
  'toast.downloadResumedMessage': '{title} se está descargando de nuevo',
  'toast.downloadDeleted': 'Descarga Eliminada',
  'toast.downloadDeletedMessage': '{title} ha sido eliminado',
  'toast.addedToList': 'Añadido a la Lista',
  'toast.addedToListMessage': '{title} ha sido añadido a tu lista',
  'toast.removedFromList': 'Eliminado de la Lista',
  'toast.removedFromListMessage': '{title} ha sido eliminado de tu lista',
  'toast.settingsSaved': 'Configuración Guardada',
  'toast.settingsSavedMessage': 'Tu configuración se ha guardado correctamente',
};

export default translations;

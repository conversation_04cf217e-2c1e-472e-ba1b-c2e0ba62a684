import { NextRequest, NextResponse } from 'next/server';

/**
 * POST /api/admin/permissions/bulk
 * Apply role-based permissions to multiple users
 */
export async function POST(request: NextRequest) {
  try {
    // Try to get the user ID from multiple sources
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, check query parameters
    if (!userId) {
      const { searchParams } = new URL(request.url);
      userId = searchParams.get('userId');
    }

    // If still no userId, try to get it from the request body
    if (!userId) {
      try {
        const body = await request.json();
        userId = body.adminUserId; // Use a different field name to avoid confusion with userIds array
        // We need to clone the request since we've consumed the body
        request = new Request(request.url, {
          method: request.method,
          headers: request.headers,
          body: JSON.stringify(body),
        });
      } catch (error) {
        // Ignore JSON parsing errors
      }
    }

    if (!userId) {
      console.error('Admin bulk permissions API: No user ID found in request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const User = mongoose.default.models.User ||
                mongoose.default.model('User', new mongoose.default.Schema({
                  role: String,
                  name: String,
                  email: String,
                  permissions: mongoose.default.Schema.Types.Mixed
                }));

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the UserActivity schema directly
    const UserActivitySchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      action: String,
      details: String,
      ipAddress: String,
      userAgent: String,
      timestamp: { type: Date, default: Date.now },
      metadata: mongoose.default.Schema.Types.Mixed
    }, {
      timestamps: true
    });

    // Get the UserActivity model
    const UserActivity = mongoose.default.models.UserActivity ||
                        mongoose.default.model('UserActivity', UserActivitySchema);

    // Get request data
    const data = await request.json();

    // Validate request data
    if (!data.userIds || !Array.isArray(data.userIds) || data.userIds.length === 0) {
      return NextResponse.json(
        { error: 'User IDs are required' },
        { status: 400 }
      );
    }

    if (!data.role || !['user', 'moderator', 'admin'].includes(data.role)) {
      return NextResponse.json(
        { error: 'Valid role is required' },
        { status: 400 }
      );
    }

    // Validate user IDs
    const validUserIds = data.userIds.filter((id: string) => mongoose.default.Types.ObjectId.isValid(id));

    if (validUserIds.length === 0) {
      return NextResponse.json(
        { error: 'No valid user IDs provided' },
        { status: 400 }
      );
    }

    // Get users to update
    const users = await User.find({ _id: { $in: validUserIds } }).select('_id name email');

    if (users.length === 0) {
      return NextResponse.json(
        { error: 'No users found with the provided IDs' },
        { status: 404 }
      );
    }

    // Define default permissions for each role
    const defaultPermissions = {
      user: {
        canViewContent: true,
        canRateContent: true,
        canComment: true,
        canCreateLists: true,
        canShareContent: true
      },
      moderator: {
        canViewContent: true,
        canRateContent: true,
        canComment: true,
        canCreateLists: true,
        canShareContent: true,
        canModerateComments: true,
        canEditMetadata: true,
        canApproveContent: true
      },
      admin: {
        canViewContent: true,
        canRateContent: true,
        canComment: true,
        canCreateLists: true,
        canShareContent: true,
        canModerateComments: true,
        canEditMetadata: true,
        canApproveContent: true,
        canManageUsers: true,
        canManageContent: true,
        canManageSettings: true,
        canViewAnalytics: true,
        canManageRoles: true
      }
    };

    // Apply permissions to each user
    const results = await Promise.all(
      users.map(async (user) => {
        try {
          // Update user role and permissions directly
          await User.findByIdAndUpdate(user._id, {
            role: data.role,
            permissions: defaultPermissions[data.role as keyof typeof defaultPermissions]
          });

          return {
            userId: user._id.toString(),
            name: user.name,
            email: user.email,
            success: true
          };
        } catch (error) {
          console.error(`Error resetting permissions for user ${user._id}:`, error);

          return {
            userId: user._id.toString(),
            name: user.name,
            email: user.email,
            success: false,
            error: 'Failed to reset permissions'
          };
        }
      })
    );

    // Log admin activity directly
    await UserActivity.create({
      userId: new mongoose.default.Types.ObjectId(userId),
      type: 'admin',
      action: 'bulk_update_permissions',
      details: `Admin applied ${data.role} permissions to ${results.filter(r => r.success).length} users`,
      ipAddress: request.ip || request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      timestamp: new Date(),
      metadata: {
        role: data.role,
        userCount: results.length,
        successCount: results.filter(r => r.success).length
      }
    });

    // Return results
    return NextResponse.json({
      success: true,
      message: `Applied ${data.role} permissions to ${results.filter(r => r.success).length} of ${results.length} users`,
      results
    });
  } catch (error) {
    console.error('Error applying bulk permissions:', error);
    return NextResponse.json(
      { error: 'Failed to apply bulk permissions', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

'use client';

import Image from 'next/image';
import { motion } from 'framer-motion';

interface CastCrewCardProps {
  name: string;
  role?: string;
  imageUrl?: string | null;
  index: number;
}

export function CastCrewCard({ name, role, imageUrl, index }: CastCrewCardProps) {
  const fallbackImageUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(
    name
  )}&background=1a1a1a&color=fff&size=128`;

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: index * 0.02, duration: 0.3 }}
      className="group relative flex items-center bg-vista-dark/40 hover:bg-vista-dark/60 backdrop-blur-sm rounded-lg p-3 min-w-[280px] transition-all duration-300 hover:scale-[1.02] border border-vista-light/5 hover:border-vista-blue/20"
    >
      <div className="relative h-12 w-12 rounded-full overflow-hidden shadow-md flex-shrink-0 transition-transform duration-300 group-hover:scale-105">
        <Image
          src={imageUrl ? `https://image.tmdb.org/t/p/w185${imageUrl}` : fallbackImageUrl}
          alt={name}
          fill
          className="object-cover"
          unoptimized
        />
        <div className="absolute inset-0 bg-black/10 group-hover:bg-black/20 transition-colors duration-300" />
      </div>
      <div className="ml-3 flex-1 min-w-0">
        <p className="font-semibold text-vista-light text-sm leading-tight truncate">{name}</p>
        {role && (
          <p className="text-xs text-vista-light/60 leading-tight truncate mt-0.5">{role}</p>
        )}
      </div>
    </motion.div>
  );
}

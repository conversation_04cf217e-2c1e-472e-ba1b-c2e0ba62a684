import { NextRequest, NextResponse } from 'next/server';
import { pusherServer } from '@/lib/pusher-server';

/**
 * POST /api/admin/notifications/broadcast
 * Send a notification to all users
 */

// Define interface for user document from lean query
interface UserDocument {
  _id: string;
  role?: string;
}

export async function POST(request: NextRequest) {
  try {
    // Try to get the user ID from the cookie directly
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    // For POST requests, we'll try to extract the userId from the body
    if (!userId) {
      try {
        const bodyData = await request.json();
        userId = bodyData.userId;
        // Clone the request since we've consumed the body
        request = new NextRequest(request.url, {
          headers: request.headers,
          method: request.method,
          body: JSON.stringify(bodyData),
        });
      } catch (error) {
        // Ignore JSON parsing errors
      }
    }

    if (!userId) {
      console.error('Admin notifications broadcast API: No user ID found in request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const User = mongoose.default.models.User ||
                mongoose.default.model('User', new mongoose.default.Schema({
                  role: String,
                  email: String,
                  name: String
                }));

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean() as UserDocument;
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the Notification schema directly
    const NotificationSchema = new mongoose.default.Schema({
      title: String,
      message: String,
      type: String,
      userId: mongoose.default.Schema.Types.ObjectId,
      contentId: mongoose.default.Schema.Types.ObjectId,
      contentType: String,
      image: String,
      read: Boolean,
      createdAt: Date,
      expiresAt: Date,
      deletedBy: [mongoose.default.Schema.Types.ObjectId],
      isGlobal: Boolean
    }, {
      timestamps: true
    });

    // Get the Notification model
    const Notification = mongoose.default.models.Notification ||
                        mongoose.default.model('Notification', NotificationSchema);

    // Get notification data from request
    const notificationData = await request.json();

    // Log the notification data for debugging
    console.log('Notification data received:', {
      type: notificationData.type,
      title: notificationData.title,
      message: notificationData.message?.substring(0, 20) + '...',
      contentId: notificationData.contentId,
      contentType: notificationData.contentType,
      expiresAt: notificationData.expiresAt,
      hasExpiresAt: !!notificationData.expiresAt,
      expiresAtType: notificationData.expiresAt ? typeof notificationData.expiresAt : 'undefined'
    });

    // Validate required fields
    if (!notificationData.type || !notificationData.title || !notificationData.message) {
      return NextResponse.json(
        { error: 'Type, title and message are required' },
        { status: 400 }
      );
    }
    
    // Validate content-specific fields
    if (notificationData.type === 'new_content' || notificationData.type === 'recommendation') {
      if (!notificationData.contentId) {
        return NextResponse.json(
          { error: 'Content ID is required for content-related notifications' },
          { status: 400 }
        );
      }
      
      const isValidContentType = notificationData.contentType === 'movie' || notificationData.contentType === 'show';
      if (!notificationData.contentType || !isValidContentType) {
        return NextResponse.json(
          { error: 'Valid content type (movie or show) is required for content-related notifications' },
          { status: 400 }
        );
      }
    }

    // Get all users
    const users = await User.find({});

    if (users.length === 0) {
      return NextResponse.json({
        message: 'No users found',
        sentCount: 0
      });
    }

    // Create notifications for each user
    const notifications = [];
    const createdAt = new Date();

    // Parse expiresAt date if it exists
    let parsedExpiresAt;
    if (notificationData.expiresAt) {
      try {
        // Ensure we're working with a valid date string
        parsedExpiresAt = new Date(notificationData.expiresAt);

        // Validate the date
        if (isNaN(parsedExpiresAt.getTime())) {
          console.error('Invalid date format received:', notificationData.expiresAt);
          parsedExpiresAt = undefined;
        } else {
          // Ensure time is set to end of day (23:59:59) if not already
          if (parsedExpiresAt.getHours() === 0 &&
              parsedExpiresAt.getMinutes() === 0 &&
              parsedExpiresAt.getSeconds() === 0) {
            parsedExpiresAt.setHours(23, 59, 59, 999);
          }

          console.log('Parsed expiresAt date:', {
            original: notificationData.expiresAt,
            parsed: parsedExpiresAt,
            isValid: true,
            isoString: parsedExpiresAt.toISOString(),
            formattedLocal: parsedExpiresAt.toLocaleString()
          });
        }
      } catch (error) {
        console.error('Error parsing expiresAt date:', error);
        parsedExpiresAt = undefined;
      }
    }

    for (const user of users) {
      const notification = await Notification.create({
        userId: user._id,
        type: notificationData.type,
        title: notificationData.title,
        message: notificationData.message,
        contentId: notificationData.contentId,
        contentType: notificationData.contentType,
        image: notificationData.image,
        read: false,
        createdAt,
        expiresAt: parsedExpiresAt,
        deletedBy: [],
        isGlobal: true // Mark as a global notification sent by admin
      });

      // Log the created notification for debugging
      console.log('Created notification:', {
        id: notification._id.toString(),
        userId: notification.userId.toString(),
        expiresAt: notification.expiresAt,
        hasExpiresAt: !!notification.expiresAt
      });

      // Add notification to the batch
      notifications.push(notification);
    }

    // Send a single Pusher event after creating all notifications
    // This is more efficient than sending multiple events
    try {
      console.log('Broadcasting notification to all users');

      // First, trigger the global channel for all users
      console.log('Triggering global-notifications channel with new-notification event');
      await pusherServer.trigger(
        'global-notifications',
        'new-notification',
        {
          message: notificationData.message,
          timestamp: new Date().toISOString(),
          notificationId: notifications[0]?._id?.toString(),
          title: notificationData.title,
          type: notificationData.type,
          count: notifications.length,
          // Control whether to show a toast notification
          showToast: true,
          fullNotification: {
            _id: notifications[0]?._id?.toString(),
            userId: notifications[0]?.userId?.toString(), // Include userId which is required
            type: notificationData.type,
            title: notificationData.title,
            message: notificationData.message,
            contentId: notificationData.contentId,
            contentType: notificationData.contentType,
            image: notificationData.image,
            read: false,
            createdAt: createdAt.toISOString(),
            expiresAt: parsedExpiresAt ? parsedExpiresAt.toISOString() : undefined
          }
        }
      );

      // Then, trigger individual user channels for more reliable delivery
      for (const user of users) {
        try {
          // Find this user's specific notification
          const userNotification = notifications.find(n => n.userId.toString() === user._id.toString());

          if (userNotification) {
            console.log(`Triggering user-${user._id} channel with new-notification event`);
            await pusherServer.trigger(
              `user-${user._id}`,
              'new-notification',
              {
                message: notificationData.message,
                timestamp: new Date().toISOString(),
                notificationId: userNotification._id.toString(),
                title: notificationData.title,
                type: notificationData.type,
                // Control whether to show a toast notification
                showToast: true,
                // Include the full notification data for immediate UI updates
                fullNotification: {
                  _id: userNotification._id.toString(),
                  userId: userNotification.userId.toString(),
                  type: notificationData.type,
                  title: notificationData.title,
                  message: notificationData.message,
                  contentId: notificationData.contentId,
                  contentType: notificationData.contentType,
                  image: notificationData.image,
                  read: false,
                  createdAt: createdAt.toISOString(),
                  expiresAt: parsedExpiresAt ? parsedExpiresAt.toISOString() : undefined
                }
              }
            );
          }
        } catch (userError) {
          console.error(`Error sending notification to user ${user._id}:`, userError);
        }
      }
    } catch (error) {
      console.error('Error sending Pusher notification:', error);
    }
    return NextResponse.json({
      success: true,
      sentCount: notifications.length,
      notifications: notifications.map(n => n._id)
    });
  } catch (error) {
    console.error('Error broadcasting notification:', error);
    return NextResponse.json(
      { error: 'Failed to broadcast notification', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

import React, { useState } from 'react'
import { format } from 'date-fns'
import { MoreHorizontal, SmileIcon, Reply, Pencil, Trash2 } from 'lucide-react'
import { ChatMessageProps } from '@/types/chat'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { cn } from '@/lib/utils'
import data from '@emoji-mart/data'
import Picker from '@emoji-mart/react'

export function ChatMessage({
  message,
  currentUserId,
  onReaction,
  onReply,
  onEdit,
  onDelete
}: ChatMessageProps) {
  const [isEmojiPickerOpen, setIsEmojiPickerOpen] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [editedContent, setEditedContent] = useState(message.content)

  const isCurrentUser = message.sender.id === currentUserId
  const isSystem = message.type === 'system'
  const hasReactions = message.metadata?.reactions && Object.keys(message.metadata.reactions).length > 0

  const handleEmojiSelect = (emoji: any) => {
    onReaction?.(message.id, emoji.native)
    setIsEmojiPickerOpen(false)
  }

  const handleEdit = () => {
    if (isEditing) {
      onEdit?.(message.id, editedContent)
      setIsEditing(false)
    } else {
      setIsEditing(true)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleEdit()
    } else if (e.key === 'Escape') {
      setIsEditing(false)
      setEditedContent(message.content)
    }
  }

  if (isSystem) {
    return (
      <div className="flex justify-center py-2">
        <span className="text-sm text-muted-foreground">{message.content}</span>
      </div>
    )
  }

  return (
    <div className={cn(
      'group flex gap-3 py-2 px-4 hover:bg-muted/50 relative',
      isCurrentUser && 'flex-row-reverse'
    )}>
      <Avatar className="h-8 w-8">
        <AvatarImage src={message.sender.avatar} />
        <AvatarFallback>
          {message.sender.name.substring(0, 2).toUpperCase()}
        </AvatarFallback>
      </Avatar>

      <div className={cn(
        'flex flex-col max-w-[70%]',
        isCurrentUser && 'items-end'
      )}>
        <div className="flex items-center gap-2">
          <span className="text-sm font-semibold">
            {message.sender.name}
          </span>
          <span className="text-xs text-muted-foreground">
            {format(new Date(message.timestamp), 'HH:mm')}
          </span>
          {message.metadata?.edited && (
            <span className="text-xs text-muted-foreground">(edited)</span>
          )}
        </div>

        <div className="mt-1">
          {isEditing ? (
            <input
              type="text"
              value={editedContent}
              onChange={(e) => setEditedContent(e.target.value)}
              onKeyDown={handleKeyPress}
              className="w-full px-2 py-1 rounded border bg-background"
              autoFocus
            />
          ) : (
            <p className="text-sm whitespace-pre-wrap">{message.content}</p>
          )}
        </div>

        {hasReactions && (
          <div className="flex flex-wrap gap-1 mt-1">
            {Object.entries(message.metadata!.reactions!).map(([emoji, users]) => (
              <Button
                key={emoji}
                variant="ghost"
                size="sm"
                className="h-6 px-2 text-xs"
                onClick={() => onReaction?.(message.id, emoji)}
              >
                {emoji} {users.length}
              </Button>
            ))}
          </div>
        )}
      </div>

      <div className={cn(
        'absolute top-2 opacity-0 group-hover:opacity-100 transition-opacity',
        isCurrentUser ? 'left-4' : 'right-4',
        'flex items-center gap-1'
      )}>
        <Popover open={isEmojiPickerOpen} onOpenChange={setIsEmojiPickerOpen}>
          <PopoverTrigger asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <SmileIcon className="h-4 w-4" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0" side="top" align="end">
            <Picker
              data={data}
              onEmojiSelect={handleEmojiSelect}
              theme="dark"
              previewPosition="none"
              skinTonePosition="none"
            />
          </PopoverContent>
        </Popover>

        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8"
          onClick={() => onReply?.(message.id)}
        >
          <Reply className="h-4 w-4" />
        </Button>

        {isCurrentUser && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleEdit}>
                <Pencil className="h-4 w-4 mr-2" />
                {isEditing ? 'Save' : 'Edit'}
              </DropdownMenuItem>
              <DropdownMenuItem
                className="text-destructive"
                onClick={() => onDelete?.(message.id)}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </div>
  )
} 
"use client";

import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { CheckCircle } from 'lucide-react';

interface FeatureItem {
  id: number;
  text: string;
}

const features: FeatureItem[] = [
  { id: 1, text: "Exclusive originals you can't find anywhere else" },
  { id: 2, text: "Stream on up to 4 devices at the same time" },
  { id: 3, text: "Download and watch offline" },
  { id: 4, text: "No ads, no interruptions" },
  { id: 5, text: "Cancel anytime, no commitment" },
];

export default function CallToAction() {
  return (
    <section className="py-16 md:py-24 relative">
      {/* Background */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-vista-accent/20 via-black to-vista-accent-alt/10" />
      </div>

      <div className="container px-4 md:px-6 mx-auto relative z-10">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <motion.h2
            className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 text-gradient"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            Get Total Access to StreamVista
          </motion.h2>

          <motion.p
            className="text-lg md:text-xl text-vista-light/80 mb-8"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
          >
            Stream star-studded originals, blockbuster movies, and more
          </motion.p>

          <motion.div
            className="flex flex-col sm:flex-row justify-center gap-4 mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <Button
              size="lg"
              className="bg-vista-accent hover:bg-vista-accent-alt text-white text-lg py-7 px-8"
            >
              Start Free Trial
            </Button>

            <Button
              variant="outline"
              size="lg"
              className="border-vista-light/30 text-vista-light hover:bg-vista-light/10 text-lg py-7 px-8"
            >
              Learn More
            </Button>
          </motion.div>

          <motion.p
            className="text-xs md:text-sm text-vista-light/60"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
          >
            7-day free trial, then $9.99/month. Cancel anytime.
          </motion.p>
        </div>

        {/* Feature list */}
        <div className="max-w-2xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
            {features.map((feature, index) => (
              <motion.div
                key={feature.id}
                className="flex items-start gap-3"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 + (index * 0.1) }}
                viewport={{ once: true }}
              >
                <CheckCircle className="h-6 w-6 text-vista-accent flex-shrink-0 mt-0.5" />
                <p className="text-vista-light/90">{feature.text}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}

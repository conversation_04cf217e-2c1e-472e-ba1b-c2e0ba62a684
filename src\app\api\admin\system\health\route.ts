import { NextRequest, NextResponse } from 'next/server';
import os from 'os';

// GET handler to fetch system health information
export async function GET(request: NextRequest) {
  try {
    // Try to get the user ID from the cookie directly
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, try to get it from the request body
    if (!userId) {
      try {
        const body = await request.json();
        userId = body.userId;
      } catch (error) {
        // Ignore JSON parsing errors
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    if (!userId) {
      console.error('System health API: No user ID found in request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const User = mongoose.default.models.User ||
                mongoose.default.model('User', new mongoose.default.Schema({
                  role: String
                }));

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Check database health directly
    let dbHealth = { status: 'unknown', ping: 0, connections: { active: 0, available: 0 } };
    try {
      // Check MongoDB connection
      const adminDb = mongoose.default.connection.db.admin();
      const serverStatus = await adminDb.serverStatus();

      // Measure ping time
      const startTime = Date.now();
      await mongoose.default.connection.db.command({ ping: 1 });
      const pingTime = Date.now() - startTime;

      dbHealth = {
        status: 'healthy',
        ping: pingTime,
        connections: {
          active: serverStatus.connections.current,
          available: serverStatus.connections.available
        },
        version: serverStatus.version
      };
    } catch (dbError) {
      console.error('MongoDB health check failed:', dbError);
      dbHealth = {
        status: 'degraded',
        ping: 0,
        connections: { active: 0, available: 0 },
        error: dbError instanceof Error ? dbError.message : 'Unknown database error'
      };
    }

    // Get system information
    const systemInfo = {
      platform: os.platform(),
      arch: os.arch(),
      cpus: os.cpus().length,
      totalMemory: os.totalmem(),
      freeMemory: os.freemem(),
      uptime: os.uptime(),
      loadAverage: os.loadavg(),
    };

    // Calculate memory usage
    const memoryUsage = {
      total: Math.round(systemInfo.totalMemory / (1024 * 1024 * 1024) * 100) / 100, // GB
      free: Math.round(systemInfo.freeMemory / (1024 * 1024 * 1024) * 100) / 100, // GB
      used: Math.round((systemInfo.totalMemory - systemInfo.freeMemory) / (1024 * 1024 * 1024) * 100) / 100, // GB
      usedPercentage: Math.round((1 - systemInfo.freeMemory / systemInfo.totalMemory) * 100),
    };

    // Format uptime
    const uptimeFormatted = formatUptime(systemInfo.uptime);

    // Get process information
    const processInfo = {
      pid: process.pid,
      memoryUsage: process.memoryUsage(),
      uptime: process.uptime(),
      version: process.version,
    };

    // Format process memory usage
    const processMemoryUsage = {
      rss: Math.round(processInfo.memoryUsage.rss / (1024 * 1024) * 100) / 100, // MB
      heapTotal: Math.round(processInfo.memoryUsage.heapTotal / (1024 * 1024) * 100) / 100, // MB
      heapUsed: Math.round(processInfo.memoryUsage.heapUsed / (1024 * 1024) * 100) / 100, // MB
      external: Math.round(processInfo.memoryUsage.external / (1024 * 1024) * 100) / 100, // MB
    };

    // Return system health information
    return NextResponse.json({
      timestamp: new Date().toISOString(),
      status: dbHealth.status === 'healthy' ? 'healthy' : 'degraded',
      database: dbHealth,
      system: {
        ...systemInfo,
        uptimeFormatted,
        memoryUsage,
        cpuUsage: Math.round(systemInfo.loadAverage[0] * 100 / systemInfo.cpus), // Approximate CPU usage percentage
      },
      process: {
        ...processInfo,
        memoryUsage: processMemoryUsage,
        uptimeFormatted: formatUptime(processInfo.uptime),
      },
    });
  } catch (error) {
    console.error('Error fetching system health:', error);
    return NextResponse.json(
      { error: 'Failed to fetch system health information', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// Helper function to format uptime
function formatUptime(uptime: number): string {
  const days = Math.floor(uptime / (24 * 60 * 60));
  const hours = Math.floor((uptime % (24 * 60 * 60)) / (60 * 60));
  const minutes = Math.floor((uptime % (60 * 60)) / 60);
  const seconds = Math.floor(uptime % 60);

  return `${days}d ${hours}h ${minutes}m ${seconds}s`;
}

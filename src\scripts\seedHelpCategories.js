/**
 * Help Categories Seeding Script (JavaScript version)
 * Run with: node src/scripts/seedHelpCategories.js
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables from .env.local and .env
// Resolve paths relative to the project root
const projectRoot = path.resolve(__dirname, '../..');
dotenv.config({ path: path.join(projectRoot, '.env.local') });
dotenv.config({ path: path.join(projectRoot, '.env') });

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI;
const MONGODB_DB = process.env.MONGODB_DB || 'streamvista';

if (!MONGODB_URI) {
  console.error('❌ MONGODB_URI environment variable is not defined');
  console.error('Make sure .env.local file exists with MONGODB_URI variable');
  process.exit(1);
}

// Help Category Schema
const HelpCategorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  icon: {
    type: String,
    required: true,
    trim: true
  },
  color: {
    type: String,
    required: true,
    trim: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  sortOrder: {
    type: Number,
    default: 0
  },
  parentCategory: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'HelpCategory'
  },
  subCategories: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'HelpCategory'
  }],
  ticketCount: {
    type: Number,
    default: 0
  },
  averageResolutionTime: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

const defaultCategories = [
  {
    name: 'Subscription & Billing',
    slug: 'subscription',
    description: 'Questions about your subscription, billing, and payments',
    icon: 'CreditCard',
    color: '#3B82F6',
    isActive: true,
    sortOrder: 1
  },
  {
    name: 'Technical Support',
    slug: 'technical',
    description: 'Streaming issues, app problems, and technical difficulties',
    icon: 'Settings',
    color: '#10B981',
    isActive: true,
    sortOrder: 2
  },
  {
    name: 'Account Management',
    slug: 'account',
    description: 'Profile settings, password reset, and account security',
    icon: 'FileText',
    color: '#8B5CF6',
    isActive: true,
    sortOrder: 3
  },
  {
    name: 'Content & Features',
    slug: 'content',
    description: 'Questions about shows, movies, and platform features',
    icon: 'HelpCircle',
    color: '#F59E0B',
    isActive: true,
    sortOrder: 4
  },
  {
    name: 'Report a Bug',
    slug: 'bug_report',
    description: 'Found something broken? Let us know so we can fix it',
    icon: 'Bug',
    color: '#EF4444',
    isActive: true,
    sortOrder: 5
  },
  {
    name: 'Feature Request',
    slug: 'feature_request',
    description: 'Suggest new features or improvements to StreamVista',
    icon: 'Lightbulb',
    color: '#F59E0B',
    isActive: true,
    sortOrder: 6
  },
  {
    name: 'Billing Issues',
    slug: 'billing',
    description: 'Payment problems, refunds, and billing inquiries',
    icon: 'CreditCard',
    color: '#DC2626',
    isActive: true,
    sortOrder: 7
  },
  {
    name: 'Other',
    slug: 'other',
    description: 'General questions and other inquiries',
    icon: 'MessageSquare',
    color: '#6B7280',
    isActive: true,
    sortOrder: 8
  }
];

async function seedHelpCategories() {
  try {
    console.log('🚀 Connecting to MongoDB...');
    
    await mongoose.connect(MONGODB_URI, {
      dbName: MONGODB_DB,
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 20000,
      connectTimeoutMS: 10000,
    });
    
    console.log('✅ Connected to MongoDB');
    
    // Get or create the HelpCategory model
    const HelpCategory = mongoose.models.HelpCategory || mongoose.model('HelpCategory', HelpCategorySchema);
    
    console.log('🌱 Seeding help categories...');
    
    // Check if categories already exist
    const existingCategories = await HelpCategory.find();
    if (existingCategories.length > 0) {
      console.log('✅ Help categories already exist. Skipping seed.');
      console.log(`📊 Found ${existingCategories.length} existing categories`);
      return { success: true, message: 'Categories already exist', count: existingCategories.length };
    }
    
    let createdCount = 0;
    
    // Create categories
    for (const categoryData of defaultCategories) {
      const existingCategory = await HelpCategory.findOne({ slug: categoryData.slug });
      if (!existingCategory) {
        const category = new HelpCategory(categoryData);
        await category.save();
        console.log(`✅ Created category: ${categoryData.name}`);
        createdCount++;
      } else {
        console.log(`⚠️  Category already exists: ${categoryData.name}`);
      }
    }
    
    console.log(`🎉 Help categories seeded successfully! Created ${createdCount} categories.`);
    return { success: true, message: 'Categories seeded successfully', count: createdCount };
    
  } catch (error) {
    console.error('❌ Error seeding help categories:', error);
    throw error;
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the seeder if this file is executed directly
if (require.main === module) {
  seedHelpCategories()
    .then((result) => {
      console.log('✅ Seeding completed:', result.message);
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Seeding failed:', error);
      process.exit(1);
    });
}

module.exports = { seedHelpCategories };

import { NextRequest, NextResponse } from 'next/server';
import { getAdminUserId, verifyAdmin } from '@/lib/admin-auth';
import { ensureMongooseConnection } from '@/lib/mongodb';
import User from '@/models/User';
import UserActivity from '@/models/UserActivity';
import { isValidObjectId } from 'mongoose';
import logger from '@/lib/logger';
import { createErrorResponse, withTimeout, generateRequestId, OperationalError, ErrorContext } from '@/lib/error-handler';
import mongoose from 'mongoose';

/**
 * PUT /api/admin/users/bulk
 * Perform bulk actions on users
 */
export async function PUT(request: NextRequest) {
  const startTime = Date.now();
  const requestId = generateRequestId();
  let adminEmail: string | undefined;

  const errorContext: ErrorContext = {
    operation: 'bulk_user_update',
    requestId,
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    netlify: process.env.NETLIFY === 'true'
  };

  try {
    // Verify admin authentication with timeout
    const adminSession = await withTimeout(
      verifyAdmin(request),
      10000,
      'Admin verification'
    );

    if (!adminSession) {
      throw new OperationalError('Unauthorized access', 401, 'AUTH_FAILED');
    }

    adminEmail = adminSession.user.email;
    errorContext.adminEmail = adminEmail;

    // Connect to database with timeout
    await withTimeout(
      ensureMongooseConnection(),
      15000,
      'Database connection'
    );

    // Define the UserActivity schema directly
    const UserActivitySchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      action: String,
      details: String,
      ipAddress: String,
      userAgent: String,
      timestamp: { type: Date, default: Date.now },
      metadata: mongoose.default.Schema.Types.Mixed
    }, {
      timestamps: true
    });

    // Get the UserActivity model
    const UserActivity = mongoose.default.models.UserActivity ||
                        mongoose.default.model('UserActivity', UserActivitySchema);

    // Get request data
    const data = await request.json();
    const { userIds, action } = data;

    // Validate request
    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return NextResponse.json({ error: 'No users specified' }, { status: 400 });
    }

    if (!action) {
      return NextResponse.json({ error: 'No action specified' }, { status: 400 });
    }

    // Validate user IDs
    const validUserIds = userIds.filter(id => mongoose.default.Types.ObjectId.isValid(id));
    if (validUserIds.length === 0) {
      return NextResponse.json({ error: 'No valid user IDs provided' }, { status: 400 });
    }

    let result;

    // Perform the requested action
    switch (action) {
      case 'changeRole':
        const { role } = data;
        if (!role || !['user', 'moderator', 'admin'].includes(role)) {
          return NextResponse.json({ error: 'Invalid role specified' }, { status: 400 });
        }

        result = await User.updateMany(
          { _id: { $in: validUserIds } },
          { $set: { role } }
        );

        // Log admin activity directly
        try {
          await UserActivity.create({
            userId: new mongoose.default.Types.ObjectId(userId),
            type: 'admin',
            action: 'bulk_change_role',
            details: `Admin changed role to ${role} for ${result.modifiedCount} users`,
            ipAddress: request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
            userAgent: request.headers.get('user-agent') || 'unknown',
            timestamp: new Date(),
            metadata: {
              userIds: validUserIds,
              role,
              affectedCount: result.modifiedCount
            }
          });
        } catch (logError) {
          console.error('Error logging admin activity:', logError);
          // Continue even if logging fails
        }

        return NextResponse.json({
          message: `Updated ${result.modifiedCount} users to role: ${role}`,
          modifiedCount: result.modifiedCount
        });

      case 'changeVerification':
        const { verified } = data;
        if (verified === undefined) {
          return NextResponse.json({ error: 'Verification status not specified' }, { status: 400 });
        }

        result = await User.updateMany(
          { _id: { $in: validUserIds } },
          { $set: { emailVerified: verified ? new Date() : null } }
        );

        // Log admin activity directly
        try {
          await UserActivity.create({
            userId: new mongoose.default.Types.ObjectId(userId),
            type: 'admin',
            action: 'bulk_change_verification',
            details: `Admin ${verified ? 'verified' : 'unverified'} ${result.modifiedCount} users`,
            ipAddress: request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
            userAgent: request.headers.get('user-agent') || 'unknown',
            timestamp: new Date(),
            metadata: {
              userIds: validUserIds,
              verified,
              affectedCount: result.modifiedCount
            }
          });
        } catch (logError) {
          console.error('Error logging admin activity:', logError);
          // Continue even if logging fails
        }

        return NextResponse.json({
          message: `Updated verification status for ${result.modifiedCount} users`,
          modifiedCount: result.modifiedCount
        });

      default:
        return NextResponse.json({ error: 'Invalid action specified' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error performing bulk user action:', error);
    return NextResponse.json(
      { error: 'Failed to perform bulk action on users', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/users/bulk
 * Delete multiple users
 */
export async function DELETE(request: NextRequest) {
  const startTime = Date.now();
  const requestId = generateRequestId();
  let adminEmail: string | undefined;

  const errorContext: ErrorContext = {
    operation: 'bulk_user_deletion',
    requestId,
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    netlify: process.env.NETLIFY === 'true'
  };

  try {
    // Verify admin authentication with timeout
    const adminSession = await withTimeout(
      verifyAdmin(request),
      10000,
      'Admin verification'
    );

    if (!adminSession) {
      throw new OperationalError('Unauthorized access', 401, 'AUTH_FAILED');
    }

    adminEmail = adminSession.user.email;
    errorContext.adminEmail = adminEmail;

    // Connect to database with timeout
    await withTimeout(
      ensureMongooseConnection(),
      15000,
      'Database connection'
    );

    // Get request data with timeout
    const data = await withTimeout(
      request.json(),
      5000,
      'Request data parsing'
    );
    const { userIds } = data;

    // Validate request
    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      throw new OperationalError('No users specified', 400, 'INVALID_REQUEST');
    }

    // Validate user IDs
    const validUserIds = userIds.filter(id => isValidObjectId(id));
    if (validUserIds.length === 0) {
      throw new OperationalError('No valid user IDs provided', 400, 'INVALID_USER_IDS');
    }

    errorContext.userIds = validUserIds;
    errorContext.userCount = validUserIds.length;

    // Check for superadmin users before deletion
    const usersToDelete = await withTimeout(
      User.find({ _id: { $in: validUserIds } }).select('_id email role'),
      10000,
      'User lookup for validation'
    );

    const superadminUsers = usersToDelete.filter(user => user.role === 'superadmin');
    if (superadminUsers.length > 0) {
      throw new OperationalError(
        `Cannot delete superadmin accounts: ${superadminUsers.map(u => u.email).join(', ')}`,
        403,
        'SUPERADMIN_DELETE_FORBIDDEN'
      );
    }

    // Delete users with timeout
    const result = await withTimeout(
      User.deleteMany({ _id: { $in: validUserIds } }),
      20000,
      'Bulk user deletion'
    );

    const duration = Date.now() - startTime;
    errorContext.duration = `${duration}ms`;
    errorContext.deletedCount = result.deletedCount;

    // Log admin activity
    logger.info(
      `Admin ${adminEmail} bulk deleted ${result.deletedCount} users`,
      {
        ...errorContext,
        success: true,
        deletedUserIds: validUserIds
      }
    );

    // Also log to UserActivity for audit trail
    try {
      await withTimeout(
        UserActivity.create({
          userId: new mongoose.Types.ObjectId(adminSession.userId),
          type: 'admin',
          action: 'bulk_delete_users',
          details: `Admin ${adminEmail} deleted ${result.deletedCount} users`,
          ipAddress: request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown',
          timestamp: new Date(),
          metadata: {
            userIds: validUserIds,
            affectedCount: result.deletedCount,
            requestId,
            duration: `${duration}ms`
          }
        }),
        5000,
        'Activity logging'
      );
    } catch (logError) {
      logger.warning('Failed to log admin activity to database', {
        error: logError instanceof Error ? logError.message : String(logError),
        ...errorContext
      });
      // Continue even if logging fails
    }

    return NextResponse.json({
      message: `Successfully deleted ${result.deletedCount} users`,
      deletedCount: result.deletedCount,
      duration: `${duration}ms`,
      requestId
    });
  } catch (error) {
    const duration = Date.now() - startTime;
    errorContext.duration = `${duration}ms`;
    errorContext.adminEmail = adminEmail || 'unknown';

    // Use enhanced error handling
    return createErrorResponse(
      error instanceof Error ? error : new Error(String(error)),
      errorContext,
      process.env.NODE_ENV === 'development'
    );
  }
}

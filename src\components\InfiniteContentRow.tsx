"use client";

import { useState, useRef, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { ChevronRight, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import ContentCard from './ContentCard';
import { ContentCardType } from '@/lib/content-utils';

interface InfiniteContentRowProps {
  title: string;
  subtitle?: string;
  seeAllLink?: string;
  initialContents: ContentCardType[];
  fetchMoreContents?: (page: number) => Promise<ContentCardType[]>;
  itemsPerPage?: number;
}

export default function InfiniteContentRow({
  title,
  subtitle,
  seeAllLink,
  initialContents,
  fetchMoreContents,
  itemsPerPage = 20
}: InfiniteContentRowProps) {
  // Core state
  const [contents, setContents] = useState<ContentCardType[]>(initialContents);
  const [page, setPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(!!fetchMoreContents);

  // Create a unique key for this row to store its scroll position
  const rowKey = `infinite-row-${title.replace(/\s+/g, '-').toLowerCase()}`;

  // Scroll navigation state
  const rowRef = useRef<HTMLDivElement>(null);
  const [showLeftButton, setShowLeftButton] = useState(false);
  const [showRightButton, setShowRightButton] = useState(false);
  const isNavigatingRef = useRef(false);

  // Intersection observer for infinite scroll
  const loadTriggerRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const isLoadingRef = useRef(false);
  const lastScrollPositionRef = useRef(0);
  const pageScrollPositionRef = useRef(0);

  // Load more content function with enhanced scroll position preservation
  const loadMore = useCallback(async () => {
    if (isLoadingRef.current || !hasMore || !fetchMoreContents) return;

    isLoadingRef.current = true;
    setIsLoading(true);

    // Preserve both horizontal scroll position and page scroll position
    const scrollContainer = rowRef.current;
    const preserveScrollLeft = scrollContainer?.scrollLeft || 0;
    const preservePageScrollY = window.scrollY;

    // Store the current scroll positions
    lastScrollPositionRef.current = preserveScrollLeft;
    pageScrollPositionRef.current = preservePageScrollY;

    // Create a scroll lock to prevent page jumping during content loading
    const scrollLock = (e: Event) => {
      e.preventDefault();
      window.scrollTo({ top: preservePageScrollY, behavior: 'instant' });
    };

    // Add scroll lock during loading
    window.addEventListener('scroll', scrollLock, { passive: false });

    try {
      const nextPage = page + 1;
      const newContents = await fetchMoreContents(nextPage);

      if (newContents.length > 0) {
        // Filter out duplicates
        const uniqueNewContents = newContents.filter(newItem =>
          !contents.some(existingItem => existingItem.id === newItem.id)
        );

        if (uniqueNewContents.length > 0) {
          setContents(prev => [...prev, ...uniqueNewContents]);
          setPage(nextPage);

          // Enhanced scroll position restoration
          const restoreScrollPosition = () => {
            // Restore horizontal scroll position
            if (scrollContainer) {
              scrollContainer.scrollLeft = preserveScrollLeft;
            }
            // Restore page scroll position
            window.scrollTo({ top: preservePageScrollY, behavior: 'instant' });
          };

          // Use multiple timing strategies to ensure scroll position is preserved
          requestAnimationFrame(restoreScrollPosition);
          setTimeout(restoreScrollPosition, 0);
          setTimeout(restoreScrollPosition, 50);
          setTimeout(restoreScrollPosition, 100);

          // Remove scroll lock after content is fully loaded and positioned
          setTimeout(() => {
            window.removeEventListener('scroll', scrollLock);
          }, 200);

          // Continue loading as long as we get the expected number of items
          // This creates truly infinite scroll without arbitrary limits
          const shouldContinue = newContents.length === itemsPerPage;
          setHasMore(shouldContinue);
        } else {
          setHasMore(false);
        }
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error(`[InfiniteScroll ${title}] Error loading more:`, error);
      setHasMore(false);
      // Remove scroll lock on error
      window.removeEventListener('scroll', scrollLock);
    } finally {
      setIsLoading(false);
      isLoadingRef.current = false;
    }
  }, [hasMore, page, fetchMoreContents, itemsPerPage, contents, title]);

  // Set up intersection observer for infinite scroll with enhanced stability
  useEffect(() => {
    if (!hasMore || !fetchMoreContents || !loadTriggerRef.current || !rowRef.current) return;

    const options = {
      root: rowRef.current, // Use the scroll container as root to prevent page scroll interference
      rootMargin: '20px', // Minimal margin to prevent premature triggering
      threshold: 0.8 // High threshold for precise triggering only when element is mostly visible
    };

    let debounceTimeout: NodeJS.Timeout;
    let isObserving = true;

    const observer = new IntersectionObserver((entries) => {
      const [entry] = entries;

      // Only trigger if we're still observing and not already loading
      if (entry.isIntersecting && !isLoadingRef.current && isObserving) {
        // Additional check: only trigger if we're scrolled significantly to the right
        const scrollContainer = rowRef.current;
        const scrollProgress = scrollContainer ?
          scrollContainer.scrollLeft / (scrollContainer.scrollWidth - scrollContainer.clientWidth) : 0;

        // Only load more if we've scrolled at least 70% through the current content
        if (scrollProgress > 0.7) {
          // Debounce to prevent multiple rapid calls
          clearTimeout(debounceTimeout);
          debounceTimeout = setTimeout(() => {
            if (isObserving) {
              loadMore();
            }
          }, 150);
        }
      }
    }, options);

    observerRef.current = observer;
    observer.observe(loadTriggerRef.current);

    return () => {
      isObserving = false;
      clearTimeout(debounceTimeout);
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [hasMore, fetchMoreContents, loadMore]);

  // Handle scroll navigation buttons
  const updateScrollButtons = useCallback(() => {
    if (!rowRef.current) return;

    const { scrollLeft, scrollWidth, clientWidth } = rowRef.current;
    const canScroll = scrollWidth > clientWidth;

    setShowLeftButton(canScroll && scrollLeft > 10);
    setShowRightButton(canScroll && scrollLeft < scrollWidth - clientWidth - 10);
  }, []);

  // Set up scroll listener
  useEffect(() => {
    const scrollContainer = rowRef.current;
    if (!scrollContainer) return;

    updateScrollButtons();
    scrollContainer.addEventListener('scroll', updateScrollButtons, { passive: true });

    return () => {
      scrollContainer.removeEventListener('scroll', updateScrollButtons);
    };
  }, [updateScrollButtons, contents.length]);

  // Global scroll position monitoring to prevent unwanted page jumps
  useEffect(() => {
    const handleScroll = () => {
      // Only update if we're not currently navigating
      if (!isNavigatingRef.current && !isLoadingRef.current) {
        pageScrollPositionRef.current = window.scrollY;
      }
    };

    const handleUnexpectedScroll = () => {
      // If page scroll changes unexpectedly during navigation or loading, restore it
      if ((isNavigatingRef.current || isLoadingRef.current) &&
          Math.abs(window.scrollY - pageScrollPositionRef.current) > 5) {
        window.scrollTo({ top: pageScrollPositionRef.current, behavior: 'instant' });
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    // Monitor for unexpected scroll changes
    const scrollMonitor = setInterval(handleUnexpectedScroll, 50);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearInterval(scrollMonitor);
    };
  }, []);

  // Restore and save scroll position for this specific row
  useEffect(() => {
    const scrollContainer = rowRef.current;
    if (!scrollContainer) return;

    // Restore saved scroll position on mount
    const savedScrollLeft = sessionStorage.getItem(`${rowKey}-scroll`);
    if (savedScrollLeft) {
      scrollContainer.scrollLeft = parseInt(savedScrollLeft, 10);
    }

    // Save scroll position when it changes
    const saveScrollPosition = () => {
      sessionStorage.setItem(`${rowKey}-scroll`, scrollContainer.scrollLeft.toString());
    };

    scrollContainer.addEventListener('scroll', saveScrollPosition, { passive: true });

    return () => {
      scrollContainer.removeEventListener('scroll', saveScrollPosition);
    };
  }, [rowKey]);

  // Page-level scroll position memory
  useEffect(() => {
    // Restore page scroll position on mount
    const savedPageScroll = sessionStorage.getItem('homepage-scroll');
    if (savedPageScroll) {
      const scrollY = parseInt(savedPageScroll, 10);
      window.scrollTo({ top: scrollY, behavior: 'instant' });
      pageScrollPositionRef.current = scrollY;
    }

    // Save page scroll position periodically
    const savePageScroll = () => {
      if (!isNavigatingRef.current && !isLoadingRef.current) {
        sessionStorage.setItem('homepage-scroll', window.scrollY.toString());
      }
    };

    const scrollSaveInterval = setInterval(savePageScroll, 1000);

    // Save on page unload
    const handleBeforeUnload = () => {
      sessionStorage.setItem('homepage-scroll', window.scrollY.toString());
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      clearInterval(scrollSaveInterval);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  // Scroll functions with enhanced page scroll prevention
  const scrollLeft = (e?: React.MouseEvent) => {
    e?.preventDefault();
    e?.stopPropagation();

    if (rowRef.current && !isNavigatingRef.current) {
      isNavigatingRef.current = true;

      // Store current page scroll position
      const currentPageScroll = window.scrollY;

      // Create a scroll lock to prevent any page scroll during navigation
      const scrollLock = () => {
        if (window.scrollY !== currentPageScroll) {
          window.scrollTo({ top: currentPageScroll, behavior: 'instant' });
        }
      };

      // Add temporary scroll listener
      window.addEventListener('scroll', scrollLock, { passive: false });

      rowRef.current.scrollBy({ left: -rowRef.current.clientWidth * 0.8, behavior: 'smooth' });

      // Remove scroll lock after animation completes
      setTimeout(() => {
        window.removeEventListener('scroll', scrollLock);
        isNavigatingRef.current = false;
      }, 500);
    }
  };

  const scrollRight = (e?: React.MouseEvent) => {
    e?.preventDefault();
    e?.stopPropagation();

    if (rowRef.current && !isNavigatingRef.current) {
      isNavigatingRef.current = true;

      // Store current page scroll position
      const currentPageScroll = window.scrollY;

      // Create a scroll lock to prevent any page scroll during navigation
      const scrollLock = () => {
        if (window.scrollY !== currentPageScroll) {
          window.scrollTo({ top: currentPageScroll, behavior: 'instant' });
        }
      };

      // Add temporary scroll listener
      window.addEventListener('scroll', scrollLock, { passive: false });

      rowRef.current.scrollBy({ left: rowRef.current.clientWidth * 0.8, behavior: 'smooth' });

      // Remove scroll lock after animation completes
      setTimeout(() => {
        window.removeEventListener('scroll', scrollLock);
        isNavigatingRef.current = false;
      }, 500);
    }
  };

  // Always render to prevent hydration mismatches
  // The intersection observer will only work after mounting

  return (
    <section className="py-6 md:py-8">
      <div className="container px-4 md:px-6 mx-auto">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl md:text-2xl font-semibold text-vista-light tracking-tight">
              {title}
            </h2>
            {subtitle && (
              <p className="text-sm text-vista-light/70 mt-1">{subtitle}</p>
            )}
          </div>
          {seeAllLink && (
            <Link href={seeAllLink}>
              <Button variant="ghost" size="sm" className="text-vista-light hover:text-vista-light/80 hover:bg-vista-dark-lighter">
                See All <ChevronRight className="ml-1 h-4 w-4" />
              </Button>
            </Link>
          )}
        </div>

        <div className="relative">
          {showLeftButton && (
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-black/60 text-vista-light hover:bg-black/80 rounded-full h-8 w-8 shadow-md"
              onClick={scrollLeft}
              onMouseDown={(e) => e.preventDefault()}
              onFocus={(e) => e.preventDefault()}
              aria-label="Scroll left"
            >
              <ChevronRight className="h-5 w-5 rotate-180" />
            </Button>
          )}

          <div
            ref={rowRef}
            className="flex space-x-3 md:space-x-4 overflow-x-auto scrollbar-hide pb-1 -mx-1 px-1 pt-1"
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {contents.map((content, index) => (
              <div
                key={`${content.id}-${index}`}
                className="flex-none w-32 sm:w-[170px] md:w-[170px] lg:w-[170px]"
              >
                <ContentCard
                  id={content.id}
                  title={content.title}
                  imagePath={content.imagePath}
                  type={content.type}
                  year={content.year}
                  ageRating={content.ageRating}
                  index={index}
                  link={`/watch/${content.id}?forcePlay=true&contentType=${content.type === 'shows' ? 'show' : 'movie'}`}
                  isAwardWinning={content.isAwardWinning}
                  dataSource={content.dataSource}
                />
              </div>
            ))}

            {/* Infinite scroll trigger - minimal and positioned at the end */}
            {hasMore && (
              <div
                ref={loadTriggerRef}
                className="flex-none w-1 h-full opacity-0 pointer-events-none"
                aria-hidden="true"
              />
            )}

            {/* Loading indicator - separate from trigger to prevent scroll issues */}
            {isLoading && (
              <div className="flex-none w-32 sm:w-[170px] md:w-[170px] lg:w-[170px] flex items-center justify-center">
                <Loader2 className="h-8 w-8 text-vista-blue animate-spin" />
              </div>
            )}
          </div>

          {showRightButton && (
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-black/60 text-vista-light hover:bg-black/80 rounded-full h-8 w-8 shadow-md"
              onClick={scrollRight}
              onMouseDown={(e) => e.preventDefault()}
              onFocus={(e) => e.preventDefault()}
              aria-label="Scroll right"
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          )}
        </div>
      </div>
    </section>
  );
}


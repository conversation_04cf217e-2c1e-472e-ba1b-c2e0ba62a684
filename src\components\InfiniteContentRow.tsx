"use client";

import { useState, useRef, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { ChevronRight, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import ContentCard from './ContentCard';
import { ContentCardType } from '@/lib/content-utils';

interface InfiniteContentRowProps {
  title: string;
  subtitle?: string;
  seeAllLink?: string;
  initialContents: ContentCardType[];
  fetchMoreContents?: (page: number) => Promise<ContentCardType[]>;
  itemsPerPage?: number;
}

export default function InfiniteContentRow({
  title,
  subtitle,
  seeAllLink,
  initialContents,
  fetchMoreContents,
  itemsPerPage = 20
}: InfiniteContentRowProps) {
  // Core state
  const [contents, setContents] = useState<ContentCardType[]>(initialContents);
  const [page, setPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(!!fetchMoreContents);

  // Scroll navigation state
  const rowRef = useRef<HTMLDivElement>(null);
  const [showLeftButton, setShowLeftButton] = useState(false);
  const [showRightButton, setShowRightButton] = useState(false);

  // Intersection observer for infinite scroll
  const loadTriggerRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const isLoadingRef = useRef(false);
  const lastScrollPositionRef = useRef(0);

  // Simple load more function
  const loadMore = useCallback(async () => {
    if (isLoadingRef.current || !hasMore || !fetchMoreContents) return;

    isLoadingRef.current = true;
    setIsLoading(true);

    // Preserve horizontal scroll position only
    const scrollContainer = rowRef.current;
    const preserveScrollLeft = scrollContainer?.scrollLeft || 0;
    lastScrollPositionRef.current = preserveScrollLeft;

    try {
      const nextPage = page + 1;
      const newContents = await fetchMoreContents(nextPage);

      if (newContents && newContents.length > 0) {
        // Filter out duplicates
        const uniqueNewContents = newContents.filter(newItem =>
          !contents.some(existingItem => existingItem.id === newItem.id)
        );

        if (uniqueNewContents.length > 0) {
          setContents(prev => [...prev, ...uniqueNewContents]);
          setPage(nextPage);

          // Restore horizontal scroll position
          requestAnimationFrame(() => {
            if (scrollContainer) {
              scrollContainer.scrollLeft = preserveScrollLeft;
            }
          });

          const shouldContinue = newContents.length === itemsPerPage;
          setHasMore(shouldContinue);
        } else {
          setHasMore(false);
        }
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error(`[InfiniteScroll ${title}] Error loading more:`, error);
      setHasMore(false);
    } finally {
      setIsLoading(false);
      isLoadingRef.current = false;
    }
  }, [hasMore, page, fetchMoreContents, itemsPerPage, contents, title]);

  // Set up intersection observer for infinite scroll with enhanced stability
  useEffect(() => {
    if (!hasMore || !fetchMoreContents || !loadTriggerRef.current || !rowRef.current) return;

    const options = {
      root: rowRef.current, // Use the scroll container as root to prevent page scroll interference
      rootMargin: '20px', // Minimal margin to prevent premature triggering
      threshold: 0.8 // High threshold for precise triggering only when element is mostly visible
    };

    let debounceTimeout: NodeJS.Timeout;
    let isObserving = true;

    const observer = new IntersectionObserver((entries) => {
      const [entry] = entries;

      // Only trigger if we're still observing and not already loading
      if (entry.isIntersecting && !isLoadingRef.current && isObserving) {
        // Additional check: only trigger if we're scrolled significantly to the right
        const scrollContainer = rowRef.current;
        const scrollProgress = scrollContainer ?
          scrollContainer.scrollLeft / (scrollContainer.scrollWidth - scrollContainer.clientWidth) : 0;

        // Only load more if we've scrolled at least 70% through the current content
        if (scrollProgress > 0.7) {
          // Debounce to prevent multiple rapid calls
          clearTimeout(debounceTimeout);
          debounceTimeout = setTimeout(() => {
            if (isObserving) {
              loadMore();
            }
          }, 150);
        }
      }
    }, options);

    observerRef.current = observer;
    observer.observe(loadTriggerRef.current);

    return () => {
      isObserving = false;
      clearTimeout(debounceTimeout);
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [hasMore, fetchMoreContents, loadMore]);

  // Handle scroll navigation buttons
  const updateScrollButtons = useCallback(() => {
    if (!rowRef.current) return;

    const { scrollLeft, scrollWidth, clientWidth } = rowRef.current;
    const canScroll = scrollWidth > clientWidth;

    setShowLeftButton(canScroll && scrollLeft > 10);
    setShowRightButton(canScroll && scrollLeft < scrollWidth - clientWidth - 10);
  }, []);

  // Set up scroll listener
  useEffect(() => {
    const scrollContainer = rowRef.current;
    if (!scrollContainer) return;

    updateScrollButtons();
    scrollContainer.addEventListener('scroll', updateScrollButtons, { passive: true });

    return () => {
      scrollContainer.removeEventListener('scroll', updateScrollButtons);
    };
  }, [updateScrollButtons, contents.length]);

  // Simple scroll functions
  const scrollLeft = () => {
    if (rowRef.current) {
      rowRef.current.scrollBy({ left: -rowRef.current.clientWidth * 0.8, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (rowRef.current) {
      rowRef.current.scrollBy({ left: rowRef.current.clientWidth * 0.8, behavior: 'smooth' });
    }
  };

  // Always render to prevent hydration mismatches
  // The intersection observer will only work after mounting

  return (
    <section className="py-6 md:py-8">
      <div className="container px-4 md:px-6 mx-auto">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl md:text-2xl font-semibold text-vista-light tracking-tight">
              {title}
            </h2>
            {subtitle && (
              <p className="text-sm text-vista-light/70 mt-1">{subtitle}</p>
            )}
          </div>
          {seeAllLink && (
            <Link href={seeAllLink}>
              <Button variant="ghost" size="sm" className="text-vista-light hover:text-vista-light/80 hover:bg-vista-dark-lighter">
                See All <ChevronRight className="ml-1 h-4 w-4" />
              </Button>
            </Link>
          )}
        </div>

        <div className="relative">
          {showLeftButton && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-black/60 text-vista-light hover:bg-black/80 rounded-full h-8 w-8 shadow-md"
              onClick={scrollLeft}
            >
              <ChevronRight className="h-5 w-5 rotate-180" />
            </Button>
          )}

          <div
            ref={rowRef}
            className="flex space-x-3 md:space-x-4 overflow-x-auto scrollbar-hide pb-1 -mx-1 px-1 pt-1"
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {contents.map((content, index) => (
              <div
                key={`${content.id}-${index}`}
                className="flex-none w-32 sm:w-[170px] md:w-[170px] lg:w-[170px]"
              >
                <ContentCard
                  id={content.id}
                  title={content.title}
                  imagePath={content.imagePath}
                  type={content.type}
                  year={content.year}
                  ageRating={content.ageRating}
                  index={index}
                  link={`/watch/${content.id}?forcePlay=true&contentType=${content.type === 'shows' ? 'show' : 'movie'}`}
                  isAwardWinning={content.isAwardWinning}
                  dataSource={content.dataSource}
                />
              </div>
            ))}

            {/* Infinite scroll trigger - minimal and positioned at the end */}
            {hasMore && (
              <div
                ref={loadTriggerRef}
                className="flex-none w-1 h-full opacity-0 pointer-events-none"
                aria-hidden="true"
              />
            )}

            {/* Loading indicator - separate from trigger to prevent scroll issues */}
            {isLoading && (
              <div className="flex-none w-32 sm:w-[170px] md:w-[170px] lg:w-[170px] flex items-center justify-center">
                <Loader2 className="h-8 w-8 text-vista-blue animate-spin" />
              </div>
            )}
          </div>

          {showRightButton && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-black/60 text-vista-light hover:bg-black/80 rounded-full h-8 w-8 shadow-md"
              onClick={scrollRight}
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          )}
        </div>
      </div>
    </section>
  );
}


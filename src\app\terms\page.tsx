'use client';

import { Navbar } from '@/components/Navbar';
import Footer from '@/components/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { FileText, Shield, Users, CreditCard, AlertTriangle, Scale } from 'lucide-react';

const sections = [
  {
    id: 'acceptance',
    title: 'Acceptance of Terms',
    icon: FileText,
    content: `By accessing and using StreamVista's services, you acknowledge that you have read, understood, and agree to be bound by these Terms of Service. If you do not agree to these terms, please do not use our services.

These terms constitute a legally binding agreement between you and StreamVista Inc. ("we," "us," or "our"). Your use of our service is also governed by our Privacy Policy, which is incorporated by reference into these terms.`
  },
  {
    id: 'service',
    title: 'Description of Service',
    icon: Shield,
    content: `StreamVista provides a subscription-based streaming service that allows members to access and view a wide variety of entertainment content, including movies, TV shows, documentaries, and original programming.

Our service is available on various internet-connected devices and is subject to these Terms of Service. The service may include advertisements and promotional content. We reserve the right to modify, suspend, or discontinue any aspect of the service at any time.`
  },
  {
    id: 'eligibility',
    title: 'Eligibility and Registration',
    icon: Users,
    content: `You must be at least 18 years old to create an account. If you are under 18, you may use the service only with the involvement and consent of a parent or guardian.

When creating an account, you must provide accurate and complete information. You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account.

You may not:
• Create multiple accounts
• Share your account with others outside your household
• Use automated systems to access the service
• Attempt to circumvent geographic restrictions`
  },
  {
    id: 'subscription',
    title: 'Subscription and Billing',
    icon: CreditCard,
    content: `StreamVista offers various subscription plans with different features and pricing. Your subscription will automatically renew at the end of each billing period unless you cancel before the renewal date.

Billing:
• Charges are billed in advance on a monthly or annual basis
• All fees are non-refundable except as required by law
• We may change subscription fees with 30 days' notice
• Failed payments may result in service suspension

Cancellation:
• You may cancel your subscription at any time
• Cancellation takes effect at the end of your current billing period
• You will retain access to the service until the end of your paid period`
  },
  {
    id: 'content',
    title: 'Content and Intellectual Property',
    icon: Scale,
    content: `All content on StreamVista, including but not limited to movies, TV shows, images, text, graphics, logos, and software, is owned by StreamVista or our content providers and is protected by copyright, trademark, and other intellectual property laws.

Your Rights:
• Personal, non-commercial use of the service
• Streaming content for your household
• Creating personal watchlists and profiles

Restrictions:
• No downloading, copying, or redistribution of content
• No commercial use of any content
• No reverse engineering or circumventing security measures
• No creating derivative works from our content`
  },
  {
    id: 'conduct',
    title: 'User Conduct and Prohibited Uses',
    icon: AlertTriangle,
    content: `You agree to use StreamVista in accordance with all applicable laws and regulations. You will not use the service for any unlawful purpose or in any way that could damage, disable, or impair the service.

Prohibited Activities:
• Violating any laws or regulations
• Infringing on intellectual property rights
• Transmitting harmful or malicious code
• Attempting to gain unauthorized access to our systems
• Using the service to harass, abuse, or harm others
• Sharing account credentials with unauthorized users
• Using VPNs or proxies to circumvent geographic restrictions
• Engaging in any activity that interferes with the service`
  }
];

const quickLinks = [
  { title: 'Privacy Policy', href: '/privacy' },
  { title: 'Cookie Policy', href: '/cookies' },
  { title: 'Contact Us', href: '/contact' },
  { title: 'Help Center', href: '/help' }
];

export default function TermsPage() {
  return (
    <div className="min-h-screen bg-vista-dark text-vista-light">
      <Navbar />
      
      {/* Hero Section */}
      <section className="relative py-20 px-4 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-vista-blue/10 to-transparent" />
        <div className="container mx-auto text-center relative z-10">
          <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-vista-light to-vista-blue bg-clip-text text-transparent">
            Terms of Service
          </h1>
          <p className="text-xl md:text-2xl text-vista-light/80 max-w-3xl mx-auto mb-8">
            Please read these terms carefully before using StreamVista's services.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Badge variant="secondary" className="bg-vista-blue/20 text-vista-blue">
              Last Updated: January 15, 2024
            </Badge>
            <Badge variant="outline" className="border-vista-light/20 text-vista-light/70">
              Version 2.1
            </Badge>
          </div>
        </div>
      </section>

      {/* Quick Navigation */}
      <section className="py-8 px-4 bg-vista-card/30">
        <div className="container mx-auto">
          <h2 className="text-xl font-semibold mb-4 text-vista-light">Quick Navigation</h2>
          <div className="flex flex-wrap gap-2">
            {sections.map((section) => (
              <a
                key={section.id}
                href={`#${section.id}`}
                className="px-3 py-1 text-sm bg-vista-card border border-vista-light/10 rounded-md hover:border-vista-blue/30 transition-colors text-vista-light/80 hover:text-vista-light"
              >
                {section.title}
              </a>
            ))}
          </div>
        </div>
      </section>

      {/* Terms Content */}
      <section className="py-20 px-4">
        <div className="container mx-auto max-w-4xl">
          <div className="space-y-8">
            {sections.map((section, index) => (
              <Card key={section.id} id={section.id} className="bg-vista-card border-vista-light/10">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3 text-vista-light">
                    <div className="w-10 h-10 bg-vista-blue/20 rounded-full flex items-center justify-center">
                      <section.icon className="w-5 h-5 text-vista-blue" />
                    </div>
                    <span className="text-xl">{section.title}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="prose prose-invert max-w-none">
                    {section.content.split('\n\n').map((paragraph, pIndex) => (
                      <div key={pIndex} className="mb-4">
                        {paragraph.includes('•') ? (
                          <div>
                            {paragraph.split('\n').map((line, lIndex) => (
                              <div key={lIndex}>
                                {line.startsWith('•') ? (
                                  <div className="flex items-start gap-2 ml-4 mb-1">
                                    <span className="text-vista-blue mt-1">•</span>
                                    <span className="text-vista-light/80 text-sm">{line.substring(2)}</span>
                                  </div>
                                ) : (
                                  <p className="text-vista-light/80 text-sm mb-2">{line}</p>
                                )}
                              </div>
                            ))}
                          </div>
                        ) : (
                          <p className="text-vista-light/80 text-sm leading-relaxed">{paragraph}</p>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <Separator className="bg-vista-light/10" />

      {/* Additional Terms */}
      <section className="py-16 px-4 bg-vista-card/30">
        <div className="container mx-auto max-w-4xl">
          <h2 className="text-3xl font-bold mb-8 text-vista-light">Additional Important Information</h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            <Card className="bg-vista-card border-vista-light/10">
              <CardHeader>
                <CardTitle className="text-vista-light">Limitation of Liability</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-vista-light/80 text-sm">
                  StreamVista's liability is limited to the maximum extent permitted by law. We are not liable for any indirect, incidental, special, or consequential damages arising from your use of the service.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-vista-card border-vista-light/10">
              <CardHeader>
                <CardTitle className="text-vista-light">Governing Law</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-vista-light/80 text-sm">
                  These terms are governed by the laws of the State of California, United States. Any disputes will be resolved in the courts of San Francisco County, California.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-vista-card border-vista-light/10">
              <CardHeader>
                <CardTitle className="text-vista-light">Changes to Terms</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-vista-light/80 text-sm">
                  We may update these terms from time to time. We will notify you of significant changes via email or through the service. Continued use constitutes acceptance of updated terms.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-vista-card border-vista-light/10">
              <CardHeader>
                <CardTitle className="text-vista-light">Contact Information</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-vista-light/80 text-sm">
                  For questions about these terms, please contact <NAME_EMAIL> or through our contact page.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Related Links */}
      <section className="py-16 px-4">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl font-bold mb-8 text-vista-light">Related Documents</h2>
          <div className="flex flex-wrap justify-center gap-4">
            {quickLinks.map((link, index) => (
              <a
                key={index}
                href={link.href}
                className="px-6 py-3 bg-vista-card border border-vista-light/10 rounded-lg hover:border-vista-blue/30 transition-colors text-vista-light hover:text-vista-blue"
              >
                {link.title}
              </a>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}

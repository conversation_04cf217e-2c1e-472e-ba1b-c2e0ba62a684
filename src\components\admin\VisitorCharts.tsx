'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@/components/ui/charts';
import { VisitorDistribution } from '@/hooks/useVisitorData';
import { 
  LayoutGrid, 
  BarChart3, 
  <PERSON><PERSON><PERSON> as Pie<PERSON>hartIcon,
  ArrowUpRight,
  Info,
  Monitor,
  Globe,
  Chrome
} from 'lucide-react';

interface VisitorChartsProps {
  distributions: {
    device: VisitorDistribution[];
    browser: VisitorDistribution[];
    os: VisitorDistribution[];
    country: VisitorDistribution[];
  };
  dailyVisitors: {
    _id: string;
    count: number;
  }[];
  isLoading: boolean;
}

export default function VisitorCharts({ distributions, dailyVisitors, isLoading }: VisitorChartsProps) {
  const [chartView, set<PERSON>hartView] = useState<'combined' | 'detail'>('combined');
  const [distributionType, setDistributionType] = useState('device');

  // Check if there's any data
  const hasData = distributions.device.length > 0 ||
                 distributions.browser.length > 0 ||
                 distributions.os.length > 0 ||
                 distributions.country.length > 0 ||
                 dailyVisitors.length > 0;

  // Process data for charts
  const deviceData = distributions.device.slice(0, 5).map(item => ({
    name: item._id || 'Unknown',
    value: item.count
  }));

  const browserData = distributions.browser.slice(0, 5).map(item => ({
    name: item._id || 'Unknown',
    value: item.count
  }));

  const osData = distributions.os.slice(0, 5).map(item => ({
    name: item._id || 'Unknown',
    value: item.count
  }));

  const countryData = distributions.country.slice(0, 8).map(item => ({
    name: item._id || 'Unknown',
    value: item.count
  }));

  // Process daily visitor data
  const dailyData = dailyVisitors.map(item => ({
    date: item._id,
    visitors: item.count
  }));

  // Distribution icon mapping
  const distributionIcons = {
    device: <Monitor className="h-4 w-4" />,
    browser: <Chrome className="h-4 w-4" />,
    os: <LayoutGrid className="h-4 w-4" />,
    country: <Globe className="h-4 w-4" />
  };

  // If there's no data and not loading, show empty state
  if (!hasData && !isLoading) {
    return (
      <Card className="bg-vista-dark-lighter border-vista-light/10 mb-6">
        <CardHeader className="pb-2">
          <CardTitle className="text-vista-light flex items-center justify-between">
            <span className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-vista-blue" />
              Visitor Analytics
            </span>
          </CardTitle>
          <CardDescription>Visual representation of visitor metrics</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] flex items-center justify-center">
            <div className="text-center text-vista-light/50">
              <p>No visitor data available yet</p>
              <p className="text-sm mt-1">Charts will appear as visitors browse your site</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-vista-dark-lighter border-vista-light/10 mb-6">
      <CardHeader className="pb-2 flex flex-row items-center justify-between">
        <div>
          <CardTitle className="text-vista-light flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-vista-blue" />
            Visitor Analytics
          </CardTitle>
          <CardDescription>Visual representation of visitor metrics</CardDescription>
        </div>
        
        <div className="flex items-center gap-2">
          <Tabs value={chartView} onValueChange={(value) => setChartView(value as 'combined' | 'detail')} className="w-auto">
            <TabsList className="h-8 p-1">
              <TabsTrigger value="combined" className="h-6 px-2 text-xs">
                <LayoutGrid className="h-3.5 w-3.5 mr-1" />
                Dashboard
              </TabsTrigger>
              <TabsTrigger value="detail" className="h-6 px-2 text-xs">
                <BarChart3 className="h-3.5 w-3.5 mr-1" />
                Details
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </CardHeader>

      <CardContent>
        {isLoading ? (
          <div className="h-[400px] w-full bg-vista-dark/50 animate-pulse rounded"></div>
        ) : (
          <>
            {chartView === 'combined' ? (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 h-[400px]">
                {/* Enhanced Trends Chart - Takes up 2/3 of the space */}
                <div className="col-span-2 bg-gradient-to-br from-vista-dark/30 to-vista-dark/10 border border-vista-light/10 rounded-xl p-4 backdrop-blur-sm">
                  <div className="flex justify-between items-center mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-blue-500/20 rounded-lg">
                        <BarChart3 className="h-4 w-4 text-blue-400" />
                      </div>
                      <div>
                        <h3 className="text-sm font-semibold text-vista-light">Daily Visitor Trends</h3>
                        <p className="text-xs text-vista-light/60">Visitor activity over time</p>
                      </div>
                    </div>
                    <div className="text-xs text-blue-400 bg-blue-500/10 px-3 py-1 rounded-full">
                      Last 30 days
                    </div>
                  </div>
                  <div className="h-[320px]">
                    <LineChart
                      data={dailyData}
                      index="date"
                      categories={['visitors']}
                      colors={['#3b82f6']}
                      valueFormatter={(value) => `${value.toLocaleString()} visitors`}
                      showLegend={false}
                      showGridLines={true}
                      showAnimation={true}
                      curveType="smooth"
                    />
                  </div>
                </div>

                {/* Enhanced Distribution Charts - Takes up 1/3 of the space */}
                <div className="bg-gradient-to-br from-purple-500/10 to-purple-600/5 border border-purple-500/20 rounded-xl p-4 backdrop-blur-sm">
                  <div className="flex justify-between items-center mb-4">
                    <div className="flex items-center gap-2">
                      <div className="p-2 bg-purple-500/20 rounded-lg">
                        {distributionIcons[distributionType as keyof typeof distributionIcons]}
                      </div>
                      <div>
                        <h3 className="text-sm font-semibold text-vista-light capitalize">
                          {distributionType} Distribution
                        </h3>
                        <p className="text-xs text-vista-light/60">Top categories</p>
                      </div>
                    </div>

                    <select
                      value={distributionType}
                      onChange={(e) => setDistributionType(e.target.value)}
                      className="text-xs bg-vista-dark/80 border border-vista-light/20 rounded-lg px-3 py-1.5 text-vista-light/80 focus:border-purple-500/50 focus:outline-none transition-colors"
                    >
                      <option value="device">Device</option>
                      <option value="browser">Browser</option>
                      <option value="os">OS</option>
                      <option value="country">Country</option>
                    </select>
                  </div>
                  
                  <div className="h-[320px]">
                    {distributionType === 'country' ? (
                      <BarChart
                        data={countryData}
                        index="name"
                        categories={['value']}
                        colors={['#8b5cf6']}
                        valueFormatter={(value) => `${value.toLocaleString()}`}
                        showLegend={false}
                        showGridLines={true}
                        showAnimation={true}
                        layout="vertical"
                      />
                    ) : (
                      <PieChart
                        data={
                          distributionType === 'device' ? deviceData :
                          distributionType === 'browser' ? browserData : osData
                        }
                        category="value"
                        index="name"
                        colors={['#8b5cf6', '#3b82f6', '#ec4899', '#f97316', '#10b981']}
                        valueFormatter={(value) => `${value.toLocaleString()}`}
                        showAnimation={true}
                        showLegend={true}
                        innerRadius={30}
                        outerRadius={70}
                      />
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <div className="h-[400px]">
                <Tabs defaultValue="device" className="w-full h-full">
                  <TabsList className="mb-4 grid grid-cols-4 h-8">
                    <TabsTrigger value="device" className="flex items-center gap-1">
                      <Monitor className="h-3.5 w-3.5" />
                      Device
                    </TabsTrigger>
                    <TabsTrigger value="browser" className="flex items-center gap-1">
                      <Chrome className="h-3.5 w-3.5" />
                      Browser
                    </TabsTrigger>
                    <TabsTrigger value="os" className="flex items-center gap-1">
                      <LayoutGrid className="h-3.5 w-3.5" />
                      OS
                    </TabsTrigger>
                    <TabsTrigger value="country" className="flex items-center gap-1">
                      <Globe className="h-3.5 w-3.5" />
                      Country
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="device" className="h-[350px]">
                    <PieChart
                      data={deviceData}
                      category="value"
                      index="name"
                      colors={['#3b82f6', '#8b5cf6', '#ec4899', '#f97316', '#10b981']}
                      valueFormatter={(value) => `${value.toLocaleString()} visitors`}
                      showAnimation={true}
                    />
                  </TabsContent>

                  <TabsContent value="browser" className="h-[350px]">
                    <PieChart
                      data={browserData}
                      category="value"
                      index="name"
                      colors={['#3b82f6', '#8b5cf6', '#ec4899', '#f97316', '#10b981']}
                      valueFormatter={(value) => `${value.toLocaleString()} visitors`}
                      showAnimation={true}
                    />
                  </TabsContent>

                  <TabsContent value="os" className="h-[350px]">
                    <PieChart
                      data={osData}
                      category="value"
                      index="name"
                      colors={['#3b82f6', '#8b5cf6', '#ec4899', '#f97316', '#10b981']}
                      valueFormatter={(value) => `${value.toLocaleString()} visitors`}
                      showAnimation={true}
                    />
                  </TabsContent>

                  <TabsContent value="country" className="h-[350px]">
                    <BarChart
                      data={countryData}
                      index="name"
                      categories={['value']}
                      colors={['#3b82f6']}
                      valueFormatter={(value) => `${value.toLocaleString()} visitors`}
                      showLegend={false}
                      showGridLines={false}
                      showAnimation={true}
                      layout="vertical"
                    />
                  </TabsContent>
                </Tabs>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}

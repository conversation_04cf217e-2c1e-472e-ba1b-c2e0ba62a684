'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Loader2 } from 'lucide-react';
import { CloudinaryUploadWidget } from './CloudinaryUploadWidget';
import { useAuth } from '@/contexts/AuthContext';
import { useProfiles } from '@/contexts/ProfileContext';
import { toast } from 'sonner';

interface ProfileImageUploaderProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  onImageUpdated?: (imageUrl: string) => void;
  profileMode?: boolean; // Indicates if this uploader is for profile avatars rather than user account
  shape?: 'circle' | 'oval'; // Add shape option with default to circle
}

const sizeMap = {
  sm: 'w-16 h-16',
  md: 'w-24 h-24',
  lg: 'w-32 h-32',
  xl: 'w-40 h-40',
};

export function ProfileImageUploader({
  size = 'lg',
  className = '',
  onImageUpdated,
  profileMode = false,
  shape = 'circle'
}: ProfileImageUploaderProps) {
  const { user, updateProfileImage } = useAuth();
  const { activeProfile, updateProfile } = useProfiles();
  const [isUpdating, setIsUpdating] = useState(false);
  const [imageUrl, setImageUrl] = useState<string | null>(null);

  // Update image URL based on context - either user account or active profile
  useEffect(() => {
    if (profileMode && activeProfile?.avatar) {
      setImageUrl(activeProfile.avatar);
    } else if (!profileMode && user?.picture) {
      setImageUrl(user.picture);
    }
  }, [user, activeProfile, profileMode]);

  // Safety timeout to reset loading state if widget doesn't properly close
  useEffect(() => {
    let safetyTimeout: NodeJS.Timeout | null = null;

    if (isUpdating) {
      // Set a timeout to reset the loading state after 30 seconds
      // This ensures we don't get stuck in a loading state if something goes wrong
      safetyTimeout = setTimeout(() => {
        console.log('Safety timeout triggered - resetting loading state');
        setIsUpdating(false);
      }, 30000); // 30 seconds
    }

    return () => {
      if (safetyTimeout) {
        clearTimeout(safetyTimeout);
      }
    };
  }, [isUpdating]);

  // Calculate initials as fallback
  const userInitials = profileMode && activeProfile?.name
    ? activeProfile.name.substring(0, 2).toUpperCase()
    : user?.name
      ? user.name.substring(0, 2).toUpperCase()
      : 'SV';

  const sizeClass = sizeMap[size];
  // Add shape-specific classes
  const shapeClass = shape === 'oval' ? 'rounded-[40%/50%] aspect-[4/5]' : 'rounded-full';
  const ringClass = '';

  const handleImageUpload = async (url: string) => {
    // Set image URL immediately for faster UI feedback
    setImageUrl(url);
    setIsUpdating(true);

    try {
      if (profileMode && activeProfile?.id) {
        // Update profile image
        const result = await updateProfile(activeProfile.id, {
          avatar: url
        });

        if (result.success) {
          if (onImageUpdated) {
            onImageUpdated(url);
          }

          const profileType = activeProfile.isPrimary ? 'main profile' : 'profile';
          toast.success(`${activeProfile.name}'s ${profileType} avatar updated successfully`);
        } else {
          // Revert to previous image if update fails
          setImageUrl(activeProfile.avatar);
          toast.error(result.error || 'Failed to update profile avatar');
        }
      } else {
        // Update user account image
        const result = await updateProfileImage(url);
        if (result.success) {
          if (onImageUpdated) {
            onImageUpdated(url);
          }
          toast.success('Account image updated successfully');
        } else {
          // Revert to previous image if update fails
          setImageUrl(user?.picture || null);
          toast.error(result.error || 'Failed to update account image');
        }
      }
    } catch (error) {
      toast.error('An error occurred while updating your image');
      console.error('Error in handleImageUpload:', error);
      // Revert to previous image on error
      if (profileMode && activeProfile?.avatar) {
        setImageUrl(activeProfile.avatar);
      } else if (!profileMode && user?.picture) {
        setImageUrl(user?.picture || null);
      }
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div
      className={`relative group ${sizeClass} ${className}`}
      onClick={() => {
        // For mobile devices, we'll trigger the Cloudinary widget directly on click
        if (window.innerWidth < 768) {
          setIsUpdating(true);
          // Access the Cloudinary widget through the CloudinaryUploadWidget component
          const button = document.querySelector('.cloudinary-button') as HTMLButtonElement;
          if (button) {
            button.click();

            // Add a listener to detect when the modal is closed
            const checkForModalClosed = setInterval(() => {
              // Check if the Cloudinary modal is still open
              const cloudinaryModal = document.querySelector('.cloudinary-overlay');
              if (!cloudinaryModal) {
                // Modal is closed, reset the loading state
                setIsUpdating(false);
                clearInterval(checkForModalClosed);
              }
            }, 1000); // Check every second

            // Clear the interval after 30 seconds as a safety measure
            setTimeout(() => {
              clearInterval(checkForModalClosed);
            }, 30000);
          } else {
            // If we can't find the button, reset the loading state
            setIsUpdating(false);
          }
        }
      }}
    >
      <div className={`${sizeClass} ${shapeClass} ${ringClass} overflow-hidden bg-vista-dark-lighter`}>
        {isUpdating ? (
          <div className="absolute inset-0 flex items-center justify-center bg-vista-dark/50">
            <Loader2 className="w-8 h-8 animate-spin text-vista-blue" />
          </div>
        ) : imageUrl ? (
          <Image
            src={imageUrl}
            alt={profileMode ? activeProfile?.name || 'Profile' : user?.name || 'User'}
            width={240}
            height={240}
            className="w-full h-full object-cover"
            priority
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-vista-blue/20 text-vista-light">
            <span className="text-2xl font-semibold">{userInitials}</span>
          </div>
        )}
      </div>

      {/* Overlay containing the upload button - visible on hover for desktop */}
      <div className={`absolute inset-0 ${shapeClass} flex items-center justify-center bg-vista-dark/70 opacity-0 group-hover:opacity-100 transition-opacity hidden md:flex z-10`}>
        <CloudinaryUploadWidget
          onUploadSuccess={handleImageUpload}
          onUploadStart={() => setIsUpdating(true)}
          onUploadCancel={() => setIsUpdating(false)}
          buttonText="Change"
          className="bg-vista-blue hover:bg-vista-blue/90 text-white border-none w-24 h-9"
        />
      </div>

      {/* Mobile-only indicator */}
      <div className="absolute bottom-3 right-3 w-9 h-9 bg-vista-blue rounded-full flex items-center justify-center md:hidden shadow-md">
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
          <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
          <circle cx="12" cy="13" r="4"></circle>
        </svg>
      </div>
    </div>
  );
}
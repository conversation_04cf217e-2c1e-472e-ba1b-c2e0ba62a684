'use client';

import Pusher, { Channel } from 'pusher-js';

// Pusher client instance
let pusher: Pusher | null = null;
const channels: Map<string, Channel> = new Map();

// Export a default client instance for backward compatibility
export const pusherClient = {
  subscribe: (channelName: string) => {
    const channel = getChannel(channelName);
    if (!channel) {
      // Return a mock channel if Pusher is not available
      return {
        bind: () => {},
        unbind: () => {},
      };
    }
    return channel;
  },
  unsubscribe: (channelName: string) => {
    unsubscribeFromChannel(channelName);
  },
  // Add other methods as needed for compatibility
};

// Initialize Pusher client
export function initializePusher(): Pusher | null {
  const pusherKey = process.env.NEXT_PUBLIC_PUSHER_KEY;
  const pusherCluster = process.env.NEXT_PUBLIC_PUSHER_CLUSTER || 'us2';

  if (!pusherKey) {
    console.warn('Pusher key not found. Real-time features will be disabled.');
    return null;
  }

  if (!pusher) {
    try {
      pusher = new Pusher(pusherKey, {
        cluster: pusherCluster,
        forceTLS: true,
        enabledTransports: ['ws', 'wss'],
        disabledTransports: ['xhr_streaming', 'xhr_polling'],
      });

      // Handle connection events
      pusher.connection.bind('connected', () => {
        console.log('Pusher connected successfully');
      });

      pusher.connection.bind('disconnected', () => {
        console.log('Pusher disconnected');
      });

      pusher.connection.bind('error', (error: Error) => {
        console.error('Pusher connection error:', error);
      });

      console.log('Pusher client initialized');
    } catch (error) {
      console.error('Failed to initialize Pusher:', error);
      return null;
    }
  }

  return pusher;
}

// Get or create a channel
export function getChannel(channelName: string): Channel | null {
  if (!pusher) {
    pusher = initializePusher();
    if (!pusher) return null;
  }

  if (!channels.has(channelName)) {
    try {
      const channel = pusher.subscribe(channelName);
      channels.set(channelName, channel);
      console.log(`Subscribed to channel: ${channelName}`);
    } catch (error) {
      console.error(`Failed to subscribe to channel ${channelName}:`, error);
      return null;
    }
  }

  return channels.get(channelName) || null;
}

// Unsubscribe from a channel
export function unsubscribeFromChannel(channelName: string): void {
  if (pusher && channels.has(channelName)) {
    try {
      pusher.unsubscribe(channelName);
      channels.delete(channelName);
      console.log(`Unsubscribed from channel: ${channelName}`);
    } catch (error) {
      console.error(`Failed to unsubscribe from channel ${channelName}:`, error);
    }
  }
}

// Disconnect Pusher client
export function disconnectPusher(): void {
  if (pusher) {
    try {
      // Unsubscribe from all channels
      channels.forEach((channel, channelName) => {
        pusher?.unsubscribe(channelName);
      });
      channels.clear();

      // Disconnect the client
      pusher.disconnect();
      pusher = null;
      console.log('Pusher client disconnected');
    } catch (error) {
      console.error('Error disconnecting Pusher:', error);
    }
  }
}

// Send an event to a channel
export function sendEvent(channelName: string, eventName: string, data: Record<string, unknown>): boolean {
  const channel = getChannel(channelName);
  if (!channel) {
    console.error(`Channel ${channelName} not available`);
    return false;
  }

  try {
    channel.trigger(eventName, data);
    return true;
  } catch (error) {
    console.error(`Failed to send event ${eventName} to channel ${channelName}:`, error);
    return false;
  }
}

// Bind to an event on a channel
export function bindToEvent(
  channelName: string,
  eventName: string,
  callback: (data: unknown) => void
): boolean {
  const channel = getChannel(channelName);
  if (!channel) {
    console.error(`Channel ${channelName} not available`);
    return false;
  }

  try {
    channel.bind(eventName, callback);
    console.log(`Bound to event ${eventName} on channel ${channelName}`);
    return true;
  } catch (error) {
    console.error(`Failed to bind to event ${eventName} on channel ${channelName}:`, error);
    return false;
  }
}

// Unbind from an event on a channel
export function unbindFromEvent(
  channelName: string,
  eventName: string,
  callback?: (data: unknown) => void
): boolean {
  const channel = channels.get(channelName);
  if (!channel) {
    console.warn(`Channel ${channelName} not found for unbinding`);
    return false;
  }

  try {
    if (callback) {
      channel.unbind(eventName, callback);
    } else {
      channel.unbind(eventName);
    }
    console.log(`Unbound from event ${eventName} on channel ${channelName}`);
    return true;
  } catch (error) {
    console.error(`Failed to unbind from event ${eventName} on channel ${channelName}:`, error);
    return false;
  }
}

// Get connection state
export function getConnectionState(): string {
  if (!pusher) return 'disconnected';
  return pusher.connection.state;
}

// Check if Pusher is connected
export function isConnected(): boolean {
  if (!pusher) return false;
  return pusher.connection.state === 'connected';
}

// Get all active channels
export function getActiveChannels(): string[] {
  return Array.from(channels.keys());
}

// Watch party specific functions
export function joinWatchParty(partyId: string, userId: string, userName: string): boolean {
  const channelName = `watch-party-${partyId}`;
  const channel = getChannel(channelName);
  
  if (!channel) return false;

  // Send join event
  return sendEvent(channelName, 'member-joined', {
    userId,
    userName,
    timestamp: new Date().toISOString(),
  });
}

export function leaveWatchParty(partyId: string, userId: string, userName: string): boolean {
  const channelName = `watch-party-${partyId}`;
  
  // Send leave event
  const success = sendEvent(channelName, 'member-left', {
    userId,
    userName,
    timestamp: new Date().toISOString(),
  });

  // Unsubscribe from channel
  unsubscribeFromChannel(channelName);
  
  return success;
}

export function sendWatchPartyMessage(partyId: string, userId: string, userName: string, message: string): boolean {
  const channelName = `watch-party-${partyId}`;
  
  return sendEvent(channelName, 'chat-message', {
    userId,
    userName,
    message,
    timestamp: new Date().toISOString(),
  });
}

export function updateWatchPartyPlayback(
  partyId: string,
  currentTime: number,
  isPlaying: boolean,
  isPaused: boolean,
  isBuffering: boolean
): boolean {
  const channelName = `watch-party-${partyId}`;
  
  return sendEvent(channelName, 'playback-update', {
    currentTime,
    isPlaying,
    isPaused,
    isBuffering,
    timestamp: new Date().toISOString(),
  });
}

// Export types for use in other files
export interface PusherMessage {
  userId: string;
  userName: string;
  message: string;
  timestamp: string;
}

export interface PusherPlaybackUpdate {
  currentTime: number;
  isPlaying: boolean;
  isPaused: boolean;
  isBuffering: boolean;
  timestamp: string;
}

export interface PusherMemberEvent {
  userId: string;
  userName: string;
  timestamp: string;
} 
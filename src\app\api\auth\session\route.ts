import { NextRequest, NextResponse } from 'next/server';
import { UserSession } from '@/lib/types';

// Simple in-memory cache for session validation
type SessionCache = {
  [userId: string]: {
    timestamp: number;
    user: UserSession;
  };
};

// Cache session validation results for 5 minutes
const SESSION_CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds
const sessionCache: SessionCache = {};

// Define the POST handler function
export async function POST(request: NextRequest) {

  // Add cache control headers to the response
  const headers = {
    'Cache-Control': 'private, max-age=300', // 5 minutes
  };

  try {
    // Import connection utilities
    let ensureMongooseConnection;
    try {
      const mongoModule = await import('@/lib/mongodb');
      ensureMongooseConnection = mongoModule.ensureMongooseConnection;
    } catch (importError) {
      console.error('Critical error importing MongoDB utilities:', importError);
      return NextResponse.json(
        {
          valid: false,
          error: 'Critical error during session validation: Database module unavailable',
          details: importError instanceof Error ? importError.message : 'Unknown import error'
        },
        { status: 500, headers }
      );
    }

    // Get established connection or create a new one with retry logic
    let mongoose;
    let connectionAttempts = 0;
    const MAX_ATTEMPTS = 3;

    while (connectionAttempts < MAX_ATTEMPTS) {
      try {
        connectionAttempts++;
        mongoose = await ensureMongooseConnection();
        if (mongoose) break;

        console.warn(`MongoDB connection attempt ${connectionAttempts} failed, retrying...`);
        // Wait before retrying (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, 100 * Math.pow(2, connectionAttempts)));
      } catch (connError) {
        console.error(`Connection error on attempt ${connectionAttempts}:`, connError);

        // If we have more attempts, wait and try again
        if (connectionAttempts < MAX_ATTEMPTS) {
          await new Promise(resolve => setTimeout(resolve, 200 * Math.pow(2, connectionAttempts)));
        } else {
          console.error('All connection attempts failed:', connError);
          return NextResponse.json(
            {
              valid: false,
              error: 'Critical error during session validation: Database connection failed',
              details: connError instanceof Error ? connError.message : 'Unknown connection error'
            },
            { status: 503, headers }  // 503 Service Unavailable
          );
        }
      }
    }

    if (!mongoose) {
      return NextResponse.json(
        {
          valid: false,
          error: `Critical error during session validation: Could not establish MongoDB connection after ${MAX_ATTEMPTS} attempts`
        },
        { status: 503, headers }  // 503 Service Unavailable
      );
    }

    // Get the User model from mongoose
    let User;
    try {
      // Import User model directly to avoid issues with mongoose instances
      const UserModule = await import('@/models/User');
      User = UserModule.default;

      if (!User) {
        throw new Error('User model import failed');
      }


    } catch (modelError) {
      console.error('Critical error importing User model:', modelError);

      // Try a fallback approach if the direct import fails
      try {
        console.log('Attempting fallback User model creation');

        // Import mongoose directly
        const mongooseModule = await import('mongoose');
        const mongooseInstance = mongooseModule.default;

        // Create a basic User schema
        const Schema = mongooseInstance.Schema;
        const UserSchema = new Schema({
          name: String,
          email: String,
          password: String,
          googleId: String,
          picture: String,
          role: String,
          createdAt: Date,
          updatedAt: Date
        });

        // Check if model already exists to avoid model overwrite error
        User = mongooseInstance.models.User || mongooseInstance.model('User', UserSchema);
        console.log('Fallback User model created successfully');
      } catch (fallbackError) {
        console.error('Fallback User model creation failed:', fallbackError);
        return NextResponse.json(
          {
            valid: false,
            error: 'Critical error during session validation: Database model initialization failed',
            details: `Original error: ${modelError instanceof Error ? modelError.message : 'Unknown model error'}. Fallback error: ${fallbackError instanceof Error ? fallbackError.message : 'Unknown fallback error'}`
          },
          { status: 500, headers }
        );
      }
    }

    // Get userId from request
    let userId;
    try {
      // First try to get userId from cookies for better compatibility
      userId = request.cookies.get('userId')?.value;



      // If not in cookies, try to extract from request body
      if (!userId) {
        try {
          // Check if request has a body before trying to parse it
          const contentType = request.headers.get('content-type');
          if (contentType && contentType.includes('application/json')) {
            const sessionData = await request.json();
            userId = sessionData.userId;
          }
        } catch (parseError) {
          console.error('Error parsing session data request body:', parseError);
          // Continue with cookie-based auth if body parsing fails
        }
      }
    } catch (userIdError) {
      console.error('Error extracting userId:', userIdError);
      return NextResponse.json(
        {
          valid: false,
          error: 'Critical error during session validation: Failed to extract user ID',
          details: userIdError instanceof Error ? userIdError.message : 'Unknown userId extraction error'
        },
        { status: 400, headers }
      );
    }

    if (!userId) {
      console.log('No user ID provided for session validation');
      return NextResponse.json(
        { valid: false, error: 'User ID is required' },
        { status: 400, headers }
      );
    }

    // Check if we have a valid cached session
    const cachedSession = sessionCache[userId];
    if (cachedSession && (Date.now() - cachedSession.timestamp) < SESSION_CACHE_TTL) {
      console.log('Using cached session validation for user ID:', userId);
      return NextResponse.json({
        valid: true,
        user: cachedSession.user,
        cached: true
      }, { headers });
    }

    // Validate MongoDB ObjectId format
    try {
      // Import mongoose directly to avoid issues with the mongoose instance
      const mongooseForValidation = await import('mongoose');

      if (!mongooseForValidation.default.Types.ObjectId.isValid(userId)) {
        console.log('Invalid ObjectId format provided:', userId);
        return NextResponse.json(
          { valid: false, error: 'Invalid user ID format' },
          { status: 400, headers }
        );
      }
    } catch (objectIdError) {
      console.error('Error validating ObjectId:', objectIdError);
      return NextResponse.json(
        {
          valid: false,
          error: 'Critical error during session validation: Object ID validation failed',
          details: objectIdError instanceof Error ? objectIdError.message : 'Unknown ObjectId error'
        },
        { status: 400, headers }
      );
    }

    try {

      // Find the user by ID with a more direct approach to avoid TypeScript issues
      let user;
      try {
        // Use a direct approach with a more specific type casting to avoid TypeScript errors
        // Define a minimal interface for the User model to satisfy TypeScript
        interface UserModelType {
          findOne: (query: { _id: string }) => {
            lean: () => Promise<Record<string, unknown>>;
          };
        }

        // Cast to the specific interface
        const userModel = User as UserModelType;
        user = await userModel.findOne({ _id: userId }).lean();


      } catch (queryError) {
        console.error('Error with user query:', queryError);

        // Try a raw MongoDB approach as a last resort
        try {
          console.log('Attempting raw MongoDB query as fallback');

          // Get the MongoDB connection directly
          const mongoModule = await import('@/lib/mongodb');
          const mongoose = await mongoModule.ensureMongooseConnection();

          if (!mongoose) {
            throw new Error('Failed to get MongoDB connection');
          }

          // Import mongoose for ObjectId
          const mongooseModule = await import('mongoose');

          const db = mongoose.connection.db;
          if (!db) {
            throw new Error('Database connection not available');
          }
          const usersCollection = db.collection('users');

          // Create ObjectId safely
          let objectId;
          try {
            objectId = new mongooseModule.default.Types.ObjectId(userId);
          } catch (objectIdError) {
            console.error('Error creating ObjectId:', objectIdError);
            // Try a direct string comparison as last resort
            user = await usersCollection.findOne({ _id: userId });
          }

          if (!user && objectId) {
            user = await usersCollection.findOne({ _id: objectId });
          }

          if (user) {
            console.log('User found using raw MongoDB query');
          }
        } catch (rawQueryError) {
          console.error('Raw MongoDB query also failed:', rawQueryError);
          throw new Error('All query attempts failed');
        }
      }

      if (!user) {
        console.log('No user found with ID:', userId);
        // Clear the userId cookie
        const response = NextResponse.json(
          { valid: false, error: 'User not found' },
          { status: 404, headers }
        );

        // Clear the userId cookie with improved settings
        response.cookies.set('userId', '', {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax', // Changed from default to 'lax' for better cross-domain compatibility
          expires: new Date(0), // Expire immediately
          path: '/'
        });

        return response;
      }

      // Define a type for the MongoDB user document
      interface UserDocument {
        _id: { toString: () => string };
        googleId?: string;
        email?: string;
        name?: string;
        picture?: string;
        role?: string;
        createdAt?: Date;
      }

      // Create a user session object
      const userObj = user as UserDocument;

      // Validate the role value to ensure it matches the expected union type
      let userRole: 'user' | 'admin' | 'superadmin' = 'user'; // Default to 'user'
      if (userObj.role === 'admin' || userObj.role === 'superadmin') {
        userRole = userObj.role;
      }

      const userSession = {
        id: userObj._id.toString(),
        googleId: userObj.googleId,
        email: userObj.email || '',
        name: userObj.name || '',
        picture: userObj.picture,
        role: userRole,
        createdAt: userObj.createdAt ? new Date(userObj.createdAt).toISOString() : new Date().toISOString(),
      };



      // We no longer update lastLogin during session validation
      // lastLogin should only be updated during actual login events

      // Cache the successful validation
      sessionCache[userId] = {
        timestamp: Date.now(),
        user: userSession
      };

      // Return the validation result and user data
      return NextResponse.json({
        valid: true,
        user: userSession,
        cached: false
      }, { headers });
    } catch (dbError) {
      console.error('Session validation error details:', dbError);

      if (dbError instanceof Error) {
        console.error('Error message:', dbError.message);
        console.error('Error stack:', dbError.stack);
      }

      // Handle specific MongoDB errors
      if (dbError instanceof Error && dbError.name && dbError.name.includes('Mongo')) {
        console.error('MongoDB error type:', dbError.constructor.name);

        // For database errors, consider returning a degraded response to avoid
        // automatic logout on temporary database issues
        return NextResponse.json({
          valid: true,
          partialValidation: true,
          error: `Database error during session validation: ${dbError.message}`,
          message: 'Session validation continued with limited database connectivity',
          user: {
            id: userId,
            email: '', // Empty string as we couldn't verify the email
            name: '',
            role: 'user', // Default to user role for security
            createdAt: new Date().toISOString()
          }
        }, { headers });
      }

      // For other errors, return a more specific error
      return NextResponse.json({
        valid: false,
        error: 'Critical error during session validation: Database query failed',
        details: dbError instanceof Error ? dbError.message : 'Unknown database error'
      }, { status: 500, headers });
    }
  } catch (generalError) {
    // This is the top-level error handler
    console.error('Unhandled error in session validation:', generalError);

    if (generalError instanceof Error) {
      console.error('Error name:', generalError.name);
      console.error('Error message:', generalError.message);
      console.error('Error stack:', generalError.stack);
    }

    return NextResponse.json({
      valid: false,
      error: 'Critical error during session validation',
      details: generalError instanceof Error ? generalError.message : 'Unhandled server error'
    }, { status: 500, headers });
  }
}

// Handle GET requests
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405, headers: { 'Cache-Control': 'no-store' } }
  );
}
import { NextRequest, NextResponse } from 'next/server';
import os from 'os';
import { getUserActivityModel } from '@/lib/activity-logger';

interface ActivityData {
  type: string;
  action?: string;
  details?: string;
  timestamp: Date | string;
  userId?: {
    _id?: string | unknown;
    name?: string;
    email?: string;
  } | string | unknown;
}

interface PopularContent {
  id: string;
  title: string;
  type: string;
  views: number;
}

interface PopulatedUser {
  _id: string | unknown;
  name?: string;
  email?: string;
}

interface UserActivityWithPopulatedUser {
  type: string;
  action?: string;
  details?: string;
  timestamp: Date | string;
  userId?: PopulatedUser | string | unknown;
}

/**
 * GET /api/admin/dashboard/stats
 * Get dashboard statistics
 */
export async function GET(request: NextRequest) {
  try {
    // Try to get the user ID from the cookie directly
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, try to get it from the request body
    if (!userId) {
      try {
        const body = await request.json();
        userId = body.userId;
      } catch (error) {
        // Ignore JSON parsing errors
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const User = mongoose.default.models.User ||
                mongoose.default.model('User', new mongoose.default.Schema({
                  role: String,
                  emailVerified: Date,
                  createdAt: Date
                }));

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();

    // Create a properly typed interface for the user object
    interface UserWithRole {
      role?: string;
      [key: string]: unknown;
    }

    if (!user || ((user as UserWithRole).role !== 'admin')) {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Get the Content model directly
    const Content = mongoose.default.models.Content ||
                   mongoose.default.model('Content', new mongoose.default.Schema({
                     title: String,
                     type: String,
                     posterPath: String,
                     rating: Number,
                     createdAt: Date
                   }));

    // Get the UserActivity model using the consistent function
    const UserActivity = getUserActivityModel();

    // Get the ContentView model directly
    const ContentView = mongoose.default.models.ContentView ||
                       mongoose.default.model('ContentView', new mongoose.default.Schema({
                         contentId: mongoose.default.Schema.Types.ObjectId,
                         userId: mongoose.default.Schema.Types.ObjectId,
                         progress: Number,
                         createdAt: Date
                       }));

    // Get user statistics with more detailed queries
    const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

    // Get total users with a valid query
    const totalUsers = await User.countDocuments({});

    // Get active users (those who have verified their email)
    const activeUsers = await User.countDocuments({
      $or: [
        { emailVerified: { $ne: null } },
        { email_verified: true }
      ]
    });

    // Get new users in the last 7 days - more comprehensive query
    const newUsers = await User.countDocuments({
      $or: [
        { createdAt: { $gte: oneWeekAgo } },
        { created_at: { $gte: oneWeekAgo } },
        { registeredAt: { $gte: oneWeekAgo } },
        { registered_at: { $gte: oneWeekAgo } },
        { signupDate: { $gte: oneWeekAgo } },
        { signup_date: { $gte: oneWeekAgo } }
      ]
    });

    // Check UserActivity for signup events
    const signupActivities = await UserActivity.countDocuments({
      type: 'auth',
      action: { $in: ['signup', 'register'] },
      timestamp: { $gte: oneWeekAgo }
    });

    // Get anonymous visitors count
    const AnonymousVisitor = mongoose.default.models.AnonymousVisitor ||
                            mongoose.default.model('AnonymousVisitor', new mongoose.default.Schema({
                              visitorId: String,
                              firstVisit: Date,
                              lastVisit: Date,
                              convertedToUser: Boolean
                            }));

    // Get total anonymous visitors
    const totalAnonymousVisitors = await AnonymousVisitor.countDocuments({});

    // Get new anonymous visitors in the last 7 days
    const newAnonymousVisitors = await AnonymousVisitor.countDocuments({
      $or: [
        { firstVisit: { $gte: oneWeekAgo } },
        { first_visit: { $gte: oneWeekAgo } }
      ]
    });

    // Get content statistics
    const totalMovies = await Content.countDocuments({ type: 'movie' });
    const totalShows = await Content.countDocuments({ type: 'show' });

    // Get episodes count
    const totalEpisodes = await Content.aggregate([
      { $match: { type: 'show' } },
      { $lookup: {
          from: 'episodes',
          localField: '_id',
          foreignField: 'showId',
          as: 'episodes'
      }},
      { $group: { _id: null, total: { $sum: { $size: "$episodes" } } } }
    ]).then(result => result[0]?.total || 0);

    // Get new content counts
    const newMovies = await Content.countDocuments({
      type: 'movie',
      createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
    });

    const newShows = await Content.countDocuments({
      type: 'show',
      createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
    });

    // Get system info
    const systemInfo = {
      platform: os.platform(),
      cpus: os.cpus().length,
      totalMemory: os.totalmem(),
      freeMemory: os.freemem(),
      uptime: os.uptime()
    };

    // Calculate memory usage
    const memoryUsed = systemInfo.totalMemory - systemInfo.freeMemory;
    const memoryUsedPercentage = Math.round((memoryUsed / systemInfo.totalMemory) * 100);

    // Format uptime
    const days = Math.floor(systemInfo.uptime / (24 * 60 * 60));
    const hours = Math.floor((systemInfo.uptime % (24 * 60 * 60)) / (60 * 60));
    const minutes = Math.floor((systemInfo.uptime % (60 * 60)) / 60);
    const uptimeFormatted = `${days}d ${hours}h ${minutes}m`;

    // Determine system status based on memory usage
    let systemStatus: 'healthy' | 'warning' | 'critical' | 'degraded' = 'healthy';
    if (memoryUsedPercentage > 90) {
      systemStatus = 'critical';
    } else if (memoryUsedPercentage > 75) {
      systemStatus = 'warning';
    }

    // Get total views and recent views
    const viewStats = await ContentView.aggregate([
      {
        $facet: {
          total: [{ $count: 'count' }],
          weekly: [
            { $match: { createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } } },
            { $count: 'count' }
          ]
        }
      }
    ]);

    const totalViews = viewStats[0]?.total[0]?.count || 0;
    const weeklyViews = viewStats[0]?.weekly[0]?.count || 0;

    // No longer tracking popular content
    const mappedPopular: PopularContent[] = [];

    // Get recent activity (simplified approach)
    let recentActivity: ActivityData[] = [];

    try {
      // Get recent activities from the last 7 days
      const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

      recentActivity = await UserActivity.find({
        timestamp: { $gte: sevenDaysAgo }
      })
      .sort({ timestamp: -1 })
      .limit(50)
      .populate('userId', 'name email')
      .lean();

      // If no recent activity found, that's okay - we'll show empty state
      if (recentActivity.length === 0) {
      }
    } catch (error) {
      console.error('Error fetching user activities:', error);
      recentActivity = [];
    }

    // Map activity data to the required format (simplified)
    const mappedActivity = recentActivity.map(activity => {
      // Determine activity type based on UserActivity type and action
      let type: 'user_registration' | 'user_login' | 'system_alert' = 'system_alert';

      if (activity.type === 'auth') {
        if (activity.action === 'signup' || activity.action === 'register') {
          type = 'user_registration';
        } else if (activity.action === 'login' || activity.action === 'signin') {
          type = 'user_login';
        }
      }

      // Create a descriptive message
      let message = activity.details || 'System event';
      if (type === 'user_registration') {
        const userName = (activity.userId as PopulatedUser)?.name || 'User';
        message = `New user registered: ${userName}`;
      } else if (type === 'user_login') {
        const userName = (activity.userId as PopulatedUser)?.name || 'User';
        message = `User logged in: ${userName}`;
      }

      return {
        type,
        action: activity.action || 'system',
        message,
        details: activity.details || '',
        timestamp: activity.timestamp instanceof Date ? activity.timestamp.toISOString() : activity.timestamp,
        userId: (activity.userId as PopulatedUser)?._id?.toString() || activity.userId?.toString() || ''
      };
    });

    // Return statistics
    return NextResponse.json({
      users: {
        total: totalUsers,
        active: activeUsers,
        new: Math.max(newUsers, signupActivities) // Use the higher count between user documents and signup activities
      },
      visitors: {
        total: totalAnonymousVisitors,
        new: newAnonymousVisitors
      },
      content: {
        movies: totalMovies,
        shows: totalShows,
        episodes: totalEpisodes,
        newMovies,
        newShows,
        total: totalMovies + totalShows
      },
      views: {
        total: totalViews || 0,
        weekly: weeklyViews || 0,
        dailyAverage: Math.round(weeklyViews / 7) || 0
      },
      system: {
        status: systemStatus,
        uptime: uptimeFormatted,
        load: `${memoryUsedPercentage}%`,
        memory: {
          total: systemInfo.totalMemory,
          free: systemInfo.freeMemory,
          used: memoryUsed,
          usedPercentage: memoryUsedPercentage
        },
        platform: systemInfo.platform,
        cpus: systemInfo.cpus
      },
      popular: mappedPopular,
      activity: mappedActivity,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard statistics', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

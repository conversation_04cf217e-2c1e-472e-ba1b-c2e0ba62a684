import { Suspense } from 'react';
import DetailClient from './DetailClient';
import { Metadata } from 'next';

// Static export configuration
export const preferredRegion = 'auto'
export const dynamic = 'auto'

// Define the metadata for the page
export async function generateMetadata({ params }: { params: { type: string; id: string } }): Promise<Metadata> {
  const { type, id } = await Promise.resolve(params);

  return {
    title: `${id.charAt(0).toUpperCase() + id.slice(1).replace(/-/g, ' ')} | StreamVista`,
    description: `Watch ${id.replace(/-/g, ' ')} on StreamVista - Premium Streaming Experience`,
  };
}

// Static paths generation
export function generateStaticParams() {
  return [
    { type: 'shows', id: 'stranger-things' },
    { type: 'shows', id: 'the-crown' },
    { type: 'shows', id: 'breaking-bad' },
    { type: 'shows', id: 'squid-game' },
    { type: 'movies', id: 'movie-1' },
    { type: 'movies', id: 'movie-2' }
  ];
}

// Server component
export default function DetailPage({ params }: { params: { type: string; id: string } }) {
  // Validate params before rendering
  console.log('DetailPage received params:', params);

  // Ensure we have valid params
  const validType = params.type && (params.type === 'shows' || params.type === 'movies');
  const validId = params.id && params.id !== 'undefined' && params.id !== 'null' && params.id !== 'undefined';

  // Log validation results
  console.log('DetailPage validation:', { validType, validId, type: params.type, id: params.id });

  // If params are invalid, show error
  if (!validType || !validId) {
    return (
      <div className="min-h-screen bg-vista-dark flex items-center justify-center">
        <div className="text-center p-8 bg-vista-dark-lighter rounded-lg max-w-md">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-vista-light/30 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h2 className="text-2xl text-vista-light font-bold">Content not found</h2>
          <p className="text-vista-light/70 mt-2">
            {!validType ? 'Invalid content type' : 'Invalid content ID'}
          </p>
          <a
            href="/"
            className="mt-6 inline-block px-4 py-2 bg-vista-blue hover:bg-vista-blue/90 text-white rounded-md"
          >
            Return Home
          </a>
        </div>
      </div>
    );
  }

  return (
    <Suspense fallback={
      <div className="min-h-screen bg-vista-dark flex items-center justify-center">
        <div className="w-16 h-16 border-4 border-vista-blue/20 border-t-vista-blue rounded-full animate-spin"></div>
      </div>
    }>
      <DetailClient params={params} />
    </Suspense>
  );
}

"use client"

import React from 'react'
import { motion } from 'framer-motion'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Calendar, Clock } from 'lucide-react'
import { useContentUpload, MetadataData } from './ContentUploadProvider'
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select'

// Define the validation schema using zod
const metadataSchema = z.object({
  title: z.string().min(3, 'Title must be at least 3 characters').max(100, 'Title must be less than 100 characters'),
  description: z.string().max(5000, 'Description must be less than 5000 characters').optional(),
  genres: z.array(z.string()).min(1, 'Select at least one genre'),
  type: z.enum(['movie', 'show']),
  releaseYear: z.number().min(1900).max(new Date().getFullYear() + 5),
  duration: z.string().optional(),
  episodeNumber: z.number().optional(),
  seasonNumber: z.number().optional(),
})

export function MetadataStep() {
  const { uploadData, updateMetadata } = useContentUpload()
  const { metadata } = uploadData

  // Set up the form with react-hook-form
  const form = useForm<MetadataData>({
    resolver: zodResolver(metadataSchema),
    defaultValues: {
      ...metadata
    }
  })

  // Define available genres
  const availableGenres = [
    'Action',
    'Adventure',
    'Comedy',
    'Drama',
    'Fantasy',
    'Horror',
    'Mystery',
    'Romance',
    'Sci-Fi',
    'Thriller',
    'Documentary',
    'Animation'
  ]

  // Generate years for select dropdown (1970 to current year + 3)
  const currentYear = new Date().getFullYear()
  const years = Array.from({ length: currentYear - 1970 + 4 }, (_, i) => currentYear + 3 - i)

  // Handle form submission - updates context with form values
  const onSubmit = (data: MetadataData) => {
    updateMetadata(data)
  }

  // Handle form changes and update context in real-time
  const handleFormChange = () => {
    const values = form.getValues()
    updateMetadata(values)
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="space-y-2">
        <h2 className="text-xl font-semibold text-vista-light">Content Details</h2>
        <p className="text-vista-light/70">
          Add information about your content to help viewers discover it.
        </p>
      </div>

      <Form {...form}>
        <form onChange={handleFormChange} onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Content Type */}
          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Content Type</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="flex space-x-3"
                  >
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="movie" id="movie" />
                      </FormControl>
                      <FormLabel className="cursor-pointer" htmlFor="movie">Movie</FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="show" id="show" />
                      </FormControl>
                      <FormLabel className="cursor-pointer" htmlFor="show">TV Show</FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Title */}
          <FormField
            control={form.control}
            name="title"
            render={({ field }) => (
              <FormItem>
                <FormLabel required>Title</FormLabel>
                <FormControl>
                  <Input placeholder="Enter content title" {...field} />
                </FormControl>
                <FormDescription>
                  The title of your content as it will appear to viewers.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Description */}
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe your content..."
                    {...field}
                    className="min-h-[120px] resize-y"
                  />
                </FormControl>
                <FormDescription>
                  A detailed description helps viewers understand what your content is about.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Genres */}
          <FormField
            control={form.control}
            name="genres"
            render={({ field }) => (
              <FormItem>
                <FormLabel required>Genres</FormLabel>
                <FormControl>
                  <div className="flex flex-wrap gap-2">
                    {availableGenres.map((genre) => (
                      <button
                        key={genre}
                        type="button"
                        className={`px-3 py-1.5 rounded-full text-sm transition-colors ${
                          field.value.includes(genre)
                            ? 'bg-vista-blue text-white'
                            : 'bg-vista-dark-lighter text-vista-light hover:bg-vista-dark-lightest'
                        }`}
                        onClick={() => {
                          const updatedGenres = field.value.includes(genre)
                            ? field.value.filter((g) => g !== genre)
                            : [...field.value, genre]
                          field.onChange(updatedGenres)
                          handleFormChange()
                        }}
                      >
                        {genre}
                      </button>
                    ))}
                  </div>
                </FormControl>
                <FormDescription>
                  Select all genres that apply to your content.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Release Year */}
            <FormField
              control={form.control}
              name="releaseYear"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Release Year</FormLabel>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(parseInt(value, 10))
                      handleFormChange()
                    }}
                    defaultValue={field.value.toString()}
                  >
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <Calendar className="mr-2 h-4 w-4 opacity-70" />
                        <SelectValue placeholder="Select year" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {years.map((year) => (
                        <SelectItem key={year} value={year.toString()}>
                          {year}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Duration (for movies) or Season/Episode (for shows) */}
            {form.watch('type') === 'movie' ? (
              <FormField
                control={form.control}
                name="duration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Duration</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-vista-light/50 h-4 w-4" />
                        <Input
                          placeholder="1h 30m"
                          className="pl-10"
                          {...field}
                          value={field.value || ''}
                        />
                      </div>
                    </FormControl>
                    <FormDescription>
                      The length of your movie (e.g., 1h 30m)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            ) : (
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="seasonNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Season</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="1"
                          min={1}
                          {...field}
                          value={field.value || ''}
                          onChange={(e) => {
                            const value = e.target.value ? parseInt(e.target.value, 10) : undefined
                            field.onChange(value)
                            handleFormChange()
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="episodeNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Episode</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="1"
                          min={1}
                          {...field}
                          value={field.value || ''}
                          onChange={(e) => {
                            const value = e.target.value ? parseInt(e.target.value, 10) : undefined
                            field.onChange(value)
                            handleFormChange()
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}
          </div>
        </form>
      </Form>
    </motion.div>
  )
}

import { NextRequest, NextResponse } from 'next/server';

/**
 * GET /api/admin/email-templates
 * Get all email templates
 */
export async function GET(request: NextRequest) {
  try {
    // Try to get the user ID from the cookie directly
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, try to get it from the request body
    if (!userId) {
      try {
        const body = await request.json();
        userId = body.userId;
        // Clone the request since we've consumed the body
        request = new Request(request.url, {
          headers: request.headers,
          method: request.method,
          body: JSON.stringify(body),
          cache: request.cache,
          credentials: request.credentials,
          integrity: request.integrity,
          keepalive: request.keepalive,
          mode: request.mode,
          redirect: request.redirect,
          referrer: request.referrer,
          referrerPolicy: request.referrerPolicy,
          signal: request.signal,
        });
      } catch (error) {
        // Ignore JSON parsing errors
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    if (!userId) {
      console.error('Admin email templates API: No user ID found in request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const User = mongoose.default.models.User ||
                mongoose.default.model('User', new mongoose.default.Schema({
                  role: String
                }));

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the EmailTemplate schema directly
    const EmailTemplateSchema = new mongoose.default.Schema({
      name: {
        type: String,
        required: true,
        unique: true,
        trim: true
      },
      subject: {
        type: String,
        required: true,
        trim: true
      },
      body: {
        type: String,
        required: true
      },
      description: {
        type: String,
        required: true
      },
      variables: [{
        type: String,
        trim: true
      }],
      isDefault: {
        type: Boolean,
        default: false
      },
      createdBy: {
        type: mongoose.default.Schema.Types.ObjectId,
        ref: 'User',
        required: true
      },
      updatedBy: {
        type: mongoose.default.Schema.Types.ObjectId,
        ref: 'User'
      },
      active: {
        type: Boolean,
        default: true
      }
    }, {
      timestamps: true
    });

    // Get the EmailTemplate model
    const EmailTemplate = mongoose.default.models.EmailTemplate ||
                         mongoose.default.model('EmailTemplate', EmailTemplateSchema);

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const active = searchParams.get('active');

    // Build query
    const query: any = {};
    if (active !== null) {
      query.active = active === 'true';
    }

    // Fetch templates
    const templates = await EmailTemplate.find(query)
      .sort({ name: 1 })
      .lean();

    return NextResponse.json({ templates });
  } catch (error) {
    console.error('Error fetching email templates:', error);
    return NextResponse.json(
      { error: 'Failed to fetch email templates', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/email-templates
 * Create a new email template
 */
export async function POST(request: NextRequest) {
  try {
    // Try to get the user ID from the cookie directly
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    // For POST requests, we'll try to extract the userId from the body
    if (!userId) {
      try {
        const bodyData = await request.json();
        userId = bodyData.userId;
        // Clone the request since we've consumed the body
        request = new Request(request.url, {
          headers: request.headers,
          method: request.method,
          body: JSON.stringify(bodyData),
          cache: request.cache,
          credentials: request.credentials,
          integrity: request.integrity,
          keepalive: request.keepalive,
          mode: request.mode,
          redirect: request.redirect,
          referrer: request.referrer,
          referrerPolicy: request.referrerPolicy,
          signal: request.signal,
        });
      } catch (error) {
        // Ignore JSON parsing errors
      }
    }

    if (!userId) {
      console.error('Admin email templates API (POST): No user ID found in request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const User = mongoose.default.models.User ||
                mongoose.default.model('User', new mongoose.default.Schema({
                  role: String
                }));

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the EmailTemplate schema directly
    const EmailTemplateSchema = new mongoose.default.Schema({
      name: {
        type: String,
        required: true,
        unique: true,
        trim: true
      },
      subject: {
        type: String,
        required: true,
        trim: true
      },
      body: {
        type: String,
        required: true
      },
      description: {
        type: String,
        required: true
      },
      variables: [{
        type: String,
        trim: true
      }],
      isDefault: {
        type: Boolean,
        default: false
      },
      createdBy: {
        type: mongoose.default.Schema.Types.ObjectId,
        ref: 'User',
        required: true
      },
      updatedBy: {
        type: mongoose.default.Schema.Types.ObjectId,
        ref: 'User'
      },
      active: {
        type: Boolean,
        default: true
      }
    }, {
      timestamps: true
    });

    // Get the EmailTemplate model
    const EmailTemplate = mongoose.default.models.EmailTemplate ||
                         mongoose.default.model('EmailTemplate', EmailTemplateSchema);

    // Get data from request
    const data = await request.json();
    const { name, subject, body, description, variables } = data;

    // Validate required fields
    if (!name || !subject || !body || !description) {
      return NextResponse.json(
        { error: 'Name, subject, body, and description are required' },
        { status: 400 }
      );
    }

    // Check if template with the same name already exists
    const existingTemplate = await EmailTemplate.findOne({ name });
    if (existingTemplate) {
      return NextResponse.json(
        { error: 'A template with this name already exists' },
        { status: 400 }
      );
    }

    // Create the template
    const template = await EmailTemplate.create({
      name,
      subject,
      body,
      description,
      variables: variables || [],
      createdBy: new mongoose.default.Types.ObjectId(userId),
      updatedBy: new mongoose.default.Types.ObjectId(userId)
    });

    return NextResponse.json({
      success: true,
      template
    });
  } catch (error) {
    console.error('Error creating email template:', error);
    return NextResponse.json(
      { error: 'Failed to create email template', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

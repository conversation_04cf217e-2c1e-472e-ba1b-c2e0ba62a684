// This is a simple script to generate test notifications
// Run it with: node src/scripts/generate-test-notifications.js

const userId = process.argv[2];

if (!userId) {
  console.error('Please provide a user ID as an argument');
  console.error('Example: node src/scripts/generate-test-notifications.js 6123456789abcdef01234567');
  process.exit(1);
}

// Generate sample notifications
async function generateNotifications() {
  try {
    const response = await fetch(`http://localhost:3000/api/notifications/generate-samples?userId=${userId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId }),
    });

    const data = await response.json();
    console.log('Response:', data);
  } catch (error) {
    console.error('Error generating notifications:', error);
  }
}

generateNotifications();

import { ToastProvider } from '@/lib/ToastContext'
import { WatchPartyProvider } from '@/lib/WatchPartyContext'
import { ErrorProvider } from '@/lib/ErrorContext'
import { AuthProvider } from '@/contexts/AuthContext'

interface ProvidersProps {
  children: React.ReactNode
}

export function Providers({ children }: ProvidersProps) {
  return (
    <ErrorProvider>
      <AuthProvider>
        <ToastProvider>
          <WatchPartyProvider>
            {children}
          </WatchPartyProvider>
        </ToastProvider>
      </AuthProvider>
    </ErrorProvider>
  )
}
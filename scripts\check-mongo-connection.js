/**
 * MongoDB Connection Test Script
 *
 * This script tests the MongoDB connection and provides detailed diagnostics.
 * Run it with: node scripts/check-mongo-connection.js
 */

// Use CommonJS syntax for compatibility
const { MongoClient } = require('mongodb');
const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Get MongoDB URI from environment variables
const MONGODB_URI = process.env.MONGODB_URI;
const MONGODB_DB = process.env.MONGODB_DB || 'streamvista';

if (!MONGODB_URI) {
  console.error('Error: MONGODB_URI environment variable is not defined');
  process.exit(1);
}

// Connection options
const options = {
  connectTimeoutMS: 10000,
  socketTimeoutMS: 20000,
  maxPoolSize: 10,
  minPoolSize: 1,
  retryWrites: true,
  w: 'majority',
};

async function testMongoDBConnection() {
  console.log('=== MongoDB Connection Test ===');
  console.log(`Database: ${MONGODB_DB}`);
  // Don't log any part of the connection string as it may contain credentials
  console.log('Testing connection to MongoDB...');

  // Test native MongoDB driver connection
  console.log('\n1. Testing MongoDB Native Driver Connection...');
  let nativeClient;

  try {
    console.time('Native connection time');
    nativeClient = new MongoClient(MONGODB_URI, options);
    await nativeClient.connect();
    console.timeEnd('Native connection time');

    console.log('✅ Native driver connection successful');

    // Test ping
    console.time('Ping time');
    await nativeClient.db(MONGODB_DB).command({ ping: 1 });
    console.timeEnd('Ping time');
    console.log('✅ Ping successful');

    // Get server info
    const serverInfo = await nativeClient.db(MONGODB_DB).admin().serverInfo();
    console.log(`MongoDB version: ${serverInfo.version}`);

    // Get connection stats
    const stats = await nativeClient.db(MONGODB_DB).admin().serverStatus();
    console.log(`Active connections: ${stats.connections.current}`);
    console.log(`Available connections: ${stats.connections.available}`);

  } catch (error) {
    console.error('❌ Native driver connection failed:', error.message);
  }

  // Test Mongoose connection
  console.log('\n2. Testing Mongoose Connection...');

  try {
    // Mongoose options
    const mongooseOptions = {
      dbName: MONGODB_DB,
      connectTimeoutMS: 10000,
      socketTimeoutMS: 20000,
      serverSelectionTimeoutMS: 10000,
    };

    console.time('Mongoose connection time');
    await mongoose.connect(MONGODB_URI, mongooseOptions);
    console.timeEnd('Mongoose connection time');

    console.log('✅ Mongoose connection successful');
    console.log(`Mongoose version: ${mongoose.version}`);
    console.log(`Connection state: ${mongoose.connection.readyState}`);

    // Test a simple query
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log(`Collections in database: ${collections.length}`);
    collections.forEach(collection => {
      console.log(`- ${collection.name}`);
    });

  } catch (error) {
    console.error('❌ Mongoose connection failed:', error.message);
  }

  // Clean up
  console.log('\nCleaning up connections...');

  if (nativeClient) {
    await nativeClient.close();
    console.log('Native client connection closed');
  }

  if (mongoose.connection.readyState !== 0) {
    await mongoose.connection.close();
    console.log('Mongoose connection closed');
  }

  console.log('\n=== Test Complete ===');
}

// Run the test
testMongoDBConnection()
  .then(() => {
    console.log('Connection test completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Connection test failed:', error);
    process.exit(1);
  });

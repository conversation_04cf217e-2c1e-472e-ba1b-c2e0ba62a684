# Recommendation Service

## Overview
The Recommendation Service provides personalized content suggestions based on user watch history, preferences, and behavior patterns. The service implements a sophisticated recommendation engine that combines content-based filtering, user insights, and external recommendations from TMDb.

## Core Components

### User Insights Interface
```typescript
interface UserInsights {
  genrePreferences: Record<string, number>;  // normalized weights
  preferMoviesOverShows: number;            // -1 to 1 scale
}
```

### Content Score Interface
```typescript
interface ContentScore {
  content: IContent;
  score: number;
}
```

### Watch History Item Interface
```typescript
interface WatchHistoryItem {
  contentId: string;
  type: 'movie' | 'show';
  progress: number;      // 0-100
  watchTime: number;     // seconds
  watchedAt: string;     // ISO date
  completed: boolean;
  season?: number;       // for shows
  episode?: number;      // for shows
}
```

## Features

### 1. Personalized Recommendations
- User watch history analysis
- Genre preference calculation
- Content type preference tracking
- Watch time weighting
- Completion rate consideration
- Recent activity analysis

### 2. Content-Based Filtering
- Genre matching
- Release year consideration
- Popularity weighting
- User rating analysis
- Content type alignment
- Similar content suggestions

### 3. External Recommendations
- TMDb integration
- Movie recommendations
- TV show recommendations
- Trending content
- Popular content
- Top-rated content

### 4. Recommendation Categories
- Personalized "For You"
- "Because You Watched"
- Trending Now
- Popular Movies/Shows
- Top Rated Content
- Genre-based suggestions

## Implementation Details

### Recommendation Engine
```typescript
function generateRecommendations(
  watchHistory: WatchHistoryItem[],
  allContent: IContent[],
  count: number = 10
): IContent[] {
  // Create a stable random function for consistent results
  const getRandom = stableRandomGenerator(42);

  // Return random popular content for new users
  if (watchHistory.length === 0) {
    return stableShuffle(allContent, getRandom).slice(0, count);
  }

  // Get user insights
  const userInsights = generateUserInsights(watchHistory, allContent);
  const watchedContentIds = new Set(watchHistory.map(item => item.contentId));

  // Score content based on user preferences
  const contentScores = allContent
    .filter(content => !watchedContentIds.has(content.id))
    .map(content => {
      let score = 0;

      // Base score for newer content
      const currentYear = new Date().getFullYear();
      score += Math.max(0, 10 - (currentYear - content.year));

      // Genre preference score
      content.genres.forEach(genre => {
        const genrePreference = userInsights.genrePreferences[genre] || 0;
        score += genrePreference * 5;
      });

      // Content type preference score
      if (content.type === 'movie' && userInsights.preferMoviesOverShows > 0) {
        score += userInsights.preferMoviesOverShows * 3;
      } else if (content.type === 'show' && userInsights.preferMoviesOverShows < 0) {
        score += Math.abs(userInsights.preferMoviesOverShows) * 3;
      }

      // Popularity boost
      if (content.popularity) {
        score += content.popularity;
      }

      return { content, score };
    });

  // Sort by score and add randomization
  contentScores.sort((a, b) => b.score - a.score);
  const topRecommendations = contentScores.slice(0, count * 2).map(item => item.content);
  return stableShuffle(topRecommendations, getRandom).slice(0, count);
}
```

### User Insights Generation
```typescript
function generateUserInsights(
  watchHistory: WatchHistoryItem[],
  allContent: IContent[]
): UserInsights {
  const contentMap = new Map(allContent.map(item => [item.id, item]));
  const genreCounts: Record<string, number> = {};
  let movieCount = 0;
  let showCount = 0;

  // Process watch history
  watchHistory.forEach(item => {
    const content = contentMap.get(item.contentId);
    if (!content) return;

    // Count content types
    item.type === 'movie' ? movieCount++ : showCount++;

    // Weight genres by progress and completion
    const weight = (item.progress / 100) * (item.completed ? 1.5 : 1);
    content.genres.forEach(genre => {
      genreCounts[genre] = (genreCounts[genre] || 0) + weight;
    });
  });

  // Normalize genre preferences
  const totalGenreWeight = Object.values(genreCounts).reduce((sum, count) => sum + count, 0);
  const genrePreferences: Record<string, number> = {};
  
  if (totalGenreWeight > 0) {
    Object.entries(genreCounts).forEach(([genre, count]) => {
      genrePreferences[genre] = count / totalGenreWeight;
    });
  }

  // Calculate content type preference
  const totalItems = movieCount + showCount;
  const preferMoviesOverShows = totalItems > 0
    ? (movieCount - showCount) / totalItems
    : 0;

  return { genrePreferences, preferMoviesOverShows };
}
```

### External Recommendations Integration
```typescript
async function getExternalRecommendations(id: string, type: 'movie' | 'tv'): Promise<IContent[]> {
  const endpoint = type === 'movie'
    ? `/movie/${id}/recommendations`
    : `/tv/${id}/recommendations`;

  const data = await fetchFromTMDB(endpoint);
  return data.results.map(item => ({
    id: item.id,
    title: type === 'movie' ? item.title : item.name,
    type: type,
    year: new Date(item.release_date || item.first_air_date).getFullYear(),
    genres: item.genre_ids,
    popularity: item.vote_average,
    posterUrl: item.poster_path
      ? `https://image.tmdb.org/t/p/w300${item.poster_path}`
      : null
  }));
}
```

## Best Practices

### 1. Performance
- Use efficient data structures
- Implement caching strategies
- Optimize scoring algorithms
- Batch process recommendations
- Use stable sorting for consistency

### 2. User Experience
- Provide diverse recommendations
- Explain recommendation reasons
- Update recommendations regularly
- Handle cold start scenarios
- Support pagination and filtering

### 3. Data Management
- Maintain data consistency
- Handle missing data gracefully
- Implement data validation
- Use appropriate data types
- Regular data cleanup

### 4. Error Handling
- Validate input parameters
- Handle API failures gracefully
- Provide fallback recommendations
- Log errors appropriately
- Implement retry mechanisms

## Integration Guidelines

### 1. Adding New Recommendation Types
```typescript
interface RecommendationType {
  name: string;
  description: string;
  scoreFunction: (content: IContent, userInsights: UserInsights) => number;
  filter?: (content: IContent) => boolean;
}

function addRecommendationType(type: RecommendationType) {
  // Validate the recommendation type
  if (!type.name || !type.scoreFunction) {
    throw new Error('Invalid recommendation type');
  }

  // Register the new type
  recommendationTypes.set(type.name, type);
}
```

### 2. Custom Scoring Functions
```typescript
function createCustomScoreFunction(
  weights: Record<string, number>
): (content: IContent, userInsights: UserInsights) => number {
  return (content: IContent, userInsights: UserInsights) => {
    let score = 0;

    // Apply custom weights to different factors
    if (weights.genre) {
      score += calculateGenreScore(content, userInsights) * weights.genre;
    }
    if (weights.recency) {
      score += calculateRecencyScore(content) * weights.recency;
    }
    if (weights.popularity) {
      score += calculatePopularityScore(content) * weights.popularity;
    }

    return score;
  };
}
```

### 3. Recommendation Display
```typescript
function getRecommendationReason(
  content: IContent,
  userInsights: UserInsights
): string {
  if (content.isInWatchlist) {
    return 'In your watchlist';
  }
  if (content.watchTimeMinutes) {
    return 'Continue Watching';
  }
  if (isNewRelease(content)) {
    return 'New Release';
  }
  if (content.voteAverage >= 8.5) {
    return 'Top Rated';
  }
  if (matchesTopGenre(content, userInsights)) {
    return `Based on your interest in ${content.genres[0]}`;
  }
  return 'Recommended for you';
}
```

## Development Tools

### 1. Testing Utilities
```typescript
const mockRecommendationEngine = {
  generateRecommendations: jest.fn(),
  getUserInsights: jest.fn(),
  getExternalRecommendations: jest.fn()
};

function createTestWatchHistory(count: number): WatchHistoryItem[] {
  return Array.from({ length: count }, (_, i) => ({
    id: `test-${i}`,
    contentId: `content-${i}`,
    type: Math.random() > 0.5 ? 'movie' : 'show',
    progress: Math.random() * 100,
    watchTime: Math.random() * 3600,
    completed: Math.random() > 0.5,
    watchedAt: new Date().toISOString()
  }));
}
```

### 2. Debug Helpers
```typescript
const debugRecommendations = {
  logScoring: true,
  logFiltering: true,
  logInsights: true,
  logInterval: 5000
};

function logRecommendationEvent(event: string, data: any) {
  if (debugRecommendations.logScoring && event.includes('score')) {
    console.log(`[Recommendations] ${event}:`, data);
  }
}
```

### 3. Monitoring Tools
```typescript
function monitorRecommendationMetrics() {
  setInterval(() => {
    const metrics = {
      totalRecommendations: recommendationCount,
      averageScore: calculateAverageScore(),
      genreDiversity: calculateGenreDiversity(),
      userSatisfaction: calculateUserSatisfaction()
    };

    console.table(metrics);
  }, 60000);
}
``` 
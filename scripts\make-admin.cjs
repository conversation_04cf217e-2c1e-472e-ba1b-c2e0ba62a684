// <PERSON>ript to make a user an admin (CommonJS version)
// Usage: node scripts/make-admin-cjs.js <EMAIL>

// Import dependencies
const mongoose = require('mongoose');
const { Schema } = mongoose;
const fs = require('fs');
const path = require('path');

// Load the MongoDB URI from .env.local file
function loadMongoDBUri() {
  try {
    const envPath = path.join(process.cwd(), '.env.local');
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');
    
    for (const line of lines) {
      if (line.startsWith('MONGODB_URI=')) {
        return line.substring('MONGODB_URI='.length).trim();
      }
    }
    
    throw new Error('MONGODB_URI not found in .env.local');
  } catch (error) {
    console.error('Error loading MongoDB URI:', error);
    process.exit(1);
  }
}

// Get the MongoDB URI
const MONGODB_URI = loadMongoDBUri();
console.log('Found MongoDB URI in .env.local');

// User schema (simplified version for this script)
const UserSchema = new Schema({
  email: {
    type: String,
    required: true,
    unique: true,
  },
  name: {
    type: String,
    required: true,
  },
  role: {
    type: String,
    enum: ['user', 'admin', 'superadmin'],
    default: 'user',
  },
});

// Connect to MongoDB
async function connectToMongoDB() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
}

async function makeAdmin(email) {
  if (!email) {
    console.error('Error: Email is required');
    console.log('Usage: node scripts/make-admin-cjs.js <EMAIL>');
    process.exit(1);
  }

  console.log(`Attempting to make user with email ${email} an admin...`);
  
  try {
    // Connect to MongoDB
    await connectToMongoDB();
    
    // Create User model (if not already defined)
    const User = mongoose.models.User || mongoose.model('User', UserSchema);
    
    // Find the user first to confirm they exist
    const user = await User.findOne({ email });
    
    if (!user) {
      console.error(`Error: No user found with email ${email}`);
      process.exit(1);
    }
    
    console.log(`Found user: ${user.name} (Current role: ${user.role || 'user'})`);
    
    // Update the user's role to admin
    const result = await User.updateOne(
      { email }, 
      { $set: { role: 'admin' } }
    );
    
    if (result.modifiedCount === 0) {
      console.log('No changes made - user might already be an admin');
    } else {
      console.log(`Success! Updated ${result.modifiedCount} user(s) to admin role`);
    }
    
    // Verify the change
    const updatedUser = await User.findOne({ email });
    console.log(`User ${updatedUser.name} now has role: ${updatedUser.role}`);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    // Close the connection and exit
    await mongoose.connection.close();
    process.exit();
  }
}

// Get email from command line arguments
const email = process.argv[2];
makeAdmin(email); 
import mongoose, { Document, Schema } from 'mongoose';

export interface IUserContentInteraction extends Document {
  userId: mongoose.Types.ObjectId;
  contentId: mongoose.Types.ObjectId;
  interactionType: 'view' | 'like' | 'dislike' | 'favorite' | 'share' | 'rate';
  timestamp: Date;
  duration?: number; // For view interactions, duration in seconds
  progress?: number; // For view interactions, percentage watched
  rating?: number; // For rate interactions, 1-10
  metadata?: Record<string, any>;
}

const UserContentInteractionSchema = new Schema<IUserContentInteraction>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true
    },
    contentId: {
      type: Schema.Types.ObjectId,
      ref: 'Content',
      required: true,
      index: true
    },
    interactionType: {
      type: String,
      required: true,
      enum: ['view', 'like', 'dislike', 'favorite', 'share', 'rate'],
      index: true
    },
    timestamp: {
      type: Date,
      default: Date.now,
      index: true
    },
    duration: {
      type: Number
    },
    progress: {
      type: Number
    },
    rating: {
      type: Number,
      min: 1,
      max: 10
    },
    metadata: {
      type: Schema.Types.Mixed
    }
  },
  {
    timestamps: true
  }
);

// Create indexes for efficient querying
UserContentInteractionSchema.index({ userId: 1, contentId: 1, interactionType: 1 });
UserContentInteractionSchema.index({ contentId: 1, interactionType: 1 });
UserContentInteractionSchema.index({ timestamp: -1 });

// Create the model if it doesn't exist already
const UserContentInteraction = mongoose.models.UserContentInteraction || mongoose.model<IUserContentInteraction>('UserContentInteraction', UserContentInteractionSchema);

export default UserContentInteraction;

'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { 
  ArrowLeft, 
  Send, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  MessageSquare,
  Calendar,
  User,
  Star,
  Refresh<PERSON><PERSON>,
  Edit,
  Trash2,
  User<PERSON><PERSON>,
  XCircle,
  <PERSON>ert<PERSON>riangle,
  FileText,
  Shield
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';

interface TicketResponse {
  _id: string;
  message: string;
  responderId: {
    _id: string;
    name: string;
    email: string;
    profileImage?: string;
    role: string;
  };
  responderType: 'admin' | 'user';
  isInternal: boolean;
  createdAt: string;
  attachments?: string[];
}

interface Ticket {
  _id: string;
  ticketNumber: string;
  subject: string;
  description: string;
  category: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'open' | 'in_progress' | 'waiting_for_user' | 'resolved' | 'closed';
  createdAt: string;
  updatedAt: string;
  userId: {
    _id: string;
    name: string;
    email: string;
    profileImage?: string;
  };
  assignedTo?: {
    _id: string;
    name: string;
    email: string;
  };
  escalated: boolean;
  escalatedAt?: string;
  resolvedAt?: string;
  resolvedBy?: {
    _id: string;
    name: string;
  };
  resolutionNotes?: string;
  satisfactionRating?: number;
  satisfactionFeedback?: string;
  internalNotes?: string;
  tags: string[];
}

const statusConfig = {
  open: { label: 'Open', color: 'bg-blue-500/20 text-blue-400', icon: AlertCircle },
  in_progress: { label: 'In Progress', color: 'bg-yellow-500/20 text-yellow-400', icon: Clock },
  waiting_for_user: { label: 'Waiting for User', color: 'bg-orange-500/20 text-orange-400', icon: MessageSquare },
  resolved: { label: 'Resolved', color: 'bg-green-500/20 text-green-400', icon: CheckCircle },
  closed: { label: 'Closed', color: 'bg-gray-500/20 text-gray-400', icon: CheckCircle }
};

const priorityConfig = {
  low: { label: 'Low', color: 'bg-gray-500/20 text-gray-400' },
  medium: { label: 'Medium', color: 'bg-blue-500/20 text-blue-400' },
  high: { label: 'High', color: 'bg-orange-500/20 text-orange-400' },
  urgent: { label: 'Urgent', color: 'bg-red-500/20 text-red-400' }
};

export default function AdminTicketDetailPage() {
  const { user, isAdmin } = useAuth();
  const params = useParams();
  const router = useRouter();
  
  const [ticket, setTicket] = useState<Ticket | null>(null);
  const [responses, setResponses] = useState<TicketResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [newResponse, setNewResponse] = useState('');
  const [isInternal, setIsInternal] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [adminUsers, setAdminUsers] = useState<Array<{id: string, name: string}>>([]);
  
  // Dialog states
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [newStatus, setNewStatus] = useState('');
  const [resolutionNotes, setResolutionNotes] = useState('');
  const [assigneeId, setAssigneeId] = useState('');
  const [internalNotes, setInternalNotes] = useState('');

  useEffect(() => {
    if (!user || !isAdmin()) {
      router.push('/admin');
      return;
    }
    
    if (params.id) {
      fetchTicketDetails();
      fetchAdminUsers();
    }
  }, [params.id, user, isAdmin, router]);

  const fetchAdminUsers = async () => {
    try {
      const response = await fetch(`/api/admin/users?role=admin&userId=${user?.id}`, {
        credentials: 'include'
      });
      if (response.ok) {
        const data = await response.json();
        setAdminUsers(data.users?.map((u: any) => ({ id: u._id, name: u.name })) || []);
      }
    } catch (error) {
      console.error('Error fetching admin users:', error);
    }
  };

  const fetchTicketDetails = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/help/tickets/${params.id}?userId=${user?.id}`, {
        credentials: 'include'
      });
      if (!response.ok) {
        if (response.status === 404) {
          toast.error('Ticket not found');
          router.push('/admin/help');
          return;
        }
        throw new Error('Failed to fetch ticket details');
      }

      const data = await response.json();
      setTicket(data.ticket);
      setResponses(data.responses);
      setInternalNotes(data.ticket.internalNotes || '');
    } catch (error) {
      console.error('Error fetching ticket details:', error);
      toast.error('Failed to load ticket details');
    } finally {
      setLoading(false);
    }
  };

  const handleAddResponse = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newResponse.trim()) return;

    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/help/tickets/${params.id}/responses`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          userId: user?.id,
          message: newResponse.trim(),
          isInternal
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to add response');
      }

      const data = await response.json();
      setResponses(prev => [...prev, data.response]);
      setNewResponse('');
      setIsInternal(false);
      toast.success('Response added successfully');
      
      fetchTicketDetails();
    } catch (error) {
      console.error('Error adding response:', error);
      toast.error('Failed to add response');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleTicketAction = async (action: string, data?: any) => {
    try {
      const response = await fetch(`/api/help/tickets/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          userId: user?.id,
          ...data
        }),
      });

      if (!response.ok) throw new Error(`Failed to ${action} ticket`);

      toast.success(`Ticket ${action} successfully`);
      fetchTicketDetails();
    } catch (error) {
      console.error(`Error ${action} ticket:`, error);
      toast.error(`Failed to ${action} ticket`);
    }
  };

  const handleDeleteTicket = async () => {
    try {
      const response = await fetch(`/api/help/tickets/${params.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${user?.id}`,
          'Cache-Control': 'no-cache'
        },
        credentials: 'include',
      });

      if (!response.ok) throw new Error('Failed to delete ticket');

      toast.success('Ticket deleted successfully');
      router.push('/admin/help');
    } catch (error) {
      console.error('Error deleting ticket:', error);
      toast.error('Failed to delete ticket');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-vista-light/20 rounded w-1/3 mb-6"></div>
          <div className="h-96 bg-vista-light/20 rounded"></div>
        </div>
      </div>
    );
  }

  if (!ticket) {
    return (
      <div className="p-6 text-center">
        <h1 className="text-2xl font-bold text-vista-light mb-4">Ticket Not Found</h1>
        <Link href="/admin/help">
          <Button variant="outline" className="border-vista-light/20 text-vista-light">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Help Center
          </Button>
        </Link>
      </div>
    );
  }

  const StatusIcon = statusConfig[ticket.status].icon;

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/admin/help">
            <Button variant="outline" size="sm" className="border-vista-light/20 text-vista-light">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-vista-light">{ticket.subject}</h1>
            <p className="text-vista-light/70">Ticket #{ticket.ticketNumber}</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={fetchTicketDetails}
            className="border-vista-light/20 text-vista-light"
          >
            <RefreshCw className="w-4 h-4 mr-1" />
            Refresh
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => setIsDeleteDialogOpen(true)}
          >
            <Trash2 className="w-4 h-4 mr-1" />
            Delete
          </Button>
        </div>
      </div>

      {/* Ticket Details */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Ticket Info */}
          <Card className="bg-vista-card border-vista-light/10">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-vista-light">Ticket Details</CardTitle>
                <div className="flex items-center gap-2">
                  <Badge className={statusConfig[ticket.status].color}>
                    <StatusIcon className="w-3 h-3 mr-1" />
                    {statusConfig[ticket.status].label}
                  </Badge>
                  <Badge className={priorityConfig[ticket.priority].color}>
                    {priorityConfig[ticket.priority].label}
                  </Badge>
                  {ticket.escalated && (
                    <Badge className="bg-red-500/20 text-red-400">
                      Escalated
                    </Badge>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold text-vista-light mb-2">Description</h3>
                <p className="text-vista-light/80 whitespace-pre-wrap">{ticket.description}</p>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-vista-light/60">Category:</span>
                  <span className="text-vista-light ml-2">{ticket.category.replace('_', ' ')}</span>
                </div>
                <div>
                  <span className="text-vista-light/60">Created:</span>
                  <span className="text-vista-light ml-2">{formatDate(ticket.createdAt)}</span>
                </div>
                <div>
                  <span className="text-vista-light/60">Customer:</span>
                  <span className="text-vista-light ml-2">{ticket.userId.name} ({ticket.userId.email})</span>
                </div>
                <div>
                  <span className="text-vista-light/60">Last Updated:</span>
                  <span className="text-vista-light ml-2">{formatDate(ticket.updatedAt)}</span>
                </div>
                {ticket.assignedTo && (
                  <div>
                    <span className="text-vista-light/60">Assigned to:</span>
                    <span className="text-vista-light ml-2">{ticket.assignedTo.name}</span>
                  </div>
                )}
                {ticket.resolvedAt && (
                  <div>
                    <span className="text-vista-light/60">Resolved:</span>
                    <span className="text-vista-light ml-2">{formatDate(ticket.resolvedAt)}</span>
                  </div>
                )}
              </div>

              {ticket.resolutionNotes && (
                <div>
                  <h4 className="font-semibold text-vista-light mb-2">Resolution Notes</h4>
                  <p className="text-vista-light/80 whitespace-pre-wrap">{ticket.resolutionNotes}</p>
                </div>
              )}

              {ticket.satisfactionRating && (
                <div>
                  <h4 className="font-semibold text-vista-light mb-2">Customer Satisfaction</h4>
                  <div className="flex items-center gap-2">
                    <div className="flex">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          className={`w-4 h-4 ${
                            star <= ticket.satisfactionRating!
                              ? 'text-yellow-400 fill-current'
                              : 'text-gray-400'
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-vista-light/70">({ticket.satisfactionRating}/5)</span>
                  </div>
                  {ticket.satisfactionFeedback && (
                    <p className="text-vista-light/80 mt-2">{ticket.satisfactionFeedback}</p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Conversation */}
          <Card className="bg-vista-card border-vista-light/10">
            <CardHeader>
              <CardTitle className="text-vista-light">Conversation</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {responses.map((response) => (
                  <div key={response._id} className="border-l-2 border-vista-light/20 pl-4">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="font-semibold text-vista-light">{response.responderId.name}</span>
                      <Badge
                        variant="outline"
                        className={`text-xs ${
                          response.responderType === 'admin'
                            ? 'border-vista-blue/30 text-vista-blue'
                            : 'border-vista-light/20 text-vista-light/60'
                        }`}
                      >
                        {response.responderType === 'admin' ? 'Support Team' : 'Customer'}
                      </Badge>
                      {response.isInternal && (
                        <Badge className="bg-orange-500/20 text-orange-400 text-xs">
                          <Shield className="w-3 h-3 mr-1" />
                          Internal
                        </Badge>
                      )}
                      <span className="text-xs text-vista-light/60">{formatDate(response.createdAt)}</span>
                    </div>
                    <p className="text-vista-light/80 whitespace-pre-wrap">{response.message}</p>
                  </div>
                ))}
              </div>

              {/* Add Response Form */}
              <Separator className="my-6 bg-vista-light/20" />
              <form onSubmit={handleAddResponse} className="space-y-4">
                <div>
                  <Label htmlFor="response" className="text-vista-light">Add Response</Label>
                  <Textarea
                    id="response"
                    placeholder="Type your response here..."
                    value={newResponse}
                    onChange={(e) => setNewResponse(e.target.value)}
                    className="bg-vista-dark border-vista-light/20 text-vista-light mt-2"
                    rows={4}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="internal"
                      checked={isInternal}
                      onChange={(e) => setIsInternal(e.target.checked)}
                      className="w-4 h-4 text-vista-blue bg-vista-dark border-vista-light/20 rounded focus:ring-vista-blue"
                    />
                    <Label htmlFor="internal" className="text-vista-light/70 text-sm">
                      Internal note (not visible to customer)
                    </Label>
                  </div>
                  <Button
                    type="submit"
                    disabled={isSubmitting || !newResponse.trim()}
                    className="bg-vista-blue hover:bg-vista-blue/90"
                  >
                    {isSubmitting ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        Sending...
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4 mr-2" />
                        Send Response
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>

        {/* Admin Actions Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card className="bg-vista-card border-vista-light/10">
            <CardHeader>
              <CardTitle className="text-vista-light">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {ticket.status !== 'resolved' && (
                <Button
                  onClick={() => {
                    setNewStatus('resolved');
                    setIsStatusDialogOpen(true);
                  }}
                  className="w-full bg-green-600 hover:bg-green-700"
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Mark as Resolved
                </Button>
              )}

              {ticket.status !== 'in_progress' && (
                <Button
                  onClick={() => handleTicketAction('updated', { status: 'in_progress' })}
                  variant="outline"
                  className="w-full border-yellow-500/30 text-yellow-400 hover:bg-yellow-500/10"
                >
                  <Clock className="w-4 h-4 mr-2" />
                  Mark as In Progress
                </Button>
              )}

              {ticket.status !== 'closed' && (
                <Button
                  onClick={() => handleTicketAction('updated', { status: 'closed' })}
                  variant="outline"
                  className="w-full border-gray-500/30 text-gray-400 hover:bg-gray-500/10"
                >
                  <XCircle className="w-4 h-4 mr-2" />
                  Close Ticket
                </Button>
              )}

              <Button
                onClick={() => setIsAssignDialogOpen(true)}
                variant="outline"
                className="w-full border-vista-blue/30 text-vista-blue hover:bg-vista-blue/10"
              >
                <UserPlus className="w-4 h-4 mr-2" />
                {ticket.assignedTo ? 'Reassign' : 'Assign'} Ticket
              </Button>

              {!ticket.escalated && (
                <Button
                  onClick={() => handleTicketAction('escalated', {
                    escalated: true,
                    escalatedAt: new Date(),
                    escalatedBy: user?.id
                  })}
                  variant="outline"
                  className="w-full border-orange-500/30 text-orange-400 hover:bg-orange-500/10"
                >
                  <AlertTriangle className="w-4 h-4 mr-2" />
                  Escalate Ticket
                </Button>
              )}
            </CardContent>
          </Card>

          {/* Internal Notes */}
          <Card className="bg-vista-card border-vista-light/10">
            <CardHeader>
              <CardTitle className="text-vista-light">Internal Notes</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                placeholder="Add internal notes (not visible to customer)..."
                value={internalNotes}
                onChange={(e) => setInternalNotes(e.target.value)}
                className="bg-vista-dark border-vista-light/20 text-vista-light mb-3"
                rows={4}
              />
              <Button
                onClick={() => handleTicketAction('updated', { internalNotes })}
                variant="outline"
                size="sm"
                className="w-full border-vista-light/20 text-vista-light"
              >
                <FileText className="w-4 h-4 mr-2" />
                Save Notes
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Status Update Dialog */}
      <Dialog open={isStatusDialogOpen} onOpenChange={setIsStatusDialogOpen}>
        <DialogContent className="bg-vista-dark border-vista-light/20">
          <DialogHeader>
            <DialogTitle className="text-vista-light">Update Ticket Status</DialogTitle>
            <DialogDescription className="text-vista-light/70">
              Mark this ticket as resolved and add resolution notes.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="resolution-notes" className="text-vista-light">Resolution Notes</Label>
              <Textarea
                id="resolution-notes"
                placeholder="Describe how this issue was resolved..."
                value={resolutionNotes}
                onChange={(e) => setResolutionNotes(e.target.value)}
                className="bg-vista-dark border-vista-light/20 text-vista-light mt-2"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsStatusDialogOpen(false);
                setResolutionNotes('');
              }}
              className="border-vista-light/20 text-vista-light"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                const updateData: any = { status: newStatus };
                if (newStatus === 'resolved') {
                  updateData.resolvedAt = new Date();
                  updateData.resolvedBy = user?.id;
                  if (resolutionNotes.trim()) {
                    updateData.resolutionNotes = resolutionNotes.trim();
                  }
                }
                handleTicketAction('updated', updateData);
                setIsStatusDialogOpen(false);
                setResolutionNotes('');
              }}
              className="bg-vista-blue hover:bg-vista-blue/90"
            >
              Update Status
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Assignment Dialog */}
      <Dialog open={isAssignDialogOpen} onOpenChange={setIsAssignDialogOpen}>
        <DialogContent className="bg-vista-dark border-vista-light/20">
          <DialogHeader>
            <DialogTitle className="text-vista-light">Assign Ticket</DialogTitle>
            <DialogDescription className="text-vista-light/70">
              Assign this ticket to an admin user for handling.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="assignee" className="text-vista-light">Assign to</Label>
              <Select value={assigneeId} onValueChange={setAssigneeId}>
                <SelectTrigger className="bg-vista-dark border-vista-light/20 text-vista-light mt-2">
                  <SelectValue placeholder="Select an admin user" />
                </SelectTrigger>
                <SelectContent className="bg-vista-dark border-vista-light/20">
                  {adminUsers.map((admin) => (
                    <SelectItem key={admin.id} value={admin.id}>
                      {admin.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsAssignDialogOpen(false);
                setAssigneeId('');
              }}
              className="border-vista-light/20 text-vista-light"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (assigneeId) {
                  const selectedAdmin = adminUsers.find(admin => admin.id === assigneeId);
                  handleTicketAction('assigned', {
                    assignedTo: assigneeId,
                    assignedToName: selectedAdmin?.name
                  });
                  setIsAssignDialogOpen(false);
                  setAssigneeId('');
                }
              }}
              disabled={!assigneeId}
              className="bg-vista-blue hover:bg-vista-blue/90"
            >
              Assign Ticket
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="bg-vista-dark border-vista-light/20">
          <DialogHeader>
            <DialogTitle className="text-vista-light">Delete Ticket</DialogTitle>
            <DialogDescription className="text-vista-light/70">
              Are you sure you want to delete this ticket? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              className="border-vista-light/20 text-vista-light"
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteTicket}
            >
              Delete Ticket
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { UserCheck, Upload, Trash2, AlertCircle, ArrowLeft, Check, UserPlus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { UserAvatar } from '@/components/UserAvatar';
import { useAuth } from '@/contexts/AuthContext';
import { useProfiles } from '@/contexts/ProfileContext';
import { toast } from 'sonner';
import { CloudinaryUploadWidget } from '@/components/CloudinaryUploadWidget';
import { Navbar } from '@/components/Navbar';
import Footer from '@/components/Footer';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

// Define the avatar options
const defaultAvatars = [
  '/avatars/avatar-1.png',
  '/avatars/avatar-2.png',
  '/avatars/avatar-3.png',
  '/avatars/avatar-4.png',
  '/avatars/avatar-5.png',
  '/avatars/avatar-6.png',
];

export default function EditProfilesPage() {
  const router = useRouter();
  const { user, isLoading: isAuthLoading } = useAuth();
  const { profiles, fetchProfiles, updateProfile, deleteProfile } = useProfiles();
  const [selectedProfileId, setSelectedProfileId] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [profileToDelete, setProfileToDelete] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [name, setName] = useState('');
  const [isKids, setIsKids] = useState(false);
  const [selectedAvatar, setSelectedAvatar] = useState('');
  const [isCustomAvatar, setIsCustomAvatar] = useState(false);
  const [nameError, setNameError] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [avatarUploadLoading, setAvatarUploadLoading] = useState(false);

  // Load profiles when the component mounts and auth is resolved
  useEffect(() => {
    // Wait for authentication to finish loading
    if (isAuthLoading) {
      return; // Do nothing until auth state is confirmed
    }

    const loadProfiles = async () => {
      setIsLoading(true); // Set profile loading state
      try {
        await fetchProfiles();
      } catch (error) {
        console.error('Error loading profiles:', error);
        toast.error('Failed to load profiles');
      } finally {
        setIsLoading(false); // Clear profile loading state
      }
    };
    
    if (user?.id) {
      // User is authenticated, load profiles
      loadProfiles();
    } else {
      // User is not authenticated, redirect to signin
      console.log('User not authenticated, redirecting to signin');
      router.push('/signin');
    }
  // Depend on auth loading state and user ID
  }, [isAuthLoading, user?.id, fetchProfiles, router]);
  
  // Load a profile for editing
  const editProfile = (profileId: string) => {
    const profile = profiles.find(p => p.id === profileId);
    if (profile) {
      setSelectedProfileId(profileId);
      setName(profile.name);
      setIsKids(profile.isKids);
      setSelectedAvatar(profile.avatar);
      // Determine if the avatar is custom or default
      setIsCustomAvatar(!defaultAvatars.includes(profile.avatar));
      setIsEditing(true);
    }
  };
  
  // Reset form
  const resetForm = () => {
    setSelectedProfileId(null);
    setName('');
    setIsKids(false);
    setSelectedAvatar('');
    setIsCustomAvatar(false);
    setNameError('');
    setIsEditing(false);
  };
  
  // Validate form on submit
  const validateForm = () => {
    if (!name.trim()) {
      setNameError('Please enter a profile name');
      return false;
    }
    if (name.length > 20) {
      setNameError('Profile name must be 20 characters or less');
      return false;
    }
    setNameError('');
    return true;
  };
  
  // Handle profile save
  const handleSaveProfile = async () => {
    if (!validateForm()) return;
    
    setIsSaving(true);
    
    try {
      const result = await updateProfile(selectedProfileId!, {
        name: name.trim(),
        avatar: selectedAvatar,
        isKids
      });
      
      if (result.success) {
        toast.success('Profile updated successfully');
        resetForm();
      } else {
        toast.error(result.error || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Error saving profile:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsSaving(false);
    }
  };
  
  // Handle delete confirmation
  const confirmDelete = (profileId: string) => {
    const profile = profiles.find(p => p.id === profileId);
    if (profile?.isPrimary) {
      toast.error("You can't delete the primary profile");
      return;
    }
    
    setProfileToDelete(profileId);
    setIsDeleting(true);
  };
  
  // Handle profile deletion
  const handleDeleteProfile = async () => {
    if (!profileToDelete) return;
    
    try {
      const result = await deleteProfile(profileToDelete);
      
      if (result.success) {
        toast.success('Profile deleted successfully');
        
        // If the deleted profile was being edited, reset the form
        if (selectedProfileId === profileToDelete) {
          resetForm();
        }
      } else {
        toast.error(result.error || 'Failed to delete profile');
      }
    } catch (error) {
      console.error('Error deleting profile:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsDeleting(false);
      setProfileToDelete(null);
    }
  };
  
  // Handle Cloudinary upload success
  const handleUploadSuccess = (url: string) => {
    setSelectedAvatar(url);
    setIsCustomAvatar(true);
    setAvatarUploadLoading(false);
    toast.success('Avatar uploaded successfully');
  };

  // Create upload button click handler
  const handleUploadClick = () => {
    setAvatarUploadLoading(true);
  };
  
  return (
    <div className="min-h-screen bg-vista-dark text-vista-light flex flex-col">
      <Navbar />
      
      {/* Header */}
      <div className="border-b border-vista-light/10 px-4 py-4 mt-16">
        <div className="container max-w-4xl mx-auto flex items-center justify-between">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.push('/profiles')}
              className="mr-2 text-vista-light/70 hover:text-vista-light"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-xl md:text-2xl font-bold">Manage Profiles</h1>
          </div>
          <div>
            <Button
              onClick={() => router.push('/profiles/new')}
              className="bg-vista-blue hover:bg-vista-blue/90"
              disabled={profiles.length >= 5}
            >
              Add Profile
            </Button>
          </div>
        </div>
      </div>
      
      {/* Content */}
      <div className="flex-1 container max-w-4xl mx-auto px-4 py-8">
        {isLoading ? (
          <div className="flex justify-center items-center py-12">
            <div className="h-12 w-12 border-4 border-vista-blue/30 border-t-vista-blue rounded-full animate-spin"></div>
          </div>
        ) : isEditing ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4 }}
            className="bg-vista-dark-lighter rounded-xl p-6 md:p-8"
          >
            {/* Add an indicator if editing the primary profile */}
            {profiles.find(p => p.id === selectedProfileId)?.isPrimary && (
              <div className="mb-6 p-4 bg-vista-blue/10 border border-vista-blue/30 rounded-lg">
                <h3 className="text-vista-blue font-medium text-lg flex items-center mb-2">
                  <UserPlus className="h-5 w-5 mr-2" />
                  Main Profile
                </h3>
                <p className="text-vista-light/80">
                  This is your main profile tied to your account. You can change its settings, 
                  but it cannot be deleted as it's connected to your account information.
                </p>
              </div>
            )}
          
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Avatar Selection */}
              <div className="flex flex-col items-center justify-start">
                <div className="relative mb-6 group">
                  {avatarUploadLoading ? (
                    <div className="h-32 w-32 flex items-center justify-center border-4 border-vista-blue rounded-full">
                      <div className="h-8 w-8 border-4 border-vista-blue/30 border-t-vista-blue rounded-full animate-spin"></div>
                    </div>
                  ) : (
                    <UserAvatar
                      src={selectedAvatar}
                      alt="Profile Avatar"
                      size="xl"
                      className={`h-32 w-32 border-4 ${isKids ? 'border-amber-500' : 'border-vista-blue'}`}
                    />
                  )}
                  {isKids && (
                    <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 bg-amber-500 text-black text-xs font-bold px-3 py-0.5 rounded-full">
                      KIDS
                    </div>
                  )}
                </div>
                
                <h3 className="font-medium text-lg mb-3">Choose Avatar</h3>
                
                <div className="grid grid-cols-3 gap-3">
                  {defaultAvatars.map((avatar) => (
                    <button
                      key={avatar}
                      onClick={() => {
                        setSelectedAvatar(avatar);
                        setIsCustomAvatar(false);
                      }}
                      className={`relative w-14 h-14 rounded-lg overflow-hidden border-2 transition-all ${
                        selectedAvatar === avatar && !isCustomAvatar
                          ? isKids 
                            ? 'border-amber-500 ring-2 ring-amber-500/30' 
                            : 'border-vista-blue ring-2 ring-vista-blue/30'
                          : 'border-transparent hover:border-vista-light/30'
                      }`}
                    >
                      <Image
                        src={avatar}
                        alt="Avatar option"
                        fill
                        sizes="56px"
                        className="object-cover"
                      />
                      {selectedAvatar === avatar && !isCustomAvatar && (
                        <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                          <Check className="h-5 w-5 text-white" />
                        </div>
                      )}
                    </button>
                  ))}
                </div>
                
                {/* Custom Avatar Option */}
                <div className="flex items-center gap-4 mt-4">
                  <CloudinaryUploadWidget
                    onUploadSuccess={handleUploadSuccess}
                    onUploadStart={handleUploadClick}
                    buttonText={avatarUploadLoading ? 'Uploading...' : 'Upload Custom Avatar'}
                    className="border-vista-light/30 hover:border-vista-light hover:bg-vista-light/10 text-sm"
                  />

                  {selectedAvatar && isCustomAvatar && (
                    <div className="relative w-16 h-16 rounded-full overflow-hidden border-2 border-vista-blue shadow-lg">
                      <Image src={selectedAvatar} alt="Custom Avatar" layout="fill" objectFit="cover" />
                    </div>
                  )}
                </div>
              </div>
              
              {/* Profile Details */}
              <div className="md:col-span-2 space-y-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="profile-name">Profile Name</Label>
                    <Input
                      id="profile-name"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      placeholder="Enter a profile name"
                      className="bg-vista-dark/60 border-vista-light/20 focus:border-vista-blue"
                    />
                    {nameError && (
                      <div className="flex items-center text-red-400 text-sm mt-1">
                        <AlertCircle className="h-4 w-4 mr-1" />
                        {nameError}
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center justify-between pt-3">
                    <div className="space-y-1">
                      <Label htmlFor="kids-profile" className="text-base flex items-center">
                        Kids Profile
                        <Badge className="ml-2 bg-amber-500 text-black text-xs">KIDS</Badge>
                      </Label>
                      <p className="text-vista-light/70 text-sm">
                        Content will be limited to child-friendly ratings
                      </p>
                    </div>
                    <Switch
                      id="kids-profile"
                      checked={isKids}
                      onCheckedChange={setIsKids}
                    />
                  </div>
                  
                  {isKids && (
                    <div className="p-4 bg-vista-dark/40 rounded-lg border border-amber-500/30 mt-4">
                      <h4 className="font-medium text-amber-400 flex items-center mb-2">
                        <UserCheck className="h-4 w-4 mr-2" />
                        Kids Profile Features
                      </h4>
                      <ul className="text-sm text-vista-light/80 space-y-1 ml-6 list-disc">
                        <li>Child-friendly content only</li>
                        <li>No mature themes or explicit language</li>
                        <li>Simplified navigation experience</li>
                        <li>Parental controls and monitoring</li>
                      </ul>
                    </div>
                  )}
                </div>
                
                <div className="pt-6 flex flex-col md:flex-row md:justify-end space-y-3 md:space-y-0 md:space-x-3">
                  <Button
                    variant="outline"
                    onClick={resetForm}
                    className="border-vista-light/20 hover:bg-vista-light/10"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSaveProfile}
                    disabled={isSaving}
                    className="bg-vista-blue hover:bg-vista-blue/90"
                  >
                    {isSaving ? (
                      <>
                        <div className="h-4 w-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                        Saving...
                      </>
                    ) : (
                      'Save Changes'
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </motion.div>
        ) : (
          <div className="space-y-6">
            <p className="text-vista-light/70 mb-6">
              Choose a profile to edit or add a new profile.
            </p>
            
            {/* Main profile section */}
            {profiles.some(p => p.isPrimary) && (
              <div className="mb-8">
                <h2 className="text-lg font-medium text-vista-light/70 mb-4 flex items-center">
                  <UserPlus className="h-4 w-4 mr-2 text-vista-blue" />
                  Main Profile
                </h2>
                
                {profiles
                  .filter(profile => profile.isPrimary)
                  .map((profile) => (
                    <div 
                      key={profile.id}
                      className="bg-vista-dark-lighter rounded-lg p-4 border-l-4 border-vista-blue flex items-center justify-between hover:bg-vista-dark-lighter/80 transition-colors"
                    >
                      <div className="flex items-center">
                        <div className="relative mr-4">
                          <UserAvatar
                            src={profile.avatar}
                            alt={profile.name}
                            size="md"
                            className="border-2 border-vista-blue"
                          />
                          <div className="absolute -top-1 -right-1 bg-vista-blue rounded-full w-5 h-5 flex items-center justify-center">
                            <UserCheck className="h-3 w-3 text-white" />
                          </div>
                        </div>
                        <div>
                          <h3 className="font-medium flex items-center">
                            {profile.name}
                            <Badge className="ml-2 bg-vista-blue text-white">Main</Badge>
                          </h3>
                          <div className="flex items-center text-sm text-vista-light/60 mt-1">
                            <span className="mr-2">Connected to your account</span>
                            {profile.isKids && (
                              <Badge className="bg-amber-500 text-black text-xs">KIDS</Badge>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          className="border-vista-light/20 hover:bg-vista-light/10"
                          onClick={() => editProfile(profile.id)}
                        >
                          Edit
                        </Button>
                      </div>
                    </div>
                  ))}
              </div>
            )}
            
            {/* Other profiles section */}
            {profiles.some(p => !p.isPrimary) && (
              <div>
                <h2 className="text-lg font-medium text-vista-light/70 mb-4">
                  {profiles.some(p => p.isPrimary) ? 'Other Profiles' : 'Your Profiles'}
                </h2>
                
                <div className="grid grid-cols-1 gap-4">
                  {profiles
                    .filter(profile => !profile.isPrimary)
                    .map((profile) => (
                      <div 
                        key={profile.id}
                        className="bg-vista-dark-lighter rounded-lg p-4 flex items-center justify-between"
                      >
                        <div className="flex items-center">
                          <div className="relative mr-4">
                            <UserAvatar
                              src={profile.avatar}
                              alt={profile.name}
                              size="md"
                              className={profile.isKids ? 'border-2 border-amber-500' : ''}
                            />
                          </div>
                          <div>
                            <h3 className="font-medium">{profile.name}</h3>
                            <div className="flex items-center text-sm text-vista-light/60 mt-1">
                              {profile.isKids && (
                                <Badge className="bg-amber-500 text-black text-xs">KIDS</Badge>
                              )}
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            className="border-vista-light/20 hover:bg-vista-light/10"
                            onClick={() => editProfile(profile.id)}
                          >
                            Edit
                          </Button>
                          
                          <Button
                            size="sm"
                            variant="destructive"
                            className="bg-red-900/60 hover:bg-red-900/80"
                            onClick={() => confirmDelete(profile.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
      
      {/* Delete Profile Confirmation Dialog */}
      <AlertDialog open={isDeleting} onOpenChange={setIsDeleting}>
        <AlertDialogContent className="bg-vista-dark-lighter border-vista-light/10">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-vista-light">
              Delete Profile?
            </AlertDialogTitle>
            <AlertDialogDescription className="text-vista-light/70">
              This will permanently delete the profile and all its settings.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel 
              className="bg-transparent border-vista-light/20 hover:bg-vista-light/10 text-vista-light"
              onClick={() => setIsDeleting(false)}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-700 hover:bg-red-800 text-white"
              onClick={handleDeleteProfile}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      <Footer />
    </div>
  );
} 
'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import UserActivityLogs from '@/components/admin/UserActivityLogs';
import UserActivityManagement from '@/components/admin/UserActivityManagement';
import { Tabs, TabsContent, TabsList } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Activity, AlertCircle, ArrowLeft, RefreshCw, Settings } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';

interface UserActivityLogsProps {
  userId?: string;
  limit?: number;
  autoRefresh?: boolean;
  refreshInterval?: number;
  onError?: (message: string) => void;
}

export default function AdminActivityPage() {
  const { user, isAdmin } = useAuth();
  const router = useRouter();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('logs');
  const [refreshKey, setRefreshKey] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Ensure authentication is loaded
  useEffect(() => {
    // In development mode, set a default userId if not present
    if (process.env.NODE_ENV === 'development') {
      if (!localStorage.getItem('userId')) {
        localStorage.setItem('userId', 'dev-admin-id');
        console.log('Set default userId for development');
      }
    }

    if (user === null) {
      // User is not logged in
      if (process.env.NODE_ENV !== 'development') {
        router.push('/login?returnUrl=/admin/activity');
      }
    } else if (user && !isAdmin()) {
      // User is logged in but not an admin
      if (process.env.NODE_ENV !== 'development') {
        router.push('/');
      }
    }
  }, [user, isAdmin, router]);

  // Track if component is mounted
  const isMounted = useRef(true);
  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Reference to the UserActivityLogs component for direct refresh
  const activityLogsRef = useRef<{ fetchLogs: (silent: boolean) => Promise<void> } | null>(null);

  // Track last refresh time to prevent spamming
  const lastRefreshTimeRef = useRef<number>(0);
  const REFRESH_COOLDOWN = 5000; // 5 seconds cooldown between manual refreshes

  // Track if rate limiting is active
  const [rateLimitActive, setRateLimitActive] = useState(false);

  // Track management component errors to prevent tab switching issues
  const [managementError, setManagementError] = useState<string | null>(null);
  const [managementLoading, setManagementLoading] = useState(false);

  // Prevent accidental double tab switching
  const isTabSwitchingRef = useRef(false);

  // Check for circuit breaker in session storage
  useEffect(() => {
    try {
      // Check for circuit breaker
      const circuitOpen = sessionStorage.getItem('circuitBreakerOpen') === 'true';
      const circuitResetTimeStr = sessionStorage.getItem('circuitBreakerResetTime');

      if (circuitOpen && circuitResetTimeStr) {
        const circuitResetTime = parseInt(circuitResetTimeStr, 10);
        const now = Date.now();

        if (now < circuitResetTime) {
          // Circuit breaker is still open
          const remainingSeconds = Math.ceil((circuitResetTime - now) / 1000);
          console.log(`Circuit breaker is open. Resets in ${remainingSeconds}s`);
          setRateLimitActive(true);
          setError(`API temporarily unavailable due to rate limiting. Please wait ${remainingSeconds} seconds before trying again.`);
        } else {
          // Circuit breaker has expired, clear it
          console.log('Circuit breaker has expired. Clearing status.');
          sessionStorage.removeItem('circuitBreakerOpen');
          sessionStorage.removeItem('circuitBreakerResetTime');
        }
      }
    } catch (e) {
      // Ignore storage errors
    }
  }, []);

  // Function to refresh activity logs without remounting the component
  const refreshLogs = () => {
    // Check if rate limiting is active
    if (rateLimitActive) {
      console.warn('Refresh blocked: Rate limiting is active');

      toast({
        title: 'Rate Limit Active',
        description: (
          <div className="flex flex-col gap-2">
            <p>API access is temporarily limited. This might be a false positive.</p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                try {
                  // Clear circuit breaker in session storage
                  sessionStorage.removeItem('circuitBreakerOpen');
                  sessionStorage.removeItem('circuitBreakerResetTime');
                  setRateLimitActive(false);
                  setError(null);
                  toast({
                    title: 'Rate Limiting Reset',
                    description: 'Rate limiting has been cleared. You can now refresh again.',
                    variant: 'default'
                  });
                } catch (e) {
                  console.error('Failed to clear circuit breaker:', e);
                }
              }}
              className="w-full"
            >
              Clear Rate Limiting
            </Button>
          </div>
        ),
        variant: 'default'
      });

      return;
    }

    // Check if circuit breaker is open
    try {
      const circuitOpen = sessionStorage.getItem('circuitBreakerOpen') === 'true';
      const circuitResetTimeStr = sessionStorage.getItem('circuitBreakerResetTime');

      if (circuitOpen && circuitResetTimeStr) {
        const circuitResetTime = parseInt(circuitResetTimeStr, 10);
        const now = Date.now();

        if (now < circuitResetTime) {
          // Circuit breaker is still open
          const remainingSeconds = Math.ceil((circuitResetTime - now) / 1000);
          console.log(`Refresh blocked: Circuit breaker is open. Resets in ${remainingSeconds}s`);

          toast({
            title: 'API Temporarily Unavailable',
            description: (
              <div className="flex flex-col gap-2">
                <p>The API is temporarily unavailable due to excessive requests. This might be a false positive detection.</p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    try {
                      // Clear circuit breaker in session storage
                      sessionStorage.removeItem('circuitBreakerOpen');
                      sessionStorage.removeItem('circuitBreakerResetTime');
                      setRateLimitActive(false);
                      setError(null);
                      toast({
                        title: 'Circuit Breaker Reset',
                        description: 'Circuit breaker has been reset. You can now refresh again.',
                        variant: 'default'
                      });
                    } catch (e) {
                      console.error('Failed to clear circuit breaker:', e);
                    }
                  }}
                  className="w-full"
                >
                  Reset Circuit Breaker
                </Button>
              </div>
            ),
            variant: 'destructive'
          });

          setError(`API temporarily unavailable due to excessive requests. Please wait ${remainingSeconds} seconds before trying again.`);
          return;
        } else {
          // Circuit breaker has expired, clear it
          console.log('Circuit breaker has expired. Clearing status.');
          sessionStorage.removeItem('circuitBreakerOpen');
          sessionStorage.removeItem('circuitBreakerResetTime');
        }
      }
    } catch (e) {
      // Ignore storage errors
    }

    // Check if we're in cooldown period
    const now = Date.now();
    const timeSinceLastRefresh = now - lastRefreshTimeRef.current;

    if (timeSinceLastRefresh < REFRESH_COOLDOWN) {
      const remainingCooldown = Math.ceil((REFRESH_COOLDOWN - timeSinceLastRefresh) / 1000);
      console.log(`Refresh on cooldown. Please wait ${remainingCooldown} seconds.`);
      toast({
        title: 'Refresh Cooldown',
        description: `Please wait ${remainingCooldown} seconds before refreshing again.`,
        variant: 'default'
      });
      return;
    }

    // Update last refresh time
    lastRefreshTimeRef.current = now;

    setError(null);

    // Set loading state to show spinner
    setIsLoading(true);

    console.log("Refreshing logs, activityLogsRef exists:", !!activityLogsRef.current);

    // If we have a reference to the component, call its fetchLogs method directly
    if (activityLogsRef.current) {
      try {
        activityLogsRef.current.fetchLogs(false)
          .then(() => {
            console.log("Fetch logs completed successfully");
            // Reset loading state immediately
            setIsLoading(false);
          })
          .catch((err) => {
            // Log the error and ensure loading state is reset
            console.error("Error fetching logs:", err);
            setIsLoading(false);
            setError("Failed to refresh logs. Please try again.");
          });
      } catch (e) {
        console.error("Exception calling fetchLogs:", e);
        setIsLoading(false);
        setError("An error occurred while refreshing logs.");
      }
    } else {
      console.log("Using fallback refresh method (incrementing key)");
      // Fallback to the old method if we don't have a reference
      setRefreshKey(prev => prev + 1);

      // Reset loading state after a short delay
      setTimeout(() => {
        setIsLoading(false);
      }, 300);
    }
  };

  // Handle activity management completion
  const handleManagementComplete = () => {
    setManagementError(null);
    refreshLogs();
  };

  // Handle errors from activity logs component
  const handleError = useCallback((errorMessage: string) => {
    if (isMounted.current) {
      setError(errorMessage);
      toast({
        variant: "destructive",
        title: "Error",
        description: errorMessage,
      });
    }
  }, [toast]);

  // Handle errors specifically for the management component
  const handleManagementError = useCallback((errorMessage: string) => {
    if (isMounted.current) {
      setManagementError(errorMessage);

      // Only show toast if this is a serious error
      if (errorMessage.includes('authentication') || errorMessage.includes('permission')) {
        toast({
          variant: "destructive",
          title: "Management Error",
          description: errorMessage,
        });
      }
    }
  }, [toast]);

  // Handle management loading state
  const handleManagementLoadingChange = (loading: boolean) => {
    setManagementLoading(loading);
  };

  // Handle tab change with debounce and safety checks
  const handleTabChange = (value: string) => {
    // Prevent rapid tab switching
    if (isTabSwitchingRef.current) {
      console.log("Tab switch blocked: Switch already in progress");
      return;
    }

    // Don't allow switching if management component is loading
    if (managementLoading && activeTab === 'manage') {
      console.log("Tab switch blocked: Management operation in progress");
      toast({
        title: 'Operation in Progress',
        description: 'Please wait for the current operation to complete before switching tabs.',
        variant: 'default'
      });
      return;
    }

    // Set the switching lock
    isTabSwitchingRef.current = true;

    // Log the tab change
    console.log("Tab changed to:", value);

    // Clear errors when switching tabs
    setError(null);

    // Set the active tab
    setActiveTab(value);

    // Briefly show loading state when switching to logs tab
    if (value === 'logs') {
      setIsLoading(true);
      setTimeout(() => {
        if (isTabSwitchingRef.current) {
          setIsLoading(false);
          // Release the lock after a short delay
          isTabSwitchingRef.current = false;
        }
      }, 300);
    } else {
      // For other tabs, release the lock after a short delay
      setTimeout(() => {
        isTabSwitchingRef.current = false;
      }, 300);
    }
  };

  // Check authentication and set loading state
  useEffect(() => {
    // Start with loading state
    setIsLoading(true);

    let mounted = true;

    // Check if user is authenticated
    const checkAuth = async () => {
      try {
        // Check if user object exists in auth context
        // We'll continue even if not authenticated, as we're using cookies for auth
        if (!user) {
          console.log('No user object found in auth context, but continuing with cookies');
        }

        // We'll skip the admin check here and let the server-side middleware handle it
        // This is because we're using cookies for authentication
        console.log('Skipping client-side admin check, relying on server-side middleware');

        // If we get here, we'll proceed with the request
        console.log('Proceeding with admin activity page load');

        // Set a timer to ensure minimum loading indicator display time (for UX)
        const timer = setTimeout(() => {
          if (mounted) {
            setIsLoading(false);
          }
        }, 600); // Reduced from 800ms to 600ms for better UX

        return () => clearTimeout(timer);
      } catch (error) {
        console.error('Error checking authentication:', error);
        if (mounted) {
          setError('Error checking authentication. Please try refreshing the page.');
          setIsLoading(false);
        }
      }
    };

    checkAuth();

    // Clean up on unmount
    return () => {
      mounted = false;
    };
  }, [isAdmin, user]);

  // Reset loading state when changing tabs to ensure proper loading UI
  useEffect(() => {
    if (activeTab === 'logs') {
      // Brief loading state when switching to logs tab
      setIsLoading(true);
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 300); // Reduced from 400ms for better UX

      return () => clearTimeout(timer);
    }
  }, [activeTab]);

  return (
    <div className="container mx-auto p-4 space-y-4">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">User Activity Dashboard</h1>
          <p className="text-sm text-muted-foreground">
            Monitor and manage user activities across the platform
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.push('/admin')}
          className="flex items-center gap-1"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Admin
        </Button>
      </div>

      <Tabs
        value={activeTab}
        onValueChange={handleTabChange}
        className="w-full"
      >
        <div className="mb-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-2 bg-vista-dark-lighter/30 p-1 rounded-lg">
              <Button
                variant={activeTab === 'logs' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => handleTabChange('logs')}
                className={`flex items-center gap-2 px-4 py-2 transition-all ${activeTab === 'logs' ? 'bg-vista-blue text-white shadow-md' : 'hover:bg-vista-dark-lighter'}`}
              >
                <Activity className="h-4 w-4" />
                Activity Logs
              </Button>
              <Button
                variant={activeTab === 'manage' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => handleTabChange('manage')}
                className={`flex items-center gap-2 px-4 py-2 transition-all ${activeTab === 'manage' ? 'bg-vista-blue text-white shadow-md' : 'hover:bg-vista-dark-lighter'}`}
              >
                <Settings className="h-4 w-4" />
                Management
              </Button>
            </div>

            {activeTab === 'logs' && (
              <div className="flex items-center gap-2">
                {rateLimitActive && (
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => {
                      try {
                        // Clear circuit breaker in session storage
                        sessionStorage.removeItem('circuitBreakerOpen');
                        sessionStorage.removeItem('circuitBreakerResetTime');
                        setRateLimitActive(false);
                        setError(null);
                        toast({
                          title: 'Rate Limiting Reset',
                          description: 'Rate limiting has been cleared. You can now refresh again.',
                          variant: 'default'
                        });
                      } catch (e) {
                        console.error('Failed to clear circuit breaker:', e);
                      }
                    }}
                    className="hover:bg-red-600"
                  >
                    <AlertCircle className="mr-2 h-4 w-4" />
                    Reset Rate Limit
                  </Button>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={refreshLogs}
                  disabled={isLoading || rateLimitActive}
                  className="ml-auto hover:bg-vista-blue/10 hover:text-vista-blue hover:border-vista-blue/30"
                >
                  <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
              </div>
            )}
          </div>
        </div>

        <TabsContent value="logs" className="mt-0">
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Card>
            <CardContent className="p-0">
              {isLoading && !activityLogsRef.current ? (
                <div className="p-6 space-y-4">
                  <Skeleton className="h-8 w-full" />
                  <Skeleton className="h-64 w-full" />
                </div>
              ) : (
                <UserActivityLogs
                  key={refreshKey}
                  ref={(ref) => {
                    // Store reference to the component instance
                    if (ref && typeof ref === 'object' && 'fetchLogs' in ref) {
                      activityLogsRef.current = ref as { fetchLogs: (silent: boolean) => Promise<void> };
                    }
                  }}
                  limit={100}
                  autoRefresh={true}
                  refreshInterval={300000} // 5 minutes
                  onError={handleError}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="manage" className="mt-0">
          {managementError && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{managementError}</AlertDescription>
            </Alert>
          )}

          <UserActivityManagement
            onComplete={handleManagementComplete}
            onError={handleManagementError}
            onLoadingChange={handleManagementLoadingChange}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}

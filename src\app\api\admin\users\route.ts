import { NextRequest, NextResponse } from 'next/server';
import User from '@/models/User';
import { ensureMongooseConnection } from '@/lib/mongodb';
import mongoose from 'mongoose';

/**
 * GET /api/admin/users
 * Get user list with pagination and search
 */
export async function GET(request: NextRequest) {
  try {
    // Try to get the user ID from the cookie directly
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, try to get it from the request body
    if (!userId) {
      try {
        const body = await request.json();
        userId = body.userId;
        // We don't need to clone the request for NextRequest
        // Just note that we've already consumed the body
      } catch (error) {
        // Ignore JSON parsing errors
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    if (!userId) {
      console.error('Admin users API: No user ID found in request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as { role?: string }).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('q') || '';
    const role = searchParams.get('role') || '';
    const status = searchParams.get('status') || ''; // Status filter (active/inactive)
    const dateFrom = searchParams.get('dateFrom') || '';
    const dateTo = searchParams.get('dateTo') || '';
    const subscription = searchParams.get('subscription') || '';
    const verified = searchParams.get('verified');

    // Define a type for MongoDB query
    interface MongoQuery {
      $or?: Array<Record<string, unknown>>;
      role?: string;
      status?: string;
      emailVerified?: { $ne: null } | null;
      createdAt?: {
        $gte?: Date;
        $lte?: Date;
      };
    }

    // Build query object
    const query: MongoQuery = {};

    // Add search condition
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    // Add role filter
    if (role) {
      query.role = role;
    }

    // Add status filter
    if (status) {
      query.status = status;
    }

    // Add verification status filter
    if (verified !== null && verified !== undefined) {
      if (verified === 'true') {
        query.emailVerified = { $ne: null };
      } else if (verified === 'false') {
        query.emailVerified = null;
      }
    }

    // Add date range filter
    if (dateFrom || dateTo) {
      query.createdAt = {};

      if (dateFrom) {
        query.createdAt.$gte = new Date(dateFrom);
      }

      if (dateTo) {
        query.createdAt.$lte = new Date(dateTo);
      }
    }

    // Add subscription filter (in a real app, this would filter by subscription plan)
    // This is just a placeholder since we don't have subscription data in our model yet
    if (subscription) {
      // In a real app: query.subscription.plan = subscription;
      // For now, we'll just log it
      console.log(`Filtering by subscription: ${subscription}`);
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get direct access to the users collection for more reliable data access
    if (!mongoose.connection.db) {
      throw new Error('Database connection not established');
    }
    const db = mongoose.connection.db;
    const usersCollection = db.collection('users');

    // Fetch users with pagination
    const users = await usersCollection.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .toArray();

    // Get total count for pagination
    const total = await usersCollection.countDocuments(query);

    // Map users to the response format
    const mappedUsers = users.map((user) => {
      // Removed excessive logging

      // Handle the lastLogin field - ensure we're using the actual timestamp without manipulation
      let lastLoginISO = null;
      if (user.lastLogin) {
        try {
          // Convert to Date object and then to ISO string
          // Make sure we're using the actual date stored in MongoDB
          const lastLoginDate = new Date(user.lastLogin);

          // Validate the date is not in the future
          const now = new Date();
          if (lastLoginDate > now) {
            console.warn(`Future lastLogin date detected for user ${user._id}, using current date instead`);
            lastLoginISO = now.toISOString();
          } else {
            lastLoginISO = lastLoginDate.toISOString();
          }
        } catch (error) {
          console.error(`Error converting lastLogin for user ${user._id}`);
        }
      }

      // Process profile image URLs to ensure they're valid
      const processImageUrl = (url?: string) => {
        if (!url) return null;

        // Check if the URL is a relative path (which won't work)
        if (!url.startsWith('http') && !url.startsWith('data:')) {
          return "https://res.cloudinary.com/streamvista/image/upload/v1743812698/defaults/default_avatar.jpg";
        }

        // If it's a Cloudinary URL, ensure it has the correct cloud name (lowercase)
        if (url.includes('cloudinary.com')) {
          // Check if the URL contains the correct cloud name (should be lowercase)
          if (!url.includes('cloudinary.com/streamvista/')) {
            // Fix the URL by replacing the cloud name with the lowercase version
            return url.replace(/cloudinary\.com\/([^\/]+)\//, 'cloudinary.com/streamvista/');
          }
        }
        return url;
      };

      // Default Cloudinary avatar URL
      const defaultAvatarUrl = "https://res.cloudinary.com/streamvista/image/upload/v1743812698/defaults/default_avatar.jpg";

      // Get profile image, ensuring it has the correct format
      let profileImage = processImageUrl(user.profileImage || user.picture) || defaultAvatarUrl;
      let picture = processImageUrl(user.picture || user.profileImage) || defaultAvatarUrl;

      // Add cache busting for Cloudinary URLs
      if (profileImage.includes('cloudinary.com')) {
        const separator = profileImage.includes('?') ? '&' : '?';
        profileImage = `${profileImage}${separator}t=${Date.now()}`;
      }

      if (picture.includes('cloudinary.com')) {
        const separator = picture.includes('?') ? '&' : '?';
        picture = `${picture}${separator}t=${Date.now()}`;
      }

      return {
        id: user._id.toString(),
        name: user.name,
        email: user.email,
        profileImage,
        picture,
        role: user.role,
        status: user.status || 'active',
        emailVerified: user.emailVerified,
        // Use the converted lastLogin or null
        lastLogin: lastLoginISO,
        // Handle subscription which might be an object or a string
        subscription: typeof user.subscription === 'object'
          ? (user.subscription.plan || 'Free Plan')
          : (user.subscription || 'Free Plan'),
        subscriptionStatus: user.subscriptionStatus || 'active',
        subscriptionRenewal: user.subscriptionRenewal,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      };
    });

    // Return users with pagination info
    return NextResponse.json({
      users: mappedUsers,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/users
 * Create a new user
 */
export async function POST(request: NextRequest) {
  try {
    // Try to get the user ID from the cookie directly
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    if (!userId) {
      console.error('Admin users API: No user ID found in request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Check if user is admin
    const adminUser = await User.findById(userId).select('role').lean();
    if (!adminUser || (adminUser as { role?: string }).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Parse the request body
    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.email) {
      return NextResponse.json({ error: 'Name and email are required' }, { status: 400 });
    }

    // Check if email already exists
    const existingUser = await User.findOne({ email: body.email });
    if (existingUser) {
      return NextResponse.json({ error: 'Email already exists' }, { status: 400 });
    }

    // Generate a random password if not provided
    if (!body.password) {
      body.password = Math.random().toString(36).slice(-8);
    }

    // Create the user
    const profileImageToUse = body.profileImage || '';
    const user = await User.create({
      name: body.name,
      email: body.email,
      password: body.password, // Will be hashed by the pre-save hook in the User model
      role: body.role || 'user',
      status: body.status || 'active',
      profileImage: profileImageToUse,
      picture: profileImageToUse, // Also set picture field to ensure consistency
      emailVerified: body.emailVerified ? new Date() : null,
    });

    // Return the created user (without password)
    return NextResponse.json({
      id: user._id.toString(),
      name: user.name,
      email: user.email,
      role: user.role,
      status: user.status,
      profileImage: user.profileImage,
      createdAt: user.createdAt,
      emailVerified: user.emailVerified
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { error: 'Failed to create user', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, MoreHorizontal } from 'lucide-react';

interface EnhancedPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  isLoading?: boolean;
  totalItems?: number;
  itemsPerPage?: number;
  showPageInput?: boolean;
  showQuickJump?: boolean;
  compact?: boolean;
}

export function EnhancedPagination({
  currentPage,
  totalPages,
  onPageChange,
  isLoading = false,
  totalItems,
  itemsPerPage,
  showPageInput = true,
  showQuickJump = true,
  compact = false
}: EnhancedPaginationProps) {
  const [inputPage, setInputPage] = useState(currentPage.toString());
  const [isInputFocused, setIsInputFocused] = useState(false);

  // Update input when current page changes (only if input is empty or not focused)
  useEffect(() => {
    if (!isInputFocused && (!inputPage || inputPage === '')) {
      console.log('Updating empty input to current page:', currentPage);
      setInputPage(currentPage.toString());
    }
  }, [currentPage, isInputFocused, inputPage]);

  // Calculate the range of items being displayed
  const startItem = totalItems && itemsPerPage ? ((currentPage - 1) * itemsPerPage) + 1 : null;
  const endItem = totalItems && itemsPerPage ? Math.min(currentPage * itemsPerPage, totalItems) : null;

  // Generate page numbers to display with improved logic
  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = compact ? 5 : 7;
    
    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is small
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);
      
      // Calculate range around current page
      let rangeStart = Math.max(2, currentPage - Math.floor(maxVisiblePages / 2));
      let rangeEnd = Math.min(totalPages - 1, currentPage + Math.floor(maxVisiblePages / 2));
      
      // Adjust range to maintain consistent number of visible pages
      if (rangeEnd - rangeStart < maxVisiblePages - 3) {
        if (currentPage < totalPages / 2) {
          rangeEnd = Math.min(totalPages - 1, rangeStart + maxVisiblePages - 3);
        } else {
          rangeStart = Math.max(2, rangeEnd - maxVisiblePages + 3);
        }
      }
      
      // Add ellipsis before range if needed
      if (rangeStart > 2) {
        pages.push('ellipsis-start');
      }
      
      // Add range pages
      for (let i = rangeStart; i <= rangeEnd; i++) {
        pages.push(i);
      }
      
      // Add ellipsis after range if needed
      if (rangeEnd < totalPages - 1) {
        pages.push('ellipsis-end');
      }
      
      // Always show last page if there is more than one page
      if (totalPages > 1) {
        pages.push(totalPages);
      }
    }
    
    return pages;
  };

  const handleInputSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted with input:', inputPage);
    console.log('Current page is:', currentPage);
    console.log('Total pages:', totalPages);
    
    const pageNum = parseInt(inputPage);
    console.log('Parsed page number:', pageNum);
    
    // Validate page number
    if (isNaN(pageNum) || pageNum < 1 || pageNum > totalPages) {
      console.log('Invalid page number, resetting');
      setInputPage(currentPage.toString());
      setIsInputFocused(false);
      return;
    }
    
    // Only navigate if it's a different page
    if (pageNum !== currentPage) {
      console.log('Navigating to page:', pageNum);
      console.log('Calling onPageChange with:', pageNum);
      // Don't reset focus immediately to prevent input reset
      try {
        onPageChange(pageNum);
        console.log('onPageChange called successfully');
      } catch (error) {
        console.error('Error calling onPageChange:', error);
      }
      // Delay the focus reset to allow navigation to complete
      setTimeout(() => {
        console.log('Resetting input focus and clearing input');
        setIsInputFocused(false);
        // Clear the input so it gets reset to new current page
        setInputPage('');
      }, 200);
          } else {
        console.log('Same page, no navigation needed');
        setIsInputFocused(false);
        // Clear input to show current page
        setInputPage('');
      }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Allow empty input or positive integers
    if (value === '' || /^\d+$/.test(value)) {
      setInputPage(value);
    }
  };

  const handleInputFocus = () => {
    setIsInputFocused(true);
  };

  const handleInputBlur = () => {
    setIsInputFocused(false);
    // Don't auto-reset on blur to allow form submission
  };

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Escape') {
      setInputPage(currentPage.toString());
      setIsInputFocused(false);
      e.currentTarget.blur();
    }
  };

  // Check if the current input is valid for submission
  const isValidPageInput = () => {
    if (!inputPage || inputPage.trim() === '') return false;
    const pageNum = parseInt(inputPage);
    // Allow submission if it's a valid page number, regardless of whether it's the current page
    return !isNaN(pageNum) && pageNum >= 1 && pageNum <= totalPages;
  };

  // Get input field styling based on validation state
  const getInputClassName = () => {
    const baseClass = "w-16 h-8 text-center text-sm bg-vista-dark text-vista-light transition-colors";
    
    if (!inputPage || inputPage.trim() === '') {
      return `${baseClass} border-vista-light/20 focus:border-vista-blue focus:ring-vista-blue/20`;
    }
    
    const pageNum = parseInt(inputPage);
    const isValid = !isNaN(pageNum) && pageNum >= 1 && pageNum <= totalPages;
    
    if (isValid) {
      if (pageNum === currentPage) {
        return `${baseClass} border-yellow-500/50 focus:border-yellow-500 focus:ring-yellow-500/20`;
      } else {
        return `${baseClass} border-green-500/50 focus:border-green-500 focus:ring-green-500/20`;
      }
    } else {
      return `${baseClass} border-red-500/50 focus:border-red-500 focus:ring-red-500/20`;
    }
  };

  if (totalPages <= 1) return null;

  return (
    <div className="flex flex-col gap-6 mt-12 mb-8">
      {/* Items info - Only show if we have the data and it's not compact */}
      {!compact && startItem && endItem && totalItems && (
        <div className="text-center">
          <p className="text-sm text-vista-light/60">
            Showing <span className="font-medium text-vista-light/80">{startItem.toLocaleString()}</span> to{' '}
            <span className="font-medium text-vista-light/80">{endItem.toLocaleString()}</span> of{' '}
            <span className="font-medium text-vista-blue">{totalItems.toLocaleString()}</span> results
          </p>
        </div>
      )}
      
      {/* Main pagination controls */}
      <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
        {/* Navigation buttons */}
        <div className="flex items-center gap-1">
          {/* First page */}
          {showQuickJump && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onPageChange(1)}
              disabled={currentPage === 1 || isLoading}
              className="h-10 w-10 p-0 text-vista-light/60 hover:text-vista-light hover:bg-vista-light/5 disabled:opacity-50 disabled:cursor-not-allowed"
              title="First page"
            >
              <ChevronsLeft className="h-4 w-4" />
            </Button>
          )}
          
          {/* Previous page */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1 || isLoading}
            className="h-10 w-10 p-0 text-vista-light/60 hover:text-vista-light hover:bg-vista-light/5 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Previous page"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          
          {/* Page numbers */}
          <div className="flex items-center gap-1 mx-2">
            {getPageNumbers().map((page, index) => {
              if (page === 'ellipsis-start' || page === 'ellipsis-end') {
                return (
                  <div key={`ellipsis-${index}`} className="flex h-10 w-10 items-center justify-center">
                    <MoreHorizontal className="h-4 w-4 text-vista-light/40" />
                  </div>
                );
              }
              
              const isCurrentPage = currentPage === page;
              
              return (
                <Button
                  key={`page-${page}`}
                  variant="ghost"
                  size="sm"
                  onClick={() => onPageChange(Number(page))}
                  disabled={isLoading}
                  className={`h-10 w-10 p-0 text-sm font-medium transition-all ${
                    isCurrentPage
                      ? "bg-vista-blue text-white hover:bg-vista-blue/90 shadow-lg" 
                      : "text-vista-light/70 hover:text-vista-light hover:bg-vista-light/5"
                  }`}
                >
                  {page}
                </Button>
              );
            })}
          </div>
          
          {/* Next page */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages || isLoading}
            className="h-10 w-10 p-0 text-vista-light/60 hover:text-vista-light hover:bg-vista-light/5 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Next page"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
          
          {/* Last page */}
          {showQuickJump && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onPageChange(totalPages)}
              disabled={currentPage === totalPages || isLoading}
              className="h-10 w-10 p-0 text-vista-light/60 hover:text-vista-light hover:bg-vista-light/5 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Last page"
            >
              <ChevronsRight className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Page input - Only show if enabled and not compact */}
        {showPageInput && !compact && (
          <div className="flex items-center gap-3 px-4 py-2 bg-vista-dark-lighter/50 rounded-lg border border-vista-light/10">
            <span className="text-sm text-vista-light/70 whitespace-nowrap">Go to page:</span>
            <form onSubmit={handleInputSubmit} className="flex items-center gap-2">
              <Input
                type="text"
                value={inputPage}
                onChange={handleInputChange}
                onFocus={handleInputFocus}
                onBlur={handleInputBlur}
                onKeyDown={handleInputKeyDown}
                disabled={isLoading}
                className={getInputClassName()}
                placeholder={currentPage.toString()}
              />
              <span className="text-sm text-vista-light/50">of {totalPages.toLocaleString()}</span>
              <Button
                type="submit"
                variant="outline"
                size="sm"
                disabled={isLoading}
                className="h-8 px-3 text-xs bg-vista-blue/10 border-vista-blue/30 text-vista-blue hover:bg-vista-blue/20 hover:border-vista-blue/50 disabled:opacity-50 transition-all"
                title={
                  !inputPage || inputPage.trim() === '' 
                    ? 'Enter a page number' 
                    : !isValidPageInput() 
                    ? `Enter a number between 1 and ${totalPages}` 
                    : parseInt(inputPage) === currentPage
                    ? `Already on page ${inputPage}`
                    : `Go to page ${inputPage}`
                }
              >
                Go
              </Button>
            </form>
          </div>
        )}
      </div>

      {/* Compact page input for mobile */}
      {showPageInput && compact && (
        <div className="flex items-center justify-center gap-2 text-sm">
          <form onSubmit={handleInputSubmit} className="flex items-center gap-2">
            <span className="text-vista-light/70">Page</span>
            <Input
              type="text"
              value={inputPage}
              onChange={handleInputChange}
              onFocus={handleInputFocus}
              onBlur={handleInputBlur}
              onKeyDown={handleInputKeyDown}
              disabled={isLoading}
              className={getInputClassName().replace('w-16', 'w-14')}
            />
            <span className="text-vista-light/50">of {totalPages.toLocaleString()}</span>
            <Button
              type="submit"
              variant="outline"
              size="sm"
              disabled={isLoading}
              className="h-8 px-2 text-xs bg-vista-blue/10 border-vista-blue/30 text-vista-blue hover:bg-vista-blue/20"
            >
              Go
            </Button>
          </form>
        </div>
      )}
    </div>
  );
} 
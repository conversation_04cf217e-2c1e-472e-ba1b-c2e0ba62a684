import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';

/**
 * POST /api/admin/users/reset-password
 * Reset a user's password (admin only)
 */
export async function POST(request: NextRequest) {
  try {
    // Get the user ID from the cookie directly
    const userId = request.cookies.get('userId')?.value;
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const UserSchema = new mongoose.default.Schema({
      name: String,
      email: String,
      password: String,
      role: String
    });

    const User = mongoose.default.models.User ||
                mongoose.default.model('User', UserSchema);

    // Check if user is admin
    const adminUser = await User.findById(userId).select('role').lean();
    if (!adminUser || (adminUser as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the Notification schema directly
    const NotificationSchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      title: String,
      message: String,
      read: Boolean
    }, {
      timestamps: true
    });

    // Get the Notification model
    const Notification = mongoose.default.models.Notification ||
                        mongoose.default.model('Notification', NotificationSchema);

    // Get data from request
    const data = await request.json();
    const { userId: targetUserId, newPassword } = data;

    // Validate required fields
    if (!targetUserId || !newPassword) {
      return NextResponse.json(
        { error: 'User ID and new password are required' },
        { status: 400 }
      );
    }

    // Validate password strength
    if (newPassword.length < 8) {
      return NextResponse.json(
        { error: 'Password must be at least 8 characters long' },
        { status: 400 }
      );
    }

    // Find the target user
    const targetUser = await User.findById(targetUserId);
    if (!targetUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Update the user's password
    targetUser.password = hashedPassword;
    await targetUser.save();

    // Create a notification for the user
    await Notification.create({
      userId: targetUser._id,
      type: 'system',
      title: 'Password Reset',
      message: 'Your password has been reset by an administrator. Please use your new password to log in.',
      read: false
    });

    return NextResponse.json({
      success: true,
      message: 'Password reset successfully'
    });
  } catch (error) {
    console.error('Error resetting password:', error);
    return NextResponse.json(
      { error: 'Failed to reset password', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

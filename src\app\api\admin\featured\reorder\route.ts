import { NextRequest, NextResponse } from 'next/server';
import { isAdmin } from '@/lib/middleware';
import { ensureMongooseConnection } from '@/lib/mongodb';
import mongoose from 'mongoose';

// Type definition for featured content document
interface FeaturedContentDocument {
  _id: mongoose.Types.ObjectId;
  contentId: mongoose.Types.ObjectId;
  order: number;
}

/**
 * POST /api/admin/featured/reorder
 * Reorder featured content
 */
export async function POST(request: NextRequest) {
  try {
    // Check if user is admin using the proper middleware
    const adminCheck = await isAdmin(request);
    if (!adminCheck.isAuthorized) {
      return NextResponse.json({ 
        error: adminCheck.message || 'Unauthorized' 
      }, { status: 401 });
    }

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Get admin user ID
    const userId = adminCheck.user?._id?.toString();

    // Define the FeaturedContent schema directly
    const FeaturedContentSchema = new mongoose.Schema({
      contentId: mongoose.Schema.Types.ObjectId,
      contentType: String,
      title: String,
      posterPath: String,
      order: Number,
      active: Boolean
    }, {
      timestamps: true
    });

    // Get the FeaturedContent model
    const FeaturedContent = mongoose.models.FeaturedContent ||
                           mongoose.model('FeaturedContent', FeaturedContentSchema);

    // Define the UserActivity schema directly
    const UserActivitySchema = new mongoose.Schema({
      userId: mongoose.Schema.Types.ObjectId,
      type: String,
      action: String,
      details: String,
      ipAddress: String,
      userAgent: String,
      timestamp: { type: Date, default: Date.now },
      metadata: mongoose.Schema.Types.Mixed
    }, {
      timestamps: true
    });

    // Get the UserActivity model
    const UserActivity = mongoose.models.UserActivity ||
                        mongoose.model('UserActivity', UserActivitySchema);

    // Get request data
    const data = await request.json();

    // Validate required fields
    if (!data.items || !Array.isArray(data.items)) {
      return NextResponse.json({ error: 'Items array is required' }, { status: 400 });
    }

    // Update order for each item
    const updatePromises = data.items.map(async (item: { id: string }, index: number) => {
      if (!item.id) {
        throw new Error('Each item must have an id');
      }

      return FeaturedContent.findByIdAndUpdate(item.id, { order: index + 1 });
    });

    // Wait for all updates to complete
    await Promise.all(updatePromises);

    // Log admin activity directly
    if (userId) {
      await UserActivity.create({
        userId: new mongoose.Types.ObjectId(userId),
        type: 'admin',
        action: 'reorder_featured_content',
        details: `Admin reordered featured content`,
        ipAddress: request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        timestamp: new Date()
      });
    }

    // Return success
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error reordering featured content:', error);
    return NextResponse.json(
      { error: 'Failed to reorder featured content', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

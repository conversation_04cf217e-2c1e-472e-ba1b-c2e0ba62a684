# State Management Documentation

## Overview

StreamVista uses a combination of React Context and local state management to handle application state. The state management is organized into key contexts that handle specific aspects of the application.

## Core Contexts

### Watch Party Context with Pusher
```typescript
// src/lib/WatchPartyContext.tsx
interface WatchPartyContextType {
  currentParty: WatchPartyState | null;
  isHost: boolean;
  createParty: (content: IContent) => Promise<string>;
  joinParty: (partyId: string, memberName: string) => Promise<void>;
  leaveParty: () => void;
  updatePlayback: (currentTime: number, isPlaying: boolean) => void;
  sendMessage: (message: string) => void;
  sendReaction: (reaction: string) => void;
  connectionState: 'connected' | 'connecting' | 'disconnected' | 'failed';
  availableParties: WatchPartyState[];
  fetchAvailableParties: () => void;
  isLoading: boolean;
  userId: string;
}

const WatchPartyContext = createContext<WatchPartyContextType | undefined>(undefined);

export function useWatchParty() {
  const context = useContext(WatchPartyContext);
  if (context === undefined) {
    throw new Error('useWatchParty must be used within a WatchPartyProvider');
  }
  return context;
}

export function WatchPartyProvider({ children }: WatchPartyProviderProps) {
  // Implementation using Pusher for real-time communication
}
```

### Language Context
```typescript
// src/contexts/LanguageContext.tsx
interface LanguageContextType {
  language: string;
  setLanguage: (lang: string) => void;
  t: (key: string, params?: Record<string, string>) => string;
  languages: {
    code: string;
    name: string;
    flag: string;
  }[];
}

const LanguageContext = createContext<LanguageContextType | null>(null);

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Implementation with language switching and translation loading
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) throw new Error('useLanguage must be used within a LanguageProvider');
  return context;
};
```

### Toast Context
```typescript
// src/contexts/ToastContext.tsx
interface ToastContextType {
  toasts: Toast[];
  addToast: (toast: Omit<Toast, 'id'>) => string;
  removeToast: (id: string) => void;
  clearToasts: () => void;
}

const ToastContext = createContext<ToastContextType | null>(null);

export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Implementation with toast state management and auto-dismiss
};

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) throw new Error('useToast must be used within a ToastProvider');
  return context;
};
```

### Error Context
```typescript
// src/contexts/ErrorContext.tsx
interface ErrorContextType {
  error: Error | null;
  setError: (error: Error | null) => void;
  clearError: () => void;
  showErrorDialog: boolean;
  setShowErrorDialog: (show: boolean) => void;
}

const ErrorContext = createContext<ErrorContextType | null>(null);

export const ErrorProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Implementation with error state management and error boundary integration
};

export const useError = () => {
  const context = useContext(ErrorContext);
  if (!context) throw new Error('useError must be used within an ErrorProvider');
  return context;
};
```

### Loading Context
```typescript
// src/contexts/LoadingContext.tsx
interface LoadingContextType {
  isLoading: boolean;
  setLoading: (loading: boolean) => void;
  progress: number;
  setProgress: (progress: number) => void;
  message: string;
  setMessage: (message: string) => void;
}

const LoadingContext = createContext<LoadingContextType | null>(null);

export const LoadingProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Implementation with loading state management and progress tracking
};

export const useLoading = () => {
  const context = useContext(LoadingContext);
  if (!context) throw new Error('useLoading must be used within a LoadingProvider');
  return context;
};
```

## State Management Patterns

### Context Organization
```typescript
// src/app/providers.tsx
export const Providers: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ErrorProvider>
      <LoadingProvider>
        <LanguageProvider>
          <WatchPartyProvider>
            <ToastProvider>
              {children}
            </ToastProvider>
          </WatchPartyProvider>
        </LanguageProvider>
      </LoadingProvider>
    </ErrorProvider>
  );
};
```

### State Updates
```typescript
// Example of atomic state updates
const updatePartyState = (update: Partial<WatchPartyState>) => {
  setState(prev => prev ? { ...prev, ...update } : null);
};

// Example of derived state
const isHost = useMemo(() => {
  return state?.host === userId;
}, [state?.host, userId]);

// Example of state reset
const resetState = useCallback(() => {
  setState(null);
  setError(null);
  setLoading(false);
}, []);
```

### Pusher Integration
```typescript
// Example of Pusher integration
const useWatchPartyWithPusher = () => {
  const { showError } = useErrorHelpers();
  
  // User identity management
  const [userId] = useState<string>(() => {
    const storedId = typeof window !== 'undefined' ? localStorage.getItem('watchPartyUserId') : null;
    const id = storedId || uuidv4().substring(0, 8);
    if (typeof window !== 'undefined' && !storedId) {
      localStorage.setItem('watchPartyUserId', id);
    }
    return id;
  });
  
  // Subscribe to Pusher channels
  useEffect(() => {
    // Subscribe to the main channel for all parties
    const channel = pusherClient.subscribe('watch-parties');
    
    // Handle global events
    channel.bind('party-created', handlePartyCreated);
    channel.bind('party-updated', handlePartyUpdated);
    channel.bind('party-deleted', handlePartyDeleted);
    
    return () => {
      channel.unbind_all();
      pusherClient.unsubscribe('watch-parties');
    };
  }, []);
  
  // Party-specific subscription
  useEffect(() => {
    if (!currentParty?.id) return;
    
    // Subscribe to the party-specific channel
    const channel = pusherClient.subscribe(`watch-party-${currentParty.id}`);
    
    // Handle party events
    channel.bind('playback-update', handlePlaybackUpdate);
    channel.bind('new-message', handleNewMessage);
    channel.bind('member-update', handleMemberUpdate);
    
    return () => {
      channel.unbind_all();
      pusherClient.unsubscribe(`watch-party-${currentParty.id}`);
    };
  }, [currentParty?.id]);

  return {
    // State and methods
  };
};
```

### Best Practices
1. Keep contexts focused and single-responsibility
2. Use composition for complex state interactions
3. Implement proper error boundaries
4. Handle loading states consistently
5. Maintain atomic state updates
6. Use memoization for expensive computations
7. Implement proper cleanup in effects
8. Handle edge cases and race conditions

## Real-time State Management

### Pusher State
```typescript
// Example of Pusher state management
const [connectionState, setConnectionState] = useState<'connected' | 'connecting' | 'disconnected' | 'failed'>('connecting');

// Monitor Pusher connection status
useEffect(() => {
  const handleConnected = () => setConnectionState('connected');
  const handleDisconnected = () => setConnectionState('disconnected');
  const handleFailed = () => setConnectionState('failed');
  
  pusherClient.connection.bind('connected', handleConnected);
  pusherClient.connection.bind('disconnected', handleDisconnected);
  pusherClient.connection.bind('failed', handleFailed);
  
  // Set initial state
  if (pusherClient.connection.state === 'connected') {
    setConnectionState('connected');
  }
  
  return () => {
    pusherClient.connection.unbind('connected', handleConnected);
    pusherClient.connection.unbind('disconnected', handleDisconnected);
    pusherClient.connection.unbind('failed', handleFailed);
  };
}, []);
``` 
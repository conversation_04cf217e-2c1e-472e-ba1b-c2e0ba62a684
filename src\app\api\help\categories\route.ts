import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongoose';
import { HelpCategory } from '@/models/HelpTicket';
import User from '@/models/User';
import { authMiddleware } from '@/lib/middleware';

/**
 * GET /api/help/categories
 * Get all help categories
 */
export async function GET(request: NextRequest) {
  try {
    await ensureMongooseConnection();

    const { searchParams } = new URL(request.url);
    const includeInactive = searchParams.get('includeInactive') === 'true';

    const query = includeInactive ? {} : { isActive: true };

    const categories = await HelpCategory.find(query)
      .sort({ sortOrder: 1, name: 1 })
      .lean();

    return NextResponse.json({ categories });

  } catch (error) {
    console.error('Error fetching help categories:', error);
    return NextResponse.json(
      { error: 'Failed to fetch help categories' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/help/categories
 * Create a new help category (admin only)
 */
export async function POST(request: NextRequest) {
  try {
    // Use the same authentication pattern as other API routes
    const authResult = await authMiddleware(request);
    if (!authResult.isAuthenticated || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = authResult.user;
    const userId = user._id.toString();

    // Check if user is admin
    if (user.role !== 'admin' && user.role !== 'superadmin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const {
      name,
      slug,
      description,
      icon,
      color,
      isActive = true,
      sortOrder = 0,
      parentCategory
    } = body;

    if (!name || !slug || !description || !icon || !color) {
      return NextResponse.json(
        { error: 'Name, slug, description, icon, and color are required' },
        { status: 400 }
      );
    }

    // Check if slug already exists
    const existingCategory = await HelpCategory.findOne({ slug });
    if (existingCategory) {
      return NextResponse.json(
        { error: 'Category with this slug already exists' },
        { status: 400 }
      );
    }

    const category = new HelpCategory({
      name,
      slug,
      description,
      icon,
      color,
      isActive,
      sortOrder,
      parentCategory: parentCategory || undefined
    });

    await category.save();

    // If this is a subcategory, add it to parent's subCategories array
    if (parentCategory) {
      await HelpCategory.findByIdAndUpdate(
        parentCategory,
        { $push: { subCategories: category._id } }
      );
    }

    return NextResponse.json({
      message: 'Help category created successfully',
      category
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating help category:', error);
    return NextResponse.json(
      { error: 'Failed to create help category' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/help/categories
 * Update help categories (admin only)
 */
export async function PUT(request: NextRequest) {
  try {
    // Use the same authentication pattern as other API routes
    const authResult = await authMiddleware(request);
    if (!authResult.isAuthenticated || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = authResult.user;
    const userId = user._id.toString();

    // Check if user is admin
    if (user.role !== 'admin' && user.role !== 'superadmin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const { categoryId, updates } = body;

    if (!categoryId) {
      return NextResponse.json(
        { error: 'Category ID is required' },
        { status: 400 }
      );
    }

    const category = await HelpCategory.findByIdAndUpdate(
      categoryId,
      { ...updates, updatedAt: new Date() },
      { new: true }
    );

    if (!category) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: 'Help category updated successfully',
      category
    });

  } catch (error) {
    console.error('Error updating help category:', error);
    return NextResponse.json(
      { error: 'Failed to update help category' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/help/categories
 * Delete a help category (admin only)
 */
export async function DELETE(request: NextRequest) {
  try {
    // Use the same authentication pattern as other API routes
    const authResult = await authMiddleware(request);
    if (!authResult.isAuthenticated || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = authResult.user;
    const userId = user._id.toString();

    // Check if user is admin
    if (user.role !== 'admin' && user.role !== 'superadmin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const categoryId = searchParams.get('id');

    if (!categoryId) {
      return NextResponse.json(
        { error: 'Category ID is required' },
        { status: 400 }
      );
    }

    const category = await HelpCategory.findById(categoryId);
    if (!category) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }

    // Check if category has tickets
    const { HelpTicket } = await import('@/models/HelpTicket');
    const ticketCount = await HelpTicket.countDocuments({ category: category.slug });
    
    if (ticketCount > 0) {
      return NextResponse.json(
        { error: 'Cannot delete category with existing tickets' },
        { status: 400 }
      );
    }

    // Remove from parent's subCategories if it's a subcategory
    if (category.parentCategory) {
      await HelpCategory.findByIdAndUpdate(
        category.parentCategory,
        { $pull: { subCategories: categoryId } }
      );
    }

    // Delete all subcategories
    if (category.subCategories.length > 0) {
      await HelpCategory.deleteMany({ _id: { $in: category.subCategories } });
    }

    await HelpCategory.findByIdAndDelete(categoryId);

    return NextResponse.json({
      message: 'Help category deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting help category:', error);
    return NextResponse.json(
      { error: 'Failed to delete help category' },
      { status: 500 }
    );
  }
}

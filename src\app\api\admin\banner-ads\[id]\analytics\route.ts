import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import BannerAd from '@/models/BannerAd';
import mongoose from 'mongoose';

/**
 * POST /api/admin/banner-ads/[id]/analytics
 * Track banner ad analytics (views, clicks, impressions)
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await ensureMongooseConnection();

    const id = (await params).id;
    const body = await request.json();

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid banner ad ID' },
        { status: 400 }
      );
    }

    // Validate action type
    const { action } = body;
    if (!['view', 'click', 'impression'].includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action type. Must be view, click, or impression' },
        { status: 400 }
      );
    }

    // Check if banner ad exists
    const bannerAd = await BannerAd.findById(id);
    if (!bannerAd) {
      return NextResponse.json(
        { error: 'Banner ad not found' },
        { status: 404 }
      );
    }

    // Update analytics based on action
    const updateField = action === 'view' ? 'analytics.views' : 
                       action === 'click' ? 'analytics.clicks' : 
                       'analytics.impressions';

    const updatedBannerAd = await BannerAd.findByIdAndUpdate(
      id,
      { $inc: { [updateField]: 1 } },
      { new: true }
    ).select('analytics');

    return NextResponse.json({
      success: true,
      analytics: updatedBannerAd?.analytics
    });

  } catch (error) {
    console.error('Error tracking banner ad analytics:', error);
    return NextResponse.json(
      { error: 'Failed to track analytics' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/admin/banner-ads/[id]/analytics
 * Get banner ad analytics
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await ensureMongooseConnection();

    const id = (await params).id;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid banner ad ID' },
        { status: 400 }
      );
    }

    // Get banner ad analytics
    const bannerAd = await BannerAd.findById(id).select('analytics title').lean();

    if (!bannerAd) {
      return NextResponse.json(
        { error: 'Banner ad not found' },
        { status: 404 }
      );
    }

    // Calculate click-through rate
    const ctr = bannerAd.analytics.impressions > 0 
      ? (bannerAd.analytics.clicks / bannerAd.analytics.impressions * 100).toFixed(2)
      : '0.00';

    return NextResponse.json({
      title: bannerAd.title,
      analytics: {
        ...bannerAd.analytics,
        clickThroughRate: `${ctr}%`
      }
    });

  } catch (error) {
    console.error('Error fetching banner ad analytics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analytics' },
      { status: 500 }
    );
  }
}

'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, Calendar, Filter, Grid, List } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Navbar } from '@/components/Navbar';
import Footer from '@/components/Footer';
import ContentCard from '@/components/ContentCard';
import { getTrendingDaily, getTrendingWeekly, MappedContent } from '@/lib/tmdb-api';
import { formatTMDbContentForCards, ContentCardType } from '@/lib/content-utils';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2 } from 'lucide-react';

type TimeWindow = 'day' | 'week';
type MediaType = 'all' | 'movie' | 'tv';
type ViewMode = 'grid' | 'list';

export default function TrendingPage() {
  const [timeWindow, setTimeWindow] = useState<TimeWindow>('day');
  const [mediaType, setMediaType] = useState<MediaType>('all');
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [content, setContent] = useState<ContentCardType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  // Fetch trending content
  const fetchTrendingContent = async (resetPage = false) => {
    try {
      setLoading(true);
      setError(null);
      
      const currentPage = resetPage ? 1 : page;
      let trendingData: MappedContent[];
      
      if (timeWindow === 'day') {
        trendingData = await getTrendingDaily(mediaType, currentPage);
      } else {
        trendingData = await getTrendingWeekly(mediaType, currentPage);
      }
      
      const formattedContent = formatTMDbContentForCards(trendingData);
      
      if (resetPage) {
        setContent(formattedContent);
        setPage(2);
      } else {
        setContent(prev => [...prev, ...formattedContent]);
        setPage(prev => prev + 1);
      }
      
      // Check if we have more content (TMDb typically has 20 items per page)
      setHasMore(formattedContent.length === 20);
      
    } catch (err) {
      console.error('Error fetching trending content:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch trending content');
    } finally {
      setLoading(false);
    }
  };

  // Load more content
  const loadMore = () => {
    if (!loading && hasMore) {
      fetchTrendingContent(false);
    }
  };

  // Reset and fetch when filters change
  useEffect(() => {
    setPage(1);
    fetchTrendingContent(true);
  }, [timeWindow, mediaType]);

  return (
    <div className="min-h-screen bg-vista-dark">
      <Navbar />
      <main className="min-h-screen bg-vista-dark pt-16 md:pt-20">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-3">
              <TrendingUp className="h-8 w-8 text-vista-blue" />
              <div>
                <h1 className="text-3xl font-bold text-vista-light mb-2">Trending</h1>
                <p className="text-vista-light/60">
                  Discover what's popular right now
                </p>
              </div>
            </div>
            
            {/* View Mode Toggle */}
            <div className="flex gap-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="icon"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="icon"
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Filters */}
          <div className="flex flex-wrap gap-4 mb-8">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-vista-light/60" />
              <Select value={timeWindow} onValueChange={(value: TimeWindow) => setTimeWindow(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="day">Today</SelectItem>
                  <SelectItem value="week">This Week</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-vista-light/60" />
              <Select value={mediaType} onValueChange={(value: MediaType) => setMediaType(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="movie">Movies</SelectItem>
                  <SelectItem value="tv">TV Shows</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Error State */}
          {error && (
            <Alert className="mb-8">
              <AlertDescription>
                {error}
              </AlertDescription>
            </Alert>
          )}

          {/* Loading State */}
          {loading && content.length === 0 && (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
              {[...Array(20)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="bg-vista-dark-card rounded-lg h-64 mb-3"></div>
                  <div className="bg-vista-dark-card rounded h-4 mb-2"></div>
                  <div className="bg-vista-dark-card rounded h-3 w-3/4"></div>
                </div>
              ))}
            </div>
          )}

          {/* Content Grid */}
          {!loading || content.length > 0 ? (
            <>
              <div className={`grid gap-6 ${
                viewMode === 'grid' 
                  ? 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5' 
                  : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
              }`}>
                {content.map((item, index) => (
                  <motion.div
                    key={`${item.id}-${index}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                  >
                    <ContentCard
                      id={item.id}
                      title={item.title}
                      imagePath={item.imagePath}
                      type={item.type}
                      year={item.year}
                      ageRating={item.ageRating}
                      index={index}
                      link={`/watch/${item.id}?forcePlay=true&contentType=${item.type === 'shows' ? 'show' : 'movie'}`}
                      isAwardWinning={item.isAwardWinning}
                      dataSource={item.dataSource}
                    />
                  </motion.div>
                ))}
              </div>

              {/* Load More Button */}
              {hasMore && (
                <div className="flex justify-center mt-12">
                  <Button 
                    onClick={loadMore} 
                    disabled={loading}
                    className="gap-2"
                  >
                    {loading && <Loader2 className="h-4 w-4 animate-spin" />}
                    Load More
                  </Button>
                </div>
              )}

              {/* End Message */}
              {!hasMore && content.length > 0 && (
                <div className="text-center mt-12">
                  <p className="text-vista-light/60">
                    You've reached the end of trending content
                  </p>
                </div>
              )}
            </>
          ) : null}

          {/* Empty State */}
          {!loading && !error && content.length === 0 && (
            <div className="text-center py-16">
              <TrendingUp className="h-16 w-16 text-vista-light/40 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-vista-light mb-2">
                No trending content found
              </h2>
              <p className="text-vista-light/60">
                Try adjusting your filters or check back later
              </p>
            </div>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
}

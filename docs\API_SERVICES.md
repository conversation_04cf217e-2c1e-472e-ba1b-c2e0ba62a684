# API and Services Documentation

## API Routes

### Content API
```typescript
// src/app/api/content/
- GET /api/content/popular - Get popular content
- GET /api/content/[id] - Get content details
- GET /api/content/recommendations - Get content recommendations
```

### Search API
```typescript
// src/app/api/search/
- GET /api/search - Search movies and TV shows
- GET /api/search/suggestions - Get search suggestions
```

### Watch Party API
```typescript
// src/app/api/watch-party/
- POST /api/watch-party/create - Create a new watch party
- POST /api/watch-party/join - Join an existing party
- POST /api/watch-party/leave - Leave a party
- GET /api/watch-party/[id] - Get party details
```

### Streaming API
```typescript
// src/app/api/streamer/
- GET /api/streamer/sources - Get available sources
- GET /api/streamer/quality - Get quality options
- GET /api/streamer/subtitles - Get subtitle tracks
```

## External Services Integration

### TMDB Service

#### Configuration
```typescript
const TMDB_CONFIG = {
  API_KEY: process.env.NEXT_PUBLIC_TMDB_API_KEY,
  ACCESS_TOKEN: process.env.NEXT_PUBLIC_TMDB_ACCESS_TOKEN,
  BASE_URL: 'https://api.themoviedb.org/3',
  IMAGE_BASE_URL: 'https://image.tmdb.org/t/p',
  POSTER_SIZES: {
    small: 'w185',
    medium: 'w342',
    large: 'w500',
    original: 'original'
  }
}
```

#### Data Models

##### Movie Result
```typescript
interface TMDBMovieResult {
  id: number;
  title: string;
  poster_path: string | null;
  backdrop_path: string | null;
  overview: string;
  release_date: string;
  genre_ids: number[];
  vote_average: number;
  runtime?: number;
  imdb_id?: string;
}
```

##### TV Result
```typescript
interface TMDBTVResult {
  id: number;
  name: string;
  overview: string;
  poster_path: string | null;
  backdrop_path: string | null;
  first_air_date: string;
  vote_average: number;
  genre_ids: number[];
  number_of_seasons?: number;
  number_of_episodes?: number;
}
```

#### Core Functions

##### API Fetch Utility
```typescript
async function fetchFromTMDB<T>(endpoint: string, params: Record<string, string> = {}): Promise<T> {
  if (!TMDB_ACCESS_TOKEN) {
    throw new Error('TMDB Access Token not found');
  }

  const url = new URL(`${TMDB_BASE_URL}${endpoint}`);
  Object.entries(params).forEach(([key, value]) => {
    url.searchParams.append(key, value);
  });

  const response = await fetch(url.toString(), {
    headers: {
      Authorization: `Bearer ${TMDB_ACCESS_TOKEN}`,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => null);
    throw new Error(errorData?.status_message || `TMDB API error: ${response.status}`);
  }

  return response.json();
}
```

##### Data Conversion
```typescript
function convertMovieToContent(movie: TMDBMovieResult): IContent {
  return {
    id: movie.id.toString(),
    title: movie.title,
    type: 'movie',
    year: movie.release_date?.split('-')[0] || '',
    posterPath: movie.poster_path ? `${TMDB_IMAGE_BASE_URL}/w500${movie.poster_path}` : '',
    backdropPath: movie.backdrop_path ? `${TMDB_IMAGE_BASE_URL}/original${movie.backdrop_path}` : '',
    overview: movie.overview,
    genres: movie.genre_ids?.map(id => genreMap[id]).filter(Boolean) || [],
    rating: movie.vote_average,
    runtime: movie.runtime,
    tmdbId: movie.id.toString()
  };
}

function convertTVToContent(show: TMDBTVResult): IContent {
  return {
    id: show.id.toString(),
    title: show.name,
    type: 'show',
    year: show.first_air_date?.split('-')[0] || '',
    posterPath: show.poster_path ? `${TMDB_IMAGE_BASE_URL}/w500${show.poster_path}` : '',
    backdropPath: show.backdrop_path ? `${TMDB_IMAGE_BASE_URL}/original${show.backdrop_path}` : '',
    overview: show.overview,
    genres: show.genre_ids?.map(id => genreMap[id]).filter(Boolean) || [],
    rating: show.vote_average,
    seasons: show.number_of_seasons,
    episodes: show.number_of_episodes,
    tmdbId: show.id.toString()
  };
}
```

#### API Functions

##### Content Discovery
```typescript
// Get popular movies with pagination
async function getPopularMovies(page: number = 1): Promise<IContent[]>

// Get popular TV shows with pagination
async function getPopularShows(page: number = 1): Promise<IContent[]>

// Get top-rated movies with pagination
async function getTopRatedMovies(page: number = 1): Promise<IContent[]>

// Get top-rated TV shows with pagination
async function getTopRatedShows(page: number = 1): Promise<IContent[]>

// Get trending content (daily)
async function getTrendingDaily(mediaType: 'all' | 'movie' | 'tv' = 'all', page = 1): Promise<IContent[]>

// Get trending content (weekly)
async function getTrendingWeekly(mediaType: 'all' | 'movie' | 'tv' = 'all', page = 1): Promise<IContent[]>
```

##### Content Search
```typescript
// Search for movies and TV shows
async function searchContent(query: string, page: number = 1): Promise<{
  movies: IContent[];
  shows: IContent[];
}>

// Search by IMDb ID
async function getMovieByImdbId(imdbId: string): Promise<IContent | null>
async function getTVByImdbId(imdbId: string): Promise<IContent | null>
```

##### Content Details
```typescript
// Get detailed movie information
async function getMovieDetails(id: string): Promise<IContent>

// Get detailed TV show information
async function getTVDetails(id: string): Promise<IContent>

// Get movie recommendations
async function getMovieRecommendations(id: string, page: number = 1): Promise<IContent[]>

// Get TV show recommendations
async function getTVRecommendations(id: string, page: number = 1): Promise<IContent[]>
```

#### API Routes

##### Content Details Route
```typescript
// GET /api/content?id={id}&type={movie|show}
export async function GET(request: NextRequest) {
  const id = searchParams.get('id');
  const type = searchParams.get('type') as 'movie' | 'show';
  
  // Fetch content based on type
  const content = type === 'movie' 
    ? await getMovieDetails(id)
    : await getTVDetails(id);
    
  return NextResponse.json(content);
}
```

##### Search Route
```typescript
// GET /api/search?query={query}&type={movie|show}&limit={limit}
// GET /api/search?imdbId={id}&type={movie|show}
// GET /api/search?genre={genre}&type={movie|show}
export async function GET(request: NextRequest) {
  const query = searchParams.get('query');
  const imdbId = searchParams.get('imdbId');
  const genre = searchParams.get('genre');
  const type = searchParams.get('type') as 'movie' | 'show';
  const limit = parseInt(searchParams.get('limit') || '20');
  
  // Handle different search types
  if (imdbId) {
    const content = type === 'movie'
      ? await getMovieByImdbId(imdbId)
      : await getTVByImdbId(imdbId);
    return NextResponse.json(content);
  }
  
  if (query) {
    const results = await searchContent(query);
    return NextResponse.json(results);
  }
  
  if (genre) {
    const results = await searchByGenre(genre, type);
    return NextResponse.json(results);
  }
}
```

### Error Handling

#### API Errors
```typescript
interface TMDBError {
  status_message: string;
  status_code: number;
}

// Error handling in fetch utility
try {
  const response = await fetch(url.toString(), options);
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.status_message);
  }
  return response.json();
} catch (error) {
  console.error('TMDB API Error:', error);
  throw error;
}
```

### Caching and Performance

#### Response Caching
```typescript
export const CACHE_CONFIG = {
  revalidate: 3600, // 1 hour
  tags: ['tmdb-content']
}

// Usage in API routes
export const GET = cache(async (request: NextRequest) => {
  // ... handler implementation
}, CACHE_CONFIG);
```

#### Image Optimization
```typescript
const POSTER_SIZES = {
  small: 'w185',
  medium: 'w342',
  large: 'w500',
  original: 'original'
};

function getOptimizedImageUrl(path: string, size: keyof typeof POSTER_SIZES = 'medium'): string {
  return path ? `${TMDB_IMAGE_BASE_URL}/${POSTER_SIZES[size]}${path}` : '';
}
```

### Development Tools

#### Mock Data
```typescript
const MOCK_CONTENT: Record<string, IContent> = {
  // Mock content for development
};

function getMockContent(id: string): IContent | null {
  return MOCK_CONTENT[id] || null;
}

// Usage in development
const content = process.env.NODE_ENV === 'development'
  ? getMockContent(id)
  : await fetchFromTMDB(`/movie/${id}`);
```

#### Debug Helpers
```typescript
const debugTMDB = {
  logRequests: true,
  logErrors: true,
  mockResponses: false
};

function logTMDBRequest(endpoint: string, params: Record<string, string>) {
  if (debugTMDB.logRequests) {
    console.log('TMDB Request:', { endpoint, params });
  }
}
```

## Data Models

### Content Model
```typescript
interface IContent {
  id: string;
  title: string;
  type: 'movie' | 'show';
  year: string;
  posterPath: string;
  backdropPath: string;
  overview: string;
  genres: string[];
  rating: number;
  runtime?: number;
  seasons?: number;
  episodes?: number;
  tmdbId: string;
  imdbId?: string;
}
```

### Watch Party Model
```typescript
interface WatchPartyState {
  id: string;
  contentId: string;
  host: string;
  members: WatchPartyMember[];
  currentTime: number;
  isPlaying: boolean;
  messages: ChatMessage[];
  reactions: Reaction[];
}

interface WatchPartyMember {
  id: string;
  name: string;
  avatar?: string;
  isHost: boolean;
  joinedAt: string;
  isReady: boolean;
}
```

## Error Handling

### API Errors
```typescript
class APIError extends Error {
  constructor(
    public statusCode: number,
    public message: string,
    public details?: any
  ) {
    super(message);
  }
}

// Error handling middleware
function handleAPIError(error: unknown) {
  if (error instanceof APIError) {
    return new Response(JSON.stringify({
      error: error.message,
      details: error.details
    }), { status: error.statusCode });
  }
  
  console.error('Unhandled error:', error);
  return new Response(JSON.stringify({
    error: 'Internal server error'
  }), { status: 500 });
}
```

### TMDB Error Handling
```typescript
async function handleTMDBError(error: unknown) {
  console.error('TMDB API error:', error);
  
  // Return mock data in development
  if (process.env.NODE_ENV === 'development') {
    return getMockData();
  }
  
  throw new APIError(503, 'TMDB service unavailable');
}
```

## Rate Limiting

### API Rate Limits
```typescript
const RATE_LIMITS = {
  'api/content/*': {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // requests per window
  },
  'api/watch-party/*': {
    windowMs: 60 * 1000, // 1 minute
    max: 30 // requests per window
  }
}
```

### TMDB Rate Limiting
```typescript
const TMDB_RATE_LIMITS = {
  windowMs: 60 * 1000, // 1 minute
  max: 30 // requests per window
}
```

## Caching

### Cache Configuration
```typescript
const CACHE_CONFIG = {
  popularContent: 60 * 60, // 1 hour
  contentDetails: 24 * 60 * 60, // 24 hours
  search: 15 * 60 // 15 minutes
}
```

### Cache Implementation
```typescript
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresIn: number;
}

class ContentCache {
  private cache: Map<string, CacheEntry<any>>;
  
  set<T>(key: string, data: T, expiresIn: number): void;
  get<T>(key: string): T | null;
  clear(): void;
}
```

## Security

### Authentication
- Token-based authentication
- Session management
- CSRF protection
- XSS prevention

### API Security
- Request validation
- Response sanitization
- Rate limiting
- Error handling

## Monitoring

### Performance Monitoring
- API response times
- Error rates
- Cache hit rates
- Rate limit tracking

### Error Tracking
- Error logging
- Stack traces
- Request context
- User impact

## Development Tools

### API Testing
```typescript
// Test Utilities
const mockTMDBResponse = <T>(data: T): Promise<T> => {
  return Promise.resolve(data);
};

// Test Helpers
const createTestContent = (overrides = {}): IContent => ({
  id: 'test-id',
  title: 'Test Content',
  type: 'movie',
  year: '2024',
  ...overrides
});
```

### Development Environment
```env
# Environment Variables
NEXT_PUBLIC_TMDB_API_KEY=your_api_key
NEXT_PUBLIC_TMDB_ACCESS_TOKEN=your_access_token
NEXT_PUBLIC_SOCKET_URL=ws://localhost:3001
``` 
import { IContent, Genre } from '@/data/content';
import { MappedContent } from '@/lib/tmdb-api';

/**
 * Defines the content card data structure
 */
export interface ContentCardType {
  id: string;
  title: string;
  imagePath: string;
  type: 'shows' | 'movies';
  year: string;
  ageRating?: string;
  userRating?: number;
  duration?: string;
  isNew?: boolean;
  isAwardWinning?: boolean;
  isTrending?: boolean;
  isInWatchlist?: boolean;
  watchTimeMinutes?: number;
  tmdbId?: string;
  imdbId?: string;
  dataSource?: 'tmdb' | 'omdb' | 'both'; // Source of the content data
  index?: number; // Animation index for staggered animations
}

/**
 * Formats content data for display in content cards
 */
export function formatContentForCards(content: IContent[]): ContentCardType[] {
  return content.map(item => ({
    id: typeof item.id === 'number' ? item.id.toString() : item.id,
    title: item.title,
    imagePath: item.posterPath, // Use posterPath instead of image
    type: item.type === 'show' ? 'shows' : 'movies',
    year: item.year.toString(),
    ageRating: item.rating ? item.rating.toString() : undefined,
    duration: item.type === 'movie' && item.runtime ? `${item.runtime} min` : undefined,
    userRating: item.rating, // Map rating to userRating
    isNew: false, // Default values since these aren't in IContent
    isAwardWinning: item.awards ? true : false,
    isTrending: false,
    isInWatchlist: false,
    watchTimeMinutes: undefined,
    tmdbId: item.tmdbId,
    imdbId: item.imdbId,
    dataSource: item.dataSource || 'tmdb'
  }));
}

/**
 * Gets a formatted subtitle with year, genre, and other metadata
 */
export function getContentSubtitle(content: IContent): string {
  const parts: string[] = [];

  // Add year
  if (content.year) {
    parts.push(content.year.toString());
  }

  // Add rating
  if (content.rating) {
    parts.push(content.rating.toString());
  }

  // Add runtime for movies or creator for shows
  if (content.type === 'movie' && content.runtime) {
    parts.push(`${content.runtime} min`);
  } else if (content.type === 'show' && content.creator) {
    parts.push(`Created by ${content.creator}`);
  }

  return parts.join(' • ');
}

/**
 * Formats genres as a string
 */
export function formatGenres(genres: string[]): string {
  return genres.join(', ');
}

/**
 * Gets a truncated description for use in cards
 */
export function getTruncatedDescription(description: string, maxLength: number = 120): string {
  if (description.length <= maxLength) return description;

  return description.substring(0, maxLength).trim() + '...';
}

// Type for content that might be either mapped or raw TMDb data
type FlexibleContent = MappedContent & {
  // Raw TMDb properties that might exist
  media_type?: string;
  name?: string;
  poster_path?: string;
  release_date?: string;
  first_air_date?: string;
};

/**
 * Formats TMDb mapped content for display in content cards
 */
export function formatTMDbContentForCards(content: FlexibleContent[]): ContentCardType[] {
  try {
    // Ensure content is an array
    if (!Array.isArray(content)) {
      console.error('[formatTMDbContentForCards] Content is not an array:', content);
      return [];
    }



    // Filter out items with invalid or missing data
    const validContent = content.filter(item => {
      try {
        // Make sure the item exists and has basic required properties
        const isValid = item &&
                      typeof item === 'object' &&
                      item.id &&
                      typeof item.id !== 'undefined' &&
                      item.id !== 'undefined' &&
                      item.id !== 'null';



        return isValid;
      } catch (filterError) {
        console.error('[formatTMDbContentForCards] Error filtering item:', filterError);
        return false;
      }
    });



    return validContent.map((item, index) => {
      try {
    // Ensure ID is a valid string
    const id = item.id?.toString() || '';
    if (!id || id === 'undefined' || id === 'null' || id === '[object Object]') {
      // console.error('[formatTMDbContentForCards] Invalid ID detected:', id, 'for item:', item);
    }

    // Determine content type directly from mediaType
    // For TV shows, ensure we use 'shows' as the type
    const type: 'shows' | 'movies' =
      item.mediaType === 'tv' ||
      item.media_type === 'tv' ||
      (item.name && !item.title) ? 'shows' : 'movies';

    // For TV shows, ensure we have a properly formatted image path
    let imagePath = item.posterUrl;

    // Check if we have a poster_path directly from TMDb item (common in similar/recommended results)
    const posterPath = item.poster_path;
    if ((!imagePath || imagePath.trim() === '') && posterPath) {
      // Handle different formats of poster_path
      if (typeof posterPath === 'string') {
        if (posterPath.startsWith('http')) {
          // Already a full URL
          imagePath = posterPath;
        } else if (posterPath.startsWith('/')) {
          // TMDb path format
          imagePath = `https://image.tmdb.org/t/p/w342${posterPath}`;
        } else {
          // Some other format, try to use it directly
          imagePath = posterPath;
        }
      }
    }

    // Fallback to placeholder if no valid image path
    if (!imagePath ||
        imagePath.includes('null') ||
        imagePath.includes('undefined') ||
        imagePath.trim() === '') {
      imagePath = 'https://placehold.co/300x450/171717/CCCCCC?text=No+Image';
    }

    // Decide if the rating should be displayed
    const isValidRating = item.voteAverage && item.voteAverage >= 1 && item.voteCount && item.voteCount >= 10;

    // Ensure we have a valid tmdbId
    const tmdbId = item.tmdbId?.toString() ||
                  (id && id !== 'undefined' && id !== 'null' ?
                   id.replace(/^(movie|tv)-/, '') : '');

    const formattedCard: ContentCardType = {
      id: id || `tmdb-${Date.now()}-${index}`, // Fallback to a generated ID if needed
      title: item.title || 'Unknown Title',
      imagePath,
      type,
      year: item.year?.toString() || (() => {
              // Safely extract year from various date formats with error handling
              try {
                if (item.releaseDate && item.releaseDate !== 'null' && item.releaseDate !== 'undefined') {
                  const year = new Date(item.releaseDate).getFullYear();
                  if (!isNaN(year) && year > 1900 && year < 2100) {
                    return year.toString();
                  }
                }

                const releaseDate = item.release_date;
                if (releaseDate && releaseDate !== 'null' && releaseDate !== 'undefined') {
                  const year = new Date(releaseDate).getFullYear();
                  if (!isNaN(year) && year > 1900 && year < 2100) {
                    return year.toString();
                  }
                }

                const firstAirDate = item.first_air_date;
                if (firstAirDate && firstAirDate !== 'null' && firstAirDate !== 'undefined') {
                  const year = new Date(firstAirDate).getFullYear();
                  if (!isNaN(year) && year > 1900 && year < 2100) {
                    return year.toString();
                  }
                }

                return '';
              } catch (error) {
                console.error('Error parsing date:', error);
                return '';
              }
            })(),
      userRating: isValidRating ? item.voteAverage : undefined,
      // Add additional properties as needed
      isNew: (() => {
        try {
          if (item.releaseDate && item.releaseDate !== 'null' && item.releaseDate !== 'undefined') {
            const releaseDate = new Date(item.releaseDate);
            if (!isNaN(releaseDate.getTime())) {
              return releaseDate > new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
            }
          }
          return false;
        } catch (error) {
          console.error('Error checking if content is new:', error);
          return false;
        }
      })(), // Consider "new" if released in the last 90 days
      isTrending: item.voteAverage > 7.5, // Consider "trending" if rating > 7.5
      tmdbId,
      index // Add index for animation staggering
    };

    // Log the formatted card for debugging
    // console.log(`[formatTMDbContentForCards] Formatted card for "${formattedCard.title}": ID=${formattedCard.id}, tmdbId=${formattedCard.tmdbId}`);

    return formattedCard;
      } catch (mapError) {
        console.error('[formatTMDbContentForCards] Error mapping item:', mapError);
        // Return a safe fallback card
        return {
          id: `error-${Date.now()}-${index}`,
          title: 'Error Loading Content',
          imagePath: 'https://placehold.co/300x450/171717/CCCCCC?text=Error',
          type: 'movies',
          year: '',
          index
        };
      }
    });
  } catch (error) {
    console.error('[formatTMDbContentForCards] Fatal error processing content:', error);
    return [];
  }
}

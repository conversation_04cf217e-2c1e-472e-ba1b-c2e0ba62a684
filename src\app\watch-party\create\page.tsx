'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Search, Play, Users, Lock, Globe, Settings, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Navbar } from '@/components/Navbar';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface ContentItem {
  id: string;
  title: string;
  type: 'movie' | 'show';
  year?: string;
  poster?: string;
  imdbId?: string;
  tmdbId?: string;
  season?: number;
  episode?: number;
}

interface PartySettings {
  isPrivate: boolean;
  maxMembers: number;
  allowChat: boolean;
  allowReactions: boolean;
  autoPlay: boolean;
}

export default function CreateWatchPartyPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<ContentItem[]>([]);
  const [selectedContent, setSelectedContent] = useState<ContentItem | null>(null);
  const [loading, setLoading] = useState(false);
  const [partySettings, setPartySettings] = useState<PartySettings>({
    isPrivate: false,
    maxMembers: 10,
    allowChat: true,
    allowReactions: true,
    autoPlay: true
  });

  // Pre-populate content if provided in URL
  useEffect(() => {
    const contentId = searchParams.get('contentId');
    const title = searchParams.get('title');
    
    if (contentId && title) {
      const preSelectedContent: ContentItem = {
        id: contentId,
        title: decodeURIComponent(title),
        type: 'movie', // Default to movie, can be enhanced
        year: '2024',
        poster: '/images/placeholder-poster.jpg',
        imdbId: contentId.startsWith('tt') ? contentId : undefined,
        tmdbId: !contentId.startsWith('tt') ? contentId : undefined
      };
      setSelectedContent(preSelectedContent);
    }
  }, [searchParams]);

  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      setLoading(true);
      
      // Mock search results - in real implementation, call your content API
      const mockResults: ContentItem[] = [
        {
          id: 'tt4154796',
          title: 'Avengers: Endgame',
          type: 'movie',
          year: '2019',
          poster: '/images/placeholder-poster.jpg',
          imdbId: 'tt4154796'
        },
        {
          id: 'tt0903747',
          title: 'Breaking Bad',
          type: 'show',
          year: '2008-2013',
          poster: '/images/placeholder-poster.jpg',
          imdbId: 'tt0903747'
        },
        {
          id: 'tt0944947',
          title: 'Game of Thrones',
          type: 'show',
          year: '2011-2019',
          poster: '/images/placeholder-poster.jpg',
          imdbId: 'tt0944947'
        }
      ].filter(item => 
        item.title.toLowerCase().includes(query.toLowerCase())
      );

      setSearchResults(mockResults);
    } catch (error) {
      console.error('Search error:', error);
      toast.error('Failed to search content');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateParty = async () => {
    if (!selectedContent) {
      toast.error('Please select content to watch');
      return;
    }

    try {
      // In real implementation, create party via API
      const partyId = `party-${Date.now()}`;
      
      toast.success('Watch party created successfully!');
      
      // Navigate to the watch party
      router.push(`/watch-party/${partyId}?contentId=${selectedContent.id}&title=${encodeURIComponent(selectedContent.title)}`);
    } catch (error) {
      console.error('Error creating party:', error);
      toast.error('Failed to create watch party');
    }
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <div className="min-h-screen bg-vista-dark">
      <Navbar />
      
      <div className="pt-20 pb-8">
        <div className="container mx-auto px-4 max-w-4xl">
          {/* Header */}
          <div className="mb-8">
            <Button
              variant="ghost"
              onClick={handleBack}
              className="text-vista-light hover:text-vista-blue mb-4"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            
            <h1 className="text-4xl font-bold text-vista-light mb-2">
              Create Watch Party
            </h1>
            <p className="text-vista-light/70">
              Choose content and invite friends to watch together
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Content Selection */}
            <div className="lg:col-span-2 space-y-6">
              <Card className="bg-vista-dark-lighter border-vista-light/10">
                <CardHeader>
                  <CardTitle className="text-vista-light">Select Content</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Search */}
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-vista-light/50" />
                    <Input
                      placeholder="Search for movies or TV shows..."
                      value={searchTerm}
                      onChange={(e) => {
                        setSearchTerm(e.target.value);
                        handleSearch(e.target.value);
                      }}
                      className="pl-10 bg-vista-dark border-vista-light/20 text-vista-light placeholder:text-vista-light/50"
                    />
                  </div>

                  {/* Search Results */}
                  {searchResults.length > 0 && (
                    <div className="space-y-2 max-h-64 overflow-y-auto">
                      {searchResults.map((item) => (
                        <div
                          key={item.id}
                          onClick={() => setSelectedContent(item)}
                          className={cn(
                            "flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors",
                            selectedContent?.id === item.id
                              ? "bg-vista-blue/20 border border-vista-blue/30"
                              : "bg-vista-dark hover:bg-vista-dark-lighter border border-transparent"
                          )}
                        >
                          <img
                            src={item.poster}
                            alt={item.title}
                            className="w-12 h-16 object-cover rounded"
                          />
                          <div className="flex-1 min-w-0">
                            <h3 className="font-medium text-vista-light truncate">
                              {item.title}
                            </h3>
                            <div className="flex items-center space-x-2 text-sm text-vista-light/70">
                              <Badge variant="outline" className="border-vista-blue/30 text-vista-blue">
                                {item.type}
                              </Badge>
                              {item.year && <span>{item.year}</span>}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Selected Content */}
                  {selectedContent && (
                    <div className="p-4 bg-vista-dark rounded-lg border border-vista-blue/30">
                      <div className="flex items-center space-x-4">
                        <img
                          src={selectedContent.poster}
                          alt={selectedContent.title}
                          className="w-16 h-20 object-cover rounded"
                        />
                        <div className="flex-1">
                          <h3 className="font-semibold text-vista-light">
                            {selectedContent.title}
                          </h3>
                          <div className="flex items-center space-x-2 text-sm text-vista-light/70">
                            <Badge variant="outline" className="border-vista-blue/30 text-vista-blue">
                              {selectedContent.type}
                            </Badge>
                            {selectedContent.year && <span>{selectedContent.year}</span>}
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setSelectedContent(null)}
                          className="text-vista-light/50 hover:text-vista-light"
                        >
                          ×
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Party Settings */}
              <Card className="bg-vista-dark-lighter border-vista-light/10">
                <CardHeader>
                  <CardTitle className="text-vista-light">Party Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Privacy */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {partySettings.isPrivate ? (
                        <Lock className="h-5 w-5 text-vista-light" />
                      ) : (
                        <Globe className="h-5 w-5 text-vista-light" />
                      )}
                      <div>
                        <Label className="text-vista-light">Private Party</Label>
                        <p className="text-sm text-vista-light/70">
                          {partySettings.isPrivate 
                            ? 'Only invited users can join' 
                            : 'Anyone can discover and join'
                          }
                        </p>
                      </div>
                    </div>
                    <Switch
                      checked={partySettings.isPrivate}
                      onCheckedChange={(checked) => 
                        setPartySettings(prev => ({ ...prev, isPrivate: checked }))
                      }
                    />
                  </div>

                  {/* Max Members */}
                  <div className="space-y-2">
                    <Label className="text-vista-light">Maximum Members</Label>
                    <Select
                      value={partySettings.maxMembers.toString()}
                      onValueChange={(value) => 
                        setPartySettings(prev => ({ ...prev, maxMembers: parseInt(value) }))
                      }
                    >
                      <SelectTrigger className="bg-vista-dark border-vista-light/20 text-vista-light">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {[2, 4, 6, 8, 10, 15, 20].map(num => (
                          <SelectItem key={num} value={num.toString()}>
                            {num} members
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Features */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-vista-light">Allow Chat</Label>
                        <p className="text-sm text-vista-light/70">
                          Members can send messages during the party
                        </p>
                      </div>
                      <Switch
                        checked={partySettings.allowChat}
                        onCheckedChange={(checked) => 
                          setPartySettings(prev => ({ ...prev, allowChat: checked }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-vista-light">Allow Reactions</Label>
                        <p className="text-sm text-vista-light/70">
                          Members can send emoji reactions
                        </p>
                      </div>
                      <Switch
                        checked={partySettings.allowReactions}
                        onCheckedChange={(checked) => 
                          setPartySettings(prev => ({ ...prev, allowReactions: checked }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-vista-light">Auto-play</Label>
                        <p className="text-sm text-vista-light/70">
                          Start playing automatically when party begins
                        </p>
                      </div>
                      <Switch
                        checked={partySettings.autoPlay}
                        onCheckedChange={(checked) => 
                          setPartySettings(prev => ({ ...prev, autoPlay: checked }))
                        }
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Party Preview */}
              <Card className="bg-vista-dark-lighter border-vista-light/10">
                <CardHeader>
                  <CardTitle className="text-vista-light">Party Preview</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {selectedContent ? (
                    <>
                      <div className="text-center">
                        <img
                          src={selectedContent.poster}
                          alt={selectedContent.title}
                          className="w-full rounded-lg shadow-lg"
                        />
                        <h3 className="font-semibold text-vista-light mt-3">
                          {selectedContent.title}
                        </h3>
                        <p className="text-sm text-vista-light/70">
                          {selectedContent.type} • {selectedContent.year}
                        </p>
                      </div>

                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-vista-light/70">Privacy:</span>
                          <span className="text-vista-light">
                            {partySettings.isPrivate ? 'Private' : 'Public'}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-vista-light/70">Max Members:</span>
                          <span className="text-vista-light">{partySettings.maxMembers}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-vista-light/70">Chat:</span>
                          <span className="text-vista-light">
                            {partySettings.allowChat ? 'Enabled' : 'Disabled'}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-vista-light/70">Reactions:</span>
                          <span className="text-vista-light">
                            {partySettings.allowReactions ? 'Enabled' : 'Disabled'}
                          </span>
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="text-center py-8">
                      <Play className="h-12 w-12 text-vista-light/30 mx-auto mb-4" />
                      <p className="text-vista-light/70">
                        Select content to see party preview
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Create Button */}
              <Button
                onClick={handleCreateParty}
                disabled={!selectedContent}
                className="w-full bg-vista-blue hover:bg-vista-blue/90 text-white disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Users className="h-4 w-4 mr-2" />
                Create Watch Party
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 
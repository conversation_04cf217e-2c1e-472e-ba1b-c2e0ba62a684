"use client";

import { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { ChevronRight, PlayCircle } from 'lucide-react';
import { motion } from 'framer-motion';
import { WatchProgress } from '@/components/ui/progress';

interface WatchItem {
  id: string | number;
  title: string;
  image: string;
  type: 'show' | 'movie';
  progress: number; // 0-100
  episode?: string;
  season?: number | string;
  timestamp?: string; // e.g. "35:42"
  currentTime?: number; // in seconds
  duration?: number; // in seconds
}

interface ContinueWatchingProps {
  items: WatchItem[];
}

export default function ContinueWatching({ items }: ContinueWatchingProps) {
  const rowRef = useRef<HTMLDivElement>(null);
  const [showControls, setShowControls] = useState(false);
  const [showLeftButton, setShowLeftButton] = useState(false);
  const [showRightButton, setShowRightButton] = useState(true);

  // Detect if scrolling is possible
  useEffect(() => {
    if (rowRef.current) {
      const { scrollWidth, clientWidth } = rowRef.current;
      setShowControls(scrollWidth > clientWidth);
    }
  }, []);

  // Scroll row to the left
  const scrollLeft = () => {
    if (rowRef.current) {
      rowRef.current.scrollBy({ left: -rowRef.current.clientWidth * 0.8, behavior: 'smooth' });
    }
  };

  // Scroll row to the right
  const scrollRight = () => {
    if (rowRef.current) {
      rowRef.current.scrollBy({ left: rowRef.current.clientWidth * 0.8, behavior: 'smooth' });
    }
  };

  // Update scroll buttons visibility
  const handleScroll = () => {
    if (rowRef.current) {
      // Check if we can scroll to the left
      setShowLeftButton(rowRef.current.scrollLeft > 20);

      // Check if we can scroll to the right
      const maxScrollLeft = rowRef.current.scrollWidth - rowRef.current.clientWidth;
      setShowRightButton(Math.ceil(rowRef.current.scrollLeft) < maxScrollLeft - 20);
    }
  };

  // Convert timestamp string (mm:ss or hh:mm:ss) to seconds
  const timestampToSeconds = (timestamp: string): number => {
    if (!timestamp) return 0;

    const parts = timestamp.split(':').map(Number);
    if (parts.length === 3) {
      // Format: hh:mm:ss
      return parts[0] * 3600 + parts[1] * 60 + parts[2];
    } else if (parts.length === 2) {
      // Format: mm:ss
      return parts[0] * 60 + parts[1];
    }
    return 0;
  };

  if (items.length === 0) {
    return null;
  }

  return (
    <section className="py-6 md:py-8">
      <div className="container px-4 md:px-6 mx-auto">
        {/* Row Header */}
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl md:text-2xl font-semibold text-vista-light tracking-tight">Continue Watching</h2>
        </div>

        {/* Content Row with Scroll */}
        <div className="relative group">
          {/* Scrollable Content */}
          <div
            ref={rowRef}
            className="flex space-x-3 md:space-x-4 overflow-x-auto scrollbar-hide pb-1 -mx-1 px-1 pt-1"
            onScroll={handleScroll}
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {items.map((item, index) => {
              // Calculate current time and duration for WatchProgress
              const currentTime = item.currentTime || (item.duration ? (item.progress / 100) * item.duration : 0);
              const duration = item.duration || 100; // Default to 100 if not provided

              return (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, y: 15 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{
                    duration: 0.4,
                    delay: index * 0.05,
                    ease: [0.25, 0.1, 0.25, 1.0]
                  }}
                  className="flex-none w-64 sm:w-72 relative group"
                >
                  <div className="overflow-hidden rounded-lg hover-card-effect">
                    <div className="relative">
                      {/* Image */}
                      <div className="aspect-video relative overflow-hidden rounded-lg">
                        <Image
                          src={item.image}
                          alt={item.title}
                          fill
                          className="object-cover content-image-hover"
                        />

                        {/* Gradient overlay */}
                        <div className="absolute inset-0 bg-card-gradient opacity-90"></div>

                        {/* Play button overlay */}
                        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <Link href={`/${item.type}s/${item.id}`}>
                            <div className="bg-black/40 backdrop-blur-sm p-3 rounded-full hover:bg-vista-accent transition-colors cursor-pointer">
                              <PlayCircle className="h-10 w-10 text-white" />
                            </div>
                          </Link>
                        </div>
                      </div>

                      {/* Enhanced progress bar */}
                      <div className="mt-[-4px]">
                        <WatchProgress
                          currentTime={currentTime}
                          duration={duration}
                          size="xs"
                        />
                      </div>

                      {/* Content info */}
                      <div className="mt-2">
                        <h3 className="text-sm font-medium text-vista-light">{item.title}</h3>
                        <div className="flex justify-between items-center mt-1">
                          <p className="text-xs text-vista-light/70">
                            {item.type === 'show' && item.season && item.episode ?
                              `S${item.season} E${item.episode}` :
                              `${item.timestamp || ''}`
                            }
                          </p>
                          <span className="text-xs text-vista-accent">{item.timestamp}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>

          {/* Scroll buttons - only shown if scrolling is possible */}
          {showControls && (
            <>
              {/* Left button */}
              <button
                className={`absolute left-0 top-[30%] -translate-y-1/2 z-10 bg-black/80 text-vista-light rounded-full w-8 h-8 flex items-center justify-center transition-opacity
                ${showLeftButton ? 'opacity-0 group-hover:opacity-100' : 'opacity-0 cursor-default'}`}
                onClick={scrollLeft}
                disabled={!showLeftButton}
                aria-label="Scroll left"
              >
                <ChevronRight className="h-5 w-5 transform rotate-180" />
              </button>

              {/* Right button */}
              <button
                className={`absolute right-0 top-[30%] -translate-y-1/2 z-10 bg-black/80 text-vista-light rounded-full w-8 h-8 flex items-center justify-center transition-opacity
                ${showRightButton ? 'opacity-0 group-hover:opacity-100' : 'opacity-0 cursor-default'}`}
                onClick={scrollRight}
                disabled={!showRightButton}
                aria-label="Scroll right"
              >
                <ChevronRight className="h-5 w-5" />
              </button>
            </>
          )}
        </div>
      </div>
    </section>
  );
}

'use client';

import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useToastHelpers } from '@/lib/ToastContext';
import { useAuth } from '@/contexts/AuthContext';
import {
  BarChart3,
  Eye,
  MousePointer,
  TrendingUp,
  RefreshCw,
  Calendar,
  Loader2
} from 'lucide-react';

interface BannerAnalytics {
  totalBanners: number;
  activeBanners: number;
  totalViews: number;
  totalClicks: number;
  totalImpressions: number;
  clickThroughRate: number;
}

interface TopBanner {
  id: string;
  title: string;
  views: number;
  clicks: number;
  impressions: number;
  clickThroughRate: string;
  createdAt: string;
}

interface MaintenanceData {
  analytics: BannerAnalytics;
  topBanners: TopBanner[];
  timestamp: string;
}

export default function BannerAnalytics() {
  const [data, setData] = useState<MaintenanceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [runningMaintenance, setRunningMaintenance] = useState(false);
  const { success, error: showError } = useToastHelpers();
  const { user } = useAuth();

  const fetchAnalytics = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/banner-ads/maintenance', {
        headers: {
          'Authorization': `Bearer ${user?.id}`,
          'Cache-Control': 'no-cache'
        },
        credentials: 'include'
      });
      if (!response.ok) {
        throw new Error('Failed to fetch analytics');
      }
      const analyticsData = await response.json();
      setData(analyticsData);
    } catch (error) {
      console.error('Error fetching banner analytics:', error);
      showError('Error', 'Failed to load banner analytics');
    } finally {
      setLoading(false);
    }
  }, [user?.id, showError]);

  const runMaintenance = async () => {
    try {
      setRunningMaintenance(true);
      const response = await fetch('/api/admin/banner-ads/maintenance', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${user?.id}`,
          'Cache-Control': 'no-cache'
        },
        credentials: 'include'
      });
      
      if (!response.ok) {
        throw new Error('Failed to run maintenance');
      }

      const result = await response.json();
      success('Success', 'Banner maintenance completed successfully');
      
      // Refresh analytics after maintenance
      await fetchAnalytics();
    } catch (error) {
      console.error('Error running maintenance:', error);
      showError('Error', 'Failed to run banner maintenance');
    } finally {
      setRunningMaintenance(false);
    }
  };

  useEffect(() => {
    if (user?.id) {
      fetchAnalytics();
    }
  }, [user?.id, fetchAnalytics]);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-vista-blue" />
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-8">
        <p className="text-vista-light/60">Failed to load analytics data</p>
        <Button onClick={fetchAnalytics} className="mt-4">
          Try Again
        </Button>
      </div>
    );
  }

  const { analytics, topBanners } = data;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-vista-light">Banner Analytics</h2>
          <p className="text-vista-light/70">
            Last updated: {new Date(data.timestamp).toLocaleString()}
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={fetchAnalytics}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            onClick={runMaintenance}
            disabled={runningMaintenance}
            className="bg-vista-blue hover:bg-vista-blue/90"
          >
            {runningMaintenance && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            <Calendar className="h-4 w-4 mr-2" />
            Run Maintenance
          </Button>
        </div>
      </div>

      {/* Analytics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Banners</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.totalBanners}</div>
            <p className="text-xs text-muted-foreground">
              {analytics.activeBanners} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Views</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.totalViews.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {analytics.totalImpressions.toLocaleString()} impressions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Clicks</CardTitle>
            <MousePointer className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.totalClicks.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              User interactions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Click-Through Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.clickThroughRate.toFixed(2)}%</div>
            <p className="text-xs text-muted-foreground">
              Average CTR
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Top Performing Banners */}
      <Card>
        <CardHeader>
          <CardTitle>Top Performing Banners</CardTitle>
          <CardDescription>
            Banners ranked by clicks and engagement
          </CardDescription>
        </CardHeader>
        <CardContent>
          {topBanners.length === 0 ? (
            <div className="text-center py-8">
              <BarChart3 className="h-12 w-12 text-vista-light/40 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-vista-light mb-2">No banner data yet</h3>
              <p className="text-vista-light/60">
                Create some banner ads to see performance analytics
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Banner Title</TableHead>
                    <TableHead className="text-center">Views</TableHead>
                    <TableHead className="text-center">Clicks</TableHead>
                    <TableHead className="text-center">Impressions</TableHead>
                    <TableHead className="text-center">CTR</TableHead>
                    <TableHead className="text-center">Performance</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {topBanners.map((banner, index) => (
                    <TableRow key={banner.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            #{index + 1}
                          </Badge>
                          <span className="font-medium">{banner.title}</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        {banner.views.toLocaleString()}
                      </TableCell>
                      <TableCell className="text-center">
                        {banner.clicks.toLocaleString()}
                      </TableCell>
                      <TableCell className="text-center">
                        {banner.impressions.toLocaleString()}
                      </TableCell>
                      <TableCell className="text-center">
                        <Badge 
                          variant={parseFloat(banner.clickThroughRate) > 2 ? 'default' : 'secondary'}
                          className={parseFloat(banner.clickThroughRate) > 2 ? 'bg-green-600' : ''}
                        >
                          {banner.clickThroughRate}%
                        </Badge>
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="flex items-center justify-center">
                          {parseFloat(banner.clickThroughRate) > 5 && (
                            <Badge className="bg-yellow-600">Excellent</Badge>
                          )}
                          {parseFloat(banner.clickThroughRate) > 2 && parseFloat(banner.clickThroughRate) <= 5 && (
                            <Badge className="bg-green-600">Good</Badge>
                          )}
                          {parseFloat(banner.clickThroughRate) > 1 && parseFloat(banner.clickThroughRate) <= 2 && (
                            <Badge variant="secondary">Average</Badge>
                          )}
                          {parseFloat(banner.clickThroughRate) <= 1 && (
                            <Badge variant="outline">Low</Badge>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

'use client';

import * as React from 'react';
import { format, isValid } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { DateRange } from 'react-day-picker';

interface DateRangePickerProps {
  dateRange: {
    from: Date | null;
    to: Date | null;
  };
  onSelect?: (dateRange: DateRange | undefined) => void;
  className?: string;
  placeholder?: string;
}

export function DateRangePicker({
  dateRange,
  onSelect,
  className,
  placeholder = "Select date range"
}: DateRangePickerProps) {
  // Convert from Date | null to DateRange for react-day-picker
  const selectedRange: DateRange | undefined = dateRange.from ? {
    from: dateRange.from,
    to: dateRange.to || undefined
  } : undefined;

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant="outline"
            className={cn(
              "w-full justify-start text-left font-normal",
              !dateRange.from && "text-vista-light/50"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {dateRange.from ? (
              dateRange.to ? (
                <>
                  {isValid(dateRange.from) && format(dateRange.from, "PPP")}
                  {" to "}
                  {isValid(dateRange.to) && format(dateRange.to, "PPP")}
                </>
              ) : (
                isValid(dateRange.from) && format(dateRange.from, "PPP")
              )
            ) : (
              <span>{placeholder}</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            initialFocus
            mode="range"
            selected={selectedRange}
            onSelect={onSelect}
            numberOfMonths={2}
            defaultMonth={dateRange.from || undefined}
          />
        </PopoverContent>
      </Popover>
    </div>
  );
} 
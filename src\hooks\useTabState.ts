'use client';

import { useState, useEffect } from 'react';

/**
 * Custom hook for managing tab state with proper rerendering on navigation
 * This helps solve issues with tabs not responding after page navigation
 */
export function useTabState(defaultValue: string) {
  // Current active tab
  const [activeTab, setActiveTab] = useState(defaultValue);
  
  // Generate a unique key for the tabs component to force remount
  const [tabsKey, setTabsKey] = useState(() => 
    `tabs-${Math.random().toString(36).substring(2, 9)}`
  );
  
  // Reset tabs component on mount
  useEffect(() => {
    // Generate a new unique key to force remount
    setTabsKey(`tabs-${Math.random().toString(36).substring(2, 9)}`);
    // Reset to default tab
    setActiveTab(defaultValue);
  }, [defaultValue]);
  
  /**
   * Changes the active tab and ensures state is updated
   */
  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };
  
  return {
    activeTab,
    tabsKey,
    handleTabChange,
  };
} 
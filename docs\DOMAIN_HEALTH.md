# Domain Health Service

## Overview

The Domain Health Service is a critical component of StreamVista that monitors and manages the health of VidSrc streaming domains. It provides real-time health checks, caching mechanisms, and automatic fallback strategies to ensure reliable content delivery.

## Core Components

### Domain Health Interface

```typescript
interface DomainHealth {
  domain: string;        // Domain name
  status: 'ok' | 'error'; // Current status
  responseTime: number;   // Response time in milliseconds
  lastChecked: number;    // Timestamp of last check
}
```

### Configuration Constants

```typescript
const HEALTH_CHECK_TIMEOUT = 3000;           // 3 seconds timeout
const CACHE_DURATION = 10 * 60 * 1000;       // 10 minutes cache duration
const VIDSRC_DOMAINS = [
  'vidsrc.xyz',
  'vidsrc.in',
  'vidsrc.pm',
  'vidsrc.net'
];
```

## Core Functions

### Domain Health Check

The service provides several key functions for checking domain health:

1. `checkDomainHealth(domain: string): Promise<DomainHealth>`
   - Checks the health of a single domain
   - Performs HEAD request first, falls back to GET if needed
   - Returns domain health information

2. `checkAllDomains(forceRefresh?: boolean): Promise<DomainHealth[]>`
   - Checks all VidSrc domains in parallel
   - Supports cached results with configurable duration
   - Force refresh option available

3. `getBestDomain(): Promise<string>`
   - Returns the most optimal domain based on health and response time
   - Prioritizes reliable domains for initial loads
   - Includes fallback mechanism

4. `getNextBestDomain(currentDomain: string): Promise<string>`
   - Finds the next best domain excluding the current one
   - Useful for automatic failover
   - Includes fallback to simple rotation

## Caching Mechanism

The service implements a local storage-based caching system:

```typescript
// Cache keys
const DOMAIN_HEALTH_CACHE_KEY = 'vidsrc:domain:health';
const LAST_CHECK_CACHE_KEY = 'vidsrc:domain:last_check';

// Cache helper functions
function getCache<T>(key: string): T | null;
function setCache<T>(key: string, value: T): void;
```

## Integration

### API Integration

The service is integrated with the API layer through:
- Health check endpoint (`/api/vidsrc/health`)
- Proxy service integration
- Automatic domain fallback in the video player

### Video Player Integration

The VidSrcPlayer component uses this service to:
1. Select the optimal initial domain
2. Handle automatic failover
3. Manage domain switching
4. Monitor playback health

## Error Handling

The service implements robust error handling:
- Timeout management for health checks
- Graceful degradation with fallback domains
- Cached results for offline scenarios
- Detailed error logging and monitoring

## Best Practices

1. **Performance**
   - Use HEAD requests for quick health checks
   - Implement caching to reduce API calls
   - Parallel health checks for efficiency
   - Configurable timeouts and retry attempts

2. **Reliability**
   - Multiple fallback domains
   - Automatic domain rotation
   - Cache invalidation strategy
   - Health check result persistence

3. **Monitoring**
   - Response time tracking
   - Domain availability logging
   - Error rate monitoring
   - Cache hit rate tracking

## Usage Examples

### Basic Domain Health Check
```typescript
const health = await checkDomainHealth('vidsrc.xyz');
console.log(`Domain status: ${health.status}, Response time: ${health.responseTime}ms`);
```

### Getting Best Available Domain
```typescript
const bestDomain = await getBestDomain();
console.log(`Using optimal domain: ${bestDomain}`);
```

### Implementing Automatic Failover
```typescript
try {
  let domain = await getBestDomain();
  if (!isWorking(domain)) {
    domain = await getNextBestDomain(domain);
  }
} catch (error) {
  console.error('Domain failover error:', error);
}
```

## Future Considerations

1. **Scalability**
   - Distributed health checking
   - Regional domain optimization
   - Load balancing integration
   - Enhanced caching strategies

2. **Monitoring**
   - Real-time health dashboards
   - Automated alerting system
   - Performance metrics tracking
   - Historical data analysis

3. **Features**
   - Geographic routing optimization
   - Predictive domain selection
   - Custom health check parameters
   - Advanced caching policies 
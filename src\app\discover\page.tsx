'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import {
  Film,
  Monitor,
  Calendar,
  Eye,
  RotateCcw,
  AlertCircle,
  Search,
  Filter,
  Grid,
  List,
  Star,
  TrendingUp
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Navbar } from '@/components/Navbar';
import Footer from '@/components/Footer';
import ContentCard from '@/components/ContentCard';
import {
  getPopularMovies,
  getTopRatedMovies,
  getPopularTVShows,
  getTopRatedTVShows,
  getTrendingDaily,
  MappedContent
} from '@/lib/tmdb-api';
import { formatTMDbContentForCards, ContentCardType } from '@/lib/content-utils';
import { Loader2 } from 'lucide-react';

type Category = 'popular-movies' | 'top-rated-movies' | 'popular-shows' | 'top-rated-shows' | 'trending';
type ViewMode = 'grid' | 'list';

export default function DiscoverPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get category from URL params
  const categoryParam = searchParams.get('category') as Category | null;
  const [category, setCategory] = useState<Category>(categoryParam || 'popular-movies');
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [content, setContent] = useState<ContentCardType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  // Category configurations
  const categoryConfig = {
    'popular-movies': {
      title: 'Popular Movies',
      icon: Film,
      fetchFunction: getPopularMovies,
      description: 'Most popular movies right now'
    },
    'top-rated-movies': {
      title: 'Top Rated Movies',
      icon: Star,
      fetchFunction: getTopRatedMovies,
      description: 'Highest rated movies of all time'
    },
    'popular-shows': {
      title: 'Popular TV Shows',
      icon: Monitor,
      fetchFunction: getPopularTVShows,
      description: 'Most popular TV shows right now'
    },
    'top-rated-shows': {
      title: 'Top Rated TV Shows',
      icon: Star,
      fetchFunction: getTopRatedTVShows,
      description: 'Highest rated TV shows of all time'
    },
    'trending': {
      title: 'Trending',
      icon: TrendingUp,
      fetchFunction: (page: number) => getTrendingDaily('all', page),
      description: 'Trending movies and shows today'
    }
  };

  // Fetch content based on category
  const fetchContent = async (resetPage = false) => {
    try {
      setLoading(true);
      setError(null);

      const currentPage = resetPage ? 1 : page;
      const config = categoryConfig[category];

      if (!config) {
        throw new Error('Invalid category');
      }

      const data: MappedContent[] = await config.fetchFunction(currentPage);
      const formattedContent = formatTMDbContentForCards(data);

      if (resetPage) {
        setContent(formattedContent);
        setPage(2);
      } else {
        setContent(prev => [...prev, ...formattedContent]);
        setPage(prev => prev + 1);
      }

      // Check if we have more content (TMDb typically has 20 items per page)
      setHasMore(formattedContent.length === 20);

    } catch (err) {
      console.error('Error fetching content:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch content');
    } finally {
      setLoading(false);
    }
  };

  // Load more content
  const loadMore = () => {
    if (!loading && hasMore) {
      fetchContent(false);
    }
  };

  // Handle category change
  const handleCategoryChange = (newCategory: Category) => {
    setCategory(newCategory);
    setPage(1);

    // Update URL
    const params = new URLSearchParams(searchParams?.toString() || '');
    params.set('category', newCategory);
    router.push(`/discover?${params.toString()}`);
  };

  // Reset and fetch when category changes
  useEffect(() => {
    setPage(1);
    fetchContent(true);
  }, [category]);

  // Update category when URL changes
  useEffect(() => {
    if (categoryParam && categoryParam !== category) {
      setCategory(categoryParam);
    }
  }, [categoryParam]);

  const currentConfig = categoryConfig[category];
  const IconComponent = currentConfig.icon;

  return (
    <div className="min-h-screen bg-vista-dark">
      <Navbar />
      <main className="min-h-screen bg-vista-dark pt-16 md:pt-20">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-3">
              <IconComponent className="h-8 w-8 text-vista-blue" />
              <div>
                <h1 className="text-3xl font-bold text-vista-light mb-2">
                  {currentConfig.title}
                </h1>
                <p className="text-vista-light/60">
                  {currentConfig.description}
                </p>
              </div>
            </div>

            {/* View Mode Toggle */}
            <div className="flex gap-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="icon"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="icon"
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Category Filters */}
          <div className="flex flex-wrap gap-2 mb-8">
            {Object.entries(categoryConfig).map(([key, config]) => {
              const Icon = config.icon;
              return (
                <Button
                  key={key}
                  variant={category === key ? 'default' : 'outline'}
                  onClick={() => handleCategoryChange(key as Category)}
                  className="gap-2"
                >
                  <Icon className="h-4 w-4" />
                  {config.title}
                </Button>
              );
            })}
          </div>

          {/* Error State */}
          {error && (
            <Alert className="mb-8">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {error}
              </AlertDescription>
            </Alert>
          )}

          {/* Loading State */}
          {loading && content.length === 0 && (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
              {[...Array(20)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="bg-vista-dark-card rounded-lg h-64 mb-3"></div>
                  <div className="bg-vista-dark-card rounded h-4 mb-2"></div>
                  <div className="bg-vista-dark-card rounded h-3 w-3/4"></div>
                </div>
              ))}
            </div>
          )}

          {/* Content Grid */}
          {!loading || content.length > 0 ? (
            <>
              <div className={`grid gap-6 ${
                viewMode === 'grid'
                  ? 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5'
                  : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
              }`}>
                {content.map((item, index) => (
                  <motion.div
                    key={`${item.id}-${index}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                  >
                    <ContentCard
                      id={item.id}
                      title={item.title}
                      imagePath={item.imagePath}
                      type={item.type}
                      year={item.year}
                      ageRating={item.ageRating}
                      index={index}
                      link={`/watch/${item.id}?forcePlay=true&contentType=${item.type === 'shows' ? 'show' : 'movie'}`}
                      isAwardWinning={item.isAwardWinning}
                      dataSource={item.dataSource}
                    />
                  </motion.div>
                ))}
              </div>

              {/* Load More Button */}
              {hasMore && (
                <div className="flex justify-center mt-12">
                  <Button
                    onClick={loadMore}
                    disabled={loading}
                    className="gap-2"
                  >
                    {loading && <Loader2 className="h-4 w-4 animate-spin" />}
                    Load More
                  </Button>
                </div>
              )}

              {/* End Message */}
              {!hasMore && content.length > 0 && (
                <div className="text-center mt-12">
                  <p className="text-vista-light/60">
                    You've reached the end of {currentConfig.title.toLowerCase()}
                  </p>
                </div>
              )}
            </>
          ) : null}

          {/* Empty State */}
          {!loading && !error && content.length === 0 && (
            <div className="text-center py-16">
              <IconComponent className="h-16 w-16 text-vista-light/40 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-vista-light mb-2">
                No content found
              </h2>
              <p className="text-vista-light/60">
                Try selecting a different category
              </p>
            </div>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
}
# Authentication Service

## Overview

StreamVista's authentication system provides user authentication and session management through a client-side implementation with support for email/password authentication and social login options (Google and Facebook). The system is designed to be extensible and secure, with proper form validation and error handling.

## Components

### AuthForms Component

```typescript
interface AuthFormsProps {
  onLogin: (email: string, password: string) => void;
  onSignup: (name: string, email: string, password: string) => void;
  isLoading?: boolean;
}
```

The `AuthForms` component provides a unified interface for both login and signup functionality:
- Toggle between login and signup modes
- Form validation and error handling
- Password visibility toggle
- Loading state management
- Social login options
- Responsive design

### Authentication Page

The authentication page (`/auth/page.tsx`) handles:
- User authentication flow
- Form state management
- Navigation after successful authentication
- Error handling and user feedback

## Implementation Details

### Login Flow

```typescript
const handleLogin = async (email: string, password: string) => {
  setIsLoading(true);
  try {
    // API call would go here
    // Example: await authService.login(email, password);
    router.push('/');
  } catch (error) {
    // Handle error
  } finally {
    setIsLoading(false);
  }
};
```

### Signup Flow

```typescript
const handleSignup = async (name: string, email: string, password: string) => {
  setIsLoading(true);
  try {
    // API call would go here
    // Example: await authService.signup(name, email, password);
    router.push('/');
  } catch (error) {
    // Handle error
  } finally {
    setIsLoading(false);
  }
};
```

### Session Management

The application maintains user session information through:
- Local storage for persistent data
- Context providers for global state
- Navigation guards for protected routes

## Form Validation

### Login Form
- Email validation
- Password requirements
- Error messages and feedback

### Signup Form
- Name validation
- Email validation
- Password requirements and confirmation
- Terms agreement validation

## Social Authentication

### Supported Providers
- Google
- Facebook

### Implementation
```typescript
interface SocialAuthConfig {
  provider: 'google' | 'facebook';
  clientId: string;
  scope: string;
}

const socialAuthProviders: Record<string, SocialAuthConfig> = {
  google: {
    provider: 'google',
    clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
    scope: 'email profile'
  },
  facebook: {
    provider: 'facebook',
    clientId: process.env.NEXT_PUBLIC_FACEBOOK_APP_ID,
    scope: 'email,public_profile'
  }
};
```

## Protected Routes

### Route Protection
```typescript
// Example middleware configuration
export const config = {
  matcher: [
    '/account/:path*',
    '/watch-party/:path*',
    '/settings/:path*'
  ]
};
```

### Authentication State
```typescript
interface AuthState {
  isAuthenticated: boolean;
  user: {
    id: string;
    name: string;
    email: string;
    profileImage?: string;
  } | null;
  loading: boolean;
}
```

## Integration with Navigation

### Navbar Integration
```typescript
interface NavbarProps {
  isLoggedIn?: boolean;
  userProfileImg?: string;
  // ... other props
}
```

The Navbar component handles:
- Authentication state display
- Profile menu for authenticated users
- Sign in/out actions
- Mobile responsiveness

## Error Handling

### Error Types
```typescript
type AuthError =
  | 'INVALID_CREDENTIALS'
  | 'EMAIL_NOT_VERIFIED'
  | 'ACCOUNT_DISABLED'
  | 'NETWORK_ERROR'
  | 'SOCIAL_AUTH_ERROR';

interface AuthErrorResponse {
  type: AuthError;
  message: string;
  details?: Record<string, any>;
}
```

### Error Messages
Localized error messages are provided through the i18n system:
```typescript
const authErrorMessages = {
  'auth.error.invalidCredentials': 'Invalid email or password',
  'auth.error.emailNotVerified': 'Please verify your email address',
  'auth.error.accountDisabled': 'Your account has been disabled',
  'auth.error.networkError': 'Unable to connect to the server',
  'auth.error.socialAuthError': 'Social authentication failed'
};
```

## Best Practices

1. **Security**
   - Implement proper password hashing
   - Use HTTPS for all authentication requests
   - Implement rate limiting for login attempts
   - Secure storage of authentication tokens
   - Regular session validation

2. **User Experience**
   - Clear error messages
   - Loading states for async operations
   - Smooth transitions between auth states
   - Persistent login when requested
   - Graceful error recovery

3. **Performance**
   - Optimize authentication requests
   - Implement proper caching strategies
   - Minimize unnecessary re-renders
   - Efficient form state management

4. **Accessibility**
   - ARIA labels for form fields
   - Keyboard navigation support
   - High contrast error states
   - Screen reader compatibility
   - Focus management

5. **Maintenance**
   - Clear separation of concerns
   - Consistent error handling
   - Comprehensive logging
   - Easy configuration management
   - Modular component design 
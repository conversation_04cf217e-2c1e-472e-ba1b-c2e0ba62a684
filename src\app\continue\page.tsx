'use client';

import { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Play, Info, ChevronRight, ChevronLeft, Clock, Trash2, MoreVertical } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useLanguage } from '@/lib/i18n/LanguageContext';
import { Card, CardContent } from '@/components/ui/card';
import { useContinueWatching, WatchItem } from '@/hooks/useContinueWatching';
import { useAuth } from '@/contexts/AuthContext';
import { Navbar } from '@/components/Navbar';
import Footer from '@/components/Footer';
import { motion } from 'framer-motion';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Alert, AlertDescription } from '@/components/ui/alert';

export default function ContinuePage() {
  const { t } = useLanguage();
  const { isAuthenticated, user } = useAuth();
  const { items: continueWatchingData, isLoading, error, refetch } = useContinueWatching();
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [isSelectionMode, setIsSelectionMode] = useState(false);

  // Format timestamp for display
  const formatTimestamp = (currentTime?: number, duration?: number) => {
    if (!currentTime) return '';
    
    const hours = Math.floor(currentTime / 3600);
    const minutes = Math.floor((currentTime % 3600) / 60);
    const seconds = Math.floor(currentTime % 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Remove item from continue watching
  const removeItem = async (itemId: string) => {
    if (!user?.id) return;

    try {
      const response = await fetch(`/api/user/continue-watching/${itemId}?userId=${user.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        refetch(); // Refresh the list
      }
    } catch (error) {
      console.error('Error removing item:', error);
    }
  };

  // Clear all continue watching items
  const clearAll = async () => {
    if (!user?.id) return;

    try {
      const response = await fetch(`/api/user/continue-watching?userId=${user.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        refetch(); // Refresh the list
      }
    } catch (error) {
      console.error('Error clearing all items:', error);
    }
  };

  // Toggle selection mode
  const toggleSelectionMode = () => {
    setIsSelectionMode(!isSelectionMode);
    setSelectedItems(new Set());
  };

  // Toggle item selection
  const toggleItemSelection = (itemId: string) => {
    const newSelection = new Set(selectedItems);
    if (newSelection.has(itemId)) {
      newSelection.delete(itemId);
    } else {
      newSelection.add(itemId);
    }
    setSelectedItems(newSelection);
  };

  // Remove selected items
  const removeSelectedItems = async () => {
    if (!user?.id) return;

    try {
      await Promise.all(
        Array.from(selectedItems).map(itemId =>
          fetch(`/api/user/continue-watching/${itemId}?userId=${user.id}`, { method: 'DELETE' })
        )
      );
      setSelectedItems(new Set());
      setIsSelectionMode(false);
      refetch();
    } catch (error) {
      console.error('Error removing selected items:', error);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-vista-dark">
        <Navbar />
        <main className="min-h-screen bg-vista-dark pt-16 md:pt-20">
          <div className="container mx-auto px-4 py-8">
            <div className="text-center">
              <h1 className="text-3xl font-bold text-vista-light mb-4">Continue Watching</h1>
              <Alert className="max-w-md mx-auto">
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Please sign in to view your continue watching list.
                </AlertDescription>
              </Alert>
              <Link href="/auth">
                <Button className="mt-4">Sign In</Button>
              </Link>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-vista-dark">
      <Navbar />
      <main className="min-h-screen bg-vista-dark pt-16 md:pt-20">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-vista-light mb-2">Continue Watching</h1>
              <p className="text-vista-light/60">
                Pick up where you left off
              </p>
            </div>
            
            {continueWatchingData.length > 0 && (
              <div className="flex gap-2">
                {isSelectionMode && selectedItems.size > 0 && (
                  <Button 
                    variant="destructive" 
                    onClick={removeSelectedItems}
                    className="gap-2"
                  >
                    <Trash2 className="h-4 w-4" />
                    Remove Selected ({selectedItems.size})
                  </Button>
                )}
                
                <Button 
                  variant="outline" 
                  onClick={toggleSelectionMode}
                  className="gap-2"
                >
                  {isSelectionMode ? 'Cancel' : 'Select'}
                </Button>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="icon">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={clearAll} className="text-red-600">
                      <Trash2 className="h-4 w-4 mr-2" />
                      Clear All
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )}
          </div>

          {/* Loading State */}
          {isLoading && (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
              {[...Array(10)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="bg-vista-dark-card rounded-lg h-64 mb-3"></div>
                  <div className="bg-vista-dark-card rounded h-4 mb-2"></div>
                  <div className="bg-vista-dark-card rounded h-3 w-3/4"></div>
                </div>
              ))}
            </div>
          )}

          {/* Error State */}
          {error && (
            <Alert className="max-w-md mx-auto">
              <AlertDescription>
                {error}
              </AlertDescription>
            </Alert>
          )}

          {/* Empty State */}
          {!isLoading && !error && continueWatchingData.length === 0 && (
            <div className="text-center py-16">
              <Clock className="h-16 w-16 text-vista-light/40 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-vista-light mb-2">
                No items to continue watching
              </h2>
              <p className="text-vista-light/60 mb-6">
                Start watching movies and shows to see them here
              </p>
              <Link href="/movies">
                <Button>Browse Movies</Button>
              </Link>
            </div>
          )}

          {/* Content Grid */}
          {!isLoading && !error && continueWatchingData.length > 0 && (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
              {continueWatchingData.map((item, index) => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="relative"
                >
                  <Card className="bg-vista-dark-card border-vista-dark-card overflow-hidden group hover:scale-105 transition-transform duration-200">
                    <CardContent className="p-0">
                      <div className="relative">
                        {/* Selection Checkbox */}
                        {isSelectionMode && (
                          <div className="absolute top-2 left-2 z-10">
                            <input
                              type="checkbox"
                              checked={selectedItems.has(item.id.toString())}
                              onChange={() => toggleItemSelection(item.id.toString())}
                              className="w-4 h-4 rounded border-gray-300"
                            />
                          </div>
                        )}

                        {/* Remove Button */}
                        {!isSelectionMode && (
                          <div className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
                            <Button
                              size="icon"
                              variant="destructive"
                              className="h-8 w-8"
                              onClick={(e) => {
                                e.preventDefault();
                                removeItem(item.id.toString());
                              }}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        )}

                        <Link href={`/watch/${item.id}?forcePlay=true&contentType=${item.type}`}>
                          <div className="relative h-64 w-full">
                            <Image
                              src={item.image}
                              alt={item.title}
                              className="object-cover transition-transform group-hover:scale-105"
                              fill
                              sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, (max-width: 1280px) 25vw, 20vw"
                            />

                            {/* Progress Bar */}
                            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-3">
                              <Progress
                                value={item.progress}
                                className="h-1 mb-2"
                              />
                              <div className="flex justify-between items-center text-xs text-white">
                                <span>{Math.round(item.progress)}% watched</span>
                                {item.currentTime && (
                                  <span>{formatTimestamp(item.currentTime, item.duration)}</span>
                                )}
                              </div>
                            </div>

                            {/* Play Button Overlay */}
                            <div className="absolute inset-0 bg-black/20 group-hover:bg-black/30 transition-colors flex justify-center items-center opacity-0 group-hover:opacity-100">
                              <Button size="icon" className="rounded-full bg-vista-blue/90 hover:bg-vista-blue">
                                <Play className="h-5 w-5 text-white" fill="white" />
                              </Button>
                            </div>
                          </div>
                        </Link>
                      </div>

                      {/* Content Info */}
                      <div className="p-4">
                        <h3 className="font-semibold text-vista-light mb-1 line-clamp-2">
                          {item.title}
                        </h3>
                        <div className="text-sm text-vista-light/60">
                          {item.type === 'show' && item.episode && (
                            <p>{item.episode}</p>
                          )}
                          {item.type === 'show' && item.season && (
                            <p>Season {item.season}</p>
                          )}
                          <p className="capitalize">{item.type}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
}

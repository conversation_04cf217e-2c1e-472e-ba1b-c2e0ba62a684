'use client';

import { useEffect } from 'react';
import { Button } from '@/components/ui/button';

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Global error caught:', error);
    
    // For session-related errors, attempt to clean up local storage
    if (error.message.includes('session') || 
        error.message.includes('verification') || 
        error.message.includes('Critical error')) {
      console.log('Session error detected, cleaning up local storage...');
      try {
        localStorage.removeItem('user');
        localStorage.removeItem('userId');
        localStorage.removeItem('watchPartyUserId');
      } catch (cleanupError) {
        console.error('Failed to clean up local storage:', cleanupError);
      }
    }
  }, [error]);

  const handleReset = () => {
    try {
      reset();
    } catch (resetError) {
      console.error('Reset failed, reloading page:', resetError);
      window.location.reload();
    }
  };

  const handleSignOut = () => {
    try {
      // Clear all auth data
      localStorage.removeItem('user');
      localStorage.removeItem('userId');
      localStorage.removeItem('watchPartyUserId');
      
      // Redirect to auth page
      window.location.href = '/auth';
    } catch (signOutError) {
      console.error('Sign out failed:', signOutError);
      // Force page reload as a fallback
      window.location.reload();
    }
  };

  // Check if this is a session-related error
  const isSessionError = error.message.includes('session') || 
                         error.message.includes('verification') || 
                         error.message.includes('Critical error');

  return (
    <html>
      <body>
        <div className="flex min-h-screen items-center justify-center p-4 bg-gray-50 dark:bg-gray-900">
          <div className="w-full max-w-md p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
            <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="w-6 h-6 text-red-600 dark:text-red-400"
              >
                <circle cx="12" cy="12" r="10" />
                <line x1="12" y1="8" x2="12" y2="12" />
                <line x1="12" y1="16" x2="12.01" y2="16" />
              </svg>
            </div>
            
            <h1 className="text-xl font-bold text-center text-gray-900 dark:text-gray-100 mb-2">
              {isSessionError ? 'Session Error' : 'Critical Error'}
            </h1>
            
            <p className="text-center text-gray-600 dark:text-gray-400 mb-4">
              {isSessionError 
                ? "We're having trouble verifying your session. Please sign in again." 
                : "Something went wrong at the application level. We're sorry for the inconvenience."}
            </p>
            
            <div className="text-sm text-gray-500 dark:text-gray-500 mb-6 p-3 bg-gray-100 dark:bg-gray-700 rounded">
              <p className="break-words">{error.message || 'Unknown error'}</p>
              {error.digest && <p className="text-xs mt-1">Error ID: {error.digest}</p>}
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button 
                onClick={handleReset}
                variant="outline"
                className="w-full sm:w-auto"
              >
                Try again
              </Button>
              
              {isSessionError ? (
                <Button 
                  onClick={handleSignOut}
                  className="w-full sm:w-auto"
                >
                  Sign in again
                </Button>
              ) : (
                <Button 
                  onClick={() => window.location.href = '/'}
                  className="w-full sm:w-auto"
                >
                  Go to homepage
                </Button>
              )}
            </div>
          </div>
        </div>
      </body>
    </html>
  );
} 
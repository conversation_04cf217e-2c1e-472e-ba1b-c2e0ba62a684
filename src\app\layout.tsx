import { GeistSans } from 'geist/font/sans';
import './globals.css';
import { Toaster } from 'sonner';
import { ToastProvider } from '@/lib/ToastContext';
import { ErrorProvider } from '@/lib/ErrorContext';
import { AuthProvider } from '@/contexts/AuthContext';
import { ProfileProvider } from '@/contexts/ProfileContext';
import { WatchlistProvider } from '@/contexts/WatchlistContext';
import { NotificationProvider } from '@/contexts/NotificationContext';
import { ReviewProvider } from '@/contexts/ReviewContext';
import { WatchPartyWrapper } from './client-wrapper';
import { ClientInitializer } from '@/components/ClientInitializer';
import { QueryProvider } from "@/providers/query-provider"
import { Suspense, lazy } from 'react';
import { TooltipProvider } from '@/components/ui/tooltip';

// Lazily load the ChatAssistant component
const ChatAssistant = lazy(() => import('@/components/ui/ChatAssistant').then(mod => ({ default: mod.ChatAssistant })));

// Lazily load the AccessDeniedAlert component
const AccessDeniedAlert = lazy(() => import('@/components/AccessDeniedAlert'));

// Lazily load the VisitorTracker component
const VisitorTracker = lazy(() => import('@/components/VisitorTracker'));

export const metadata = {
  title: 'Stream Vista',
  description: 'Modern streaming platform with watch party features',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // We're now using Pusher instead of Socket.io

  return (
    <html lang="en" className="dark" suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
        <link rel="icon" href="/icon-192.webp" type="image/webp" sizes="192x192" />
        <link rel="icon" href="/icon-512.webp" type="image/webp" sizes="512x512" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.webp" type="image/webp" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#000000" />
        <meta name="msapplication-TileColor" content="#000000" />
        <meta name="msapplication-TileImage" content="/icon-512.webp" />
        <meta name="description" content="StreamVista - Premium streaming service" />
      </head>
      <body className={GeistSans.className} suppressHydrationWarning>
        <QueryProvider>
          <ErrorProvider>
            <ToastProvider>
              <AuthProvider>
                <NotificationProvider>
                  <ProfileProvider>
                    <WatchlistProvider>
                      <ReviewProvider>
                        <WatchPartyWrapper>
                          {/* Global TooltipProvider for consistent tooltip behavior */}
                          <TooltipProvider>
                            {/* Wrap the ChatAssistant with Suspense */}
                            <Suspense fallback={null}>
                              <ChatAssistant />
                            </Suspense>

                            {/* Access Denied Alert for admin routes */}
                            <Suspense fallback={null}>
                              <AccessDeniedAlert />
                            </Suspense>

                            {/* Add VisitorTracker component */}
                            <Suspense fallback={null}>
                              <VisitorTracker />
                            </Suspense>

                            {children}
                          </TooltipProvider>
                        </WatchPartyWrapper>
                      </ReviewProvider>
                    </WatchlistProvider>
                  </ProfileProvider>
                </NotificationProvider>
              </AuthProvider>
            </ToastProvider>
          </ErrorProvider>
        </QueryProvider>
        <Toaster
          position="top-right"
          theme="dark"
          closeButton
          richColors
          className="vista-toaster"
          toastOptions={{
            style: {
              background: 'rgba(15, 15, 20, 0.95)',
              border: '1px solid rgba(66, 153, 225, 0.2)',
              backdropFilter: 'blur(8px)',
              color: 'white',
              fontSize: '14px',
              boxShadow: '0 8px 30px rgba(0, 0, 0, 0.25)',
              borderRadius: '8px',
            },
            duration: 4000,
          }}
        />
        <ClientInitializer />
      </body>
    </html>
  );
}

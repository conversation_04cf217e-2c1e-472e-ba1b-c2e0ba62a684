# How to Make a User an Admin in StreamVista

Follow these steps to promote an existing user to an admin role:

## Option 1: Using the Make-Admin Script (Recommended)

1. First, make sure your server is not running, or open a new terminal window

2. Run the following command, replacing `<EMAIL>` with the actual email of the user:

   ```bash
   node scripts/make-admin.cjs <EMAIL>
   ```

3. You should see output confirming that the user's role has been updated to "admin"

4. Restart your application if needed

5. Log in with that user account and try accessing the admin panel at `/admin`

## Option 2: Using MongoDB Compass

1. Open MongoDB Compass and connect to your database

2. Navigate to your database (usually named `streamvista` or similar)

3. Open the `users` collection

4. Find the user you want to make an admin (search by email)

5. Click on "Edit Document" (pencil icon)

6. Look for the `role` field:
   - If it exists, change its value to `"admin"`
   - If it doesn't exist, add a new field called `role` with the string value `"admin"`

7. Click "Update" to save your changes

8. Restart your application if needed

## Option 3: Using the Admin API

If you already have an admin user, you can use the admin API to promote another user:

1. Log in as an existing admin user

2. Find the user ID of the user you want to promote

3. Use a tool like Postman or fetch in your browser console to make this request:

   ```javascript
   fetch('/api/admin/users/[user-id]', {
     method: 'PATCH',
     headers: {
       'Content-Type': 'application/json'
     },
     body: JSON.stringify({
       role: 'admin'
     })
   })
   .then(response => response.json())
   .then(data => console.log(data));
   ```

4. The response should confirm the user has been updated

## Verifying Admin Access

After making a user an admin:

1. Log in with that user's account
2. You should be able to access `/admin` in your application
3. All admin features should now be available

## Troubleshooting

If the user still can't access admin features:

1. Check that you updated the correct user (verify the email address)
2. Make sure the role is exactly `"admin"` (case-sensitive)
3. Clear browser cookies and log in again
4. Check the browser console for any errors
5. Verify in your database that the user has the admin role 
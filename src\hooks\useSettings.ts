'use client';

import useS<PERSON> from 'swr';
import { useState } from 'react';
import { useToastHelpers } from '@/lib/ToastContext';
import { useAuth } from '@/contexts/AuthContext';
import { SettingValue } from '@/lib/settings';

interface Setting {
  key: string;
  value: SettingValue;
  group: string;
  description?: string;
}

export function useSettings(group?: string) {
  const toast = useToastHelpers();
  const [isSaving, setIsSaving] = useState(false);

  // Get the user from auth context
  const { user } = useAuth();
  const userId = user?.id;

  // Fetch settings
  const {
    data,
    error,
    isLoading,
    mutate
  } = useSWR(
    userId ? `/api/admin/settings${group ? `?group=${group}&` : '?'}userId=${userId}` : null, // Only fetch if we have a userId
    async (url: string) => {
      try {
        // Add Authorization header with userId as bearer token
        const response = await fetch(url, {
          headers: {
            'Authorization': `Bearer ${userId}`,
            'Cache-Control': 'no-cache'
          },
          cache: 'no-store'
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch settings');
        }

        // Get raw settings from API
        const rawSettings = await response.json();

        // Process settings into grouped format
        const processedSettings: Record<string, Record<string, SettingValue>> = {};

        // Group settings by their group
        rawSettings.forEach((setting: Setting) => {
          const group = setting.group;
          if (!processedSettings[group]) {
            processedSettings[group] = {};
          }

          // Extract the key without the group prefix
          const keyParts = setting.key.split('.');
          const keyWithoutGroup = keyParts.length > 1 ? keyParts[1] : keyParts[0];

          processedSettings[group][setting.key] = setting.value;
        });

        return processedSettings;
      } catch (error) {
        console.error('Error fetching settings:', error);
        toast.error('Error', 'Failed to load settings');
        throw error;
      }
    },
    {
      revalidateOnFocus: false,
      dedupingInterval: 10000
    }
  );

  // Save settings
  const saveSettings = async (settings: Setting[]) => {
    if (!userId) {
      toast.error('Error', 'You must be logged in to save settings');
      return false;
    }

    setIsSaving(true);

    try {
      const response = await fetch(`/api/admin/settings?userId=${userId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${userId}`,
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify({
          settings,
          userId // Include userId in the body as well
        }),
        cache: 'no-store'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to save settings (${response.status})`);
      }

      // Refresh data
      await mutate();

      toast.success('Success', 'Settings saved successfully');

      return true;
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Error', error instanceof Error ? error.message : 'Failed to save settings');
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  return {
    settings: data,
    error,
    isLoading,
    isSaving,
    saveSettings,
    refetch: mutate
  };
}

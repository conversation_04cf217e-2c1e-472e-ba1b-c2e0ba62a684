'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import AuthForms from '@/components/AuthForms';
import { useAuth } from '@/contexts/AuthContext';
import { motion, AnimatePresence } from 'framer-motion';

// Featured movie backgrounds for auth page
const FEATURED_BACKGROUNDS = [
  {
    id: 1,
    path: 'https://image.tmdb.org/t/p/original/rLb2cwF3Pazuxaj0sRXQ037tGI1.jpg', // Dune
    title: 'Stream your favorite movies',
    credit: 'StreamVista Originals'
  },
  {
    id: 2,
    path: 'https://image.tmdb.org/t/p/original/9PqD3wSIjntyJDBzMNuxuKHwpUD.jpg', // Wednesday
    title: 'Discover new TV shows',
    credit: 'StreamVista Exclusives'
  },
  {
    id: 3,
    path: 'https://image.tmdb.org/t/p/original/jYEW5xZkZk2WTrdbMGAPFuBqbDc.jpg', // Stranger Things
    title: 'Watch anywhere, anytime',
    credit: 'StreamVista Collection'
  },
  {
    id: 4,
    path: 'https://image.tmdb.org/t/p/original/6LfVuZBiOOCtqch5Ukspjb9y0EB.jpg', // Squid Game
    title: 'Unlimited entertainment',
    credit: 'StreamVista Spotlight'
  },
  {
    id: 5,
    path: 'https://image.tmdb.org/t/p/original/rlCRM7U5g2hcU1O8ylGcqsMYHIP.jpg', // Oppenheimer
    title: 'Award-winning films',
    credit: 'StreamVista Premieres'
  }
];

export default function AuthPage() {
  const router = useRouter();
  const { isAuthenticated } = useAuth();
  const [redirectTo, setRedirectTo] = useState<string>('/');

  // Track whether the component has mounted
  const [isMounted, setIsMounted] = useState(false);

  // Add a flag to track manual navigation
  const [isManualNavigating, setIsManualNavigating] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Redirect to home if already authenticated
  useEffect(() => {
    if (!isMounted || isManualNavigating) return;

    // Check for the redirect parameter in the URL
    const params = new URLSearchParams(window.location.search);
    const redirect = params.get('redirect');

    if (redirect) {
      console.log(`Setting redirect destination to: ${redirect}`);
      setRedirectTo(redirect);
    }

    if (isAuthenticated) {
      console.log(`User is authenticated, redirecting to: ${redirectTo}`);

      // Short delay to ensure state is updated
      const redirectTimer = setTimeout(() => {
        try {
          console.log(`Executing redirect to: ${redirectTo}`);
          window.location.href = redirectTo;
        } catch (err) {
          console.error('Redirect error:', err);
        }
      }, 300);

      return () => clearTimeout(redirectTimer);
    }
  }, [isAuthenticated, redirectTo, isMounted, isManualNavigating]);

  // Function to handle image loading error
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    console.log('Logo image failed to load, using fallback');
    e.currentTarget.style.display = 'none'; // Hide the broken image

    // Find the parent element and add the fallback SVG
    const parent = e.currentTarget.parentElement;
    if (parent) {
      const fallbackSvg = document.createElement('div');
      fallbackSvg.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="40" height="40" class="mr-2">
          <circle cx="50" cy="50" r="45" fill="#000" />
          <path d="M34 32 C40 32, 46 36, 46 42 C46 48, 40 52, 34 52 C40 52, 46 56, 46 62 C46 68, 40 72, 32 72"
                stroke="#2d7fea" stroke-width="6" fill="none" stroke-linecap="round" />
          <path d="M52 32 L68 32 L52 52 L68 52 M52 52 L68 72"
                stroke="#2d7fea" stroke-width="6" fill="none" stroke-linecap="round" />
          <circle cx="75" cy="30" r="8" fill="#2d7fea" />
        </svg>
      `;
      const firstChild = fallbackSvg.firstChild;
      if (firstChild) {
        parent.insertBefore(firstChild, parent.firstChild);
      }
    }
  };

  // Function to handle manual navigation to home
  const navigateToHome = (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent default to ensure our code runs
    console.log('Navigating to home page');
    // Direct navigation to home page
    document.location.href = '/';
  };

  // State for background image rotation
  const [currentBgIndex, setCurrentBgIndex] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const [imagesLoaded, setImagesLoaded] = useState<Record<number, boolean>>({});

  // Ref for background images preloading
  const bgImagesRef = useRef<HTMLDivElement>(null);

  // Check if on mobile device and handle viewport height
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Fix for mobile viewport height (100vh issue)
    const setVhVariable = () => {
      // First we get the viewport height and we multiply it by 1% to get a value for a vh unit
      const vh = window.innerHeight * 0.01;
      // Then we set the value in the --vh custom property to the root of the document
      document.documentElement.style.setProperty('--vh', `${vh}px`);
    };

    checkMobile();
    setVhVariable();

    window.addEventListener('resize', checkMobile);
    window.addEventListener('resize', setVhVariable);

    return () => {
      window.removeEventListener('resize', checkMobile);
      window.removeEventListener('resize', setVhVariable);
    };
  }, []);

  // Rotate background images
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentBgIndex((prev) => (prev + 1) % FEATURED_BACKGROUNDS.length);
    }, 10000); // Change background every 10 seconds

    return () => clearInterval(interval);
  }, []);

  // Handle image load events
  const handleImageLoaded = (index: number) => {
    setImagesLoaded(prev => ({
      ...prev,
      [index]: true
    }));
  };

  return (
    <div className="h-screen flex flex-col relative overflow-hidden bg-black text-vista-light auth-page-mobile">
      {/* Cinematic Background */}
      <div className="absolute inset-0 z-0 overflow-hidden">
        {/* Preload all background images */}
        <div className="hidden" ref={bgImagesRef}>
          {FEATURED_BACKGROUNDS.map((bg, index) => (
            <Image
              key={`preload-${bg.id}`}
              src={bg.path}
              alt="Preload"
              width={1920}
              height={1080}
              onLoad={() => handleImageLoaded(index)}
            />
          ))}
        </div>

        {/* Background Images Carousel */}
        <AnimatePresence mode="wait">
          {FEATURED_BACKGROUNDS.map((bg, index) => (
            index === currentBgIndex && (
              <motion.div
                key={`bg-${bg.id}`}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 1.2 }}
                className="absolute inset-0"
              >
                <div className="absolute inset-0 bg-black/30 z-10" /> {/* Overlay for better text contrast */}
                <Image
                  src={bg.path}
                  alt={bg.title}
                  fill
                  priority
                  className="object-cover"
                  style={{ objectPosition: isMobile ? 'center 30%' : 'center 20%' }}
                />

                {/* Gradient overlays for better visibility */}
                <div className="absolute inset-0 bg-gradient-to-t from-black via-black/60 to-black/20 z-10" />
                <div className="absolute inset-0 bg-gradient-to-r from-black via-black/40 to-transparent z-10" />
                <div className="absolute inset-0 bg-gradient-to-br from-black/30 via-transparent to-black/50 z-10" />
                {isMobile && (
                  <div className="absolute inset-0 bg-black/40 backdrop-blur-[1px] z-10" />
                )}

                {/* Movie info overlay - only visible on larger screens */}
                <div className="absolute bottom-0 left-0 right-0 p-6 z-20 hidden md:block">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5, duration: 0.8 }}
                    className="bg-black/40 backdrop-blur-sm inline-block px-6 py-4 rounded-lg shadow-lg"
                  >
                    <h2 className="text-2xl font-bold text-white mb-1">{bg.title}</h2>
                    <p className="text-white/80">{bg.credit}</p>
                  </motion.div>
                </div>
              </motion.div>
            )
          ))}
        </AnimatePresence>
      </div>

      {/* Header */}
      <header className="py-2 md:py-4 px-4 md:px-8 z-20 relative flex justify-between items-center auth-header">
        <Link href="/" className="flex items-center group relative overflow-hidden">
          <div className="relative z-10">
            <div className="absolute -inset-1 opacity-0 group-hover:opacity-100 bg-gradient-to-r from-blue-600/20 via-blue-400/20 to-blue-600/20 blur-md transition-opacity duration-500"></div>
            <div className="relative md:w-14 md:h-14 w-12 h-12">
              <Image
                src="/logo.webp"
                width={96}
                height={96}
                quality={100}
                alt="StreamVista Logo"
                className="drop-shadow-lg"
                priority
                onError={() => {
                  console.log('Logo image failed to load, using fallback');
                }}
              />
            </div>
          </div>
          <div className="relative">
            <span className="text-xl md:text-2xl font-bold text-white transition-all duration-300 inline-block relative z-10">
              <span className="relative inline-block overflow-hidden">
                <span className="inline-block group-hover:translate-y-[-100%] transition-transform duration-300">StreamVista</span>
                <span className="absolute top-0 left-0 inline-block translate-y-[100%] group-hover:translate-y-0 text-white transition-transform duration-300">StreamVista</span>
              </span>
            </span>
            <div className="absolute bottom-0 left-0 w-0 h-[2px] bg-gradient-to-r from-blue-600 to-blue-400 group-hover:w-full transition-all duration-500 ease-out"></div>
          </div>

          {/* Enhanced hover effect without dots */}
          <div className="absolute -inset-4 scale-0 group-hover:scale-100 opacity-0 group-hover:opacity-100 transition-all duration-700 pointer-events-none">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-blue-400/10 rounded-full blur-md"></div>
          </div>
        </Link>

        <Link
          href="/"
          onClick={navigateToHome}
          className="group text-xs md:text-sm text-white/90 hover:text-white transition-all duration-300 px-3 py-1.5 rounded-full border border-white/20 hover:border-white/30 flex items-center shadow-sm hover:shadow-md relative overflow-hidden bg-white/10 backdrop-blur-sm hover:bg-white/15"
        >
          <span className="relative z-10 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 md:h-4 md:w-4 mr-1 md:mr-1.5 group-hover:translate-x-[-2px] transition-transform duration-300" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
            <span>Home</span>
          </span>
          <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-white/5 z-0"></div>
        </Link>
      </header>

      {/* Auth Forms */}
      <div className="flex-1 flex items-center justify-center z-20 p-2 md:p-4 overflow-auto auth-form-container">
        <div className="w-full max-w-md px-4 md:px-0 transform-gpu my-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="backdrop-blur-xl bg-gradient-to-b from-gray-900/90 to-black/80 rounded-2xl border border-white/15 shadow-[0_10px_40px_rgba(0,0,0,0.6)] overflow-hidden transition-all duration-500"
          >
            {/* Animated top accent - elegant gradient */}
            <div className="h-1.5 bg-gradient-to-r from-blue-600/80 via-vista-blue to-blue-600/80 w-full shadow-[0_1px_8px_rgba(59,130,246,0.4)]"></div>

            <div className="p-3 md:p-8">
              <AuthForms redirectTo={redirectTo} />
            </div>
          </motion.div>
        </div>
      </div>

      {/* Footer */}
      <footer className="py-2 md:py-3 px-4 md:px-8 text-center text-xs md:text-sm text-white/60 relative z-20 backdrop-blur-sm bg-black/30 auth-footer">
        <p>© 2025 StreamVista. All rights reserved.</p>
      </footer>
    </div>
  );
}

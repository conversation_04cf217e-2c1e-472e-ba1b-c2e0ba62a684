'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Download, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { formatDate } from '@/lib/utils';

interface Invoice {
  id: string;
  amount: number;
  currency: string;
  date: string;
  description: string;
  status: 'paid' | 'pending' | 'failed';
}

interface BillingHistorySectionProps {
  userId?: string;
  subscriptionPlan: string;
  onUpgradeClick: () => void;
  isAdmin?: boolean;
}

export function BillingHistorySection({
  userId,
  subscriptionPlan,
  onUpgradeClick,
  isAdmin = false
}: BillingHistorySectionProps) {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch billing history
  useEffect(() => {
    const fetchBillingHistory = async () => {
      if (!userId || subscriptionPlan === 'free') return;

      setIsLoading(true);
      try {
        // In a real app, this would be an API call to fetch invoices
        // For now, we'll simulate it with a timeout and mock data
        await new Promise(resolve => setTimeout(resolve, 500));

        // Mock data - in a real app, this would come from the API
        setInvoices([
          {
            id: 'in_1234567890',
            amount: 1499,
            currency: 'usd',
            date: '2023-06-01',
            description: 'Premium Subscription',
            status: 'paid'
          },
          {
            id: 'in_0987654321',
            amount: 1499,
            currency: 'usd',
            date: '2023-05-01',
            description: 'Premium Subscription',
            status: 'paid'
          }
        ]);
      } catch (error) {
        console.error('Error fetching billing history:', error);
        toast.error('Failed to load billing history');
      } finally {
        setIsLoading(false);
      }
    };

    fetchBillingHistory();
  }, [userId, subscriptionPlan]);

  // Handle downloading an invoice
  const handleDownloadInvoice = (id: string) => {
    // In a real app, this would download the invoice PDF
    toast.info(`This would download invoice ${id} in a real app`);
  };

  // Handle viewing all invoices
  const handleViewAllInvoices = () => {
    // In a real app, this would navigate to a full billing history page
    toast.info('This would show all invoices in a real app');
  };

  // Format amount for display
  const formatAmount = (amount: number, currency: string) => {
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
      minimumFractionDigits: 2
    });

    return formatter.format(amount / 100);
  };

  if (subscriptionPlan === 'free') {
    return (
      <div className="py-8 text-center">
        {isAdmin ? (
          <>
            <div className="flex items-center justify-center gap-2 mb-4">
              <Badge className="bg-purple-500 text-white border-none px-2 py-0.5">
                Admin Account
              </Badge>
              <p className="text-vista-light/70">Admin accounts have access to all features</p>
            </div>
            <Button
              className="bg-blue-500 hover:bg-blue-600 text-white"
              onClick={() => toast.info('Admin can access all billing records')}
            >
              View All User Invoices
            </Button>
          </>
        ) : (
          <>
            <p className="text-vista-light/70 mb-4">No billing history available on the free plan</p>
            <Button
              className="bg-blue-500 hover:bg-blue-600 text-white"
              onClick={onUpgradeClick}
            >
              Upgrade to Premium
            </Button>
          </>
        )}
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-vista-blue" />
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {isAdmin && (
        <div className="bg-purple-500/10 border border-purple-500/20 rounded-lg p-4 mb-4">
          <div className="flex items-center gap-2 mb-2">
            <Badge className="bg-purple-500 text-white border-none px-2 py-0.5">
              Admin Access
            </Badge>
            <h4 className="font-medium text-purple-400">Admin Billing Panel</h4>
          </div>
          <p className="text-sm text-vista-light/70 mb-3">
            As an admin, you have access to all billing records and subscription management features.
          </p>
          <div className="flex gap-2">
            <Button
              variant="outline"
              className="bg-black/20 hover:bg-black/40 border-white/5 hover:border-white/10 text-purple-400"
              onClick={() => toast.info('This would open the admin billing dashboard')}
            >
              Admin Dashboard
            </Button>
            <Button
              variant="outline"
              className="bg-black/20 hover:bg-black/40 border-white/5 hover:border-white/10 text-vista-light"
              onClick={() => toast.info('This would show all user invoices')}
            >
              All User Invoices
            </Button>
          </div>
        </div>
      )}

      {invoices.length > 0 ? (
        <>
          {invoices.map((invoice) => (
            <div key={invoice.id} className="bg-black/20 rounded-lg border border-white/5 p-4 flex justify-between items-center">
              <div>
                <p className="text-vista-light">{invoice.description}</p>
                <p className="text-xs text-vista-light/70">{formatDate(new Date(invoice.date))}</p>
              </div>
              <div className="text-right">
                <p className="text-vista-light">{formatAmount(invoice.amount, invoice.currency)}</p>
                <Button
                  variant="link"
                  size="sm"
                  className="h-8 px-2 text-blue-400"
                  onClick={() => handleDownloadInvoice(invoice.id)}
                >
                  <Download className="w-3 h-3 mr-1" /> Invoice
                </Button>
              </div>
            </div>
          ))}

          <Button
            variant="outline"
            className="bg-black/20 hover:bg-black/40 border-white/5 hover:border-white/10 text-vista-light w-full mt-4"
            onClick={handleViewAllInvoices}
          >
            {isAdmin ? 'View All User Invoices' : 'View All Invoices'}
          </Button>
        </>
      ) : (
        <div className="py-6 text-center">
          <p className="text-vista-light/70">No billing history available</p>
          {isAdmin && (
            <Button
              className="bg-blue-500 hover:bg-blue-600 text-white mt-4"
              onClick={() => toast.info('This would open the admin billing dashboard')}
            >
              Go to Admin Dashboard
            </Button>
          )}
        </div>
      )}
    </div>
  );
}

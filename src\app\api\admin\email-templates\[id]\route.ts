import { NextRequest, NextResponse } from 'next/server';

/**
 * GET /api/admin/email-templates/[id]
 * Get a specific email template
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the user ID from the cookie directly
    const userId = request.cookies.get('userId')?.value;
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const User = mongoose.default.models.User ||
                mongoose.default.model('User', new mongoose.default.Schema({
                  role: String
                }));

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Get template ID from params
    const { id } = params;
    if (!id) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    // Define the EmailTemplate schema directly
    const EmailTemplateSchema = new mongoose.default.Schema({
      name: {
        type: String,
        required: true,
        unique: true,
        trim: true
      },
      subject: {
        type: String,
        required: true,
        trim: true
      },
      body: {
        type: String,
        required: true
      },
      description: {
        type: String,
        required: true
      },
      variables: [{
        type: String,
        trim: true
      }],
      isDefault: {
        type: Boolean,
        default: false
      },
      createdBy: {
        type: mongoose.default.Schema.Types.ObjectId,
        ref: 'User',
        required: true
      },
      updatedBy: {
        type: mongoose.default.Schema.Types.ObjectId,
        ref: 'User'
      },
      active: {
        type: Boolean,
        default: true
      }
    }, {
      timestamps: true
    });

    // Get the EmailTemplate model
    const EmailTemplate = mongoose.default.models.EmailTemplate ||
                         mongoose.default.model('EmailTemplate', EmailTemplateSchema);

    // Find the template
    const template = await EmailTemplate.findById(id);
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ template });
  } catch (error) {
    console.error(`Error fetching email template ${params.id}:`, error);
    return NextResponse.json(
      { error: 'Failed to fetch email template', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/email-templates/[id]
 * Update a specific email template
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the user ID from the cookie directly
    const userId = request.cookies.get('userId')?.value;
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const User = mongoose.default.models.User ||
                mongoose.default.model('User', new mongoose.default.Schema({
                  role: String
                }));

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Get template ID from params
    const { id } = params;
    if (!id) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    // Define the EmailTemplate schema directly
    const EmailTemplateSchema = new mongoose.default.Schema({
      name: {
        type: String,
        required: true,
        unique: true,
        trim: true
      },
      subject: {
        type: String,
        required: true,
        trim: true
      },
      body: {
        type: String,
        required: true
      },
      description: {
        type: String,
        required: true
      },
      variables: [{
        type: String,
        trim: true
      }],
      isDefault: {
        type: Boolean,
        default: false
      },
      createdBy: {
        type: mongoose.default.Schema.Types.ObjectId,
        ref: 'User',
        required: true
      },
      updatedBy: {
        type: mongoose.default.Schema.Types.ObjectId,
        ref: 'User'
      },
      active: {
        type: Boolean,
        default: true
      }
    }, {
      timestamps: true
    });

    // Get the EmailTemplate model
    const EmailTemplate = mongoose.default.models.EmailTemplate ||
                         mongoose.default.model('EmailTemplate', EmailTemplateSchema);

    // Get data from request
    const data = await request.json();
    const { name, subject, body, description, variables, active } = data;

    // Validate required fields
    if (!name || !subject || !body || !description) {
      return NextResponse.json(
        { error: 'Name, subject, body, and description are required' },
        { status: 400 }
      );
    }

    // Check if template exists
    const template = await EmailTemplate.findById(id);
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Check if default template is being deactivated
    if (template.isDefault && active === false) {
      return NextResponse.json(
        { error: 'Default templates cannot be deactivated' },
        { status: 400 }
      );
    }

    // Update the template
    const updatedTemplate = await EmailTemplate.findByIdAndUpdate(
      id,
      {
        name,
        subject,
        body,
        description,
        variables: variables || [],
        active: active !== undefined ? active : template.active,
        updatedBy: new mongoose.default.Types.ObjectId(userId)
      },
      { new: true }
    );

    return NextResponse.json({
      success: true,
      template: updatedTemplate
    });
  } catch (error) {
    console.error(`Error updating email template ${params.id}:`, error);
    return NextResponse.json(
      { error: 'Failed to update email template', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/email-templates/[id]
 * Delete a specific email template
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the user ID from the cookie directly
    const userId = request.cookies.get('userId')?.value;
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const User = mongoose.default.models.User ||
                mongoose.default.model('User', new mongoose.default.Schema({
                  role: String
                }));

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Get template ID from params
    const { id } = params;
    if (!id) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    // Define the EmailTemplate schema directly
    const EmailTemplateSchema = new mongoose.default.Schema({
      name: {
        type: String,
        required: true,
        unique: true,
        trim: true
      },
      subject: {
        type: String,
        required: true,
        trim: true
      },
      body: {
        type: String,
        required: true
      },
      description: {
        type: String,
        required: true
      },
      variables: [{
        type: String,
        trim: true
      }],
      isDefault: {
        type: Boolean,
        default: false
      },
      createdBy: {
        type: mongoose.default.Schema.Types.ObjectId,
        ref: 'User',
        required: true
      },
      updatedBy: {
        type: mongoose.default.Schema.Types.ObjectId,
        ref: 'User'
      },
      active: {
        type: Boolean,
        default: true
      }
    }, {
      timestamps: true
    });

    // Get the EmailTemplate model
    const EmailTemplate = mongoose.default.models.EmailTemplate ||
                         mongoose.default.model('EmailTemplate', EmailTemplateSchema);

    // Find the template
    const template = await EmailTemplate.findById(id);
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Check if it's a default template
    if (template.isDefault) {
      return NextResponse.json(
        { error: 'Default templates cannot be deleted' },
        { status: 400 }
      );
    }

    // Delete the template
    await EmailTemplate.findByIdAndDelete(id);

    return NextResponse.json({
      success: true,
      message: 'Template deleted successfully'
    });
  } catch (error) {
    console.error(`Error deleting email template ${params.id}:`, error);
    return NextResponse.json(
      { error: 'Failed to delete email template', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

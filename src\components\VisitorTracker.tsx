'use client';

import { useVisitorTracking } from '@/hooks/useVisitorTracking';

/**
 * VisitorTracker component
 * 
 * This component is responsible for tracking anonymous visitors.
 * It should be included in the RootLayout to ensure it's loaded on every page.
 * It doesn't render anything visible - it just initializes the tracking.
 */
export function VisitorTracker() {
  // Initialize visitor tracking
  useVisitorTracking();
  
  // This component doesn't render anything visible
  return null;
}

export default VisitorTracker;

import { NextRequest, NextResponse } from 'next/server';
import { cleanupExpiredNotifications } from '@/lib/cleanup-utils';
import { ensureMongooseConnection } from '@/lib/mongodb';

/**
 * POST /api/admin/cleanup/notifications
 * Clean up expired notifications
 * 
 * This endpoint is only accessible to admin users.
 * It removes notifications that have passed their expiration date.
 */
export async function POST(request: NextRequest) {
  try {
    // Try to get the user ID from the cookie directly
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    if (!userId) {
      console.error('Admin cleanup notifications API: No user ID found in request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const User = mongoose.default.models.User ||
                mongoose.default.model('User', new mongoose.default.Schema({
                  role: String
                }));

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Clean up expired notifications
    const deletedCount = await cleanupExpiredNotifications();

    return NextResponse.json({
      success: true,
      deletedCount,
      message: `Removed ${deletedCount} expired notifications`
    });
  } catch (error) {
    console.error('Error cleaning up expired notifications:', error);
    return NextResponse.json(
      { error: 'Failed to clean up expired notifications', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

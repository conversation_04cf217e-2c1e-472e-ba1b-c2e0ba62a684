import { NextRequest, NextResponse } from 'next/server';
import { getMovieDetails, getTVShowDetails } from '@/lib/tmdb-api';

/**
 * API route for fetching related content (recommendations or similar) for a movie or TV show
 */
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const id = searchParams.get('id');
  const type = searchParams.get('type') as 'movie' | 'show';
  
  if (!id) {
    return NextResponse.json({ error: 'Content ID is required' }, { status: 400 });
  }
  
  if (!type || (type !== 'movie' && type !== 'show')) {
    return NextResponse.json({ error: 'Valid content type (movie or show) is required' }, { status: 400 });
  }
  
  try {
    console.log(`API: Fetching related content for ${type} with ID ${id}`);
    
    // Fetch content details based on type
    const contentDetails = type === 'movie' 
      ? await getMovieDetails(id)
      : await getTVShowDetails(id);
    
    // Extract recommendations or similar content
    const relatedContent = contentDetails.recommendations?.results || contentDetails.similar?.results || [];
    
    // Map to a consistent format
    const mappedContent = relatedContent.map((item: any) => ({
      id: item.id.toString(),
      title: item.title || item.name || 'Unknown Title',
      type: item.media_type || (item.title ? 'movie' : 'tv'),
      posterUrl: item.poster_path ? `https://image.tmdb.org/t/p/w500${item.poster_path}` : null,
      backdropUrl: item.backdrop_path ? `https://image.tmdb.org/t/p/w1280${item.backdrop_path}` : null,
      year: item.release_date
        ? new Date(item.release_date).getFullYear().toString()
        : item.first_air_date
          ? new Date(item.first_air_date).getFullYear().toString()
          : null,
      overview: item.overview,
      voteAverage: item.vote_average,
      voteCount: item.vote_count
    }));
    
    console.log(`API: Found ${mappedContent.length} related content items`);
    
    return NextResponse.json(mappedContent);
  } catch (error) {
    console.error(`Error fetching related content:`, error);
    return NextResponse.json({ error: 'Failed to fetch related content' }, { status: 500 });
  }
}

"use client"

import React, { useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Check, ChevronRight, Loader2, RefreshCw, Upload } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { ContentUploadProvider, useContentUpload } from './ContentUploadProvider'
import { FileUploadStep } from './FileUploadStep'
import { MetadataStep } from './MetadataStep'
import { ThumbnailsStep } from './ThumbnailsStep'
import { SettingsStep } from './SettingsStep'

// Steps in the upload process
const STEPS = [
  {
    id: 'upload',
    title: 'Upload',
    component: FileUploadStep
  },
  {
    id: 'metadata',
    title: 'Details',
    component: MetadataStep
  },
  {
    id: 'thumbnails',
    title: 'Thumbnail',
    component: ThumbnailsStep
  },
  {
    id: 'settings',
    title: 'Settings',
    component: SettingsStep
  }
]

// Inner component with access to the upload context
function ContentUploadInner() {
  const {
    uploadData,
    nextStep,
    prevStep,
    goToStep,
    startUpload,
    resetUpload
  } = useContentUpload()
  const { currentStep, uploadStatus, fileUpload, errorMessage } = uploadData

  // Check if we can proceed to the next step
  const canProceed = () => {
    switch (currentStep) {
      case 0: // File upload step
        return !!fileUpload.file
      case 1: // Metadata step
        const { metadata } = uploadData
        return !!metadata.title && metadata.genres.length > 0
      case 2: // Thumbnails step
        return !!uploadData.thumbnails.mainThumbnail
      case 3: // Settings step
        return true
      default:
        return false
    }
  }

  // Handle submission
  const handleSubmit = async () => {
    if (currentStep === STEPS.length - 1) {
      await startUpload()
    } else {
      nextStep()
    }
  }

  return (
    <div className="bg-vista-dark border border-vista-light/10 rounded-xl overflow-hidden">
      {/* Header with progress steps */}
      <div className="border-b border-vista-light/10 px-6 py-4">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-semibold text-vista-light">Upload Content</h1>

          {/* Only show upload status when uploading */}
          {uploadStatus !== 'idle' && uploadStatus !== 'error' && (
            <div className="flex items-center gap-2 text-sm">
              {uploadStatus === 'uploading' && (
                <>
                  <Loader2 className="animate-spin h-4 w-4 text-vista-blue" />
                  <span className="text-vista-light">Uploading... {fileUpload.progress}%</span>
                </>
              )}
              {uploadStatus === 'processing' && (
                <>
                  <RefreshCw className="animate-spin h-4 w-4 text-vista-light" />
                  <span className="text-vista-light">Processing...</span>
                </>
              )}
              {uploadStatus === 'success' && (
                <>
                  <Check className="h-4 w-4 text-green-500" />
                  <span className="text-vista-light">Upload complete</span>
                </>
              )}
            </div>
          )}
        </div>

        {/* Progress steps */}
        <div className="mt-6 flex items-center">
          {STEPS.map((step, index) => (
            <React.Fragment key={step.id}>
              <div
                className={`flex items-center ${index <= currentStep ? 'cursor-pointer' : 'cursor-not-allowed'}`}
                onClick={() => index <= currentStep && goToStep(index)}
              >
                <div
                  className={`
                    flex items-center justify-center h-8 w-8 rounded-full
                    ${index < currentStep ? 'bg-vista-blue text-white' :
                      index === currentStep ? 'bg-vista-blue/90 text-white' :
                      'bg-vista-dark-lighter text-vista-light/60'}
                  `}
                >
                  {index < currentStep ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <span>{index + 1}</span>
                  )}
                </div>
                <span
                  className={`ml-2 text-sm ${
                    index <= currentStep ? 'text-vista-light' : 'text-vista-light/60'
                  }`}
                >
                  {step.title}
                </span>
              </div>
              {index < STEPS.length - 1 && (
                <div
                  className={`flex-1 h-0.5 mx-2 ${
                    index < currentStep ? 'bg-vista-blue' : 'bg-vista-dark-lighter'
                  }`}
                />
              )}
            </React.Fragment>
          ))}
        </div>
      </div>

      {/* Content area for current step */}
      <div className="p-6">
        {/* Show success view if upload is complete */}
        {uploadStatus === 'success' ? (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center py-12"
          >
            <div className="flex justify-center mb-6">
              <div className="bg-green-500/20 p-4 rounded-full">
                <Check className="h-12 w-12 text-green-500" />
              </div>
            </div>
            <h2 className="text-2xl font-semibold text-vista-light mb-2">Upload Complete!</h2>
            <p className="text-vista-light/70 mb-8 max-w-md mx-auto">
              Your content has been successfully uploaded and is now being processed.
              You will be notified when it's ready to be viewed.
            </p>
            <div className="flex justify-center gap-4">
              <Button onClick={resetUpload} className="gap-2">
                <Upload className="h-4 w-4" />
                Upload Another
              </Button>
              <Button variant="outline">
                View Content
              </Button>
            </div>
          </motion.div>
        ) : (
          <>
            {/* Show error message if there's an error */}
            {uploadStatus === 'error' && (
              <div className="mb-6 p-4 bg-red-500/10 border border-red-500/20 rounded-md text-red-500">
                <div className="font-medium mb-1">Upload Failed</div>
                <div className="text-sm">{errorMessage || 'An unknown error occurred during upload.'}</div>
              </div>
            )}

            {/* Current step component */}
            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
              >
                {React.createElement(STEPS[currentStep].component)}
              </motion.div>
            </AnimatePresence>

            {/* Navigation buttons */}
            <div className="flex justify-between mt-8 pt-6 border-t border-vista-light/10">
              <Button
                variant="outline"
                onClick={prevStep}
                disabled={currentStep === 0 || uploadStatus === 'uploading' || uploadStatus === 'processing'}
              >
                Back
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={!canProceed() || uploadStatus === 'uploading' || uploadStatus === 'processing'}
                className="gap-2"
              >
                {currentStep === STEPS.length - 1 ? (
                  <>
                    {uploadStatus === 'uploading' || uploadStatus === 'processing' ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Upload className="h-4 w-4" />
                    )}
                    {uploadStatus === 'uploading' ? 'Uploading...' :
                      uploadStatus === 'processing' ? 'Processing...' : 'Publish Content'}
                  </>
                ) : (
                  <>
                    Next <ChevronRight className="h-4 w-4" />
                  </>
                )}
              </Button>
            </div>
          </>
        )}

        {/* Upload progress bar */}
        {uploadStatus === 'uploading' && (
          <div className="mt-6">
            <Progress value={fileUpload.progress} max={100} className="h-1" />
          </div>
        )}
      </div>
    </div>
  )
}

// Wrapper component with provider
export function ContentUpload() {
  return (
    <ContentUploadProvider>
      <ContentUploadInner />
    </ContentUploadProvider>
  )
}

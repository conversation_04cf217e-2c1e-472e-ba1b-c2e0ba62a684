Comprehensive Guide to Using Vidsrc.xyz in a Streaming Website
Introduction
Vidsrc.xyz is a video streaming API that provides access to streaming links for movies and TV shows, designed for integration into websites through embed links, API endpoints, or WordPress plugins. This guide details every aspect of Vidsrc.xyz, including its functionality, available endpoints, integration methods, and considerations for use in a streaming website. The service is noted for its auto-updating links, high-quality streams (80% in 1080p), responsive players, and subtitle support. However, its association with piracy communities raises legal concerns that website owners should carefully evaluate.
Overview of Vidsrc.xyz

Purpose: Provides streaming links for movies and TV episodes, integrable via embed URLs, API, or WordPress plugins.
Content: Hosts a large library (approximately 84,991 movies, 18,482 series, and 447,337 episodes, based on available data).
Domains: Official domains include vidsrc.in, vidsrc.pm, vidsrc.xyz, and vidsrc.net. Other domains may be fake and pose security risks.
Features:
Auto-Updated Links: Streaming links are automatically updated with new or higher-quality sources.
Responsive Player: Works on desktops, mobiles, and tablets.
High Quality: Approximately 80% of streams are in 1080p.
Multiple Servers: Users can select from various streaming servers for optimal performance.
Subtitles: Sourced from multiple websites or customizable via API parameters.
DMCA Protection: Claims links are secure and not subject to DMCA takedowns.
Support: 24/7 support available, with ad reduction for sites exceeding 10,000 daily users.
WordPress Integration: Plugins available for Dooplay and Psyplay themes.



API Endpoints and Parameters
The Vidsrc.xyz API provides several endpoints for embedding videos and retrieving content lists. Below is a detailed table of all known endpoints and their parameters, based on available documentation from vidsrc.me/api/.



Endpoint
Description
Required Parameters
Optional Parameters
Example URLs



https://vidsrc.xyz/embed/movie
Embeds a movie player
imdb or tmdb
sub_url, ds_lang, autoplay
https://vidsrc.xyz/embed/movie/tt5433140, https://vidsrc.xyz/embed/movie?tmdb=385687&ds_lang=de


https://vidsrc.xyz/embed/tv
Embeds a TV show player
imdb or tmdb
ds_lang
https://vidsrc.xyz/embed/tv/tt0944947, https://vidsrc.xyz/embed/tv?tmdb=1399&ds_lang=de


https://vidsrc.xyz/embed/tv
Embeds a specific TV episode
imdb or tmdb, season, episode
sub_url, ds_lang, autoplay, autonext
https://vidsrc.xyz/embed/tv/tt0944947/1-1, https://vidsrc.xyz/embed/tv?tmdb=1399&season=1&episode=1&autoplay=1


https://vidsrc.xyz/movies/latest/page-PAGE_NUMBER.json
Lists latest movies in JSON format
PAGE_NUMBER
None
https://vidsrc.xyz/movies/latest/page-1.json, https://vidsrc.xyz/movies/latest/page-15.json


https://vidsrc.xyz/tvshows/latest/page-PAGE_NUMBER.json
Lists latest TV shows in JSON format
PAGE_NUMBER
None
https://vidsrc.xyz/tvshows/latest/page-1.json, https://vidsrc.xyz/tvshows/latest/page-15.json


https://vidsrc.xyz/episodes/latest/page-PAGE_NUMBER.json
Lists latest episodes in JSON format
PAGE_NUMBER
None
https://vidsrc.xyz/episodes/latest/page-1.json, https://vidsrc.xyz/episodes/latest/page-25.json


Parameter Details

imdb or tmdb: The unique identifier for a movie or TV show from IMDb (e.g., tt5433140) or The Movie Database (TMDB) (e.g., 385687). IMDb IDs must include the tt prefix.
season: The season number for TV episodes (e.g., 1).
episode: The episode number within a season (e.g., 1).
sub_url: A URL-encoded .srt or .vtt subtitle file, requiring CORS headers (Access-Control-Allow-Origin: *).
ds_lang: Default subtitle language, using ISO 639-1 language codes (e.g., en for English, de for German).
autoplay: Set to 1 to enable autoplay, 0 to disable (default is enabled).
autonext: For TV episodes, enables automatic playback of the next episode.
PAGE_NUMBER: Specifies the page number for paginated JSON responses (e.g., 1, 15).

Response Formats

Embed Endpoints: Return HTML content for embedding, typically an iframe-compatible video player.
JSON Endpoints: Return JSON data, likely containing a list of objects with fields such as id, title, release_date, and other metadata. The exact structure is not fully documented, but it’s reasonable to assume it includes essential content details for building a catalog.

Integration into a Streaming Website
To integrate Vidsrc.xyz into your streaming website, follow these steps:
1. Obtain Content IDs

Requirement: You need IMDb or TMDB IDs for the movies or TV shows you want to stream.
Methods:
Maintain a database mapping user-friendly titles to their respective IDs.
Integrate with IMDb or TMDB APIs to search for and retrieve IDs dynamically.
Use Vidsrc’s website (vidsrc.xyz) to manually find IDs, as suggested by community discussions on Reddit.



2. Display Content Catalog

Using JSON Endpoints:
Fetch lists of latest movies, TV shows, or episodes using endpoints like https://vidsrc.xyz/movies/latest/page-1.json.
Parse the JSON response to display titles, posters, and descriptions on your website.
Example JSON structure (assumed):[
  {
    "id": "tt1234567",
    "title": "Movie Title",
    "release_date": "2023-01-01",
    "poster_url": "https://example.com/poster.jpg"
  },
  ...
]




Implementation: Use JavaScript or a server-side language (e.g., Python, PHP) to fetch and render the data in your website’s frontend.

3. Embed Video Players

Using Embed URLs:
Construct embed URLs with the appropriate IMDb or TMDB ID.
Embed the player using an iframe in your HTML:<iframe src="https://vidsrc.xyz/embed/movie/tt5433140" width="640" height="360" frameborder="0" allowfullscreen></iframe>


For TV episodes, include season and episode parameters:<iframe src="https://vidsrc.xyz/embed/tv/tt0944947/1-1" width="640" height="360" frameborder="0" allowfullscreen></iframe>




Customization:
Change the player’s color by appending a hexadecimal color code (e.g., https://vidsrc.xyz/embed/movie/tt5433140/color-15006D).
Enable/disable autoplay or autonext for TV episodes using parameters.



4. Handle Subtitles

Default Subtitles: Vidsrc sources subtitles from various websites, available in multiple languages.
Custom Subtitles:
Provide a .srt or .vtt file via the sub_url parameter, ensuring the file is hosted with CORS enabled.
Example: https://vidsrc.xyz/embed/movie/tt5433140?sub_url=https://example.com/subtitles.srt&ds_lang=en.



5. Manage Playback

Autoplay: Set autoplay=1 for automatic playback or autoplay=0 for manual control.
Autonext: For TV shows, use autonext=1 to automatically play the next episode.
Server Selection: The player includes a list of streaming servers, allowing users to switch if one fails.

6. Error Handling

Content Not Found: Implement fallback messages if an ID is invalid or content is unavailable.
Broken Links: Use the player’s report button to notify Vidsrc of issues, which their team addresses promptly.
Downtime: Community reports indicate occasional downtime; monitor service status and consider backup sources.

7. WordPress Integration

Plugins: Vidsrc provides plugins for Dooplay and Psyplay WordPress themes, automating integration without manual configuration.
Custom Themes: Contact Vidsrc support for plugins compatible with other themes.
Usage: Install the plugin, configure it with your content IDs, and it will handle embedding and playback.

Example Implementation
Below is a sample HTML page demonstrating how to embed a Vidsrc.xyz movie player:




  
  
  Vidsrc.xyz Movie Player


  Watch Movie
  


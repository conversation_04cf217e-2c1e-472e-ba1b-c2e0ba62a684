import { NextRequest, NextResponse } from 'next/server';
import type { Types } from 'mongoose';

/**
 * GET /api/admin/users/[id]/activity
 * Get activity logs for a specific user
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params before using its properties
    const { id } = await params;
    
    // Get the user ID from the cookie directly
    const userId = request.cookies.get('userId')?.value;
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const UserSchema = new mongoose.default.Schema({
      name: String,
      email: String,
      profileImage: String,
      role: String,
      status: String,
      emailVerified: Date,
      lastLogin: Date,
      subscription: mongoose.default.Schema.Types.Mixed,
      subscriptionStatus: String
    }, {
      timestamps: true
    });

    const User = mongoose.default.models.User ||
                mongoose.default.model('User', UserSchema);

    // Declare specific types for user role object
    interface UserWithRole {
      role?: string;
      [key: string]: unknown;
    }

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as UserWithRole).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the UserActivity schema directly
    const UserActivitySchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      action: String,
      details: String,
      ipAddress: String,
      userAgent: String,
      timestamp: { type: Date, default: Date.now },
      metadata: mongoose.default.Schema.Types.Mixed
    }, {
      timestamps: true
    });

    // Get the UserActivity model
    const UserActivity = mongoose.default.models.UserActivity ||
                        mongoose.default.model('UserActivity', UserActivitySchema);

    // Validate user ID
    if (!mongoose.default.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
    }

    // Check if target user exists
    const targetUser = await User.findById(id);
    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const page = parseInt(searchParams.get('page') || '1');
    const skip = (page - 1) * limit;
    const type = searchParams.get('type') || undefined;

    // Get activity logs for the user directly
    const query = { 
      userId: new mongoose.default.Types.ObjectId(id),
      ...(type ? { type } : {})
    };

    // Get total count for pagination
    const total = await UserActivity.countDocuments(query);

    // Get activity logs with pagination
    const logs = await UserActivity.find(query)
      .sort({ timestamp: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Format logs for response - use type assertion to handle MongoDB document
    const formattedLogs = logs.map((log) => ({
      id: String(log._id),
      userId: String(log.userId),
      type: String(log.type || ''),
      action: String(log.action || ''),
      details: String(log.details || ''),
      timestamp: log.timestamp,
      ipAddress: log.ipAddress,
      userAgent: log.userAgent,
      metadata: log.metadata || {}
    }));

    // Return activity logs with pagination info
    return NextResponse.json({
      logs: formattedLogs,
      pagination: {
        total,
        limit,
        page,
        pages: Math.ceil(total / limit)
      },
      user: {
        id: targetUser._id.toString(),
        name: targetUser.name,
        email: targetUser.email
      }
    });
  } catch (error) {
    console.error('Error fetching user activity logs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user activity logs', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';

/**
 * POST /api/tracking/pageview
 * Track a page view for an anonymous visitor
 */
export async function POST(request: NextRequest) {
  try {
    // Get request data
    const data = await request.json();
    const { path } = data;

    // Get visitor ID from cookie directly from the request
    const visitorId = request.cookies.get('visitorId')?.value;

    // If no visitor ID, we can't track the page view
    if (!visitorId) {
      return NextResponse.json({
        success: false,
        error: 'No visitor ID found'
      }, { status: 400 });
    }

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Import the AnonymousVisitor model
    const AnonymousVisitor = (await import('@/models/AnonymousVisitor')).default;

    // Update visitor's page view count
    const result = await AnonymousVisitor.updateOne(
      { visitorId },
      {
        $inc: { pagesViewed: 1 },
        $set: { lastVisit: new Date() }
      }
    );

    if (result.matchedCount === 0) {
      return NextResponse.json({
        success: false,
        error: 'Visitor not found'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      visitorId
    });
  } catch (error) {
    console.error('Error tracking page view:', error);
    return NextResponse.json(
      { error: 'Failed to track page view', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

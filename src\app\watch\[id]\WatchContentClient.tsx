'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Loader2, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Navbar } from '@/components/Navbar';

interface WatchContentClientProps {
  contentId: string;
}

export default function WatchContentClient({ contentId }: WatchContentClientProps) {
  const router = useRouter();
  const [redirecting, setRedirecting] = useState(false);

  useEffect(() => {
    // Redirect to the dedicated watch party page
    setRedirecting(true);
    router.push(`/watch-party/${contentId}`);
  }, [contentId, router]);

  return (
    <div className="min-h-screen bg-vista-dark">
      <Navbar />
      <div className="flex items-center justify-center min-h-[calc(100vh-80px)]">
        <div className="text-center space-y-6">
          <div className="flex items-center justify-center">
            <Loader2 className="h-12 w-12 animate-spin text-vista-blue" />
          </div>
          <div className="space-y-2">
            <h2 className="text-2xl font-bold text-vista-light">Joining Watch Party</h2>
            <p className="text-vista-light/70">
              Redirecting you to the watch party experience...
            </p>
          </div>
          <div className="flex items-center justify-center space-x-2 text-vista-light/50">
            <Users className="h-4 w-4" />
            <span className="text-sm">Preparing synchronized viewing</span>
          </div>
        </div>
      </div>
    </div>
  );
} 
import { NextRequest, NextResponse } from 'next/server';

/**
 * GET /api/admin/activity/logs
 * Get activity logs with filtering options
 */
export async function GET(request: NextRequest) {
  try {
    // Try to get the admin user ID from multiple sources
    let adminUserId = request.cookies.get('userId')?.value;

    // If not in cookies, check query parameters
    if (!adminUserId) {
      const { searchParams } = new URL(request.url);
      adminUserId = searchParams.get('adminUserId');
    }

    if (!adminUserId) {
      console.error('Admin activity logs API: No admin user ID found in request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const User = mongoose.default.models.User ||
                mongoose.default.model('User', new mongoose.default.Schema({
                  role: String,
                  name: String,
                  email: String
                }));

    // Check if user is admin
    const adminUser = await User.findById(adminUserId).select('role').lean();
    if (!adminUser || (adminUser as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the UserActivity schema directly
    const UserActivitySchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      action: String,
      details: String,
      ipAddress: String,
      userAgent: String,
      timestamp: { type: Date, default: Date.now },
      metadata: mongoose.default.Schema.Types.Mixed
    }, {
      timestamps: true
    });

    // Get the UserActivity model
    const UserActivity = mongoose.default.models.UserActivity ||
                        mongoose.default.model('UserActivity', UserActivitySchema);

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const action = searchParams.get('action');
    // Use targetUserId to avoid confusion with adminUserId
    const targetUserId = searchParams.get('targetUserId') || searchParams.get('userId');
    const limit = parseInt(searchParams.get('limit') || '20', 10);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const skip = (page - 1) * limit;

    // Build query
    const query: any = {};

    if (type) {
      if (type === 'permission') {
        // Special case for permission logs - include all permission-related actions
        query.action = { $in: [
          'update_user_permissions',
          'reset_user_permissions',
          'update_default_permissions',
          'reset_default_permissions',
          'bulk_update_permissions'
        ]};
      } else {
        query.type = type;
      }
    }

    if (action) {
      query.action = action;
    }

    if (targetUserId && mongoose.default.Types.ObjectId.isValid(targetUserId)) {
      query.userId = new mongoose.default.Types.ObjectId(targetUserId);
    }

    // Get total count for pagination
    const total = await UserActivity.countDocuments(query);

    // Get logs with pagination
    const logs = await UserActivity.find(query)
      .sort({ timestamp: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Get user IDs from logs
    const userIds = [...new Set(logs.map((log: any) => log.userId))];

    // Get user details
    const users = await User.find({
      _id: { $in: userIds }
    }).select('_id name email').lean();

    // Create a map of user IDs to user details
    const userMap = new Map();
    users.forEach((user: any) => {
      userMap.set(user._id.toString(), {
        name: user.name,
        email: user.email
      });
    });

    // Map logs to response format
    const mappedLogs = logs.map((log: any) => {
      const userInfo = userMap.get(log.userId?.toString());

      return {
        id: log._id.toString(),
        userId: log.userId?.toString() || '',
        userName: userInfo?.name || 'Unknown',
        userEmail: userInfo?.email || '',
        type: log.type,
        action: log.action,
        details: log.details,
        timestamp: log.timestamp,
        metadata: log.metadata || {}
      };
    });

    // Return logs with pagination info
    return NextResponse.json({
      logs: mappedLogs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching activity logs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch activity logs', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

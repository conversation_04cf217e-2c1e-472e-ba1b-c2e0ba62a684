/**
 * Enhanced Error Handling Utility
 * 
 * This utility provides comprehensive error handling and logging
 * specifically designed for production debugging in serverless environments.
 */

import { NextResponse } from 'next/server';
import logger from './logger';

export interface ErrorContext {
  operation: string;
  userId?: string;
  userEmail?: string;
  adminEmail?: string;
  duration?: string;
  environment?: string;
  netlify?: boolean;
  timestamp?: string;
  requestId?: string;
  [key: string]: any;
}

export interface ErrorDetails {
  message: string;
  stack?: string;
  code?: string;
  statusCode?: number;
  isOperational?: boolean;
}

/**
 * Enhanced error class for better error tracking
 */
export class OperationalError extends Error {
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly code?: string;

  constructor(message: string, statusCode: number = 500, code?: string) {
    super(message);
    this.name = 'OperationalError';
    this.statusCode = statusCode;
    this.isOperational = true;
    this.code = code;

    // Capture stack trace
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Categorize error types for better handling
 */
export function categorizeError(error: Error): {
  type: string;
  statusCode: number;
  userMessage: string;
  shouldRetry: boolean;
} {
  const message = error.message.toLowerCase();

  // Timeout errors
  if (message.includes('timeout')) {
    return {
      type: 'timeout',
      statusCode: 408,
      userMessage: 'Operation timed out. Please try again.',
      shouldRetry: true
    };
  }

  // Database connection errors
  if (message.includes('connection') || message.includes('network') || message.includes('econnreset')) {
    return {
      type: 'connection',
      statusCode: 503,
      userMessage: 'Database connection error. Please try again.',
      shouldRetry: true
    };
  }

  // Authentication errors
  if (message.includes('unauthorized') || message.includes('authentication') || message.includes('permission')) {
    return {
      type: 'auth',
      statusCode: 401,
      userMessage: 'Authentication failed. Please log in again.',
      shouldRetry: false
    };
  }

  // Validation errors
  if (message.includes('validation') || message.includes('invalid') || message.includes('required')) {
    return {
      type: 'validation',
      statusCode: 400,
      userMessage: 'Invalid request data.',
      shouldRetry: false
    };
  }

  // Not found errors
  if (message.includes('not found') || message.includes('does not exist')) {
    return {
      type: 'not_found',
      statusCode: 404,
      userMessage: 'Resource not found.',
      shouldRetry: false
    };
  }

  // Default to internal server error
  return {
    type: 'internal',
    statusCode: 500,
    userMessage: 'An internal error occurred.',
    shouldRetry: false
  };
}

/**
 * Log error with enhanced context
 */
export function logError(error: Error, context: ErrorContext): void {
  const errorCategory = categorizeError(error);
  const errorDetails: ErrorDetails = {
    message: error.message,
    stack: error.stack,
    code: error instanceof OperationalError ? error.code : undefined,
    statusCode: error instanceof OperationalError ? error.statusCode : errorCategory.statusCode,
    isOperational: error instanceof OperationalError ? error.isOperational : false
  };

  const logData = {
    ...context,
    error: errorDetails,
    errorType: errorCategory.type,
    shouldRetry: errorCategory.shouldRetry,
    timestamp: context.timestamp || new Date().toISOString(),
    environment: context.environment || process.env.NODE_ENV,
    netlify: context.netlify !== undefined ? context.netlify : process.env.NETLIFY === 'true'
  };

  // Log based on error severity
  if (errorCategory.statusCode >= 500) {
    logger.error(`${context.operation} failed with server error`, logData);
  } else if (errorCategory.statusCode >= 400) {
    logger.warning(`${context.operation} failed with client error`, logData);
  } else {
    logger.info(`${context.operation} completed with warnings`, logData);
  }
}

/**
 * Create standardized error response
 */
export function createErrorResponse(
  error: Error,
  context: ErrorContext,
  includeDetails: boolean = false
): NextResponse {
  const errorCategory = categorizeError(error);
  
  // Log the error
  logError(error, context);

  // Prepare response data
  const responseData: any = {
    error: errorCategory.userMessage,
    type: errorCategory.type,
    operation: context.operation,
    timestamp: new Date().toISOString()
  };

  // Add duration if available
  if (context.duration) {
    responseData.duration = context.duration;
  }

  // Add request ID if available
  if (context.requestId) {
    responseData.requestId = context.requestId;
  }

  // Include detailed error information in development or if explicitly requested
  if (includeDetails || process.env.NODE_ENV === 'development') {
    responseData.details = {
      message: error.message,
      code: error instanceof OperationalError ? error.code : undefined,
      shouldRetry: errorCategory.shouldRetry
    };

    // Include stack trace only in development
    if (process.env.NODE_ENV === 'development') {
      responseData.details.stack = error.stack;
    }
  }

  return NextResponse.json(responseData, { status: errorCategory.statusCode });
}

/**
 * Wrap async operations with timeout and error handling
 */
export async function withTimeout<T>(
  operation: Promise<T>,
  timeoutMs: number,
  operationName: string
): Promise<T> {
  return Promise.race([
    operation,
    new Promise<never>((_, reject) =>
      setTimeout(() => reject(new OperationalError(`${operationName} timeout`, 408, 'TIMEOUT')), timeoutMs)
    )
  ]);
}

/**
 * Generate unique request ID for tracking
 */
export function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

import mongoose, { Schema, Document } from 'mongoose';

export interface ICategory extends Document {
  name: string;
  slug: string;
  description?: string;
  count: number;
  createdAt: Date;
  updatedAt: Date;
}

// Define the Category schema
const CategorySchema = new Schema<ICategory>({
  name: { type: String, required: true },
  slug: { type: String, required: true, unique: true },
  description: { type: String },
  count: { type: Number, default: 0 }
}, {
  timestamps: true
});

// Add only necessary indexes not already defined by field options
// The 'unique: true' on slug already creates an index, so we don't need to define it again
CategorySchema.index({ name: 'text', description: 'text' });

// Use mongoose.models.Category if it exists, otherwise create a new model
const Category = mongoose.models.Category as mongoose.Model<ICategory> || 
                mongoose.model<ICategory>('Category', CategorySchema);

export default Category;

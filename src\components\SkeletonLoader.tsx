'use client';

import { cn } from '@/lib/utils';

interface SkeletonProps {
  className?: string;
}

export function Skeleton({ className }: SkeletonProps) {
  return (
    <div
      className={cn(
        "animate-pulse rounded-md bg-vista-dark-lighter/50",
        className
      )}
    />
  );
}

interface ContentSkeletonProps {
  count?: number;
  layout?: 'grid' | 'row';
}

export function ContentCardSkeleton({ className }: SkeletonProps) {
  return (
    <div className={cn("relative overflow-hidden rounded-lg", className)}>
      <Skeleton className="h-[200px] w-full" />
      <div className="p-2 space-y-2 mt-2">
        <Skeleton className="h-4 w-2/3" />
        <Skeleton className="h-3 w-1/2" />
      </div>
    </div>
  );
}

export default function SkeletonLoader({ count = 5, layout = 'row' }: ContentSkeletonProps) {
  return (
    <div
      className={cn(
        "w-full",
        layout === 'grid'
          ? "grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4"
          : "flex space-x-4 overflow-x-auto pb-4 scrollbar-hide"
      )}
    >
      {Array(count).fill(0).map((_, index) => (
        <div
          key={index}
          className={layout === 'row' ? "flex-shrink-0 w-[180px] md:w-[200px]" : ""}
        >
          <ContentCardSkeleton />
        </div>
      ))}
    </div>
  );
}

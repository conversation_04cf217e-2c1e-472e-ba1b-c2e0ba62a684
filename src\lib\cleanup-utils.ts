import { ensureMongooseConnection } from '@/lib/mongodb';

/**
 * Clean up old anonymous visitor data
 *
 * This function removes anonymous visitor records that are older than the specified number of days.
 * It should be called periodically to prevent the database from growing too large.
 *
 * @param daysToKeep The number of days to keep visitor data (default: 90)
 * @returns A promise that resolves to the number of records removed
 */
export async function cleanupOldVisitorData(daysToKeep = 90): Promise<number> {
  try {
    // Connect to MongoDB
    await ensureMongooseConnection();

    // Import the AnonymousVisitor model
    const AnonymousVisitor = (await import('@/models/AnonymousVisitor')).default;

    // Calculate the cutoff date
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    // Remove old records
    const result = await AnonymousVisitor.deleteMany({
      lastVisit: { $lt: cutoffDate },
      convertedToUser: false // Don't delete records of visitors who converted to users
    });

    return result.deletedCount;
  } catch (error) {
    console.error('Error cleaning up old visitor data:', error);
    return 0;
  }
}

/**
 * Clean up expired notifications
 *
 * This function removes notifications that have passed their expiration date.
 * It should be called periodically to prevent the database from growing too large.
 *
 * @returns A promise that resolves to the number of notifications removed
 */
export async function cleanupExpiredNotifications(): Promise<number> {
  try {
    // Connect to MongoDB
    await ensureMongooseConnection();

    // Import the Notification model
    const Notification = (await import('@/models/Notification')).default;

    // Get current date
    const now = new Date();

    // Remove expired notifications
    const result = await Notification.deleteMany({
      expiresAt: { $lt: now, $exists: true, $ne: null }
    });

    return result.deletedCount;
  } catch (error) {
    console.error('Error cleaning up expired notifications:', error);
    return 0;
  }
}

'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Navbar } from '@/components/Navbar';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft, 
  Send, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  MessageSquare,
  Calendar,
  User,
  Star,
  RefreshCw
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';

interface TicketResponse {
  _id: string;
  message: string;
  responderId: {
    _id: string;
    name: string;
    email: string;
    profileImage?: string;
    role: string;
  };
  responderType: 'admin' | 'user';
  isInternal: boolean;
  createdAt: string;
  attachments?: string[];
}

interface Ticket {
  _id: string;
  ticketNumber: string;
  subject: string;
  description: string;
  category: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'open' | 'in_progress' | 'waiting_for_user' | 'resolved' | 'closed';
  createdAt: string;
  updatedAt: string;
  lastResponseAt?: string;
  userId: {
    _id: string;
    name: string;
    email: string;
    profileImage?: string;
  };
  assignedTo?: {
    _id: string;
    name: string;
    email: string;
  };
  satisfactionRating?: number;
  satisfactionFeedback?: string;
  tags: string[];
}

const statusConfig = {
  open: { label: 'Open', color: 'bg-blue-500/20 text-blue-400', icon: AlertCircle },
  in_progress: { label: 'In Progress', color: 'bg-yellow-500/20 text-yellow-400', icon: Clock },
  waiting_for_user: { label: 'Waiting for You', color: 'bg-orange-500/20 text-orange-400', icon: MessageSquare },
  resolved: { label: 'Resolved', color: 'bg-green-500/20 text-green-400', icon: CheckCircle },
  closed: { label: 'Closed', color: 'bg-gray-500/20 text-gray-400', icon: CheckCircle }
};

const priorityConfig = {
  low: { label: 'Low', color: 'bg-gray-500/20 text-gray-400' },
  medium: { label: 'Medium', color: 'bg-blue-500/20 text-blue-400' },
  high: { label: 'High', color: 'bg-orange-500/20 text-orange-400' },
  urgent: { label: 'Urgent', color: 'bg-red-500/20 text-red-400' }
};

export default function TicketDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { user, isLoading } = useAuth();
  const [ticket, setTicket] = useState<Ticket | null>(null);
  const [responses, setResponses] = useState<TicketResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [newResponse, setNewResponse] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [rating, setRating] = useState<number>(0);
  const [feedback, setFeedback] = useState('');

  // Check authentication
  useEffect(() => {
    if (!isLoading && !user) {
      toast.error('Please sign in to view your support tickets');
      router.push('/auth');
    }
  }, [user, isLoading, router]);

  useEffect(() => {
    if (params.id && user) {
      fetchTicketDetails();
    }
  }, [params.id, user]);

  const fetchTicketDetails = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/help/tickets/${params.id}?userId=${user.id}`, {
        credentials: 'include' // Include cookies in the request
      });
      if (!response.ok) {
        if (response.status === 404) {
          toast.error('Ticket not found');
          router.push('/help/tickets');
          return;
        }
        throw new Error('Failed to fetch ticket details');
      }

      const data = await response.json();
      setTicket(data.ticket);
      setResponses(data.responses);
      setRating(data.ticket.satisfactionRating || 0);
      setFeedback(data.ticket.satisfactionFeedback || '');
    } catch (error) {
      console.error('Error fetching ticket details:', error);
      toast.error('Failed to load ticket details');
    } finally {
      setLoading(false);
    }
  };

  const handleAddResponse = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newResponse.trim()) return;

    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/help/tickets/${params.id}/responses`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies in the request
        body: JSON.stringify({
          userId: user.id, // Include userId in request body
          message: newResponse.trim()
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to add response');
      }

      const data = await response.json();
      setResponses(prev => [...prev, data.response]);
      setNewResponse('');
      toast.success('Response added successfully');
      
      // Refresh ticket details to get updated status
      fetchTicketDetails();
    } catch (error) {
      console.error('Error adding response:', error);
      toast.error('Failed to add response');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRatingSubmit = async () => {
    if (!rating) return;

    try {
      const response = await fetch(`/api/help/tickets/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies in the request
        body: JSON.stringify({
          userId: user.id, // Include userId in request body
          satisfactionRating: rating,
          satisfactionFeedback: feedback.trim()
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to submit rating');
      }

      toast.success('Thank you for your feedback!');
      fetchTicketDetails();
    } catch (error) {
      console.error('Error submitting rating:', error);
      toast.error('Failed to submit rating');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-vista-dark text-vista-light">
        <Navbar />
        <div className="container mx-auto max-w-4xl py-16 px-4">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-vista-light/20 rounded w-1/3"></div>
            <div className="h-32 bg-vista-light/20 rounded"></div>
            <div className="h-64 bg-vista-light/20 rounded"></div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  // Don't render if not authenticated (will redirect)
  if (!user) {
    return null;
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-vista-dark text-vista-light">
        <Navbar />
        <div className="container mx-auto max-w-4xl py-16 px-4">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-vista-light/20 rounded w-1/3"></div>
            <div className="h-32 bg-vista-light/20 rounded"></div>
            <div className="h-64 bg-vista-light/20 rounded"></div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (!ticket) {
    return (
      <div className="min-h-screen bg-vista-dark text-vista-light">
        <Navbar />
        <div className="container mx-auto max-w-4xl py-16 px-4 text-center">
          <h1 className="text-2xl font-bold text-vista-light mb-4">Ticket Not Found</h1>
          <Link href="/help/tickets">
            <Button className="bg-vista-blue hover:bg-vista-blue/90">
              Back to Tickets
            </Button>
          </Link>
        </div>
        <Footer />
      </div>
    );
  }

  const StatusIcon = statusConfig[ticket.status].icon;
  const canRate = ticket.status === 'resolved' || ticket.status === 'closed';

  return (
    <div className="min-h-screen bg-vista-dark text-vista-light">
      <Navbar />
      
      {/* Header */}
      <section className="py-16 px-4">
        <div className="container mx-auto max-w-4xl">
          <div className="flex items-center gap-4 mb-8">
            <Link href="/help/tickets">
              <Button variant="outline" size="sm" className="border-vista-light/20 text-vista-light hover:bg-vista-light/10">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Tickets
              </Button>
            </Link>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={fetchTicketDetails}
              className="border-vista-light/20 text-vista-light hover:bg-vista-light/10"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
          </div>

          {/* Ticket Header */}
          <Card className="bg-vista-card border-vista-light/10 mb-6">
            <CardHeader>
              <div className="flex flex-col md:flex-row justify-between items-start gap-4">
                <div>
                  <div className="flex items-center gap-3 mb-2">
                    <h1 className="text-2xl font-bold text-vista-light">{ticket.subject}</h1>
                    <Badge variant="outline" className="border-vista-light/20 text-vista-light/70">
                      #{ticket.ticketNumber}
                    </Badge>
                  </div>
                  <div className="flex flex-wrap items-center gap-2">
                    <Badge className={statusConfig[ticket.status].color}>
                      <StatusIcon className="w-3 h-3 mr-1" />
                      {statusConfig[ticket.status].label}
                    </Badge>
                    <Badge className={priorityConfig[ticket.priority].color}>
                      {priorityConfig[ticket.priority].label}
                    </Badge>
                    <Badge variant="outline" className="border-vista-light/20 text-vista-light/60">
                      {ticket.category.replace('_', ' ')}
                    </Badge>
                  </div>
                </div>
                <div className="text-right text-sm text-vista-light/60">
                  <div className="flex items-center gap-1 mb-1">
                    <Calendar className="w-4 h-4" />
                    Created {formatDate(ticket.createdAt)}
                  </div>
                  {ticket.assignedTo && (
                    <div className="flex items-center gap-1">
                      <User className="w-4 h-4" />
                      Assigned to {ticket.assignedTo.name}
                    </div>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="prose prose-invert max-w-none">
                <p className="text-vista-light/80 whitespace-pre-wrap">{ticket.description}</p>
              </div>
              {ticket.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-4">
                  {ticket.tags.map((tag, index) => (
                    <Badge key={index} variant="outline" className="border-vista-light/20 text-vista-light/60 text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Responses */}
          <Card className="bg-vista-card border-vista-light/10 mb-6">
            <CardHeader>
              <CardTitle className="text-vista-light flex items-center gap-2">
                <MessageSquare className="w-5 h-5" />
                Conversation ({responses.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {responses.map((response, index) => (
                  <div key={response._id}>
                    <div className="flex items-start gap-4">
                      <div className="w-10 h-10 bg-vista-blue/20 rounded-full flex items-center justify-center shrink-0">
                        <User className="w-5 h-5 text-vista-blue" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="font-semibold text-vista-light">{response.responderId.name}</span>
                          <Badge 
                            variant="outline" 
                            className={`text-xs ${
                              response.responderType === 'admin' 
                                ? 'border-vista-blue/30 text-vista-blue' 
                                : 'border-vista-light/20 text-vista-light/60'
                            }`}
                          >
                            {response.responderType === 'admin' ? 'Support Team' : 'You'}
                          </Badge>
                          <span className="text-xs text-vista-light/60">{formatDate(response.createdAt)}</span>
                        </div>
                        <div className="prose prose-invert max-w-none">
                          <p className="text-vista-light/80 whitespace-pre-wrap">{response.message}</p>
                        </div>
                      </div>
                    </div>
                    {index < responses.length - 1 && <Separator className="bg-vista-light/10 mt-6" />}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Add Response */}
          {ticket.status !== 'closed' && (
            <Card className="bg-vista-card border-vista-light/10 mb-6">
              <CardHeader>
                <CardTitle className="text-vista-light">Add Response</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleAddResponse} className="space-y-4">
                  <Textarea
                    value={newResponse}
                    onChange={(e) => setNewResponse(e.target.value)}
                    placeholder="Type your response here..."
                    className="bg-vista-dark border-vista-light/20 text-vista-light min-h-[100px]"
                  />
                  <div className="flex justify-end">
                    <Button 
                      type="submit" 
                      disabled={isSubmitting || !newResponse.trim()}
                      className="bg-vista-blue hover:bg-vista-blue/90"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin mr-2" />
                          Sending...
                        </>
                      ) : (
                        <>
                          <Send className="w-4 h-4 mr-2" />
                          Send Response
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          )}

          {/* Rating Section */}
          {canRate && (
            <Card className="bg-gradient-to-r from-vista-blue/10 to-vista-accent/10 border-vista-blue/20">
              <CardHeader>
                <CardTitle className="text-vista-light">Rate Your Experience</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <p className="text-vista-light/80 mb-3">How satisfied are you with the support you received?</p>
                    <div className="flex gap-2">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <button
                          key={star}
                          type="button"
                          onClick={() => setRating(star)}
                          className={`p-1 rounded transition-colors ${
                            star <= rating ? 'text-yellow-400' : 'text-vista-light/40 hover:text-yellow-400'
                          }`}
                        >
                          <Star className="w-6 h-6 fill-current" />
                        </button>
                      ))}
                    </div>
                  </div>
                  <Textarea
                    value={feedback}
                    onChange={(e) => setFeedback(e.target.value)}
                    placeholder="Optional: Tell us more about your experience..."
                    className="bg-vista-dark border-vista-light/20 text-vista-light"
                  />
                  <Button 
                    onClick={handleRatingSubmit}
                    disabled={!rating}
                    className="bg-vista-blue hover:bg-vista-blue/90"
                  >
                    Submit Feedback
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </section>

      <Footer />
    </div>
  );
}

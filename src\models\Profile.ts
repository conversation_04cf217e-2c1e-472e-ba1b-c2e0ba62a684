import mongoose, { Schema, model, Model } from 'mongoose';
import { ProfileDocument } from '@/lib/types';

// Profile schema definition
const ProfileSchema = new Schema<ProfileDocument>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    // Each profile has its own avatar image
    // For the primary profile, this should match the user's profile image
    // For other profiles, this can be different
    avatar: {
      type: String,
      default: 'https://res.cloudinary.com/streamvista/image/upload/v1743812698/defaults/default_avatar.jpg',
      set: function(url: string) {
        // If it's a Cloudinary URL, ensure it has the correct cloud name (lowercase)
        if (url && url.includes('cloudinary.com') && !url.includes('cloudinary.com/streamvista/')) {
          return url.replace(/cloudinary\.com\/([^\/]+)\//, 'cloudinary.com/streamvista/');
        }
        return url;
      }
    },
    isKids: {
      type: Boolean,
      default: false,
    },
    isPrimary: {
      type: Boolean,
      default: false,
    },
    preferences: {
      language: {
        type: String,
        default: 'en',
      },
      maturityLevel: {
        type: String,
        enum: ['kids', 'teen', 'adult'],
        default: 'adult',
      },
      autoplayEnabled: {
        type: Boolean,
        default: true,
      },
      subtitlesEnabled: {
        type: Boolean,
        default: false,
      },
      subtitlesLanguage: {
        type: String,
        default: 'en',
      },
    },
    watchHistory: [
      {
        contentId: String,
        progress: Number,
        lastWatched: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    favoriteGenres: [String],
    myList: [String],
  },
  {
    timestamps: true,
  }
);

// Create indexes for performance
// Note: userId already has an index from the schema definition
ProfileSchema.index({ userId: 1, isPrimary: 1 });

// Create model only if it doesn't already exist (for Next.js hot reloading)
const Profile = mongoose.models.Profile || model<ProfileDocument>('Profile', ProfileSchema);

export default Profile;
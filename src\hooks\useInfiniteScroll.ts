'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { ContentCardType } from '@/lib/content-utils';

interface UseInfiniteScrollOptions {
  initialItems: ContentCardType[];
  fetchMoreItems?: (page: number) => Promise<ContentCardType[]>;
  itemsPerPage?: number;
  maxItems?: number;
  cooldownMs?: number; // Cooldown period between API requests
}

/**
 * Custom hook for implementing infinite scroll functionality
 */
export function useInfiniteScroll({
  initialItems,
  fetchMoreItems,
  itemsPerPage = 10,
  maxItems = 100,
  cooldownMs = 1000 // Default 1 second cooldown between requests
}: UseInfiniteScrollOptions) {
  // Mobile optimization: Start with fewer items on mobile for faster initial load
  const [isMobile, setIsMobile] = useState(false);
  const [isLowEndDevice, setIsLowEndDevice] = useState(false);
  
  // Detect mobile and low-end devices immediately
  useEffect(() => {
    const mobile = window.innerWidth < 768 ||
                  /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    setIsMobile(mobile);
    
    // Enhanced low-end device detection
    let isLowEnd = false;
    if (mobile) {
      try {
        // Check device memory (if available) - using interface augmentation to safely access non-standard properties
        interface NavigatorWithMemory extends Navigator {
          deviceMemory?: number;
          connection?: {
            effectiveType?: string;
          };
        }
        
        const nav = navigator as NavigatorWithMemory;
        const deviceMemory = nav.deviceMemory;
        const hardwareConcurrency = navigator.hardwareConcurrency;
        const connection = nav.connection;
        
        isLowEnd = Boolean(
          (deviceMemory && deviceMemory < 4) ||
          (hardwareConcurrency && hardwareConcurrency < 4) ||
          (connection && (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g'))
        );
      } catch (e) {
        // Fallback detection based on user agent for older devices
        isLowEnd = /Android [2-4]\.|iPhone OS [1-8]_/.test(navigator.userAgent);
      }
    }
    setIsLowEndDevice(isLowEnd);
  }, []);

  // Mobile-optimized initial item count
  const initialItemCount = isMobile ? (isLowEndDevice ? 4 : 6) : 8;
  const [items, setItems] = useState<ContentCardType[]>(
    initialItems.slice(0, Math.min(initialItems.length, initialItemCount))
  );
  const [page, setPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const lastItemRef = useRef<HTMLDivElement | null>(null);
  const lastLoadTimeRef = useRef<number>(0); // Track when the last load happened
  const pendingRequestRef = useRef<boolean>(false); // Track if there's a pending request
  const maxItemsRef = useRef<number>(maxItems); // Store max items in a ref to avoid stale closures

  // Update maxItems ref when the prop changes
  useEffect(() => {
    maxItemsRef.current = maxItems;
  }, [maxItems]);

  const loadMoreItems = useCallback(async () => {
    if (isLoading || !hasMore || pendingRequestRef.current || !fetchMoreItems) {
      return;
    }

    const now = Date.now();
    const timeSinceLastLoad = now - lastLoadTimeRef.current;

    // Mobile optimization: Slightly longer cooldown on low-end devices
    const adjustedCooldown = isLowEndDevice ? cooldownMs * 1.5 : cooldownMs;
    
    if (timeSinceLastLoad < adjustedCooldown) {
      console.log(`Cooldown active: ${adjustedCooldown - timeSinceLastLoad}ms remaining`);
      return;
    }

    pendingRequestRef.current = true;

    try {
      setIsLoading(true);
      setError(null);
      lastLoadTimeRef.current = now; // Update last load time

      const nextPage = page + 1;
      const newItems = await fetchMoreItems(nextPage);

      if (newItems.length === 0) {
        setHasMore(false);
      } else {
        // Use a function to update items to avoid closure issues with stale state
        setItems(prevItems => {
          // Mobile optimization: Keep fewer items in memory on mobile devices
          const maxItemsToKeep = isMobile ? (isLowEndDevice ? 8 : 16) : 24;

          // If we have too many items, remove some from the beginning
          let updatedItems;
          if (prevItems.length + newItems.length > maxItemsToKeep) {
            // Keep the most recent items and add new ones
            const itemsToKeep = prevItems.slice(-Math.max(0, maxItemsToKeep - newItems.length));
            updatedItems = [...itemsToKeep, ...newItems];
          } else {
            updatedItems = [...prevItems, ...newItems];
          }

          // Check if we've reached the maximum number of items
          if (updatedItems.length >= maxItemsRef.current) {
            setHasMore(false);
          }

          return updatedItems;
        });
        setPage(nextPage);
      }
    } catch (err) {
      console.error('Error loading more items:', err);
      setError(err instanceof Error ? err.message : 'Failed to load more items');
    } finally {
      setIsLoading(false);
      pendingRequestRef.current = false;
    }
  }, [fetchMoreItems, hasMore, isLoading, page, cooldownMs, isMobile, isLowEndDevice]);

  // Set up intersection observer to detect when the last item is visible
  useEffect(() => {
    if (!fetchMoreItems) {
      setHasMore(false);
      return;
    }

    // Mobile-optimized intersection observer options
    const options = {
      root: null, // Use the viewport as the root
      // Mobile optimization: Larger rootMargin on mobile to start loading earlier due to slower networks
      rootMargin: isMobile ? (isLowEndDevice ? '200px' : '150px') : '100px',
      // Mobile optimization: Lower threshold for better performance
      threshold: isMobile ? 0.01 : 0.1
    };

    // Mobile optimization: Longer debounce for low-end devices
    let debounceTimeout: NodeJS.Timeout | null = null;
    const debounceDelay = isLowEndDevice ? 300 : (isMobile ? 200 : 100);

    const observer = new IntersectionObserver((entries) => {
      const [entry] = entries;
      if (entry.isIntersecting && hasMore && !isLoading && !pendingRequestRef.current) {
        // Clear any existing timeout
        if (debounceTimeout) {
          clearTimeout(debounceTimeout);
        }

        // Set a debounce to prevent multiple calls during rapid scrolling
        debounceTimeout = setTimeout(() => {
          loadMoreItems();
        }, debounceDelay);
      }
    }, options);

    observerRef.current = observer;

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
      if (debounceTimeout) {
        clearTimeout(debounceTimeout);
      }
    };
  }, [fetchMoreItems, hasMore, isLoading, loadMoreItems, isMobile, isLowEndDevice]);

  // Observe the last item when it changes
  const setLastItemElement = useCallback((node: HTMLDivElement | null) => {
    if (lastItemRef.current) {
      if (observerRef.current) {
        observerRef.current.unobserve(lastItemRef.current);
      }
    }

    lastItemRef.current = node;

    if (node && observerRef.current && hasMore) {
      observerRef.current.observe(node);
    }
  }, [hasMore]);

  // Function to manually trigger loading more items
  const loadMore = useCallback(() => {
    if (!isLoading && hasMore) {
      loadMoreItems();
    }
  }, [hasMore, isLoading, loadMoreItems]);

  return {
    items,
    isLoading,
    hasMore,
    error,
    loadMore,
    setLastItemElement
  };
}

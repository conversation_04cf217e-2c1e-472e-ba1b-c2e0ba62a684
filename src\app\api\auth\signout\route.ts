import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  console.log('Starting signout process');

  // Get the userId from cookies before clearing it
  const userId = request.cookies.get('userId')?.value;
  console.log('User ID from cookie:', userId);

  // Get host information for debugging
  const host = request.headers.get('host');
  console.log('Host:', host);

  // Create a response
  const response = NextResponse.json({
    success: true,
    message: 'Signed out successfully',
    userId: userId || 'not found'
  });

  // Clear the userId cookie with production-ready settings
  // Important: For cookie deletion, we need to ensure we're not setting multiple cookies with the same name
  // This can cause issues specifically on Netlify where cookie order matters
  response.cookies.delete('userId');

  // Then explicitly set it with an expired date as a fallback
  response.cookies.set('userId', '', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    expires: new Date(0),
    maxAge: 0, // Set maxAge to 0 for immediate expiration
    path: '/'
  });

  // Clear any other auth-related cookies
  response.cookies.delete('session');

  // Then explicitly set it with an expired date as a fallback
  response.cookies.set('session', '', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    expires: new Date(0),
    maxAge: 0,
    path: '/'
  });

  // Set cache control headers to prevent caching
  response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
  response.headers.set('Pragma', 'no-cache');
  response.headers.set('Expires', '0');

  console.log('Signout process completed');

  return response;
}

// Handle GET requests
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

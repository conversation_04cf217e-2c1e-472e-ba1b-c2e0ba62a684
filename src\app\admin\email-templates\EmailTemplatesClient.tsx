'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/ui/use-toast';
import AdminHeader from '@/components/admin/AdminHeader';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Mail,
  Plus,
  RefreshCw,
  Edit,
  Trash2,
  Eye,
  Send,
  AlertTriangle,
  CheckCircle,
  Info,
} from 'lucide-react';

interface EmailTemplate {
  _id: string;
  name: string;
  subject: string;
  body: string;
  description: string;
  variables: string[];
  isDefault: boolean;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function EmailTemplatesClient() {
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);
  const [isTestModalOpen, setIsTestModalOpen] = useState(false);
  const [testEmail, setTestEmail] = useState('');
  const [testVariables, setTestVariables] = useState<Record<string, string>>({});
  const [isSending, setIsSending] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string; previewUrl?: string } | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    subject: '',
    body: '',
    description: '',
    variables: '',
    active: true,
  });
  const { user, isAuthenticated, isAdmin } = useAuth();
  const router = useRouter();
  const { toast } = useToast();

  // Check if user is authenticated and is admin
  useEffect(() => {
    if (isAuthenticated && !isAdmin()) {
      router.push('/');
      toast({
        title: 'Access Denied',
        description: 'You do not have permission to access this page.',
        variant: 'destructive',
      });
    }
  }, [isAuthenticated, isAdmin, router, toast]);

  // Fetch templates
  const fetchTemplates = async () => {
    setLoading(true);
    setError(null);

    try {
      // Include userId in the request
      const userId = user?.id;
      if (!userId) {
        throw new Error('User ID not available');
      }

      const response = await fetch(`/api/admin/email-templates?userId=${userId}`, {
        headers: {
          'Authorization': `Bearer ${userId}`,
          'Cache-Control': 'no-cache'
        },
        cache: 'no-store'
      });

      if (!response.ok) {
        throw new Error('Failed to fetch email templates');
      }

      const data = await response.json();
      setTemplates(data.templates || []);
    } catch (err) {
      console.error('Error fetching email templates:', err);
      setError('Failed to load email templates. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    if (isAuthenticated && isAdmin()) {
      fetchTemplates();
    }
  }, [isAuthenticated, isAdmin]);

  // Handle form input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle switch change
  const handleSwitchChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, active: checked }));
  };

  // Handle test variable change
  const handleTestVariableChange = (variable: string, value: string) => {
    setTestVariables(prev => ({ ...prev, [variable]: value }));
  };

  // Reset form data
  const resetFormData = () => {
    setFormData({
      name: '',
      subject: '',
      body: '',
      description: '',
      variables: '',
      active: true,
    });
  };

  // Set form data from template
  const setFormDataFromTemplate = (template: EmailTemplate) => {
    setFormData({
      name: template.name,
      subject: template.subject,
      body: template.body,
      description: template.description,
      variables: template.variables.join(', '),
      active: template.active,
    });
  };

  // Handle create template
  const handleCreateTemplate = async () => {
    try {
      // Validate form
      if (!formData.name || !formData.subject || !formData.body || !formData.description) {
        toast({
          title: 'Validation Error',
          description: 'Please fill in all required fields.',
          variant: 'destructive',
        });
        return;
      }

      // Parse variables
      const variables = formData.variables
        ? formData.variables.split(',').map(v => v.trim())
        : [];

      // Include userId in the request
      const userId = user?.id;
      if (!userId) {
        throw new Error('User ID not available');
      }

      // Create template
      const response = await fetch(`/api/admin/email-templates?userId=${userId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${userId}`,
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify({
          name: formData.name,
          subject: formData.subject,
          body: formData.body,
          description: formData.description,
          variables,
          userId // Include userId in the body as well
        }),
        cache: 'no-store'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create template');
      }

      // Close modal and refresh templates
      setIsCreateModalOpen(false);
      resetFormData();
      fetchTemplates();

      toast({
        title: 'Template Created',
        description: 'Email template has been created successfully.',
      });
    } catch (err: any) {
      console.error('Error creating template:', err);
      toast({
        title: 'Error',
        description: err.message || 'Failed to create template',
        variant: 'destructive',
      });
    }
  };

  // Handle update template
  const handleUpdateTemplate = async () => {
    if (!selectedTemplate) return;

    try {
      // Validate form
      if (!formData.name || !formData.subject || !formData.body || !formData.description) {
        toast({
          title: 'Validation Error',
          description: 'Please fill in all required fields.',
          variant: 'destructive',
        });
        return;
      }

      // Parse variables
      const variables = formData.variables
        ? formData.variables.split(',').map(v => v.trim())
        : [];

      // Include userId in the request
      const userId = user?.id;
      if (!userId) {
        throw new Error('User ID not available');
      }

      // Update template
      const response = await fetch(`/api/admin/email-templates/${selectedTemplate._id}?userId=${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${userId}`,
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify({
          name: formData.name,
          subject: formData.subject,
          body: formData.body,
          description: formData.description,
          variables,
          active: formData.active,
          userId // Include userId in the body as well
        }),
        cache: 'no-store'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update template');
      }

      // Close modal and refresh templates
      setIsEditModalOpen(false);
      setSelectedTemplate(null);
      resetFormData();
      fetchTemplates();

      toast({
        title: 'Template Updated',
        description: 'Email template has been updated successfully.',
      });
    } catch (err: any) {
      console.error('Error updating template:', err);
      toast({
        title: 'Error',
        description: err.message || 'Failed to update template',
        variant: 'destructive',
      });
    }
  };

  // Handle delete template
  const handleDeleteTemplate = async () => {
    if (!selectedTemplate) return;

    try {
      // Include userId in the request
      const userId = user?.id;
      if (!userId) {
        throw new Error('User ID not available');
      }

      // Delete template
      const response = await fetch(`/api/admin/email-templates/${selectedTemplate._id}?userId=${userId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${userId}`,
          'Cache-Control': 'no-cache'
        },
        cache: 'no-store'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete template');
      }

      // Refresh templates
      setSelectedTemplate(null);
      fetchTemplates();

      toast({
        title: 'Template Deleted',
        description: 'Email template has been deleted successfully.',
      });
    } catch (err: any) {
      console.error('Error deleting template:', err);
      toast({
        title: 'Error',
        description: err.message || 'Failed to delete template',
        variant: 'destructive',
      });
    }
  };

  // Handle test email
  const handleTestEmail = async () => {
    if (!selectedTemplate || !testEmail) return;

    setIsSending(true);
    setTestResult(null);

    try {
      // Include userId in the request
      const userId = user?.id;
      if (!userId) {
        throw new Error('User ID not available');
      }

      // Send test email
      const response = await fetch(`/api/admin/email-templates/test?userId=${userId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${userId}`,
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify({
          templateId: selectedTemplate._id,
          testEmail,
          testData: testVariables,
          userId // Include userId in the body as well
        }),
        cache: 'no-store'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send test email');
      }

      const result = await response.json();

      setTestResult({
        success: true,
        message: 'Test email sent successfully!',
        previewUrl: result.previewUrl,
      });

      toast({
        title: 'Test Email Sent',
        description: 'Test email has been sent successfully.',
      });
    } catch (err: any) {
      console.error('Error sending test email:', err);
      setTestResult({
        success: false,
        message: err.message || 'Failed to send test email',
      });

      toast({
        title: 'Error',
        description: err.message || 'Failed to send test email',
        variant: 'destructive',
      });
    } finally {
      setIsSending(false);
    }
  };

  return (
    <div className="space-y-6">
      <AdminHeader />
      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
          <div>
            <h1 className="text-3xl font-bold text-vista-light flex items-center">
              <Mail className="mr-3 h-7 w-7 text-vista-blue" />
              Email Templates
            </h1>
            <p className="text-vista-light/70 mt-1">
              Manage email templates for user communications
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              onClick={fetchTemplates}
              className="text-vista-light border-vista-light/20 hover:bg-vista-light/5"
            >
              <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              {loading ? 'Refreshing...' : 'Refresh'}
            </Button>
            <Button onClick={() => {
              resetFormData();
              setIsCreateModalOpen(true);
            }}>
              <Plus className="mr-2 h-4 w-4" />
              Create Template
            </Button>
          </div>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Card>
          <CardHeader>
            <CardTitle className="text-vista-light">Email Templates</CardTitle>
            <CardDescription>
              Manage templates for system emails sent to users
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="flex items-center space-x-4">
                    <Skeleton className="h-12 w-12 rounded-full" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-[250px]" />
                      <Skeleton className="h-4 w-[200px]" />
                    </div>
                  </div>
                ))}
              </div>
            ) : templates.length === 0 ? (
              <div className="text-center py-12 border border-dashed border-vista-light/10 rounded-lg">
                <Mail className="h-12 w-12 mx-auto text-vista-light/20 mb-4" />
                <h3 className="text-xl font-medium mb-2">No templates found</h3>
                <p className="text-vista-light/60 max-w-md mx-auto mb-6">
                  You haven't created any email templates yet. Create your first template to get started.
                </p>
                <Button onClick={() => {
                  resetFormData();
                  setIsCreateModalOpen(true);
                }}>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Template
                </Button>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Subject</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {templates.map((template) => (
                      <TableRow key={template._id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center">
                            {template.name}
                            {template.isDefault && (
                              <Badge variant="outline" className="ml-2">
                                Default
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="max-w-xs truncate">{template.subject}</TableCell>
                        <TableCell className="max-w-xs truncate">{template.description}</TableCell>
                        <TableCell>
                          <Badge
                            variant={template.active ? 'default' : 'secondary'}
                            className={template.active ? 'bg-green-500' : 'bg-gray-500'}
                          >
                            {template.active ? 'Active' : 'Inactive'}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setSelectedTemplate(template);
                                setIsPreviewModalOpen(true);
                              }}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setSelectedTemplate(template);
                                setTestEmail('');
                                setTestVariables({});
                                setTestResult(null);
                                setIsTestModalOpen(true);
                              }}
                            >
                              <Send className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setSelectedTemplate(template);
                                setFormDataFromTemplate(template);
                                setIsEditModalOpen(true);
                              }}
                              disabled={template.isDefault}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="text-red-500 hover:text-red-700"
                                  disabled={template.isDefault}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    This action cannot be undone. This will permanently delete the
                                    email template.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => {
                                      setSelectedTemplate(template);
                                      handleDeleteTemplate();
                                    }}
                                    className="bg-red-500 hover:bg-red-600"
                                  >
                                    Delete
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Create Template Modal */}
      <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create Email Template</DialogTitle>
            <DialogDescription>
              Create a new email template for user communications
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Template Name</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="welcome, password-reset, etc."
                />
                <p className="text-sm text-vista-light/60">
                  Unique identifier for the template (no spaces)
                </p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="subject">Email Subject</Label>
                <Input
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  placeholder="Welcome to StreamVista!"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Input
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Welcome email sent to new users after registration"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="variables">Variables (comma-separated)</Label>
              <Input
                id="variables"
                name="variables"
                value={formData.variables}
                onChange={handleInputChange}
                placeholder="name, resetUrl, loginUrl"
              />
              <p className="text-sm text-vista-light/60">
                Variables that can be used in the template (e.g., {'{{name}}'})
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="body">Email Body (HTML)</Label>
              <Textarea
                id="body"
                name="body"
                value={formData.body}
                onChange={handleInputChange}
                placeholder="<div>Hello {{name}},</div>"
                className="min-h-[300px] font-mono"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateTemplate}>
              Create Template
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Template Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Email Template</DialogTitle>
            <DialogDescription>
              Update the email template
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">Template Name</Label>
                <Input
                  id="edit-name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="welcome, password-reset, etc."
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-subject">Email Subject</Label>
                <Input
                  id="edit-subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  placeholder="Welcome to StreamVista!"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-description">Description</Label>
              <Input
                id="edit-description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Welcome email sent to new users after registration"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-variables">Variables (comma-separated)</Label>
              <Input
                id="edit-variables"
                name="variables"
                value={formData.variables}
                onChange={handleInputChange}
                placeholder="name, resetUrl, loginUrl"
              />
              <p className="text-sm text-vista-light/60">
                Variables that can be used in the template (e.g., {'{{name}}'})
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-body">Email Body (HTML)</Label>
              <Textarea
                id="edit-body"
                name="body"
                value={formData.body}
                onChange={handleInputChange}
                placeholder="<div>Hello {{name}},</div>"
                className="min-h-[300px] font-mono"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="edit-active"
                checked={formData.active}
                onCheckedChange={handleSwitchChange}
              />
              <Label htmlFor="edit-active">Active</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateTemplate}>
              Update Template
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Preview Template Modal */}
      <Dialog open={isPreviewModalOpen} onOpenChange={setIsPreviewModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Preview Email Template</DialogTitle>
            <DialogDescription>
              {selectedTemplate?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Subject:</h3>
              <p className="p-2 bg-vista-dark-lighter rounded">{selectedTemplate?.subject}</p>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Variables:</h3>
              <div className="flex flex-wrap gap-2">
                {selectedTemplate?.variables?.map((variable) => (
                  <Badge key={variable} variant="outline">
                    {`{{${variable}}}`}
                  </Badge>
                ))}
              </div>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Body:</h3>
              <div className="border border-vista-light/10 rounded-md p-4 bg-white text-black">
                <div dangerouslySetInnerHTML={{ __html: selectedTemplate?.body || '' }} />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsPreviewModalOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Test Email Modal */}
      <Dialog open={isTestModalOpen} onOpenChange={setIsTestModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Send Test Email</DialogTitle>
            <DialogDescription>
              Test the "{selectedTemplate?.name}" template by sending a test email
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="test-email">Recipient Email</Label>
              <Input
                id="test-email"
                type="email"
                value={testEmail}
                onChange={(e) => setTestEmail(e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>

            {selectedTemplate && selectedTemplate.variables && selectedTemplate.variables.length > 0 && (
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Template Variables:</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {selectedTemplate?.variables?.map((variable) => (
                    <div key={variable} className="space-y-1">
                      <Label htmlFor={`var-${variable}`}>{`{{${variable}}}`}</Label>
                      <Input
                        id={`var-${variable}`}
                        value={testVariables[variable] || ''}
                        onChange={(e) => handleTestVariableChange(variable, e.target.value)}
                        placeholder={`Value for ${variable}`}
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}

            {testResult && (
              <Alert
                variant={testResult.success ? 'default' : 'destructive'}
                className={testResult.success ? 'bg-green-500/10 border-green-500/20' : ''}
              >
                {testResult.success ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <AlertTriangle className="h-4 w-4" />
                )}
                <AlertTitle>{testResult.success ? 'Success' : 'Error'}</AlertTitle>
                <AlertDescription>
                  {testResult.message}
                  {testResult.previewUrl && (
                    <div className="mt-2">
                      <a
                        href={testResult.previewUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-500 hover:underline flex items-center"
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View Email Preview
                      </a>
                    </div>
                  )}
                </AlertDescription>
              </Alert>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsTestModalOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleTestEmail}
              disabled={!testEmail || isSending}
            >
              {isSending ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  Send Test Email
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

import { IContent } from '@/data/content';

export interface DownloadItem {
  id: string;
  contentId: string;
  title: string;
  image: string;
  type: 'show' | 'movie';
  quality: string;
  size: string;
  progress: number; // 0-100
  status: 'downloading' | 'paused' | 'completed' | 'error';
  addedDate: string;
  expiresDate: string | null;
  season?: number;
  episode?: string;
  episodeTitle?: string;
}

export interface DownloadStats {
  totalDownloads: number;
  completedDownloads: number;
  activeDownloads: number;
  pausedDownloads: number;
  totalSpaceUsed: string;
  availableSpace: string;
}

// Quality options with corresponding file sizes
export const QUALITY_OPTIONS = [
  { label: 'Low (480p)', value: '480p', bitrate: '1 Mbps', size: { movie: '0.5 GB', episode: '250 MB' } },
  { label: 'Medium (720p)', value: '720p', bitrate: '3 Mbps', size: { movie: '1.5 GB', episode: '700 MB' } },
  { label: 'High (1080p)', value: '1080p', bitrate: '6 Mbps', size: { movie: '3 GB', episode: '1.4 GB' } },
  { label: 'Ultra HD (4K)', value: '4k', bitrate: '15 Mbps', size: { movie: '7.5 GB', episode: '3.5 GB' } },
];

// Helper to get estimated size based on quality and content type
export function getEstimatedSize(quality: string, isMovie: boolean, duration: number = 0): string {
  const qualityOption = QUALITY_OPTIONS.find(q => q.value === quality);
  if (!qualityOption) return isMovie ? '3 GB' : '1.4 GB'; // Default to 1080p equivalent

  const baseSize = isMovie ? qualityOption.size.movie : qualityOption.size.episode;

  // If duration is provided, adjust size proportionally (assuming 2h for movies, 45min for episodes)
  if (duration > 0) {
    const baseDuration = isMovie ? 120 : 45; // minutes
    const sizeValue = parseFloat(baseSize.split(' ')[0]);
    const sizeUnit = baseSize.split(' ')[1];
    const adjustedSize = (duration / baseDuration) * sizeValue;
    return `${adjustedSize.toFixed(1)} ${sizeUnit}`;
  }

  return baseSize;
}

// Function to save downloads to localStorage
export function saveDownloads(downloads: DownloadItem[]): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem('vista-downloads', JSON.stringify(downloads));
  }
}

// Function to get downloads from localStorage
export function getDownloads(): DownloadItem[] {
  if (typeof window !== 'undefined') {
    const saved = localStorage.getItem('vista-downloads');
    return saved ? JSON.parse(saved) : [];
  }
  return [];
}

// Generate a download ID
export function generateDownloadId(): string {
  return `dl_${Math.random().toString(36).substring(2, 11)}`;
}

// Format bytes to human-readable format
export function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Parse a string size to bytes
export function parseSize(sizeStr: string): number {
  const value = parseFloat(sizeStr.split(' ')[0]);
  const unit = sizeStr.split(' ')[1].toUpperCase();

  const units = { 'B': 1, 'KB': 1024, 'MB': 1024 * 1024, 'GB': 1024 * 1024 * 1024, 'TB': 1024 * 1024 * 1024 * 1024 };
  return value * (units[unit as keyof typeof units] || 1);
}

// Calculate current download stats
export function getDownloadStats(downloads: DownloadItem[]): DownloadStats {
  const totalDownloads = downloads.length;
  const completedDownloads = downloads.filter(d => d.status === 'completed').length;
  const activeDownloads = downloads.filter(d => d.status === 'downloading').length;
  const pausedDownloads = downloads.filter(d => d.status === 'paused').length;

  // Calculate total space (this is simulated)
  let totalSpaceUsedBytes = 0;
  downloads.forEach(download => {
    // For completed downloads, use full size
    if (download.status === 'completed') {
      totalSpaceUsedBytes += parseSize(download.size);
    }
    // For in-progress downloads, calculate based on progress
    else if (download.status === 'downloading' || download.status === 'paused') {
      totalSpaceUsedBytes += (parseSize(download.size) * download.progress / 100);
    }
  });

  const totalSpaceUsed = formatBytes(totalSpaceUsedBytes);
  const availableSpace = '64 GB'; // Simulated available space

  return {
    totalDownloads,
    completedDownloads,
    activeDownloads,
    pausedDownloads,
    totalSpaceUsed,
    availableSpace
  };
}

// Add a new download
export function addDownload(
  content: IContent,
  quality: string,
  season?: number,
  episode?: string,
  episodeTitle?: string
): DownloadItem {
  const now = new Date();
  const expiresDate = new Date();
  expiresDate.setDate(now.getDate() + 30); // Expires in 30 days

  // Determine if it's a movie or a show episode
  const isMovie = content.type === 'movie';
  const size = getEstimatedSize(quality, isMovie);

  const newDownload: DownloadItem = {
    id: generateDownloadId(),
    contentId: content.id,
    title: content.title,
    image: content.image,
    type: content.type,
    quality,
    size,
    progress: 0,
    status: 'downloading',
    addedDate: now.toISOString(),
    expiresDate: expiresDate.toISOString(),
  };

  // Add episode info if it's a show
  if (!isMovie && season && episode) {
    newDownload.season = season;
    newDownload.episode = episode;
    newDownload.episodeTitle = episodeTitle || `Episode ${episode}`;
    newDownload.title = `${content.title} S${season}:E${episode}`;
  }

  // Get current downloads and add the new one
  const downloads = getDownloads();
  downloads.push(newDownload);
  saveDownloads(downloads);

  return newDownload;
}

// Update download progress
export function updateDownloadProgress(id: string, progress: number): void {
  const downloads = getDownloads();
  const downloadIndex = downloads.findIndex(d => d.id === id);

  if (downloadIndex !== -1) {
    downloads[downloadIndex].progress = Math.min(100, progress);

    // Auto-complete if reached 100%
    if (progress >= 100) {
      downloads[downloadIndex].status = 'completed';
    }

    saveDownloads(downloads);
  }
}

// Change download status
export function updateDownloadStatus(id: string, status: DownloadItem['status']): void {
  const downloads = getDownloads();
  const downloadIndex = downloads.findIndex(d => d.id === id);

  if (downloadIndex !== -1) {
    downloads[downloadIndex].status = status;
    saveDownloads(downloads);
  }
}

// Delete a download
export function deleteDownload(id: string): void {
  const downloads = getDownloads();
  const filteredDownloads = downloads.filter(d => d.id !== id);
  saveDownloads(filteredDownloads);
}

// Generate sample download data for demonstration
export function generateSampleDownloads(): DownloadItem[] {
  const result: DownloadItem[] = [
    {
      id: 'dl_sample1',
      contentId: 'severance',
      title: 'Severance S1:E1',
      image: 'https://ext.same-assets.com/1339546664/1758854398.webp',
      type: 'show',
      quality: '1080p',
      size: '1.4 GB',
      progress: 100,
      status: 'completed',
      addedDate: new Date(2023, 11, 15).toISOString(),
      expiresDate: new Date(2024, 0, 15).toISOString(),
      season: 1,
      episode: '01',
      episodeTitle: 'Good News About Hell'
    },
    {
      id: 'dl_sample2',
      contentId: 'severance',
      title: 'Severance S1:E2',
      image: 'https://ext.same-assets.com/1339546664/1758854398.webp',
      type: 'show',
      quality: '1080p',
      size: '1.3 GB',
      progress: 100,
      status: 'completed',
      addedDate: new Date(2023, 11, 15).toISOString(),
      expiresDate: new Date(2024, 0, 15).toISOString(),
      season: 1,
      episode: '02',
      episodeTitle: 'Half Loop'
    },
    {
      id: 'dl_sample3',
      contentId: 'the-gorge',
      title: 'The Gorge',
      image: 'https://ext.same-assets.com/3200800809/3490029595.webp',
      type: 'movie',
      quality: '4k',
      size: '7.5 GB',
      progress: 65,
      status: 'downloading',
      addedDate: new Date().toISOString(),
      expiresDate: new Date(new Date().setDate(new Date().getDate() + 30)).toISOString()
    },
    {
      id: 'dl_sample4',
      contentId: 'luck',
      title: 'Luck',
      image: 'https://ext.same-assets.com/7113700/1443598573.webp',
      type: 'movie',
      quality: '720p',
      size: '1.5 GB',
      progress: 30,
      status: 'paused',
      addedDate: new Date().toISOString(),
      expiresDate: new Date(new Date().setDate(new Date().getDate() + 30)).toISOString()
    },
    {
      id: 'dl_sample5',
      contentId: 'berlin-er',
      title: 'Berlin ER S1:E1',
      image: 'https://ext.same-assets.com/2708030945/3342127779.webp',
      type: 'show',
      quality: '720p',
      size: '700 MB',
      progress: 15,
      status: 'downloading',
      addedDate: new Date().toISOString(),
      expiresDate: new Date(new Date().setDate(new Date().getDate() + 30)).toISOString(),
      season: 1,
      episode: '01',
      episodeTitle: 'Pilot'
    }
  ];

  return result;
}

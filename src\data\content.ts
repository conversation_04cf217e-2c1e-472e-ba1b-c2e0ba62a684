// Content Types
export type ContentType = 'show' | 'movie';

// Genre definition
export type Genre =
  | 'Action'
  | 'Comedy'
  | 'Drama'
  | 'Thriller'
  | 'Sci-Fi'
  | 'Horror'
  | 'Documentary'
  | 'Animation'
  | 'Fantasy'
  | 'Mystery'
  | 'Crime'
  | 'Medical'
  | 'Espionage'
  | 'Adventure'
  | 'Historical'
  | 'Romance';

// Content interface
export interface IContent {
  id: string | number;
  title: string;
  type: 'movie' | 'show';
  year: string;
  posterPath: string;
  backdropPath?: string;
  overview?: string;
  genres?: string[];
  runtime?: number;
  rating?: number;
  seasons?: number;
  episodes?: number;
  creator?: string;
  tmdbId?: string;
  imdbId?: string;

  // OMDB-specific fields
  director?: string;
  actors?: string[];
  awards?: string;
  rated?: string;
  released?: string;
  metascore?: number;
  dataSource?: 'tmdb' | 'omdb' | 'both';

  // Video data
  videos?: {
    results?: Array<{
      id: string;
      key: string;
      name: string;
      site: string;
      size: number;
      type: string;
      official: boolean;
    }>;
  };

  // Credits data (cast and crew)
  credits?: {
    cast?: Array<{
      id: number;
      name: string;
      character?: string;
      profile_path?: string | null;
      order?: number;
      gender?: number;
      popularity?: number;
    }>;
    crew?: Array<{
      id: number;
      name: string;
      job?: string;
      department?: string;
      profile_path?: string | null;
      gender?: number;
      popularity?: number;
    }>;
  };

  // Similar and recommendations
  similar?: any;
  recommendations?: any;

  // Analytics data
  views?: number;
}

// Watch item interface for Continue Watching section
export interface WatchItem {
  id: string;
  title: string;
  image: string;
  type: ContentType;
  progress: number; // 0-100
  episode?: string;
  season?: number | string;
  timestamp?: string; // e.g. "35:42"
}

// Featured content for hero section
export const featuredContent: IContent[] = [
  {
    id: 'horizon-line',
    title: 'Horizon Line',
    type: 'show',
    year: '2024',
    posterPath: 'https://images.unsplash.com/photo-1682687220742-aba13b6e50ba?q=80&w=2070',
    backdropPath: 'https://images.unsplash.com/photo-1682687220742-aba13b6e50ba?q=80&w=2070',
    overview: "Experience the breathtaking journey of elite climbers as they face nature's ultimate challenge. In this gripping series, witness the raw determination and unbreakable spirit required to conquer the world's most formidable peaks.",
    genres: ['Action', 'Drama', 'Thriller'],
    rating: 8.5,
    runtime: 120,
    seasons: 1,
    episodes: 10,
    creator: 'Sarah Chen'
  },
  {
    id: 'night-sky',
    title: 'The Night Sky',
    type: 'show',
    year: '2024',
    posterPath: 'https://images.unsplash.com/photo-1537420327992-d6e192287183?q=80&w=2070',
    backdropPath: 'https://images.unsplash.com/photo-1537420327992-d6e192287183?q=80&w=2070',
    overview: "When a mysterious portal appears in their backyard, a retired couple discovers an extraordinary connection to a distant planet. As they unravel this cosmic mystery, they find themselves at the center of an interstellar conspiracy that could change humanity forever.",
    genres: ['Sci-Fi', 'Mystery', 'Drama'],
    rating: 7.5,
    runtime: 120,
    seasons: 1,
    episodes: 10,
    creator: 'James Nolan'
  },
  {
    id: 'silent-echo',
    title: 'Silent Echo',
    type: 'show',
    year: '2024',
    posterPath: 'https://images.unsplash.com/photo-1614741118887-7a4ee193a5fa?q=80&w=2070',
    backdropPath: 'https://images.unsplash.com/photo-1614741118887-7a4ee193a5fa?q=80&w=2070',
    overview: "In a world where memories can be transferred, Detective Sarah Chen must confront her own dark past while investigating a series of mysterious deaths. Each victim's memories tell a different story, but they all point to a conspiracy that threatens to unravel the very fabric of society.",
    genres: ['Mystery', 'Thriller', 'Sci-Fi'],
    rating: 8.0,
    runtime: 120,
    seasons: 1,
    episodes: 10,
    creator: 'Mika Rodriguez'
  },
  {
    id: 'crossroads',
    title: 'Crossroads',
    type: 'movie',
    year: '2024',
    posterPath: 'https://images.unsplash.com/photo-1485846234645-a62644f84728?q=80&w=2070',
    backdropPath: 'https://images.unsplash.com/photo-1485846234645-a62644f84728?q=80&w=2070',
    overview: "Four strangers meet at a remote cabin, each holding a key to the other's future.",
    genres: ['Drama', 'Mystery', 'Thriller'],
    rating: 7.0,
    runtime: 135
  }
];

// Mock popular movies
export const popularMovies: IContent[] = [
  {
    id: 'chucky',
    title: 'Chucky',
    type: 'movie',
    year: '2021',
    posterPath: 'https://image.tmdb.org/t/p/w500/kY0BogCM8SkNJ0MNiHB3VTM86Tz.jpg',
    backdropPath: 'https://image.tmdb.org/t/p/original/iF8ai2QLNiHV4anwY1TuSGZXqfN.jpg',
    overview: 'After a vintage Chucky doll turns up at a suburban yard sale, an idyllic American town is thrown into chaos as a series of horrifying murders begin to expose the town\'s hypocrisies and secrets.',
    genres: ['Horror', 'Thriller'],
    runtime: 45,
    rating: 7.5,
    imdbId: 'tt14106160'
  },
  {
    id: 'm1',
    title: 'Dune: Part Two',
    type: 'movie',
    year: '2024',
    posterPath: 'https://image.tmdb.org/t/p/w500/8b8R8l88Qje9dn9OE8PY05Nxl1X.jpg',
    backdropPath: 'https://image.tmdb.org/t/p/original/eNxLzQKi5GVoPX0EvzEKW0cmWIl.jpg',
    overview: 'Follow the mythic journey of Paul Atreides as he unites with Chani and the Fremen while on a path of revenge against the conspirators who destroyed his family.',
    genres: ['Science Fiction', 'Adventure'],
    runtime: 166,
    rating: 8.5,
    imdbId: 'tt15239678'
  },
  {
    id: 'm2',
    title: 'Civil War',
    type: 'movie',
    year: '2024',
    posterPath: 'https://image.tmdb.org/t/p/w500/eN1TMy5hT72G2v92x5cNM5trdFv.jpg',
    backdropPath: 'https://image.tmdb.org/t/p/original/xvk5AhfhgQcTuaCQyq1XqChDKQG.jpg',
    overview: 'In a near-future America torn by civil war, a team of journalists travel across the increasingly dangerous country.',
    genres: ['Action', 'Thriller'],
    runtime: 109,
    rating: 7.2,
    imdbId: 'tt18556326'
  },
  {
    id: 'm3',
    title: 'Godzilla x Kong: The New Empire',
    type: 'movie',
    year: '2024',
    posterPath: 'https://image.tmdb.org/t/p/w500/kEf7QrRHrAmwW7MZogWr0XOZjGq.jpg',
    backdropPath: 'https://image.tmdb.org/t/p/original/kQJ6OtkiGvXr8wZYxjNlPQP6eJN.jpg',
    overview: 'Following their explosive showdown, Godzilla and Kong must reunite against a colossal undiscovered threat hidden within our world.',
    genres: ['Action', 'Science Fiction'],
    runtime: 115,
    rating: 7.0,
    imdbId: 'tt14539740'
  },
  {
    id: 'm4',
    title: 'Ghostbusters: Frozen Empire',
    type: 'movie',
    year: '2024',
    posterPath: 'https://image.tmdb.org/t/p/w500/6eeRYxbzThxVpEyRFQ5Z0YYwHRz.jpg',
    backdropPath: 'https://image.tmdb.org/t/p/original/gkHgWdNyQgssC8qTfcKAzzRz3m9.jpg',
    overview: 'When the discovery of an ancient artifact unleashes an evil force, Ghostbusters new and old must join forces to protect their home.',
    genres: ['Fantasy', 'Comedy'],
    runtime: 115,
    rating: 7.3,
    imdbId: 'tt12965802'
  },
  {
    id: 'm5',
    title: 'Kingdom of the Planet of the Apes',
    type: 'movie',
    year: '2024',
    posterPath: 'https://image.tmdb.org/t/p/w500/bG94z93nLb2s2gVfS9rZJiRrtGe.jpg',
    backdropPath: 'https://image.tmdb.org/t/p/original/ofi4OiBEytTlgVfI8GqPaWYVVJt.jpg',
    overview: 'Several generations after Caesar\'s reign, apes are now the dominant species and humans have been reduced to living in the shadows.',
    genres: ['Science Fiction', 'Adventure'],
    runtime: 145,
    rating: 7.2,
    imdbId: 'tt11389872'
  },
  {
    id: 'm6',
    title: 'Challengers',
    type: 'movie',
    year: '2024',
    posterPath: 'https://image.tmdb.org/t/p/w500/mR2SY0yrYAUK8mLHk3rdCjO72iR.jpg',
    backdropPath: 'https://image.tmdb.org/t/p/original/c1Q8QI5OdY4obyjdwlQYDYcHi6T.jpg',
    overview: 'A tennis star caught in a love triangle with her husband and his former best friend faces a pivotal match that could define her career.',
    genres: ['Drama', 'Romance'],
    runtime: 131,
    rating: 7.4,
    imdbId: 'tt15245034'
  }
];

// Mock popular shows
export const popularShows: IContent[] = [
  {
    id: 's1',
    title: 'House of the Dragon',
    type: 'show',
    year: '2022',
    posterPath: 'https://image.tmdb.org/t/p/w500/z2yahl2uefxDCl0nogcRBstwruJ.jpg',
    backdropPath: 'https://image.tmdb.org/t/p/original/rRPJVxj8gGRl8WLlp7ny56AnvkF.jpg',
    overview: 'The prequel series finds the Targaryen dynasty at the absolute apex of its power, with more than 15 dragons under their yoke.',
    genres: ['Drama', 'Fantasy'],
    seasons: 2,
    episodes: 18,
    rating: 8.4
  },
  {
    id: 's2',
    title: 'The Last of Us',
    type: 'show',
    year: '2023',
    posterPath: 'https://image.tmdb.org/t/p/w500/uKvVjHNqB5VmOrdxqAt2F7J78ED.jpg',
    backdropPath: 'https://image.tmdb.org/t/p/original/uDgy6hyPd82kOHh6I95FLtLnj6p.jpg',
    overview: 'Twenty years after modern civilization has been destroyed, Joel is hired to smuggle Ellie out of an oppressive quarantine zone.',
    genres: ['Drama', 'Sci-Fi'],
    seasons: 1,
    episodes: 9,
    rating: 8.7
  },
  {
    id: 's3',
    title: 'Fallout',
    type: 'show',
    year: '2024',
    posterPath: 'https://image.tmdb.org/t/p/w500/6eLXneyIHsuu2Jsb1nSw2hETM1r.jpg',
    backdropPath: 'https://image.tmdb.org/t/p/original/mDMjrG8NgqvLlTWBn4J4A9dScR6.jpg',
    overview: 'In a retrofuturistic world devastated by nuclear war, a woman from an underground bunker enters a wasteland filled with mutants and danger.',
    genres: ['Sci-Fi', 'Adventure'],
    seasons: 1,
    episodes: 8,
    rating: 8.5
  },
  {
    id: 's4',
    title: 'The Boys',
    type: 'show',
    year: '2019',
    posterPath: 'https://image.tmdb.org/t/p/w500/mY7SeH4HFFxW1hiI6cWuwCRKptN.jpg',
    backdropPath: 'https://image.tmdb.org/t/p/original/q3jHCb4dMfYF6ojikKuHd6LscxC.jpg',
    overview: 'A group of vigilantes set out to take down corrupt superheroes who abuse their superpowers.',
    genres: ['Action', 'Comedy'],
    seasons: 3,
    episodes: 24,
    rating: 8.4
  },
  {
    id: 's5',
    title: 'Shōgun',
    type: 'show',
    year: '2024',
    posterPath: 'https://image.tmdb.org/t/p/w500/n3gPPZGp0BwMJBnJftCc7rxgYqa.jpg',
    backdropPath: 'https://image.tmdb.org/t/p/original/xzXdyFygjKCs3eUuUd8KCyXKJYY.jpg',
    overview: 'Set in Japan in the year 1600, at the dawn of a century-defining civil war, Lord Yoshii Toranaga is fighting for his life as his enemies on the Council of Regents unite against him.',
    genres: ['Drama', 'War'],
    seasons: 1,
    episodes: 10,
    rating: 8.8
  },
  {
    id: 's6',
    title: 'Severance',
    type: 'show',
    year: '2022',
    posterPath: 'https://image.tmdb.org/t/p/w500/apdRdV9mYqQwdAfsv4qAJH2Zr6U.jpg',
    backdropPath: 'https://image.tmdb.org/t/p/original/50lpQDbhfMU36Q8vBIRXF8sgnCZ.jpg',
    overview: 'Mark leads a team of office workers whose memories have been surgically divided between their work and personal lives.',
    genres: ['Sci-Fi', 'Mystery'],
    seasons: 1,
    episodes: 9,
    rating: 8.7
  }
];

// Continue watching items
export const continueWatching: WatchItem[] = [
  {
    id: 'severance',
    title: 'Severance',
    image: 'https://images.unsplash.com/photo-1504384308090-c894fdcc538d?q=80&w=600',
    type: 'show',
    progress: 65,
    episode: 'S01E07',
    timestamp: '28:14'
  },
  {
    id: 'dune-part-two',
    title: 'Dune: Part Two',
    image: 'https://images.unsplash.com/photo-1506355683710-bd071c0a5828?q=80&w=600',
    type: 'movie',
    progress: 35,
    timestamp: '54:23'
  },
  {
    id: 'fallout',
    title: 'Fallout',
    image: 'https://images.unsplash.com/photo-1504192010706-dd7f569ee2be?q=80&w=600',
    type: 'show',
    progress: 80,
    episode: 'S01E05',
    timestamp: '42:10'
  },
  {
    id: 'the-gorge',
    title: 'The Gorge',
    image: 'https://images.unsplash.com/photo-1464822759023-fed622ff2c3b?q=80&w=600',
    type: 'movie',
    progress: 10,
    timestamp: '15:43'
  },
  {
    id: 'horizon-line',
    title: 'Horizon Line',
    type: 'show',
    image: 'https://images.unsplash.com/photo-1682687220742-aba13b6e50ba?q=80&w=2070',
    progress: 15,
    season: 1,
    episode: '01',
    timestamp: '38:45 left'
  }
];

// Categories for the browse section
export const categories = [
  {
    id: 'coming-soon',
    title: 'Coming Soon',
    image: 'https://images.unsplash.com/photo-1536440136628-849c177e76a1?q=80&w=2070',
    link: '/coming-soon'
  },
  {
    id: 'sci-fi',
    title: 'Sci-Fi',
    image: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?q=80&w=2070',
    link: '/genres/sci-fi'
  },
  {
    id: 'family',
    title: 'Family & Kids',
    image: 'https://images.unsplash.com/photo-1516627145497-ae6968895b74?q=80&w=2070',
    link: '/genres/family'
  },
  {
    id: 'documentary',
    title: 'Documentary',
    image: 'https://images.unsplash.com/photo-1682687220742-aba13b6e50ba?q=80&w=2070',
    link: '/genres/documentary'
  }
];

// Featured content showcase with rich media
export const showcaseContent = {
  title: "Horizon Line",
  description: "Experience the breathtaking journey of elite climbers facing the world's most formidable peaks. When a sudden storm traps the team at 28,000 feet, their adventure becomes a fight for survival against nature's most brutal elements. This groundbreaking series pushes the boundaries of human endurance and showcases the raw beauty of Earth's highest places.",
  imageUrl: "https://images.unsplash.com/photo-1682687220742-aba13b6e50ba?q=80&w=2070",
  logoUrl: "https://images.unsplash.com/photo-1682687220742-aba13b6e50ba?q=80&w=600",
  videoUrl: "#",
  releaseInfo: "NEW SERIES • 2024",
  highlights: [
    "Award-winning cinematography",
    "Featuring world-renowned climbers",
    "Shot in extreme conditions",
    "Groundbreaking documentary techniques"
  ]
};

/**
 * Get content by ID
 */
export function getContentById(id: string): IContent | null {
  // Search in all content arrays
  const allContent = [
    ...featuredContent,
    ...popularMovies,
    ...popularShows
  ];

  return allContent.find(item => item.id === id) || null;
}

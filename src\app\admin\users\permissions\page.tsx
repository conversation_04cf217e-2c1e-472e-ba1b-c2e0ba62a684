'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Shield,
  Save,
  RefreshCw,
  AlertCircle,
  Info,
  Users,
  UserCheck,
  Settings,
  Loader2,
  Activity
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { toast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { DEFAULT_PERMISSIONS } from '@/lib/permissions';
import BulkPermissionManager from '@/components/admin/BulkPermissionManager';
import PermissionAuditLogs from '@/components/admin/PermissionAuditLogs';

// Define types for permissions
interface Permission {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
}

interface PermissionGroup {
  id: string;
  name: string;
  permissions: Permission[];
}

interface RolePermissions {
  [role: string]: {
    [permissionId: string]: boolean;
  };
}

export default function UserPermissionsPage() {
  const { user, isAdmin } = useAuth();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('user');
  const [permissionGroups, setPermissionGroups] = useState<Record<string, PermissionGroup[]>>({
    user: [],
    moderator: [],
    admin: []
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [saveSuccess, setSaveSuccess] = useState(false);

  // Redirect non-admin users
  useEffect(() => {
    if (user && !isAdmin()) {
      router.push('/');
    }
  }, [user, isAdmin, router]);

  // Fetch default permissions for all roles
  useEffect(() => {
    fetchDefaultPermissions();
  }, []);

  // Fetch default permissions from API
  const fetchDefaultPermissions = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Get userId from localStorage as a fallback for cookie authentication
      const userId = user?.id || localStorage.getItem('userId');

      if (!userId) {
        throw new Error('User ID not found. Please sign in again.');
      }

      // Include userId in the query string
      const response = await fetch(`/api/admin/permissions/defaults?userId=${userId}`, {
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to fetch default permissions (${response.status})`);
      }

      const data = await response.json();

      // Transform API permissions to component state for each role
      const rolePermissions: Record<string, PermissionGroup[]> = {};

      // Process each role's permissions
      ['user', 'moderator', 'admin'].forEach(role => {
        const rolePerms = data.permissions[role] || DEFAULT_PERMISSIONS[role as keyof typeof DEFAULT_PERMISSIONS] || {};

        const groups: PermissionGroup[] = [
          {
            id: 'content',
            name: 'Content Access',
            permissions: [
              {
                id: 'content.view',
                name: 'View Content',
                description: 'Can view all content on the platform',
                enabled: !!rolePerms['content.view']
              },
              {
                id: 'content.premium',
                name: 'Premium Content',
                description: 'Can access premium content',
                enabled: !!rolePerms['content.premium']
              },
              {
                id: 'content.early_access',
                name: 'Early Access',
                description: 'Can access content before official release',
                enabled: !!rolePerms['content.early_access']
              }
            ]
          },
          {
            id: 'social',
            name: 'Social Features',
            permissions: [
              {
                id: 'social.comment',
                name: 'Comment',
                description: 'Can comment on content',
                enabled: !!rolePerms['social.comment']
              },
              {
                id: 'social.rate',
                name: 'Rate Content',
                description: 'Can rate content',
                enabled: !!rolePerms['social.rate']
              },
              {
                id: 'social.share',
                name: 'Share Content',
                description: 'Can share content with others',
                enabled: !!rolePerms['social.share']
              }
            ]
          },
          {
            id: 'account',
            name: 'Account Management',
            permissions: [
              {
                id: 'account.profile',
                name: 'Manage Profile',
                description: 'Can manage their profile settings',
                enabled: !!rolePerms['account.profile']
              },
              {
                id: 'account.subscription',
                name: 'Manage Subscription',
                description: 'Can manage their subscription',
                enabled: !!rolePerms['account.subscription']
              },
              {
                id: 'account.payment',
                name: 'Manage Payment Methods',
                description: 'Can manage their payment methods',
                enabled: !!rolePerms['account.payment']
              }
            ]
          }
        ];

        // Add moderator permissions if applicable
        if (role === 'moderator' || role === 'admin') {
          groups.push({
            id: 'moderator',
            name: 'Moderation Capabilities',
            permissions: [
              {
                id: 'moderator.review_comments',
                name: 'Review Comments',
                description: 'Can review and moderate user comments',
                enabled: !!rolePerms['moderator.review_comments']
              },
              {
                id: 'moderator.approve_content',
                name: 'Approve Content',
                description: 'Can approve user-submitted content',
                enabled: !!rolePerms['moderator.approve_content']
              }
            ]
          });
        }

        // Add admin permissions if applicable
        if (role === 'admin') {
          groups.push({
            id: 'admin',
            name: 'Admin Capabilities',
            permissions: [
              {
                id: 'admin.manage_users',
                name: 'Manage Users',
                description: 'Can manage user accounts',
                enabled: !!rolePerms['admin.manage_users']
              },
              {
                id: 'admin.manage_content',
                name: 'Manage Content',
                description: 'Can manage platform content',
                enabled: !!rolePerms['admin.manage_content']
              },
              {
                id: 'admin.manage_settings',
                name: 'Manage Settings',
                description: 'Can manage platform settings',
                enabled: !!rolePerms['admin.manage_settings']
              },
              {
                id: 'admin.view_analytics',
                name: 'View Analytics',
                description: 'Can view platform analytics',
                enabled: !!rolePerms['admin.view_analytics']
              }
            ]
          });
        }

        rolePermissions[role] = groups;
      });

      setPermissionGroups(rolePermissions);
    } catch (error) {
      console.error('Error fetching default permissions:', error);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');

      // Set default permissions from client-side constants as fallback
      const rolePermissions: Record<string, PermissionGroup[]> = {};

      ['user', 'moderator', 'admin'].forEach(role => {
        const defaultPerms = DEFAULT_PERMISSIONS[role as keyof typeof DEFAULT_PERMISSIONS] || {};

        // Create permission groups similar to the try block
        // This is simplified for brevity - in a real implementation, you'd duplicate the group creation logic
        rolePermissions[role] = [];
      });

      setPermissionGroups(rolePermissions);
    } finally {
      setIsLoading(false);
    }
  };

  // Toggle a single permission
  const togglePermission = (role: string, groupId: string, permissionId: string) => {
    setPermissionGroups(prevGroups => {
      const newGroups = { ...prevGroups };

      // Find the group and permission
      const groupIndex = newGroups[role].findIndex(g => g.id === groupId);
      if (groupIndex === -1) return prevGroups;

      const permissionIndex = newGroups[role][groupIndex].permissions.findIndex(p => p.id === permissionId);
      if (permissionIndex === -1) return prevGroups;

      // Toggle the permission
      newGroups[role][groupIndex].permissions[permissionIndex].enabled =
        !newGroups[role][groupIndex].permissions[permissionIndex].enabled;

      return newGroups;
    });
  };

  // Toggle all permissions in a group
  const toggleGroupPermissions = (role: string, groupId: string, enabled: boolean) => {
    setPermissionGroups(prevGroups => {
      const newGroups = { ...prevGroups };

      // Find the group
      const groupIndex = newGroups[role].findIndex(g => g.id === groupId);
      if (groupIndex === -1) return prevGroups;

      // Toggle all permissions in the group
      newGroups[role][groupIndex].permissions =
        newGroups[role][groupIndex].permissions.map(p => ({
          ...p,
          enabled
        }));

      return newGroups;
    });
  };

  // Check if all permissions in a group are enabled
  const areAllPermissionsEnabled = (role: string, groupId: string) => {
    const group = permissionGroups[role].find(g => g.id === groupId);
    if (!group) return false;

    return group.permissions.every(p => p.enabled);
  };

  // Check if any permissions in a group are enabled
  const areAnyPermissionsEnabled = (role: string, groupId: string) => {
    const group = permissionGroups[role].find(g => g.id === groupId);
    if (!group) return false;

    return group.permissions.some(p => p.enabled);
  };

  // Reset permissions to system defaults
  const resetToDefaults = async () => {
    if (!confirm('Are you sure you want to reset all permissions to system defaults? This cannot be undone.')) {
      return;
    }

    setIsSaving(true);

    try {
      // Get userId from localStorage as a fallback for cookie authentication
      const userId = user?.id || localStorage.getItem('userId');

      if (!userId) {
        throw new Error('User ID not found. Please sign in again.');
      }

      const response = await fetch(`/api/admin/permissions/defaults?userId=${userId}`, {
        method: 'DELETE',
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to reset permissions (${response.status})`);
      }

      toast({
        title: 'Permissions Reset',
        description: 'All role permissions have been reset to system defaults.',
        variant: 'success'
      });

      // Refresh permissions
      await fetchDefaultPermissions();
    } catch (error) {
      console.error('Error resetting permissions:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Save permissions
  const savePermissions = async () => {
    setIsSaving(true);
    setSaveSuccess(false);

    try {
      // Format permissions for API
      const rolePermissionsObj: RolePermissions = {};

      // Process each role
      Object.keys(permissionGroups).forEach(role => {
        rolePermissionsObj[role] = {};

        // Process each group
        permissionGroups[role].forEach(group => {
          // Process each permission
          group.permissions.forEach(permission => {
            rolePermissionsObj[role][permission.id] = permission.enabled;
          });
        });
      });

      // Get userId from localStorage as a fallback for cookie authentication
      const userId = user?.id || localStorage.getItem('userId');

      if (!userId) {
        throw new Error('User ID not found. Please sign in again.');
      }

      // Make API call
      const response = await fetch(`/api/admin/permissions/defaults?userId=${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ permissions: rolePermissionsObj }),
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to update permissions (${response.status})`);
      }

      toast({
        title: 'Permissions Saved',
        description: 'Default role permissions have been updated successfully.',
        variant: 'success'
      });

      setSaveSuccess(true);

      // Hide success message after 3 seconds
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Error saving permissions:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-vista-light">User Permissions</h1>
            <p className="text-vista-light/70">
              Manage default permissions for each user role
            </p>
          </div>
        </div>

        <Card>
          <CardContent className="p-8 flex justify-center items-center">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-vista-blue" />
              <p className="text-vista-light">Loading permissions...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-vista-light">User Permissions</h1>
            <p className="text-vista-light/70">
              Manage default permissions for each user role
            </p>
          </div>
        </div>

        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>

        <Card>
          <CardContent className="p-8 text-center">
            <Button onClick={fetchDefaultPermissions}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-vista-light">User Permissions</h1>
          <p className="text-vista-light/70">
            Manage default permissions for each user role
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={resetToDefaults}
            disabled={isSaving}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Reset to Defaults
          </Button>
          <Button
            onClick={savePermissions}
            disabled={isSaving}
          >
            {isSaving ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Save className="mr-2 h-4 w-4" />
            )}
            Save Changes
          </Button>
        </div>
      </div>

      {saveSuccess && (
        <Alert variant="success" className="bg-green-500/10 text-green-500 border-green-500/20">
          <Info className="h-4 w-4" />
          <AlertTitle>Success</AlertTitle>
          <AlertDescription>
            Default permissions have been saved successfully.
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="defaults" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="defaults" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Default Permissions
          </TabsTrigger>
          <TabsTrigger value="bulk" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Bulk Management
          </TabsTrigger>
          <TabsTrigger value="audit" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Audit Logs
          </TabsTrigger>
        </TabsList>

        <TabsContent value="defaults">
          <Card>
            <CardHeader>
              <CardTitle className="text-vista-light">Default Role Permissions</CardTitle>
              <CardDescription>
                Set default permissions for each user role. These permissions will be applied to new users and when resetting a user's permissions.
              </CardDescription>
            </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-6">
              <TabsTrigger value="user" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Regular Users
              </TabsTrigger>
              <TabsTrigger value="moderator" className="flex items-center gap-2">
                <UserCheck className="h-4 w-4" />
                Moderators
              </TabsTrigger>
              <TabsTrigger value="admin" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Administrators
              </TabsTrigger>
            </TabsList>

            {/* User Permissions Tab */}
            <TabsContent value="user" className="space-y-6">
              {permissionGroups.user.map(group => (
                <div key={group.id} className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium text-vista-light">{group.name}</h3>
                    <div className="flex items-center gap-2">
                      <Label htmlFor={`user-group-${group.id}`} className="text-sm text-vista-light/70">
                        {areAllPermissionsEnabled('user', group.id)
                          ? 'All Enabled'
                          : areAnyPermissionsEnabled('user', group.id)
                            ? 'Partially Enabled'
                            : 'All Disabled'}
                      </Label>
                      <Switch
                        id={`user-group-${group.id}`}
                        checked={areAllPermissionsEnabled('user', group.id)}
                        onCheckedChange={(checked) => toggleGroupPermissions('user', group.id, checked)}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    {group.permissions.map(permission => (
                      <div
                        key={permission.id}
                        className="flex items-center justify-between p-2 rounded-md bg-vista-dark"
                      >
                        <div>
                          <p className="font-medium text-vista-light">{permission.name}</p>
                          <p className="text-vista-light/70 text-sm">{permission.description}</p>
                        </div>
                        <Switch
                          id={`user-${permission.id}`}
                          checked={permission.enabled}
                          onCheckedChange={() => togglePermission('user', group.id, permission.id)}
                        />
                      </div>
                    ))}
                  </div>

                  {group !== permissionGroups.user[permissionGroups.user.length - 1] && (
                    <Separator className="my-4" />
                  )}
                </div>
              ))}
            </TabsContent>

            {/* Moderator Permissions Tab */}
            <TabsContent value="moderator" className="space-y-6">
              {permissionGroups.moderator.map(group => (
                <div key={group.id} className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium text-vista-light">{group.name}</h3>
                    <div className="flex items-center gap-2">
                      <Label htmlFor={`moderator-group-${group.id}`} className="text-sm text-vista-light/70">
                        {areAllPermissionsEnabled('moderator', group.id)
                          ? 'All Enabled'
                          : areAnyPermissionsEnabled('moderator', group.id)
                            ? 'Partially Enabled'
                            : 'All Disabled'}
                      </Label>
                      <Switch
                        id={`moderator-group-${group.id}`}
                        checked={areAllPermissionsEnabled('moderator', group.id)}
                        onCheckedChange={(checked) => toggleGroupPermissions('moderator', group.id, checked)}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    {group.permissions.map(permission => (
                      <div
                        key={permission.id}
                        className="flex items-center justify-between p-2 rounded-md bg-vista-dark"
                      >
                        <div>
                          <p className="font-medium text-vista-light">{permission.name}</p>
                          <p className="text-vista-light/70 text-sm">{permission.description}</p>
                        </div>
                        <Switch
                          id={`moderator-${permission.id}`}
                          checked={permission.enabled}
                          onCheckedChange={() => togglePermission('moderator', group.id, permission.id)}
                        />
                      </div>
                    ))}
                  </div>

                  {group !== permissionGroups.moderator[permissionGroups.moderator.length - 1] && (
                    <Separator className="my-4" />
                  )}
                </div>
              ))}
            </TabsContent>

            {/* Admin Permissions Tab */}
            <TabsContent value="admin" className="space-y-6">
              {permissionGroups.admin.map(group => (
                <div key={group.id} className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium text-vista-light">{group.name}</h3>
                    <div className="flex items-center gap-2">
                      <Label htmlFor={`admin-group-${group.id}`} className="text-sm text-vista-light/70">
                        {areAllPermissionsEnabled('admin', group.id)
                          ? 'All Enabled'
                          : areAnyPermissionsEnabled('admin', group.id)
                            ? 'Partially Enabled'
                            : 'All Disabled'}
                      </Label>
                      <Switch
                        id={`admin-group-${group.id}`}
                        checked={areAllPermissionsEnabled('admin', group.id)}
                        onCheckedChange={(checked) => toggleGroupPermissions('admin', group.id, checked)}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    {group.permissions.map(permission => (
                      <div
                        key={permission.id}
                        className="flex items-center justify-between p-2 rounded-md bg-vista-dark"
                      >
                        <div>
                          <p className="font-medium text-vista-light">{permission.name}</p>
                          <p className="text-vista-light/70 text-sm">{permission.description}</p>
                        </div>
                        <Switch
                          id={`admin-${permission.id}`}
                          checked={permission.enabled}
                          onCheckedChange={() => togglePermission('admin', group.id, permission.id)}
                        />
                      </div>
                    ))}
                  </div>

                  {group !== permissionGroups.admin[permissionGroups.admin.length - 1] && (
                    <Separator className="my-4" />
                  )}
                </div>
              ))}
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="flex justify-between border-t border-vista-light/10 pt-6">
          <div className="text-sm text-vista-light/70">
            <p>Changes will apply to new users and when resetting a user's permissions.</p>
          </div>
          <Button
            onClick={savePermissions}
            disabled={isSaving}
          >
            {isSaving ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Save className="mr-2 h-4 w-4" />
            )}
            Save Changes
          </Button>
        </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="bulk">
          <BulkPermissionManager onSuccess={() => toast({
            title: 'Permissions Updated',
            description: 'User permissions have been updated successfully.',
            variant: 'success'
          })} />
        </TabsContent>

        <TabsContent value="audit">
          <PermissionAuditLogs limit={50} />
        </TabsContent>
      </Tabs>
    </div>
  );
}

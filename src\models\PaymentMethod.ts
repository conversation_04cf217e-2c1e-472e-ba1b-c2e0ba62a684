import mongoose, { Document, Schema } from 'mongoose';

export interface IPaymentMethod extends Document {
  userId: mongoose.Types.ObjectId;
  type: 'credit_card' | 'paypal' | 'bank_account' | 'other';
  isDefault: boolean;
  lastFour?: string;
  expiryMonth?: number;
  expiryYear?: number;
  cardBrand?: string;
  cardholderName?: string;
  billingAddress?: {
    line1?: string;
    line2?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
  };
  paypalEmail?: string;
  bankName?: string;
  bankAccountLast4?: string;
  providerPaymentMethodId?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

const PaymentMethodSchema = new Schema<IPaymentMethod>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true
    },
    type: {
      type: String,
      enum: ['credit_card', 'paypal', 'bank_account', 'other'],
      required: true
    },
    isDefault: {
      type: Boolean,
      default: false
    },
    lastFour: {
      type: String
    },
    expiryMonth: {
      type: Number
    },
    expiryYear: {
      type: Number
    },
    cardBrand: {
      type: String
    },
    cardholderName: {
      type: String
    },
    billingAddress: {
      line1: { type: String },
      line2: { type: String },
      city: { type: String },
      state: { type: String },
      postalCode: { type: String },
      country: { type: String }
    },
    paypalEmail: {
      type: String
    },
    bankName: {
      type: String
    },
    bankAccountLast4: {
      type: String
    },
    providerPaymentMethodId: {
      type: String
    },
    metadata: {
      type: Schema.Types.Mixed
    }
  },
  {
    timestamps: true
  }
);

// Create indexes for efficient querying
PaymentMethodSchema.index({ userId: 1, isDefault: 1 });
PaymentMethodSchema.index({ userId: 1, type: 1 });

// Create the model if it doesn't exist already
const PaymentMethod = mongoose.models.PaymentMethod || 
                      mongoose.model<IPaymentMethod>('PaymentMethod', PaymentMethodSchema);

export default PaymentMethod;

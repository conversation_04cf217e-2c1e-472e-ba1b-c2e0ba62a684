'use client';

import * as React from 'react';
import { format } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { CustomCalendar } from '@/components/ui/custom-calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

interface DatePickerProps {
  date?: Date | null;
  onSelect?: (date: Date | undefined) => void;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
}

export function DatePicker({
  date,
  onSelect,
  className,
  placeholder = "Select date",
  disabled = false
}: DatePickerProps) {
  const [open, setOpen] = React.useState(false);
  const [selectedDate, setSelectedDate] = React.useState<Date | undefined>(date || undefined);

  // Update internal state when date prop changes
  React.useEffect(() => {
    setSelectedDate(date || undefined);
  }, [date]);

  // Function to handle date selection
  const handleSelect = (date: Date | undefined) => {
    console.log("DatePicker handleSelect called with date:", date);

    try {
      if (date) {
        // Set time to end of day (23:59:59)
        const endOfDay = new Date(date);
        endOfDay.setHours(23, 59, 59, 999);

        // Update internal state
        setSelectedDate(endOfDay);

        // Call the onSelect callback
        if (onSelect) {
          onSelect(endOfDay);
        }
      } else {
        // Clear the date
        setSelectedDate(undefined);

        // Call the onSelect callback
        if (onSelect) {
          onSelect(undefined);
        }
      }
    } catch (error) {
      console.error("Error in handleSelect:", error);
    }

    // Close the popover
    setOpen(false);
  };

  return (
    <Popover
      open={open}
      onOpenChange={setOpen}
    >
      <PopoverTrigger asChild>
        <Button
          type="button" // Explicitly set type to button to prevent form submission
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal",
            !selectedDate && "text-vista-light/50",
            className
          )}
          disabled={disabled}
          onClick={(e) => {
            e.preventDefault(); // Prevent form submission
            e.stopPropagation(); // Prevent event bubbling
            setOpen(true); // Explicitly open the calendar
          }}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {selectedDate ? format(selectedDate, "PPP") : <span>{placeholder}</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0 bg-vista-dark-lighter border border-vista-light/20 rounded-xl shadow-lg shadow-vista-blue/10" align="start">
        <div className="p-3 bg-gradient-to-b from-vista-dark to-vista-dark-lighter rounded-xl">
          <div className="flex justify-between items-center mb-2">
            <h4 className="text-sm font-medium text-vista-light">Select Expiration Date</h4>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="h-7 w-7 p-0 text-vista-light/70 hover:text-vista-light"
              onClick={() => setOpen(false)}
            >
              ✕
            </Button>
          </div>
          <CustomCalendar
            mode="single"
            selected={selectedDate}
            onSelect={handleSelect}
            defaultMonth={selectedDate || new Date()} // Set default month to selected date or current month
            modifiers={{
              disabled: [
                { before: new Date() } // Disable dates before today
              ]
            }}
            className="bg-transparent text-vista-light"
            showOutsideDays={true}
          />
          <div className="mt-2 flex justify-between">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="text-vista-light/70 hover:text-vista-light"
              onClick={() => {
                handleSelect(undefined);
              }}
            >
              Clear
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              className="bg-vista-blue/20 text-vista-blue hover:bg-vista-blue/30"
              onClick={() => {
                // If no date is selected, use today
                if (!selectedDate) {
                  handleSelect(new Date());
                } else {
                  setOpen(false);
                }
              }}
            >
              {selectedDate ? "Done" : "Use Today"}
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}

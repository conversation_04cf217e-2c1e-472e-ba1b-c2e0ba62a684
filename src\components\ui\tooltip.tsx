"use client"

import * as React from "react"
import * as TooltipPrimitive from "@radix-ui/react-tooltip"

import { cn } from "@/lib/utils"

// Use a randomized provider key to ensure tooltip state is reset on navigation
const TooltipProvider = ({ children, ...props }: TooltipPrimitive.TooltipProviderProps) => {
  // Generate a unique ID on component mount - this forces the provider to remount when navigating
  const [uniqueId] = React.useState(() => `tooltip-provider-${Math.random().toString(36).substring(2, 9)}`)
  
  return (
    <TooltipPrimitive.Provider key={uniqueId} {...props}>
      {children}
    </TooltipPrimitive.Provider>
  )
}

const Tooltip = TooltipPrimitive.Root

const TooltipTrigger = TooltipPrimitive.Trigger

const TooltipContent = React.forwardRef<
  React.ElementRef<typeof TooltipPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content> & {
    variant?: "default" | "dark" | "light"
  }
>(({ className, variant = "default", sideOffset = 4, ...props }, ref) => {
  const variantClasses = {
    default: "bg-vista-dark-lighter text-vista-light border-vista-light/20",
    dark: "bg-black/90 text-vista-light border-vista-light/10",
    light: "bg-white text-slate-950 border-slate-200"
  }

  return (
    <TooltipPrimitive.Content
      ref={ref}
      sideOffset={sideOffset}
      className={cn(
        "z-50 overflow-hidden rounded-md border px-3 py-1.5 text-sm shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
        variantClasses[variant],
        className
      )}
      {...props}
    />
  )
})
TooltipContent.displayName = TooltipPrimitive.Content.displayName

// Composite component for easier usage with improved stability
const TooltipElement = React.memo(({
  children,
  content,
  variant = "default",
  delayDuration = 300,
  ...props
}: {
  children: React.ReactNode
  content: React.ReactNode
  variant?: "default" | "dark" | "light"
  delayDuration?: number
} & Omit<React.ComponentPropsWithoutRef<typeof TooltipContent>, "children" | "content" | "variant">
) => {
  // Generate a unique ID for this tooltip instance
  const [tooltipId] = React.useState(() => `tooltip-${Math.random().toString(36).substring(2, 9)}`)
  
  return (
    <TooltipProvider delayDuration={delayDuration}>
      <Tooltip key={tooltipId}>
        <TooltipTrigger asChild>
          {children}
        </TooltipTrigger>
        <TooltipContent variant={variant} {...props}>
          {content}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
})
TooltipElement.displayName = 'TooltipElement';

export { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider, TooltipElement }

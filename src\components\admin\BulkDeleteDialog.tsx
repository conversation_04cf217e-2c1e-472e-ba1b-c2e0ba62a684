'use client';

import { useState } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

import { Badge } from '@/components/ui/badge';
import { Trash2, Loader2, AlertTriangle, Users } from 'lucide-react';

export interface BulkDeleteDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void | Promise<void>;
  title?: string;
  itemCount: number;
  itemType: string; // e.g., "user", "post", "comment"
  isLoading?: boolean;
  details?: string[];
  warningMessage?: string;
}

export default function BulkDeleteDialog({
  open,
  onOpenChange,
  onConfirm,
  title,
  itemCount,
  itemType,
  isLoading = false,
  details = [],
  warningMessage
}: BulkDeleteDialogProps) {
  const plural = itemCount > 1 ? 's' : '';
  const displayTitle = title || `Delete ${itemCount} ${itemType}${plural}`;

  const handleConfirm = async () => {
    if (isLoading) return;
    
    try {
      await onConfirm();
    } catch (error) {
      console.error('Bulk delete failed:', error);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    onOpenChange(newOpen);
  };

  return (
    <AlertDialog open={open} onOpenChange={handleOpenChange}>
      <AlertDialogContent className="bg-vista-dark border-vista-dark-lighter max-w-lg w-full p-0 overflow-hidden">
        <div className="bg-red-600/10 p-6 flex flex-col items-center justify-center border-b border-vista-dark-lighter">
          <div className="bg-red-600/20 p-3 rounded-full mb-4">
            <Trash2 className="h-8 w-8 text-red-500" />
          </div>
          <AlertDialogHeader className="text-center space-y-1">
            <AlertDialogTitle className="text-xl text-vista-light">{displayTitle}</AlertDialogTitle>
            <div className="flex items-center justify-center gap-2 mt-2">
              <Users className="h-5 w-5 text-vista-light/70" />
              <Badge variant="destructive" className="bg-red-600 text-white">
                {itemCount} {itemType}{plural} selected
              </Badge>
            </div>
          </AlertDialogHeader>
        </div>

        <div className="p-6 space-y-4">
          <AlertDialogDescription className="text-vista-light/80">
            You are about to permanently delete {itemCount} {itemType}{plural}. 
            This action <span className="text-red-500 font-semibold">cannot be undone</span>.
          </AlertDialogDescription>

          {/* Warning message */}
          {warningMessage && (
            <div className="flex items-start gap-2 bg-yellow-500/10 p-3 rounded-md border border-yellow-500/20">
              <AlertTriangle className="h-5 w-5 text-yellow-500 flex-shrink-0 mt-0.5" />
              <div className="text-sm text-vista-light/90">
                {warningMessage}
              </div>
            </div>
          )}

          {/* Additional details */}
          {details.length > 0 && (
            <div className="bg-vista-dark-lighter p-3 rounded-md border border-vista-dark-lighter">
              <div className="text-sm font-medium text-vista-light mb-2">
                Items to be deleted:
              </div>
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {details.slice(0, 10).map((detail, index) => (
                  <div key={index} className="text-sm text-vista-light/70 flex items-center gap-2">
                    <div className="w-1 h-1 bg-vista-light/50 rounded-full flex-shrink-0"></div>
                    {detail}
                  </div>
                ))}
                {details.length > 10 && (
                  <div className="text-sm text-vista-light/50 italic">
                    ... and {details.length - 10} more {itemType}{details.length - 10 > 1 ? 's' : ''}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Critical warning for bulk operations */}
          <div className="flex items-start gap-2 bg-red-500/10 p-4 rounded-md border border-red-500/20">
            <AlertTriangle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
            <div>
              <div className="font-semibold text-red-500 mb-1">Permanent Deletion Warning</div>
              <div className="text-sm text-vista-light/90">
                All associated data including profiles, history, and preferences will be permanently removed. 
                This bulk operation affects multiple {itemType}{plural} at once.
              </div>
            </div>
          </div>



          <AlertDialogFooter className="flex flex-col sm:flex-row gap-3 sm:gap-2 mt-6">
            <AlertDialogCancel
              className="bg-vista-dark-lighter text-vista-light hover:bg-vista-dark-lighter/80 hover:text-vista-light w-full sm:w-auto"
              disabled={isLoading}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-600 text-white hover:bg-red-700 focus:ring-red-600 w-full sm:w-auto disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={(e) => {
                e.preventDefault();
                handleConfirm();
              }}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting {itemCount} {itemType}{plural}...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete {itemCount} {itemType}{plural}
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </div>
      </AlertDialogContent>
    </AlertDialog>
  );
} 
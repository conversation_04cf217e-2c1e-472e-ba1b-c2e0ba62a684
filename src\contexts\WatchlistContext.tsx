'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { ContentCardType } from '@/lib/content-utils';
import { useProfiles } from '@/contexts/ProfileContext';
import { useAuth } from '@/contexts/AuthContext';
import axios from 'axios';

// Define the watchlist item type
export interface WatchlistItem extends ContentCardType {
  addedDate: string;
  genres?: string[];
}

// Define the context type
interface WatchlistContextType {
  watchlist: WatchlistItem[];
  addToWatchlist: (item: ContentCardType) => void;
  removeFromWatchlist: (id: string) => void;
  isInWatchlist: (id: string) => boolean;
  clearWatchlist: () => void;
  isLoading: boolean;
}

// Create the context with default values
const WatchlistContext = createContext<WatchlistContextType>({
  watchlist: [],
  addToWatchlist: () => {},
  removeFromWatchlist: () => {},
  isInWatchlist: () => false,
  clearWatchlist: () => {},
  isLoading: false,
});

// Custom hook to use the watchlist context
export const useWatchlist = () => useContext(WatchlistContext);

// Provider component
export const WatchlistProvider = ({ children }: { children: ReactNode }) => {
  const [watchlist, setWatchlist] = useState<WatchlistItem[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { activeProfile } = useProfiles();
  const { user } = useAuth();

  // Function to sync local watchlist with server
  const syncWatchlistWithServer = async () => {
    if (!activeProfile || !user) return;

    setIsLoading(true);
    try {
      // Fetch the profile's watchlist from the server
      const response = await axios.get(`/api/profiles/${activeProfile.id}/my-list`);

      if (response.data && response.data.myList) {
        // For each item in the server watchlist, check if we have it locally
        const serverIds = response.data.myList;

        // Filter local items not in server list
        const localOnlyItems = watchlist.filter(item => !serverIds.includes(item.id));

        // Add local items to server
        for (const item of localOnlyItems) {
          await axios.post(`/api/profiles/${activeProfile.id}/my-list`, {
            contentId: item.id
          });
        }

        // Reload the list to ensure it's in sync
        await loadWatchlist();
      }
    } catch (error) {
      console.error('Error syncing watchlist with server:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to load watchlist from both localStorage and server
  const loadWatchlist = async () => {
    setIsLoading(true);
    try {
      // Load local watchlist
      let localWatchlist: WatchlistItem[] = [];
      try {
        const savedWatchlist = localStorage.getItem('streamvista-watchlist');
        if (savedWatchlist) {
          localWatchlist = JSON.parse(savedWatchlist);

          // Validate all items to ensure no empty image paths
          localWatchlist = localWatchlist.map(item => ({
            ...item,
            imagePath: (!item.imagePath || item.imagePath.trim() === '')
              ? '/favicon.svg' // Use favicon as fallback since we know it exists
              : item.imagePath
          }));
        }
      } catch (error) {
        console.error('Error loading watchlist from localStorage:', error);
      }

      // If user is authenticated and has an active profile, load from server too
      if (activeProfile && user) {
        try {
          const response = await axios.get(`/api/profiles/${activeProfile.id}/my-list`);

          // Only process server data if we have content details for the items
          // Don't create placeholder items that would show up in an empty list
          if (response.data && response.data.contentDetails && Array.isArray(response.data.contentDetails)) {
            // Use the detailed content data from the server instead of just IDs
            const serverItems = response.data.contentDetails;

            for (const serverItem of serverItems) {
              // Only add items that have title and other required information
              if (serverItem.id && serverItem.title) {
                // Check if this item already exists in the local list
                if (!localWatchlist.some(item => item.id === serverItem.id)) {
                  // Add the item with proper data
                  localWatchlist.push({
                    id: serverItem.id,
                    title: serverItem.title,
                    type: serverItem.type === 'show' ? 'shows' : 'movies',
                    imagePath: serverItem.imagePath || '/favicon.svg',
                    year: serverItem.year || '',
                    addedDate: serverItem.addedDate || new Date().toISOString(),
                    genres: serverItem.genres || ['Drama', 'Action']
                  });
                }
              }
            }
          } else if (response.data && response.data.myList && Array.isArray(response.data.myList)) {
            // We have IDs but no detailed content - skip adding placeholders
            console.log('[DEBUG] Server returned IDs but no content details. Skipping placeholder creation.');
          }
        } catch (error) {
          console.error('Error loading watchlist from server:', error);
        }
      }

      setWatchlist(localWatchlist);
    } finally {
      setIsInitialized(true);
      setIsLoading(false);
    }
  };

  // Load watchlist on component mount and when user/profile changes
  useEffect(() => {
    loadWatchlist();
  }, [activeProfile?.id, user?.id]);

  // Sync with server when watchlist changes
  useEffect(() => {
    if (isInitialized && activeProfile && user) {
      // Don't sync on initial load, only when watchlist changes after initialization
      syncWatchlistWithServer();
    }
  }, [isInitialized, watchlist.length]);

  // Save watchlist to localStorage whenever it changes
  useEffect(() => {
    if (isInitialized) {
      localStorage.setItem('streamvista-watchlist', JSON.stringify(watchlist));
    }
  }, [watchlist, isInitialized]);

  // Add an item to the watchlist
  const addToWatchlist = async (item: ContentCardType) => {
    // Enhanced debug logging to track type of items being added
    console.log(`[DEBUG] Adding to watchlist - Item: ${item.title}, Type: ${item.type}`);
    console.log(`[DEBUG] Full item data:`, item);

    if (isInWatchlist(item.id)) {
      // Show already in list toast
      toast({
        title: `"${item.title}" is already in My List`
      });
      return;
    }

    // Determine default genres for watchlist items
    const defaultGenres = item.type === 'shows' ? ['Drama', 'Action'] : ['Action', 'Adventure'];

    // Ensure imagePath is never empty, null, or undefined
    const validatedImagePath = (!item.imagePath || item.imagePath.trim() === '')
      ? '/favicon.svg' // Use favicon as fallback since we know it exists
      : item.imagePath;

    // Ensure type is correctly set (defensive programming)
    const validatedType = item.type === 'shows' ? 'shows' : 'movies';
    console.log(`[DEBUG] Validated type for ${item.title}: ${validatedType}`);

    const watchlistItem: WatchlistItem = {
      ...item,
      type: validatedType, // Use the validated type
      imagePath: validatedImagePath,
      addedDate: new Date().toISOString(),
      genres: defaultGenres
    };

    // Add to local state
    setWatchlist(prev => [watchlistItem, ...prev]);

    // If user is logged in, also add to server
    if (activeProfile && user) {
      try {
        await axios.post(`/api/profiles/${activeProfile.id}/my-list`, {
          contentId: item.id
        });
      } catch (error) {
        console.error('Error adding to server watchlist:', error);
      }
    }

    // Show toast notification
    toast({
      title: `Added "${item.title}" to My List`
    });
  };

  // Remove an item from the watchlist
  const removeFromWatchlist = (id: string) => {
    try {
      // Find the item before removing it
      const itemToRemove = watchlist.find(item => item.id === id);

      if (!itemToRemove) {
        console.log(`Item with ID ${id} not found in watchlist`);
        return null;
      }

      console.log(`Removing item from watchlist: ${itemToRemove.title} (${id})`);

      // Use a functional update to ensure we're working with the latest state
      setWatchlist(prev => {
        // Check if the item is already removed to prevent unnecessary updates
        if (!prev.some(item => item.id === id)) {
          console.log(`Item ${id} already removed from watchlist`);
          return prev;
        }

        // Filter out the item to be removed
        return prev.filter(item => item.id !== id);
      });

      // If user is logged in, also remove from server in the background
      if (activeProfile && user) {
        // Use a separate function for the API call to avoid closure issues
        const removeFromServer = async () => {
          try {
            console.log(`Removing item ${id} from server watchlist`);
            // Use URL parameters instead of request body for DELETE
            const response = await axios.delete(`/api/profiles/${activeProfile.id}/my-list?contentId=${id}`);
            console.log(`Server response for removing item ${id}:`, response.data);
          } catch (error) {
            console.error(`Error removing item ${id} from server watchlist:`, error);
            // Log more details about the error
            if (error && typeof error === 'object' && 'response' in error) {
              const axiosError = error as { response: { data: any, status: number } };
              console.error('Error response:', axiosError.response.data);
              console.error('Error status:', axiosError.response.status);
            }
          }
        };

        // Execute the server removal after a short delay
        setTimeout(removeFromServer, 100);
      }

      // Show toast notification if item existed
      if (itemToRemove) {
        // Show toast after a delay to ensure UI updates first
        setTimeout(() => {
          toast({
            title: `Removed "${itemToRemove.title}" from My List`
          });
        }, 300);
      }

      return itemToRemove; // Return the removed item for reference
    } catch (error) {
      console.error('Error in removeFromWatchlist:', error);
      return null;
    }
  };

  // Check if an item is in the watchlist
  const isInWatchlist = (id: string) => {
    return watchlist.some(item => item.id === id);
  };

  // Clear the entire watchlist
  const clearWatchlist = async () => {
    setWatchlist([]);

    // If user is logged in, we should clear the server list too
    // This would require a new API endpoint to clear the entire list
    // As a workaround, we can remove each item individually
    if (activeProfile && user) {
      try {
        // This is not ideal but works as a temporary solution
        // In a real app, you'd create a dedicated endpoint for this operation
        for (const item of watchlist) {
          try {
            // Use URL parameters instead of request body for DELETE
            await axios.delete(`/api/profiles/${activeProfile.id}/my-list?contentId=${item.id}`);
          } catch (deleteError) {
            console.error(`Error removing item ${item.id} from server watchlist:`, deleteError);
            // Continue with the next item even if one fails
          }
        }
      } catch (error) {
        console.error('Error clearing server watchlist:', error);
      }
    }

    toast({
      title: "Watchlist Cleared",
      description: "All items have been removed from your list.",
    });
  };

  return (
    <WatchlistContext.Provider
      value={{
        watchlist,
        addToWatchlist,
        removeFromWatchlist,
        isInWatchlist,
        clearWatchlist,
        isLoading
      }}
    >
      {children}
    </WatchlistContext.Provider>
  );
};

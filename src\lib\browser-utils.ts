/**
 * Utility functions for safely handling browser-specific code
 */

/**
 * Check if code is running in a browser environment
 * Uses multiple detection methods for maximum reliability
 */
export const isBrowser = (): boolean => {
  // Method 1: Check for window and document objects
  const hasWindowAndDocument = typeof window !== 'undefined' && typeof document !== 'undefined';

  // Method 2: Check for global flag set in _document.tsx
  const hasGlobalFlag = typeof window !== 'undefined' && (window as any).__IS_BROWSER__ === true;

  // Method 3: Check for process.browser (Next.js specific)
  const isNextJsBrowser = typeof process !== 'undefined' &&
                         process.browser === true;

  // Method 4: Check for browser-specific APIs
  const hasBrowserAPIs = typeof navigator !== 'undefined' ||
                        typeof localStorage !== 'undefined';

  // Return true if any method confirms browser environment
  return hasWindowAndDocument || hasGlobalFlag || isNextJsBrowser || hasBrowserAPIs;
};

/**
 * Safely access window object
 * @returns window object or undefined
 */
export const safeWindow = (): Window | undefined => {
  return isBrowser() ? window : undefined;
};

/**
 * Safely access document object
 * @returns document object or undefined
 */
export const safeDocument = (): Document | undefined => {
  return isBrowser() ? document : undefined;
};

/**
 * Safely access navigator object
 * @returns navigator object or undefined
 */
export const safeNavigator = (): Navigator | undefined => {
  return isBrowser() ? navigator : undefined;
};

/**
 * Safely access localStorage object
 * @returns localStorage object or undefined
 */
export const safeLocalStorage = (): Storage | undefined => {
  if (isBrowser()) {
    try {
      return localStorage;
    } catch (e) {
      console.error('Error accessing localStorage:', e);
      return undefined;
    }
  }
  return undefined;
};

/**
 * Safely access sessionStorage object
 * @returns sessionStorage object or undefined
 */
export const safeSessionStorage = (): Storage | undefined => {
  if (isBrowser()) {
    try {
      return sessionStorage;
    } catch (e) {
      console.error('Error accessing sessionStorage:', e);
      return undefined;
    }
  }
  return undefined;
};

/**
 * Safely execute a function only in browser environment
 * @param fn Function to execute
 * @param fallback Optional fallback value to return in non-browser environments
 */
export const onlyInBrowser = <T>(fn: () => T, fallback?: T): T | undefined => {
  if (isBrowser()) {
    try {
      return fn();
    } catch (e) {
      console.error('Error executing browser-only function:', e);
      return fallback;
    }
  }
  return fallback;
};

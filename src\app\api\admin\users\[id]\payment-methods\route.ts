import { NextRequest, NextResponse } from 'next/server';

/**
 * GET /api/admin/users/[id]/payment-methods
 * Get payment methods for a specific user
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the user ID from the cookie directly
    const userId = request.cookies.get('userId')?.value;
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const UserSchema = new mongoose.default.Schema({
      name: String,
      email: String,
      role: String
    });

    const User = mongoose.default.models.User ||
                mongoose.default.model('User', UserSchema);

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the PaymentMethod schema directly
    const PaymentMethodSchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      isDefault: Boolean,
      lastFour: String,
      expiryMonth: Number,
      expiryYear: Number,
      cardBrand: String,
      cardholderName: String,
      billingAddress: mongoose.default.Schema.Types.Mixed,
      paypalEmail: String,
      bankName: String,
      bankAccountLast4: String,
      providerPaymentMethodId: String
    }, {
      timestamps: true
    });

    // Get the PaymentMethod model
    const PaymentMethod = mongoose.default.models.PaymentMethod ||
                         mongoose.default.model('PaymentMethod', PaymentMethodSchema);

    // Validate user ID
    if (!mongoose.default.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
    }

    // Check if target user exists
    const targetUser = await User.findById(params.id);
    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get payment methods
    const paymentMethods = await PaymentMethod.find({
      userId: params.id
    }).sort({ isDefault: -1, createdAt: -1 });

    // Format response
    const response = paymentMethods.map((method: any) => ({
      id: method._id.toString(),
      type: method.type,
      isDefault: method.isDefault,
      lastFour: method.lastFour,
      expiryMonth: method.expiryMonth,
      expiryYear: method.expiryYear,
      cardBrand: method.cardBrand,
      cardholderName: method.cardholderName,
      paypalEmail: method.paypalEmail,
      bankName: method.bankName,
      createdAt: method.createdAt
    }));

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching user payment methods:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user payment methods', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/users/[id]/payment-methods
 * Add a payment method for a specific user
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the user ID from the cookie directly
    const userId = request.cookies.get('userId')?.value;
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const UserSchema = new mongoose.default.Schema({
      name: String,
      email: String,
      role: String
    });

    const User = mongoose.default.models.User ||
                mongoose.default.model('User', UserSchema);

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the PaymentMethod schema directly
    const PaymentMethodSchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      isDefault: Boolean,
      lastFour: String,
      expiryMonth: Number,
      expiryYear: Number,
      cardBrand: String,
      cardholderName: String,
      billingAddress: mongoose.default.Schema.Types.Mixed,
      paypalEmail: String,
      bankName: String,
      bankAccountLast4: String,
      providerPaymentMethodId: String
    }, {
      timestamps: true
    });

    // Get the PaymentMethod model
    const PaymentMethod = mongoose.default.models.PaymentMethod ||
                         mongoose.default.model('PaymentMethod', PaymentMethodSchema);

    // Define the UserActivity schema directly
    const UserActivitySchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      action: String,
      details: String,
      ipAddress: String,
      userAgent: String,
      timestamp: { type: Date, default: Date.now },
      metadata: mongoose.default.Schema.Types.Mixed
    }, {
      timestamps: true
    });

    // Get the UserActivity model
    const UserActivity = mongoose.default.models.UserActivity ||
                        mongoose.default.model('UserActivity', UserActivitySchema);

    // Validate user ID
    if (!mongoose.default.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
    }

    // Check if target user exists
    const targetUser = await User.findById(params.id);
    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get request data
    const data = await request.json();

    // Validate payment method data
    if (!data.type || !['credit_card', 'paypal', 'bank_account', 'other'].includes(data.type)) {
      return NextResponse.json(
        { error: 'Invalid payment method type' },
        { status: 400 }
      );
    }

    // If setting as default, unset any existing default
    if (data.isDefault) {
      await PaymentMethod.updateMany(
        { userId: params.id, isDefault: true },
        { $set: { isDefault: false } }
      );
    }

    // Create new payment method
    const paymentMethod = await PaymentMethod.create({
      userId: params.id,
      type: data.type,
      isDefault: data.isDefault || false,
      lastFour: data.lastFour,
      expiryMonth: data.expiryMonth,
      expiryYear: data.expiryYear,
      cardBrand: data.cardBrand,
      cardholderName: data.cardholderName,
      billingAddress: data.billingAddress,
      paypalEmail: data.paypalEmail,
      bankName: data.bankName,
      bankAccountLast4: data.bankAccountLast4,
      providerPaymentMethodId: data.providerPaymentMethodId
    });

    // Log admin activity directly
    await UserActivity.create({
      userId: new mongoose.default.Types.ObjectId(userId),
      type: 'admin',
      action: 'add_payment_method',
      details: `Admin added payment method for user: ${targetUser.name} (${targetUser.email})`,
      ipAddress: request.ip || request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      timestamp: new Date(),
      metadata: {
        userId: params.id,
        paymentMethodId: paymentMethod._id.toString(),
        type: data.type
      }
    });

    // Return created payment method
    return NextResponse.json({
      id: paymentMethod._id.toString(),
      type: paymentMethod.type,
      isDefault: paymentMethod.isDefault,
      lastFour: paymentMethod.lastFour,
      expiryMonth: paymentMethod.expiryMonth,
      expiryYear: paymentMethod.expiryYear,
      cardBrand: paymentMethod.cardBrand,
      cardholderName: paymentMethod.cardholderName,
      paypalEmail: paymentMethod.paypalEmail,
      bankName: paymentMethod.bankName,
      createdAt: paymentMethod.createdAt
    });
  } catch (error) {
    console.error('Error adding payment method:', error);
    return NextResponse.json(
      { error: 'Failed to add payment method', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/users/[id]/payment-methods
 * Delete a payment method for a specific user
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the user ID from the cookie directly
    const userId = request.cookies.get('userId')?.value;
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const UserSchema = new mongoose.default.Schema({
      name: String,
      email: String,
      role: String
    });

    const User = mongoose.default.models.User ||
                mongoose.default.model('User', UserSchema);

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the PaymentMethod schema directly
    const PaymentMethodSchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      isDefault: Boolean,
      lastFour: String,
      expiryMonth: Number,
      expiryYear: Number,
      cardBrand: String,
      cardholderName: String,
      billingAddress: mongoose.default.Schema.Types.Mixed,
      paypalEmail: String,
      bankName: String,
      bankAccountLast4: String,
      providerPaymentMethodId: String
    }, {
      timestamps: true
    });

    // Get the PaymentMethod model
    const PaymentMethod = mongoose.default.models.PaymentMethod ||
                         mongoose.default.model('PaymentMethod', PaymentMethodSchema);

    // Define the UserActivity schema directly
    const UserActivitySchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      action: String,
      details: String,
      ipAddress: String,
      userAgent: String,
      timestamp: { type: Date, default: Date.now },
      metadata: mongoose.default.Schema.Types.Mixed
    }, {
      timestamps: true
    });

    // Get the UserActivity model
    const UserActivity = mongoose.default.models.UserActivity ||
                        mongoose.default.model('UserActivity', UserActivitySchema);

    // Validate user ID
    if (!mongoose.default.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
    }

    // Get payment method ID from query
    const { searchParams } = new URL(request.url);
    const paymentMethodId = searchParams.get('paymentMethodId');

    if (!paymentMethodId || !mongoose.default.Types.ObjectId.isValid(paymentMethodId)) {
      return NextResponse.json({ error: 'Invalid payment method ID' }, { status: 400 });
    }

    // Check if target user exists
    const targetUser = await User.findById(params.id);
    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if payment method exists and belongs to user
    const paymentMethod = await PaymentMethod.findOne({
      _id: paymentMethodId,
      userId: params.id
    });

    if (!paymentMethod) {
      return NextResponse.json({ error: 'Payment method not found' }, { status: 404 });
    }

    // Delete payment method
    await PaymentMethod.findByIdAndDelete(paymentMethodId);

    // If deleted method was default, set another one as default
    if (paymentMethod.isDefault) {
      const anotherMethod = await PaymentMethod.findOne({ userId: params.id });
      if (anotherMethod) {
        await PaymentMethod.findByIdAndUpdate(anotherMethod._id, { isDefault: true });
      }
    }

    // Log admin activity directly
    await UserActivity.create({
      userId: new mongoose.default.Types.ObjectId(userId),
      type: 'admin',
      action: 'delete_payment_method',
      details: `Admin deleted payment method for user: ${targetUser.name} (${targetUser.email})`,
      ipAddress: request.ip || request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      timestamp: new Date(),
      metadata: {
        userId: params.id,
        paymentMethodId
      }
    });

    // Return success
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting payment method:', error);
    return NextResponse.json(
      { error: 'Failed to delete payment method', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

import { ensureMongooseConnection } from './mongoose';
import Setting from '@/models/Setting';

// --- Augment global scope for settings flag ---
declare global {
  // eslint-disable-next-line no-var
  var settingsInitialized: boolean | undefined;
}
// ---------------------------------------------

/**
 * Define a setting value type that includes primitives and objects
 */
export type SettingValue = string | number | boolean | null | Record<string, unknown> | Array<unknown>;

/**
 * Get a setting by key
 * @param key Setting key
 * @param defaultValue Default value if setting doesn't exist
 */
export async function getSetting<T>(key: string, defaultValue: T): Promise<T> {
  try {
    // Connect to MongoDB
    await ensureMongooseConnection();

    // Find setting
    const setting = await Setting.findOne({ key });

    // Return value or default
    return setting ? setting.value : defaultValue;
  } catch (error) {
    console.error(`<PERSON>rror getting setting ${key}:`, error);
    return defaultValue;
  }
}

/**
 * Get all settings in a group
 * @param group Setting group
 */
export async function getSettingsByGroup(group: string): Promise<Record<string, SettingValue>> {
  try {
    // Connect to MongoDB
    await ensureMongooseConnection();

    // Find settings
    const settings = await Setting.find({ group });

    // Convert to object
    const result: Record<string, SettingValue> = {};
    settings.forEach(setting => {
      result[setting.key] = setting.value;
    });

    return result;
  } catch (error) {
    console.error(`Error getting settings for group ${group}:`, error);
    return {};
  }
}

/**
 * Get all settings
 */
export async function getAllSettings(): Promise<Record<string, Record<string, SettingValue>>> {
  try {
    // Connect to MongoDB
    await ensureMongooseConnection();

    // Find all settings
    const settings = await Setting.find();

    // Group by group
    const result: Record<string, Record<string, SettingValue>> = {};
    settings.forEach(setting => {
      if (!result[setting.group]) {
        result[setting.group] = {};
      }
      result[setting.group][setting.key] = setting.value;
    });

    return result;
  } catch (error) {
    console.error('Error getting all settings:', error);
    return {};
  }
}

/**
 * Set a setting
 * @param key Setting key
 * @param value Setting value
 * @param group Setting group
 * @param description Optional description
 */
export async function setSetting(
  key: string,
  value: SettingValue,
  group: string,
  description?: string
): Promise<boolean> {
  try {
    // Connect to MongoDB
    await ensureMongooseConnection();

    // Upsert setting
    await Setting.updateOne(
      { key },
      {
        key,
        value,
        group,
        ...(description && { description })
      },
      { upsert: true }
    );

    return true;
  } catch (error) {
    console.error(`Error setting ${key}:`, error);
    return false;
  }
}

/**
 * Set multiple settings at once
 * @param settings Settings to set
 */
export async function setMultipleSettings(
  settings: Array<{
    key: string;
    value: SettingValue;
    group: string;
    description?: string;
  }>
): Promise<boolean> {
  try {
    // Connect to MongoDB
    await ensureMongooseConnection();

    // Create operations
    const operations = settings.map(setting => ({
      updateOne: {
        filter: { key: setting.key },
        update: {
          key: setting.key,
          value: setting.value,
          group: setting.group,
          ...(setting.description && { description: setting.description })
        },
        upsert: true
      }
    }));

    // Execute bulk operation
    await Setting.bulkWrite(operations);

    return true;
  } catch (error) {
    console.error('Error setting multiple settings:', error);
    return false;
  }
}

/**
 * Delete a setting
 * @param key Setting key
 */
export async function deleteSetting(key: string): Promise<boolean> {
  try {
    // Connect to MongoDB
    await ensureMongooseConnection();

    // Delete setting
    await Setting.deleteOne({ key });

    return true;
  } catch (error) {
    console.error(`Error deleting setting ${key}:`, error);
    return false;
  }
}

/**
 * Initialize default settings if they don't exist
 */
export async function initializeDefaultSettings(): Promise<void> {
  // --- Check if settings are already initialized (using global flag) ---
  if (global.settingsInitialized) {
    // console.log('Default settings already initialized (global cache).'); // Optional: for debugging
    return;
  }
  // -------------------------------------------------------------------

  try {
    // Connect to MongoDB
    await ensureMongooseConnection();

    // Default settings - focusing on essential settings only
    const defaultSettings = [
      {
        key: 'site.supportEmail',
        value: '<EMAIL>',
        group: 'general',
        description: 'Support email address'
      },
      {
        key: 'site.maintenanceMode',
        value: false,
        group: 'general',
        description: 'Maintenance mode'
      },
      {
        key: 'email.senderEmail',
        value: '<EMAIL>',
        group: 'email',
        description: 'Sender email address'
      },
      {
        key: 'email.senderName',
        value: 'StreamVista',
        group: 'email',
        description: 'Sender name'
      }
    ];

    // Check if settings exist
    for (const setting of defaultSettings) {
      const exists = await Setting.findOne({ key: setting.key });
      if (!exists) {
        await Setting.create(setting);
      }
    }

    // --- Mark settings as initialized (using global flag) ---
    global.settingsInitialized = true;
    console.log('Default settings initialized successfully.');
    // -------------------------------------------------------

  } catch (error) {
    console.error('Error initializing default settings:', error);
  }
}

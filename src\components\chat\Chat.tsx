import React, { useCallback, useEffect, useRef, useState } from 'react'
import { ChatContainer } from './ChatContainer'
import { ChatInput } from './ChatInput'
import { ChatMessage } from '@/types/chat'
import { useWatchParty } from '@/hooks/useWatchParty'
import { nanoid } from 'nanoid'
import { WatchPartyMember } from '@/lib/watch-party'

export interface ChatProps {
  className?: string
}

export function Chat({ className = '' }: ChatProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const { 
    currentParty,
    userId,
    userName,
    sendMessage: sendWatchPartyMessage
  } = useWatchParty()
  
  // Get user info from the WatchParty context
  const userInfo = currentParty?.members.find(m => m.id === userId)
  
  // Custom events for typing indicators
  const sendCustomEvent = useCallback((eventName: string, data: Record<string, unknown>) => {
    // Custom events are handled directly through the UI for now
    // This could be expanded later to use <PERSON><PERSON><PERSON>'s client events
    console.log(`[Chat] Custom event: ${eventName}`, data)
  }, [])

  const handleSendMessage = useCallback((content: string) => {
    if (!userInfo) return
    
    const newMessage: ChatMessage = {
      id: nanoid(),
      type: 'chat',
      content,
      sender: {
        id: userInfo.id,
        name: userInfo.name,
        avatar: userInfo.avatar,
        isHost: userInfo.isHost,
        joinedAt: userInfo.joinedAt,
        isReady: userInfo.isReady
      },
      timestamp: new Date().toISOString()
    }
    
    // Update local state immediately for a responsive UI
    setMessages(prev => [...prev, newMessage])
    
    // Send to the watch party
    sendWatchPartyMessage(content)
  }, [userInfo, sendWatchPartyMessage])
  
  // Handle typing events
  const handleStartTyping = useCallback(() => {
    if (userInfo) {
      sendCustomEvent('typing:start', {})
    }
  }, [userInfo, sendCustomEvent])
  
  const handleStopTyping = useCallback(() => {
    if (userInfo) {
      sendCustomEvent('typing:stop', {})
    }
  }, [userInfo, sendCustomEvent])
  
  // Process incoming messages from the watch party
  useEffect(() => {
    if (!currentParty?.messages) return
    
    // Map messages from the party to our local format
    const partyMessages = currentParty.messages.map(msg => {
      // Find the member data from our members list
      const memberData = currentParty.members.find(m => m.id === msg.memberId)
      
      return {
        id: msg.id,
        type: msg.type as "text" | "emoji" | "gif" | "system" | "reaction" | "chat",
        content: msg.content,
        sender: {
          id: msg.memberId,
          name: msg.memberName,
          avatar: memberData?.avatar,
          isHost: memberData?.isHost || false,
          joinedAt: memberData?.joinedAt || new Date().toISOString(),
          isReady: memberData?.isReady || false
        },
        timestamp: msg.timestamp
      } as ChatMessage
    })
    
    setMessages(partyMessages)
  }, [currentParty?.messages, currentParty?.members])
  
  return (
    <div className={`flex flex-col h-full ${className}`}>
      <ChatContainer />
      <ChatInput
        onSendMessage={handleSendMessage}
        onStartTyping={handleStartTyping}
        onStopTyping={handleStopTyping}
        disabled={!userInfo}
        placeholder={userInfo ? "Type a message..." : "Join watch party to chat"}
      />
    </div>
  )
} 
'use client';

import { useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useRouter } from 'next/navigation';

export default function ResetChatPage() {
  const router = useRouter();
  
  // Try to reset the chat on page load
  useEffect(() => {
    try {
      // Clear all previous values
      localStorage.removeItem('chatAssistantDismissed');
      localStorage.removeItem('chatAssistantPosition');
      
      // Set fresh values
      localStorage.setItem('chatAssistantDismissed', 'false');
      localStorage.setItem('chatAssistantPosition', JSON.stringify({
        x: window.innerWidth - 80,
        y: window.innerHeight - 80
      }));
      
      // Force a storage event to notify all components
      window.dispatchEvent(new StorageEvent('storage', {
        key: 'chatAssistantDismissed',
        newValue: 'false'
      }));
      
      window.dispatchEvent(new StorageEvent('storage', {
        key: 'chatAssistantPosition',
        newValue: JSON.stringify({
          x: window.innerWidth - 80,
          y: window.innerHeight - 80
        })
      }));
    } catch (e) {
      console.error("Error resetting chat on page load:", e);
    }
  }, []);
  
  const resetChatAssistant = () => {
    try {
      // Reset the ChatAssistant state in localStorage
      localStorage.removeItem('chatAssistantDismissed');
      localStorage.removeItem('chatAssistantPosition');
      
      // Set default values
      localStorage.setItem('chatAssistantDismissed', 'false');
      localStorage.setItem('chatAssistantPosition', JSON.stringify({
        x: window.innerWidth - 80,
        y: window.innerHeight - 80
      }));
      
      // Force storage events
      window.dispatchEvent(new StorageEvent('storage', {
        key: 'chatAssistantDismissed',
        newValue: 'false'
      }));
      
      window.dispatchEvent(new StorageEvent('storage', {
        key: 'chatAssistantPosition',
        newValue: JSON.stringify({
          x: window.innerWidth - 80,
          y: window.innerHeight - 80
        })
      }));
      
      // Redirect to home page
      router.push('/');
    } catch (e) {
      console.error("Error during reset:", e);
      alert("Error resetting chat. Please try again or check console for details.");
    }
  };
  
  return (
    <div className="container flex items-center justify-center min-h-[80vh]">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Reset Chat Assistant</CardTitle>
          <CardDescription>
            Use this utility to make the chat assistant visible again if it has disappeared.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            This will reset the chat assistant to its default state and position. 
            Use this if the chat bubble is not visible or if the "Show Chat" button in the footer is not working.
          </p>
          <p className="text-sm text-amber-500 mb-4">
            <strong>Note:</strong> If this doesn't work, try opening the browser console and typing <code>resetChatAssistant()</code> then press Enter.
          </p>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={() => router.push('/')}>Cancel</Button>
          <Button onClick={resetChatAssistant}>Reset Chat Assistant</Button>
        </CardFooter>
      </Card>
    </div>
  );
} 
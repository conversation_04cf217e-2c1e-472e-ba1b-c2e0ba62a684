'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { Loader2, Refresh<PERSON>w, AlertCircle, Maximize, Minimize } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  buildMovieEmbedUrl, 
  buildTVEmbedUrl, 
  getHealthiestDomain, 
  VIDSRC_DOMAINS,
  type VidSrcDomain,
  type VidSrcEmbedOptions 
} from '@/lib/vidsrc-api';
import { cn } from '@/lib/utils';

interface VidSrcPlayerProps {
  imdbId?: string;
  tmdbId?: string;
  type?: 'movie' | 'show';
  season?: number;
  episode?: number;
  className?: string;
  height?: string;
  shouldRefresh?: boolean;
  autoplay?: boolean;
  autonext?: boolean;
  subUrl?: string;
  dsLang?: string;
  color?: string;
  onError?: (error: string) => void;
  onLoad?: () => void;
}

interface PlayerState {
  isLoading: boolean;
  error: string | null;
  currentDomain: VidSrcDomain | null;
  isFullscreen: boolean;
  isMuted: boolean;
  showControls: boolean;
  retryCount: number;
}

const MAX_RETRIES = 3;
const RETRY_DELAY = 2000;

export default function VidSrcPlayer({
  imdbId,
  tmdbId,
  type = 'movie',
  season,
  episode,
  className,
  height = '56.25vw',
  shouldRefresh = false,
  autoplay = false,
  autonext = false,
  subUrl,
  dsLang,
  color,
  onError,
  onLoad
}: VidSrcPlayerProps) {
  const [state, setState] = useState<PlayerState>({
    isLoading: true,
    error: null,
    currentDomain: null,
    isFullscreen: false,
    isMuted: false,
    showControls: false,
    retryCount: 0
  });

  const iframeRef = useRef<HTMLIFrameElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const controlsTimeoutRef = useRef<NodeJS.Timeout>();

  // Build embed URL
  const buildEmbedUrl = useCallback((domain: VidSrcDomain): string => {
    const options: VidSrcEmbedOptions = {
      imdbId,
      tmdbId,
      season,
      episode,
      subUrl,
      dsLang,
      autoplay,
      autonext,
      color
    };

    try {
      if (type === 'movie') {
        return buildMovieEmbedUrl(domain, options);
      } else {
        return buildTVEmbedUrl(domain, options);
      }
    } catch (error) {
      throw new Error(`Failed to build embed URL: ${error instanceof Error ? error.message : String(error)}`);
    }
  }, [imdbId, tmdbId, type, season, episode, subUrl, dsLang, autoplay, autonext, color]);

  // Load player with domain fallback
  const loadPlayer = useCallback(async (retryCount = 0) => {
    if (!imdbId && !tmdbId) {
      const error = 'Missing required ID: Either imdbId or tmdbId is required';
      setState(prev => ({ ...prev, error, isLoading: false }));
      onError?.(error);
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Get the healthiest domain
      const domain = await getHealthiestDomain();
      const embedUrl = buildEmbedUrl(domain);

      setState(prev => ({ 
        ...prev, 
        currentDomain: domain,
        isLoading: false 
      }));

      onLoad?.();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      if (retryCount < MAX_RETRIES) {
        console.warn(`[VidSrcPlayer] Attempt ${retryCount + 1} failed, retrying...`);
        setTimeout(() => loadPlayer(retryCount + 1), RETRY_DELAY);
        setState(prev => ({ ...prev, retryCount: retryCount + 1 }));
      } else {
        setState(prev => ({ 
          ...prev, 
          error: errorMessage,
          isLoading: false 
        }));
        onError?.(errorMessage);
      }
    }
  }, [imdbId, tmdbId, buildEmbedUrl, onError, onLoad]);

  // Handle domain switching
  const switchDomain = useCallback((newDomain: VidSrcDomain) => {
    setState(prev => ({ ...prev, currentDomain: newDomain }));
  }, []);

  // Handle fullscreen
  const toggleFullscreen = useCallback(() => {
    if (!containerRef.current) return;

    if (!state.isFullscreen) {
      if (containerRef.current.requestFullscreen) {
        containerRef.current.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
  }, [state.isFullscreen]);

  // Handle controls visibility
  const showControls = useCallback(() => {
    setState(prev => ({ ...prev, showControls: true }));
    
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }
    
    controlsTimeoutRef.current = setTimeout(() => {
      setState(prev => ({ ...prev, showControls: false }));
    }, 3000);
  }, []);

  // Handle iframe load events
  const handleIframeLoad = useCallback(() => {
    setState(prev => ({ ...prev, isLoading: false }));
    onLoad?.();
  }, [onLoad]);

  const handleIframeError = useCallback(() => {
    const error = 'Failed to load video content';
    setState(prev => ({ ...prev, error, isLoading: false }));
    onError?.(error);
  }, [onError]);

  // Effects
  useEffect(() => {
    loadPlayer();
  }, [loadPlayer]);

  useEffect(() => {
    if (shouldRefresh) {
      loadPlayer();
    }
  }, [shouldRefresh, loadPlayer]);

  useEffect(() => {
    const handleFullscreenChange = () => {
      setState(prev => ({ 
        ...prev, 
        isFullscreen: !!document.fullscreenElement 
      }));
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  // Render loading state
  if (state.isLoading) {
    return (
      <div className={cn(
        "relative bg-vista-dark-lighter rounded-lg overflow-hidden flex items-center justify-center",
        className
      )} style={{ height }}>
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-vista-blue" />
          <p className="text-vista-light/70 text-sm">
            Loading {type === 'movie' ? 'movie' : 'episode'}...
          </p>
          {state.retryCount > 0 && (
            <p className="text-vista-light/50 text-xs">
              Retry attempt {state.retryCount}/{MAX_RETRIES}
            </p>
          )}
        </div>
      </div>
    );
  }

  // Render error state
  if (state.error) {
    return (
      <div className={cn(
        "relative bg-vista-dark-lighter rounded-lg overflow-hidden flex items-center justify-center",
        className
      )} style={{ height }}>
        <div className="flex flex-col items-center space-y-4 p-6 text-center">
          <AlertCircle className="h-12 w-12 text-red-500" />
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-vista-light">
              Failed to Load Content
            </h3>
            <p className="text-vista-light/70 text-sm max-w-md">
              {state.error}
            </p>
          </div>
          <div className="flex flex-wrap gap-2">
            <Button 
              onClick={() => loadPlayer()} 
              variant="outline" 
              size="sm"
              className="border-vista-blue/30 text-vista-blue hover:bg-vista-blue/10"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
            <Select value={state.currentDomain || ''} onValueChange={(value) => switchDomain(value as VidSrcDomain)}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Domain" />
              </SelectTrigger>
              <SelectContent>
                {VIDSRC_DOMAINS.map(domain => (
                  <SelectItem key={domain} value={domain}>
                    {domain}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
    );
  }

  // Render player
  if (!state.currentDomain) return null;

  const embedUrl = buildEmbedUrl(state.currentDomain);

  return (
    <div 
      ref={containerRef}
      className={cn(
        "relative bg-vista-dark rounded-lg overflow-hidden group",
        state.isFullscreen && "fixed inset-0 z-50",
        className
      )}
      style={{ height: state.isFullscreen ? '100vh' : height }}
      onMouseMove={showControls}
      onMouseLeave={() => setState(prev => ({ ...prev, showControls: false }))}
    >
      {/* VidSrc iframe */}
      <iframe
        ref={iframeRef}
        src={embedUrl}
        className="w-full h-full border-0"
        allowFullScreen
        allow="autoplay; fullscreen; picture-in-picture"
        onLoad={handleIframeLoad}
        onError={handleIframeError}
        title={`VidSrc ${type} player`}
      />

      {/* Overlay controls */}
      <div className={cn(
        "absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 transition-opacity duration-300 pointer-events-none",
        state.showControls && "opacity-100"
      )}>
        {/* Top controls */}
        <div className="absolute top-4 left-4 right-4 flex items-center justify-between pointer-events-auto">
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="bg-vista-dark/80 text-vista-light">
              {state.currentDomain}
            </Badge>
            {type === 'show' && season && episode && (
              <Badge variant="outline" className="border-vista-blue/30 text-vista-blue">
                S{season}E{episode}
              </Badge>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleFullscreen}
              className="text-vista-light hover:bg-vista-dark/80"
            >
              {state.isFullscreen ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
            </Button>
          </div>
        </div>
      </div>

      {/* Domain selector for debugging */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute top-4 right-4 pointer-events-auto">
          <Select value={state.currentDomain} onValueChange={(value) => switchDomain(value as VidSrcDomain)}>
            <SelectTrigger className="w-32 bg-vista-dark/80 border-vista-light/20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {VIDSRC_DOMAINS.map(domain => (
                <SelectItem key={domain} value={domain}>
                  {domain}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}
    </div>
  );
} 
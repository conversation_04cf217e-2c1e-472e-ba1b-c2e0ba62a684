'use client';

import { useState, useEffect } from 'react';
import { useSettings } from '@/hooks/useSettings';
import {
  Settings,
  Save,
  AlertTriangle,
  Info,
  Loader2
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
// Import the SettingValue type from settings
import type { SettingValue } from '@/lib/settings';

export default function SettingsPage() {
  // Use settings hook
  const {
    settings,
    isLoading,
    error,
    isSaving,
    saveSettings
  } = useSettings();

  // Local state for form values
  const [formValues, setFormValues] = useState<Record<string, SettingValue>>({});
  const [saveSuccess, setSaveSuccess] = useState(false);

  // Initialize form values when settings are loaded
  useEffect(() => {
    if (settings) {
      const initialValues: Record<string, SettingValue> = {};

      // Flatten settings object
      Object.entries(settings).forEach(([_group, groupSettings]) => {
        Object.entries(groupSettings as Record<string, SettingValue>).forEach(([key, value]) => {
          initialValues[key] = value;
        });
      });

      setFormValues(initialValues);
    }
  }, [settings]);

  // Helper function to get a string setting value
  const getStringValue = (key: string, defaultValue: string = ''): string => {
    const value = formValues[key];
    return typeof value === 'string' ? value : defaultValue;
  };

  // Helper function to get a boolean setting value
  const getBooleanValue = (key: string, defaultValue: boolean = false): boolean => {
    const value = formValues[key];
    return typeof value === 'boolean' ? value : defaultValue;
  };

  // Helper function to get a number setting value
  const getNumberValue = (key: string, defaultValue: number = 0): number => {
    const value = formValues[key];
    return typeof value === 'number' ? value : defaultValue;
  };

  // Helper function to update a setting value
  const updateValue = (key: string, value: SettingValue) => {
    setFormValues(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Handle save settings
  const handleSaveSettings = async () => {
    // Prepare settings for API
    const settingsToSave = Object.entries(formValues).map(([key, value]) => {
      // Determine group from key
      const group = key.split('.')[0];
      return {
        key,
        value,
        group
      };
    });

    // Save settings
    const success = await saveSettings(settingsToSave);

    if (success) {
      setSaveSuccess(true);

      // Reset success message after 3 seconds
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
    }
  };

  // Show loading state if settings are not loaded yet
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-200px)]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-vista-blue mx-auto mb-4" />
          <p className="text-vista-light">Loading settings...</p>
        </div>
      </div>
    );
  }

  // Show error state if there was an error loading settings
  if (error) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-200px)]">
        <div className="text-center">
          <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <p className="text-vista-light mb-4">Failed to load settings</p>
          <Button onClick={() => window.location.reload()}>Retry</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-vista-light">Admin Settings</h1>
          <p className="text-vista-light/70">
            Configure system-wide settings and preferences
          </p>
        </div>
        <Button onClick={handleSaveSettings} disabled={isSaving}>
          <Save className={`mr-2 h-4 w-4 ${isSaving ? 'animate-spin' : ''}`} />
          {isSaving ? 'Saving...' : 'Save Settings'}
        </Button>
      </div>

      {saveSuccess && (
        <Alert variant="success" className="bg-green-500/10 text-green-500 border-green-500/20">
          <Info className="h-4 w-4" />
          <AlertTitle>Success</AlertTitle>
          <AlertDescription>
            Your settings have been saved successfully.
          </AlertDescription>
        </Alert>
      )}

      <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-vista-light">General Settings</CardTitle>
              <CardDescription>Basic configuration for your platform</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="support-email">Support Email</Label>
                <Input
                  id="support-email"
                  type="email"
                  value={getStringValue('site.supportEmail', '<EMAIL>')}
                  onChange={(e) => updateValue('site.supportEmail', e.target.value)}
                  placeholder="Enter support email"
                />
              </div>

              <Separator className="my-4" />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="maintenance-mode">Maintenance Mode</Label>
                  <p className="text-sm text-vista-light/70">
                    When enabled, the site will display a maintenance message to all users
                  </p>
                </div>
                <Switch
                  id="maintenance-mode"
                  checked={getBooleanValue('site.maintenanceMode', false)}
                  onCheckedChange={(value) => updateValue('site.maintenanceMode', value)}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-vista-light">Regional Settings</CardTitle>
              <CardDescription>Localization and timezone configuration</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="default-language">Default Language</Label>
                <Select defaultValue="en">
                  <SelectTrigger id="default-language">
                    <SelectValue placeholder="Select language" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="es">Spanish</SelectItem>
                    <SelectItem value="fr">French</SelectItem>
                    <SelectItem value="de">German</SelectItem>
                    <SelectItem value="ja">Japanese</SelectItem>
                    <SelectItem value="zh">Chinese</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="default-timezone">Default Timezone</Label>
                <Select defaultValue="utc">
                  <SelectTrigger id="default-timezone">
                    <SelectValue placeholder="Select timezone" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="utc">UTC</SelectItem>
                    <SelectItem value="est">Eastern Time (EST)</SelectItem>
                    <SelectItem value="cst">Central Time (CST)</SelectItem>
                    <SelectItem value="mst">Mountain Time (MST)</SelectItem>
                    <SelectItem value="pst">Pacific Time (PST)</SelectItem>
                    <SelectItem value="gmt">Greenwich Mean Time (GMT)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="date-format">Date Format</Label>
                <Select defaultValue="mdy">
                  <SelectTrigger id="date-format">
                    <SelectValue placeholder="Select date format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="mdy">MM/DD/YYYY</SelectItem>
                    <SelectItem value="dmy">DD/MM/YYYY</SelectItem>
                    <SelectItem value="ymd">YYYY/MM/DD</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-vista-light">Email Settings</CardTitle>
              <CardDescription>Configure email sending options</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="sender-email">Sender Email</Label>
                <Input
                  id="sender-email"
                  type="email"
                  value={getStringValue('email.senderEmail', '<EMAIL>')}
                  onChange={(e) => updateValue('email.senderEmail', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="sender-name">Sender Name</Label>
                <Input
                  id="sender-name"
                  value={getStringValue('email.senderName', 'StreamVista')}
                  onChange={(e) => updateValue('email.senderName', e.target.value)}
                  placeholder="Your Company Name"
                />
              </div>
            </CardContent>
          </Card>
      </div>
    </div>
  );
}

import mongoose, { Document, Schema } from 'mongoose';

export interface ITransaction extends Document {
  userId: mongoose.Types.ObjectId;
  subscriptionId?: mongoose.Types.ObjectId;
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  type: 'subscription' | 'one_time' | 'refund';
  description: string;
  paymentMethod?: string;
  paymentMethodId?: string;
  transactionDate: Date;
  providerTransactionId?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

const TransactionSchema = new Schema<ITransaction>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true
    },
    subscriptionId: {
      type: Schema.Types.ObjectId,
      ref: 'Subscription'
    },
    amount: {
      type: Number,
      required: true
    },
    currency: {
      type: String,
      required: true,
      default: 'USD'
    },
    status: {
      type: String,
      enum: ['pending', 'completed', 'failed', 'refunded'],
      required: true,
      index: true
    },
    type: {
      type: String,
      enum: ['subscription', 'one_time', 'refund'],
      required: true,
      index: true
    },
    description: {
      type: String,
      required: true
    },
    paymentMethod: {
      type: String
    },
    paymentMethodId: {
      type: String
    },
    transactionDate: {
      type: Date,
      required: true,
      default: Date.now,
      index: true
    },
    providerTransactionId: {
      type: String
    },
    metadata: {
      type: Schema.Types.Mixed
    }
  },
  {
    timestamps: true
  }
);

// Create indexes for efficient querying
TransactionSchema.index({ userId: 1, transactionDate: -1 });
TransactionSchema.index({ subscriptionId: 1 });
TransactionSchema.index({ status: 1 });
TransactionSchema.index({ type: 1 });

// Create the model if it doesn't exist already
const Transaction = mongoose.models.Transaction || 
                    mongoose.model<ITransaction>('Transaction', TransactionSchema);

export default Transaction;

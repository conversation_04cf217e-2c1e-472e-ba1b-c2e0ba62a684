import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongoose';

/**
 * Middleware to ensure MongoDB connection is established
 * This middleware handles connection errors gracefully
 */
export async function withMongoConnection(
  handler: (req: NextRequest) => Promise<NextResponse>,
  req: NextRequest
): Promise<NextResponse> {
  try {
    // Try to establish MongoDB connection
    await ensureMongooseConnection();
    
    // If connection is successful, proceed with the handler
    return await handler(req);
  } catch (error) {
    console.error('MongoDB connection error in middleware:', error);
    
    // Check if this is a connection error
    const errorMessage = (error as Error).message;
    const isConnectionError = 
      errorMessage.includes('connection') || 
      errorMessage.includes('timeout') ||
      errorMessage.includes('network');
    
    if (isConnectionError) {
      // For connection errors, return a service unavailable response
      return NextResponse.json(
        { 
          error: 'Database connection unavailable',
          message: 'The service is temporarily unavailable. Please try again later.'
        },
        { status: 503 }
      );
    }
    
    // For other errors, return a generic error response
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Wrapper function to apply MongoDB connection middleware to a route handler
 */
export function withMongo(handler: (req: NextRequest) => Promise<NextResponse>) {
  return async (req: NextRequest) => {
    return withMongoConnection(handler, req);
  };
}

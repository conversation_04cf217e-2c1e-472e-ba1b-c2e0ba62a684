'use client'

import { ReactNode, useMemo } from 'react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { cn } from '@/lib/utils'

interface UserAvatarProps {
  userId?: string
  src?: string
  alt?: string
  className?: string
  fallback?: string | ReactNode
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  status?: 'online' | 'offline' | 'away' | 'busy'
}

export function UserAvatar({
  userId,
  src,
  alt = "User avatar",
  className = '',
  fallback,
  size = 'md',
  status
}: UserAvatarProps) {
  // Generate a fallback character if fallback is not provided
  // or if it's provided as a string
  const fallbackChar = typeof fallback === 'string'
    ? fallback || (userId ? userId.charAt(0).toUpperCase() : (alt ? alt.charAt(0).toUpperCase() : 'U'))
    : null

  // Map size to class name
  const sizeClasses = {
    xs: 'h-6 w-6',
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16'
  }

  const sizeClass = sizeClasses[size] || sizeClasses.md

  // Process the image URL and determine the actual source to use - using useMemo to prevent regeneration
  const actualSrc = useMemo(() => {
    // Check if the src is a valid URL and fix common issues
    const processImageUrl = (url?: string) => {
      if (!url) return null;
      
      // Check if it's a valid URL format
      try {
        // If it's a Cloudinary URL, ensure it has the correct cloud name (lowercase)
        if (url.includes('cloudinary.com')) {
          // Check if the URL contains the correct cloud name (should be lowercase)
          if (!url.includes('cloudinary.com/streamvista/')) {
            // Fix the URL by replacing the cloud name with the lowercase version
            const fixedUrl = url.replace(/cloudinary\.com\/([^\/]+)\//, 'cloudinary.com/streamvista/');
            return fixedUrl;
          }
          return url;
        }

        // For non-Cloudinary URLs, validate the format
        new URL(url);
        return url;
      } catch (e) {
        return null;
      }
    };

    // Default Cloudinary avatar URL
    const defaultAvatarUrl = "https://res.cloudinary.com/streamvista/image/upload/v1743812698/defaults/default_avatar.jpg";

    // Check if the URL is a relative path (which won't work)
    const isRelativePath = src && !src.startsWith('http') && !src.startsWith('data:');

    // Use a stable identifier instead of Date.now() to prevent flickering
    const stableId = userId || alt || 'default';
    const cacheKey = btoa(stableId).substring(0, 8);
    
    let finalSrc = isRelativePath ? defaultAvatarUrl : (processImageUrl(src) || defaultAvatarUrl || (userId ? `https://avatar.vercel.sh/${userId}.png` : undefined));

    // Add cache busting parameter if it's a Cloudinary URL and doesn't already have a timestamp
    if (finalSrc && finalSrc.includes('cloudinary.com') && !finalSrc.includes('t=')) {
      const separator = finalSrc.includes('?') ? '&' : '?';
      finalSrc = `${finalSrc}${separator}t=${cacheKey}`;
    }

    return finalSrc;
  }, [src, userId, alt]); // Only recompute when these values change

  return (
    <div className="relative">
      <Avatar className={cn(sizeClass, className)}>
        <AvatarImage
          src={actualSrc}
          alt={alt}
          onLoadingStatusChange={(status) => {
            console.log(`Avatar image for ${alt} loading status:`, status);
          }}
          onError={(e) => {
            console.error(`Error loading avatar image for ${alt}:`, e);
            console.log(`Failed URL:`, actualSrc);
            // Try to load the image directly to see if it's accessible
            fetch(actualSrc || '')
              .then(response => {
                console.log(`Fetch response for ${actualSrc}:`, {
                  status: response.status,
                  statusText: response.statusText,
                  headers: Object.fromEntries(response.headers.entries())
                });
                return response.blob();
              })
              .then(blob => {
                console.log(`Blob for ${actualSrc}:`, {
                  type: blob.type,
                  size: blob.size
                });
              })
              .catch(error => {
                console.error(`Fetch error for ${actualSrc}:`, error);
              });
          }}
        />
        <AvatarFallback>
          {typeof fallback !== 'string' && fallback ? fallback : fallbackChar}
        </AvatarFallback>
      </Avatar>

      {status && (
        <div
          className={cn(
            "absolute bottom-0 right-0 h-2.5 w-2.5 rounded-full border-2 border-background",
            {
              'bg-green-500': status === 'online',
              'bg-red-500': status === 'busy',
              'bg-yellow-500': status === 'away',
              'bg-gray-400': status === 'offline'
            }
          )}
        />
      )}
    </div>
  )
}
'use client';

import { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Play, Info, ChevronRight, ChevronLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
// import { useLanguage } from '@/lib/i18n/LanguageContext'; // Uncomment if translations are needed
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { getPopularMovies, getPopularTVShows } from '@/lib/tmdb-api';

type PopularSectionProps = {
  type: 'movies' | 'shows';
};
interface ContentItem {
  id: string;
  title: string;
  image: string;
  year: number | string;
  rating: string;
  duration?: string;
  seasons?: number;
  posterPath?: string;
}

export default function PopularSection({ type }: PopularSectionProps) {
  // const { t } = useLanguage(); // Uncomment if translations are needed
  const scrollRef = useRef<HTMLDivElement>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);
  const [contentData, setContentData] = useState<ContentItem[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch real data from the API
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        if (type === 'movies') {
          const data = await getPopularMovies();
          const formattedData = data.map(movie => ({
            id: movie.id,
            title: movie.title,
            image: movie.posterUrl || 'https://placehold.co/300x450/171717/CCCCCC?text=No+Image',
            year: movie.year || 'Unknown',
            rating: 'PG-13', // Default rating since API might not provide it
            duration: movie.runtime ? `${movie.runtime} min` : 'Unknown'
          }));
          setContentData(formattedData);
        } else {
          const data = await getPopularTVShows();
          const formattedData = data.map(show => ({
            id: show.id,
            title: show.title,
            image: show.posterUrl || 'https://placehold.co/300x450/171717/CCCCCC?text=No+Image',
            year: show.year || 'Unknown',
            rating: 'TV-14', // Default rating since API might not provide it
            seasons: show.seasons || 0 // Use actual season count from API
          }));
          setContentData(formattedData);
        }
      } catch (error) {
        console.error('Error fetching content:', error);
        // Set empty array in case of error
        setContentData([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [type]);

  const handleScroll = () => {
    if (scrollRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;

      // Show left arrow if we've scrolled to the right
      setShowLeftArrow(scrollLeft > 0);

      // Show right arrow if there's more content to scroll to
      setShowRightArrow(scrollLeft + clientWidth < scrollWidth - 10);
    }
  };

  const scroll = (direction: 'left' | 'right') => {
    if (scrollRef.current) {
      const scrollAmount = scrollRef.current.clientWidth * 0.8;
      const newPosition = direction === 'left'
        ? scrollRef.current.scrollLeft - scrollAmount
        : scrollRef.current.scrollLeft + scrollAmount;

      scrollRef.current.scrollTo({
        left: newPosition,
        behavior: 'smooth'
      });
    }
  };

  return (
    <div className="py-8">
      <div className="container px-4 mx-auto">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-vista-light">
              {type === 'movies' ? 'Popular Movies' : 'Popular Shows'}
            </h2>
            <p className="text-sm text-vista-light/70">
              {type === 'movies' ? 'From blockbusters to indies' : 'Trending series everyone\'s watching'}
            </p>
          </div>
          <Link href={`/${type}`}>
            <Button variant="outline" className="bg-white text-black hover:bg-white/90 gap-1">
              See All
              <ChevronRight className="h-4 w-4" />
            </Button>
          </Link>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-12">
            <div className="w-10 h-10 border-4 border-vista-blue/20 border-t-vista-blue rounded-full animate-spin"></div>
          </div>
        )}

        {/* Empty State */}
        {!loading && contentData.length === 0 && (
          <div className="text-center py-12">
            <p className="text-vista-light/70">No content available at the moment.</p>
          </div>
        )}

        {/* Content Display */}
        {!loading && contentData.length > 0 && (
          <div className="relative group">
            {/* Left Scroll Button */}
            {showLeftArrow && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-vista-dark/80 text-vista-light hover:bg-vista-dark/90 rounded-full h-10 w-10 shadow-md"
                onClick={() => scroll('left')}
              >
                <ChevronLeft className="h-6 w-6" />
              </Button>
            )}

            {/* Content Scroll Area */}
            <div
              ref={scrollRef}
              className="flex gap-4 overflow-x-auto hide-scrollbar pb-4"
              onScroll={handleScroll}
            >
              {contentData.map((item) => (
                <Card
                  key={item.id}
                  className="shrink-0 bg-vista-dark-card border-vista-dark-card w-[220px] overflow-hidden"
                >
                  <CardContent className="p-0">
                    <div className="relative">
                      <div className="relative h-[308px] w-[220px] group">
                        <Image
                          src={item.image}
                          alt={item.title}
                          className="object-cover transition-transform group-hover:scale-105"
                          fill
                        />
                        <div className="absolute inset-0 bg-black/20 group-hover:bg-black/30 transition-colors flex justify-center items-center opacity-0 group-hover:opacity-100">
                          <div className="flex flex-col gap-2">
                            <Link href={`/watch/${item.id || 'unknown'}`}>
                              <Button size="icon" className="rounded-full bg-vista-blue/90 hover:bg-vista-blue">
                                <Play className="h-5 w-5 text-white" fill="white" />
                              </Button>
                            </Link>
                            <Link href={item.id ? `/details/${type}/${item.id}` : '/'}>
                              {/* Skip navigation if ID is invalid */}
                              <Button size="icon" className="rounded-full bg-vista-dark-card hover:bg-vista-dark-hover">
                                <Info className="h-4 w-4 text-vista-light" />
                              </Button>
                            </Link>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="p-3">
                      <Link href={item.id ? `/details/${type}/${item.id}` : '/'}>
                        {/* Skip navigation if ID is invalid */}
                        <h3 className="font-medium text-vista-light mb-1 line-clamp-1 hover:text-vista-blue transition-colors">
                          {item.title}
                        </h3>
                      </Link>
                      <div className="flex items-center text-xs gap-2 text-vista-light/70">
                        <Badge variant="outline" className="bg-vista-dark-hover text-vista-light/80 border-vista-dark-hover px-1.5 h-5">
                          {item.rating}
                        </Badge>
                        <div>{item.year}</div>
                        <div>
                          {type === 'movies'
                            ? item.duration
                            : `${item.seasons} ${
                                item.seasons === 1 ? 'Season' : 'Seasons'
                              }`
                          }
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Right Scroll Button */}
            {showRightArrow && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-vista-dark/80 text-vista-light hover:bg-vista-dark/90 rounded-full h-10 w-10 shadow-md"
                onClick={() => scroll('right')}
              >
                <ChevronRight className="h-6 w-6" />
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
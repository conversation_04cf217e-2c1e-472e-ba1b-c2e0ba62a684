import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import BannerAd from '@/models/BannerAd';
import mongoose from 'mongoose';

/**
 * POST /api/banner-ads/analytics
 * Public endpoint to track banner ad analytics (views, clicks)
 * This endpoint is public and doesn't require authentication
 */
export async function POST(request: NextRequest) {
  try {
    await ensureMongooseConnection();

    // Check if request has a body before trying to parse it
    const contentType = request.headers.get('content-type');
    let body: { bannerId?: string; action?: string };
    
    if (contentType && contentType.includes('application/json')) {
      try {
        body = await request.json();
      } catch (parseError) {
        console.error('Error parsing banner analytics request body:', parseError);
        return NextResponse.json(
          { error: 'Invalid JSON in request body' },
          { status: 400 }
        );
      }
    } else {
      return NextResponse.json(
        { error: 'Content-Type must be application/json' },
        { status: 400 }
      );
    }
    
    const { bannerId, action } = body;
    
    // Validate required fields
    if (!bannerId || !action) {
      return NextResponse.json(
        { error: 'Missing required fields: bannerId and action' },
        { status: 400 }
      );
    }

    // At this point, TypeScript knows bannerId and action are not undefined
    const validatedBannerId = bannerId as string;
    const validatedAction = action as string;

    // Validate banner ID
    if (!mongoose.Types.ObjectId.isValid(validatedBannerId)) {
      return NextResponse.json(
        { error: 'Invalid banner ad ID' },
        { status: 400 }
      );
    }

    // Validate action type
    if (!['view', 'click'].includes(validatedAction)) {
      return NextResponse.json(
        { error: 'Invalid action type. Must be view or click' },
        { status: 400 }
      );
    }

    // Check if banner ad exists and is active
    const bannerAd = await BannerAd.findById(validatedBannerId);
    if (!bannerAd) {
      return NextResponse.json(
        { error: 'Banner ad not found' },
        { status: 404 }
      );
    }

    // Only track analytics for active banners
    if (!bannerAd.isActive) {
      return NextResponse.json(
        { success: true, message: 'Banner is not active' },
        { status: 200 }
      );
    }

    // Update analytics based on action
    const updateField = validatedAction === 'view' ? 'analytics.views' : 'analytics.clicks';

    await BannerAd.findByIdAndUpdate(
      validatedBannerId,
      { $inc: { [updateField]: 1 } },
      { new: true }
    );

    return NextResponse.json({
      success: true,
      message: `${validatedAction} tracked successfully`
    });

  } catch (error) {
    console.error('Error tracking banner analytics:', error);
    return NextResponse.json(
      { error: 'Failed to track analytics' },
      { status: 500 }
    );
  }
} 
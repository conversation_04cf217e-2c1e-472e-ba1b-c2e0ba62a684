'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Navbar } from '@/components/Navbar';
import SearchResults from '@/components/SearchResults';
import Footer from '@/components/Footer';
import { ContentCardType } from '@/lib/content-utils';

export default function SearchPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const query = searchParams.get('q') || '';

  const [results, setResults] = useState<ContentCardType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Combined search functionality using both TMDB and OMDB APIs
  useEffect(() => {
    if (!query) {
      setResults([]);
      setIsLoading(false);
      return;
    }

    const fetchSearchResults = async () => {
      setIsLoading(true);
      try {
        // Use our search API endpoint that now includes both TMDB and OMDB data
        const response = await fetch(`/api/search?query=${encodeURIComponent(query)}`);

        if (!response.ok) {
          throw new Error(`Search failed: ${response.status}`);
        }

        const data = await response.json();

        // Map the API response to our content card format
        const formattedResults = data.results.map((item: any) => ({
          id: item.id.toString(),
          title: item.title,
          imagePath: item.posterPath || '/images/placeholder-poster.jpg',
          type: item.type === 'movie' ? 'movies' : 'shows',
          year: item.year || '',
          userRating: item.rating,
          duration: item.runtime ? `${item.runtime} min` : undefined,
          tmdbId: item.tmdbId,
          imdbId: item.imdbId,
          dataSource: item.dataSource || 'tmdb',
          isAwardWinning: item.awards ? true : false,
          isNew: item.dataSource === 'both' // Mark items with data from both sources as 'new'
        }));

        setResults(formattedResults);
        setError(null);
      } catch (error) {
        console.error('Search error:', error);
        setError('Failed to fetch search results.');
        setResults([]);
      } finally {
        setIsLoading(false);
      }
    };

    // Add a short delay before searching to avoid excessive API calls while typing
    const timeoutId = setTimeout(() => {
      fetchSearchResults();
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [query]);

  const handleClearSearch = () => {
    router.push('/search');
  };

  return (
    <div className="min-h-screen bg-vista-dark text-vista-light">
      <Navbar />

      <main className="container mx-auto px-4 pt-24 pb-12">
        <div className="mb-8 flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-vista-light">
              {query ? `Search Results for "${query}"` : 'Search'}
            </h1>
            {query && results.some(item => item.dataSource === 'both') && (
              <p className="text-sm text-vista-blue mt-1 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                  <path d="M8 16H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v2"></path>
                  <path d="M12 8h8a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-8a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2z"></path>
                </svg>
                Results with both TMDB and OMDB data are shown as combined entries
              </p>
            )}
          </div>
          {query && (
            <button
              onClick={handleClearSearch}
              className="bg-vista-dark-lighter text-vista-light px-4 py-2 rounded hover:bg-vista-light/10 transition-colors"
            >
              Clear Search
            </button>
          )}
        </div>

        <SearchResults
          results={results}
          isLoading={isLoading}
          query={query}
          error={error}
        />
      </main>

      <Footer />
    </div>
  );
}

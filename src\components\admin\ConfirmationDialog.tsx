'use client';

import { useState, ReactNode } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { AlertTriangle, Loader2 } from 'lucide-react';

export interface ConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void | Promise<void>;
  title: string;
  description: string | ReactNode;
  confirmText?: string;
  cancelText?: string;
  variant?: 'default' | 'destructive';
  icon?: ReactNode;
  requiresConfirmation?: boolean;
  confirmationText?: string;
  isLoading?: boolean;
  children?: ReactNode;
}

export default function ConfirmationDialog({
  open,
  onOpenChange,
  onConfirm,
  title,
  description,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  variant = 'default',
  icon,
  requiresConfirmation = false,
  confirmationText = 'CONFIRM',
  isLoading = false,
  children
}: ConfirmationDialogProps) {
  const [userConfirmationText, setUserConfirmationText] = useState('');

  const isConfirmationValid = !requiresConfirmation || userConfirmationText === confirmationText;
  const isDestructive = variant === 'destructive';

  const handleConfirm = async () => {
    if (!isConfirmationValid || isLoading) return;
    
    try {
      await onConfirm();
    } catch (error) {
      console.error('Confirmation action failed:', error);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    onOpenChange(newOpen);
    if (!newOpen) {
      setUserConfirmationText(''); // Reset confirmation text when dialog closes
    }
  };

  const defaultIcon = icon || (
    <div className={`p-3 rounded-full ${isDestructive ? 'bg-red-600/20' : 'bg-vista-blue/20'}`}>
      <AlertTriangle className={`h-6 w-6 ${isDestructive ? 'text-red-500' : 'text-vista-blue'}`} />
    </div>
  );

  return (
    <AlertDialog open={open} onOpenChange={handleOpenChange}>
      <AlertDialogContent className="bg-vista-dark border-vista-dark-lighter max-w-md w-full p-0 overflow-hidden">
        <div className={`p-6 flex flex-col items-center justify-center border-b border-vista-dark-lighter ${
          isDestructive ? 'bg-red-600/10' : 'bg-vista-blue/10'
        }`}>
          {defaultIcon}
          <AlertDialogHeader className="text-center space-y-1 mt-4">
            <AlertDialogTitle className="text-xl text-vista-light">{title}</AlertDialogTitle>
          </AlertDialogHeader>
        </div>

        <div className="p-6 space-y-4">
          <AlertDialogDescription className="text-vista-light/80 text-center">
            {description}
          </AlertDialogDescription>

          {/* Custom content */}
          {children}

          {/* Confirmation input if required */}
          {requiresConfirmation && (
            <div className="space-y-2">
              <Label htmlFor="confirmation" className="text-vista-light text-sm font-medium">
                Type <code className={`px-1 py-0.5 rounded ${
                  isDestructive 
                    ? 'bg-vista-dark-lighter text-red-400' 
                    : 'bg-vista-dark-lighter text-vista-blue'
                }`}>{confirmationText}</code> to confirm:
              </Label>
              <Input
                id="confirmation"
                type="text"
                placeholder={`Type "${confirmationText}" here`}
                value={userConfirmationText}
                onChange={(e) => setUserConfirmationText(e.target.value)}
                className={`bg-vista-dark-lighter border-vista-dark-lighter text-vista-light placeholder:text-vista-light/50 ${
                  isDestructive 
                    ? 'focus:border-red-500 focus:ring-red-500' 
                    : 'focus:border-vista-blue focus:ring-vista-blue'
                }`}
                disabled={isLoading}
              />
            </div>
          )}

          <AlertDialogFooter className="flex flex-col sm:flex-row gap-3 sm:gap-2 mt-6">
            <AlertDialogCancel
              className="bg-vista-dark-lighter text-vista-light hover:bg-vista-dark-lighter/80 hover:text-vista-light w-full sm:w-auto"
              disabled={isLoading}
            >
              {cancelText}
            </AlertDialogCancel>
            <AlertDialogAction
              className={`w-full sm:w-auto disabled:opacity-50 disabled:cursor-not-allowed ${
                isDestructive
                  ? 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-600'
                  : 'bg-vista-blue text-white hover:bg-vista-blue/90 focus:ring-vista-blue'
              }`}
              onClick={(e) => {
                e.preventDefault();
                handleConfirm();
              }}
              disabled={isLoading || !isConfirmationValid}
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                confirmText
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </div>
      </AlertDialogContent>
    </AlertDialog>
  );
}

// Convenience hook for using the confirmation dialog
export function useConfirmationDialog() {
  const [isOpen, setIsOpen] = useState(false);
  const [config, setConfig] = useState<Partial<ConfirmationDialogProps>>({});

  const openDialog = (dialogConfig: Omit<ConfirmationDialogProps, 'open' | 'onOpenChange'>) => {
    setConfig(dialogConfig);
    setIsOpen(true);
  };

  const closeDialog = () => {
    setIsOpen(false);
  };

  const ConfirmationDialogComponent = () => (
    <ConfirmationDialog
      open={isOpen}
      onOpenChange={setIsOpen}
      onConfirm={config.onConfirm || (() => {})}
      title={config.title || ''}
      description={config.description || ''}
      {...config}
    />
  );

  return {
    openDialog,
    closeDialog,
    ConfirmationDialog: ConfirmationDialogComponent,
    isOpen
  };
} 
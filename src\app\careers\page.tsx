'use client';

import { Navbar } from '@/components/Navbar';
import Footer from '@/components/Footer';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { MapPin, Clock, Users, Briefcase, Heart, Coffee, Gamepad2, Plane, GraduationCap, Shield } from 'lucide-react';
import Link from 'next/link';

const jobOpenings = [
  {
    title: "Senior Frontend Engineer",
    department: "Engineering",
    location: "San Francisco, CA / Remote",
    type: "Full-time",
    description: "Build the next generation of streaming interfaces using React, TypeScript, and modern web technologies.",
    requirements: ["5+ years React experience", "TypeScript proficiency", "Streaming media knowledge"],
    posted: "2 days ago"
  },
  {
    title: "Content Acquisition Manager",
    department: "Content",
    location: "Los Angeles, CA",
    type: "Full-time", 
    description: "Source and negotiate premium content deals with studios and independent creators worldwide.",
    requirements: ["Entertainment industry experience", "Negotiation skills", "Global market knowledge"],
    posted: "1 week ago"
  },
  {
    title: "DevOps Engineer",
    department: "Engineering",
    location: "Austin, TX / Remote",
    type: "Full-time",
    description: "Scale our global streaming infrastructure to serve millions of concurrent users.",
    requirements: ["AWS/GCP experience", "Kubernetes", "CDN optimization"],
    posted: "3 days ago"
  },
  {
    title: "UX Designer",
    department: "Design",
    location: "New York, NY / Remote",
    type: "Full-time",
    description: "Design intuitive and beautiful user experiences for our streaming platform.",
    requirements: ["5+ years UX design", "Figma expertise", "User research experience"],
    posted: "5 days ago"
  },
  {
    title: "Data Scientist",
    department: "Analytics",
    location: "Seattle, WA / Remote",
    type: "Full-time",
    description: "Build recommendation systems and analyze user behavior to improve content discovery.",
    requirements: ["Machine Learning", "Python/R", "Big data experience"],
    posted: "1 week ago"
  },
  {
    title: "Marketing Intern",
    department: "Marketing",
    location: "Remote",
    type: "Internship",
    description: "Support digital marketing campaigns and social media strategy for global audiences.",
    requirements: ["Marketing studies", "Social media savvy", "Creative mindset"],
    posted: "4 days ago"
  }
];

const benefits = [
  {
    icon: Heart,
    title: "Health & Wellness",
    description: "Comprehensive health, dental, and vision insurance plus mental health support."
  },
  {
    icon: Coffee,
    title: "Work-Life Balance",
    description: "Flexible hours, unlimited PTO, and remote work options for most roles."
  },
  {
    icon: GraduationCap,
    title: "Learning & Growth",
    description: "$5,000 annual learning budget and conference attendance support."
  },
  {
    icon: Gamepad2,
    title: "Entertainment Perks",
    description: "Free StreamVista subscription, movie tickets, and gaming allowances."
  },
  {
    icon: Plane,
    title: "Travel & Events",
    description: "Annual company retreats and travel opportunities for global collaboration."
  },
  {
    icon: Shield,
    title: "Financial Security",
    description: "Competitive salary, equity options, 401(k) matching, and life insurance."
  }
];

const departments = [
  { name: "Engineering", count: 12, color: "bg-blue-500/20 text-blue-400" },
  { name: "Content", count: 8, color: "bg-purple-500/20 text-purple-400" },
  { name: "Design", count: 5, color: "bg-pink-500/20 text-pink-400" },
  { name: "Marketing", count: 6, color: "bg-green-500/20 text-green-400" },
  { name: "Analytics", count: 4, color: "bg-orange-500/20 text-orange-400" },
  { name: "Operations", count: 7, color: "bg-cyan-500/20 text-cyan-400" }
];

export default function CareersPage() {
  return (
    <div className="min-h-screen bg-vista-dark text-vista-light">
      <Navbar />
      
      {/* Hero Section */}
      <section className="relative py-20 px-4 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-vista-blue/10 to-transparent" />
        <div className="container mx-auto text-center relative z-10">
          <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-vista-light to-vista-blue bg-clip-text text-transparent">
            Join Our Team
          </h1>
          <p className="text-xl md:text-2xl text-vista-light/80 max-w-3xl mx-auto mb-8">
            Help us build the future of entertainment. Work with passionate people creating experiences that delight millions worldwide.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-vista-blue hover:bg-vista-blue/90">
              View Open Positions
            </Button>
            <Link href="/about">
              <Button size="lg" variant="outline" className="border-vista-light/20 text-vista-light hover:bg-vista-light/10">
                Learn About Us
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Departments Overview */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-12 text-vista-light">Open Positions by Department</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {departments.map((dept, index) => (
              <Card key={index} className="bg-vista-card border-vista-light/10 hover:border-vista-blue/30 transition-colors">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-vista-light mb-1">{dept.count}</div>
                  <Badge className={dept.color}>{dept.name}</Badge>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <Separator className="bg-vista-light/10" />

      {/* Benefits Section */}
      <section className="py-20 px-4 bg-vista-card/30">
        <div className="container mx-auto">
          <h2 className="text-4xl md:text-5xl font-bold text-center mb-16 text-vista-light">Why Work at StreamVista?</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <Card key={index} className="bg-vista-card border-vista-light/10 hover:border-vista-blue/30 transition-colors">
                <CardContent className="p-6">
                  <div className="w-12 h-12 mb-4 bg-vista-blue/20 rounded-full flex items-center justify-center">
                    <benefit.icon className="w-6 h-6 text-vista-blue" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-vista-light">{benefit.title}</h3>
                  <p className="text-vista-light/70">{benefit.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Job Listings */}
      <section className="py-20 px-4">
        <div className="container mx-auto">
          <h2 className="text-4xl md:text-5xl font-bold text-center mb-16 text-vista-light">Current Openings</h2>
          <div className="space-y-6">
            {jobOpenings.map((job, index) => (
              <Card key={index} className="bg-vista-card border-vista-light/10 hover:border-vista-blue/30 transition-colors">
                <CardHeader>
                  <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                    <div>
                      <CardTitle className="text-xl text-vista-light mb-2">{job.title}</CardTitle>
                      <div className="flex flex-wrap gap-2">
                        <Badge variant="secondary" className="bg-vista-blue/20 text-vista-blue">
                          {job.department}
                        </Badge>
                        <Badge variant="outline" className="border-vista-light/20 text-vista-light/70">
                          <MapPin className="w-3 h-3 mr-1" />
                          {job.location}
                        </Badge>
                        <Badge variant="outline" className="border-vista-light/20 text-vista-light/70">
                          <Briefcase className="w-3 h-3 mr-1" />
                          {job.type}
                        </Badge>
                        <Badge variant="outline" className="border-vista-light/20 text-vista-light/70">
                          <Clock className="w-3 h-3 mr-1" />
                          {job.posted}
                        </Badge>
                      </div>
                    </div>
                    <Button className="bg-vista-blue hover:bg-vista-blue/90 shrink-0">
                      Apply Now
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-vista-light/80 mb-4">{job.description}</p>
                  <div>
                    <h4 className="text-sm font-semibold text-vista-light mb-2">Key Requirements:</h4>
                    <div className="flex flex-wrap gap-2">
                      {job.requirements.map((req, reqIndex) => (
                        <Badge key={reqIndex} variant="outline" className="border-vista-light/20 text-vista-light/60 text-xs">
                          {req}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Application Process */}
      <section className="py-20 px-4 bg-gradient-to-r from-vista-blue/10 to-vista-accent/10">
        <div className="container mx-auto">
          <h2 className="text-4xl md:text-5xl font-bold text-center mb-16 text-vista-light">Application Process</h2>
          <div className="grid md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-vista-blue/20 rounded-full flex items-center justify-center">
                <span className="text-2xl font-bold text-vista-blue">1</span>
              </div>
              <h3 className="text-lg font-semibold mb-2 text-vista-light">Apply Online</h3>
              <p className="text-vista-light/70">Submit your application and resume through our careers portal.</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-vista-blue/20 rounded-full flex items-center justify-center">
                <span className="text-2xl font-bold text-vista-blue">2</span>
              </div>
              <h3 className="text-lg font-semibold mb-2 text-vista-light">Initial Review</h3>
              <p className="text-vista-light/70">Our team reviews your application and reaches out within 1 week.</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-vista-blue/20 rounded-full flex items-center justify-center">
                <span className="text-2xl font-bold text-vista-blue">3</span>
              </div>
              <h3 className="text-lg font-semibold mb-2 text-vista-light">Interviews</h3>
              <p className="text-vista-light/70">2-3 rounds of interviews with team members and leadership.</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-vista-blue/20 rounded-full flex items-center justify-center">
                <span className="text-2xl font-bold text-vista-blue">4</span>
              </div>
              <h3 className="text-lg font-semibold mb-2 text-vista-light">Welcome!</h3>
              <p className="text-vista-light/70">Join our team and start making an impact from day one.</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-vista-light">Ready to Make an Impact?</h2>
          <p className="text-xl text-vista-light/80 max-w-2xl mx-auto mb-8">
            Don't see the perfect role? We're always looking for exceptional talent. Send us your resume and let's talk.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact">
              <Button size="lg" className="bg-vista-blue hover:bg-vista-blue/90">
                Contact Our Team
              </Button>
            </Link>
            <Link href="/about">
              <Button size="lg" variant="outline" className="border-vista-light/20 text-vista-light hover:bg-vista-light/10">
                Learn More About Us
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}

import { IContent, WatchItem } from '@/data/content';

// Interface for watch history items with watch time tracking
export interface WatchHistoryItem {
  id: string;
  contentId: string;
  title: string;
  type: 'show' | 'movie';
  image: string;
  watchedAt: string;
  progress: number; // 0-100
  duration: number; // in seconds
  watchTime: number; // in seconds
  completed: boolean;
  season?: number;
  episode?: string;
  episodeTitle?: string;
}

// Interface for watch statistics
export interface WatchStats {
  totalWatchTime: number; // in seconds
  genreDistribution: { [key: string]: number }; // percentage
  contentTypeDistribution: { show: number; movie: number }; // percentage
  weeklyWatchTime: { [key: string]: number }; // day: seconds
  watchTimePerTimeOfDay: { morning: number; afternoon: number; evening: number; night: number }; // percentage
  favoriteGenres: { genre: string; count: number }[];
  topWatched: WatchHistoryItem[];
  recentActivity: WatchHistoryItem[];
  watchStreak: number; // days in a row with activity
  unfinishedContent: WatchHistoryItem[];
  completionRate: number; // percentage of started content that was finished
}

// Function to save watch history to localStorage
export function saveWatchHistory(history: WatchHistoryItem[]): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem('vista-watch-history', JSON.stringify(history));
  }
}

// Function to get watch history from localStorage
export function getWatchHistory(): WatchHistoryItem[] {
  if (typeof window !== 'undefined') {
    const saved = localStorage.getItem('vista-watch-history');
    return saved ? JSON.parse(saved) : [];
  }
  return [];
}

// Format seconds to hours and minutes
export function formatWatchTime(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (hours === 0) {
    return `${minutes} min${minutes !== 1 ? 's' : ''}`;
  }

  return `${hours} hr${hours !== 1 ? 's' : ''} ${minutes} min${minutes !== 1 ? 's' : ''}`;
}

// Get day name from date
export function getDayName(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', { weekday: 'short' });
}

// Add a watch event to history
export function addWatchEvent(
  content: IContent,
  watchTime: number,
  progress: number,
  completed = false,
  season?: number,
  episode?: string,
  episodeTitle?: string
): WatchHistoryItem {
  const now = new Date();
  const duration = content.type === 'movie' ? 7200 : 2700; // Default duration: 2h for movies, 45m for episodes

  const historyItem: WatchHistoryItem = {
    id: `wh_${Math.random().toString(36).substring(2, 11)}`,
    contentId: content.id,
    title: content.title,
    type: content.type,
    image: content.image,
    watchedAt: now.toISOString(),
    progress,
    duration,
    watchTime,
    completed,
  };

  // Add episode info if it's a show
  if (content.type === 'show' && season && episode) {
    historyItem.season = season;
    historyItem.episode = episode;
    historyItem.episodeTitle = episodeTitle || `Episode ${episode}`;
    historyItem.title = `${content.title} S${season}:E${episode}`;
  }

  // Get current history and add the new item
  const history = getWatchHistory();
  history.unshift(historyItem); // Add to the beginning
  saveWatchHistory(history);

  return historyItem;
}

// Calculate watch statistics based on watch history
export function calculateWatchStats(history: WatchHistoryItem[], allContent: IContent[]): WatchStats {
  // Initialize result object
  const stats: WatchStats = {
    totalWatchTime: 0,
    genreDistribution: {},
    contentTypeDistribution: { show: 0, movie: 0 },
    weeklyWatchTime: {
      'Mon': 0,
      'Tue': 0,
      'Wed': 0,
      'Thu': 0,
      'Fri': 0,
      'Sat': 0,
      'Sun': 0
    },
    watchTimePerTimeOfDay: { morning: 0, afternoon: 0, evening: 0, night: 0 },
    favoriteGenres: [],
    topWatched: [],
    recentActivity: [],
    watchStreak: 0,
    unfinishedContent: [],
    completionRate: 0
  };

  if (history.length === 0) {
    return stats;
  }

  // Create a mapping of content IDs to their genres
  const contentGenres: { [id: string]: string[] } = {};
  allContent.forEach(item => {
    contentGenres[item.id] = item.genres;
  });

  // Calculate total watch time
  stats.totalWatchTime = history.reduce((total, item) => total + item.watchTime, 0);

  // Calculate content type distribution
  const showCount = history.filter(item => item.type === 'show').length;
  const movieCount = history.filter(item => item.type === 'movie').length;
  const totalItems = history.length;

  stats.contentTypeDistribution.show = totalItems ? (showCount / totalItems) * 100 : 0;
  stats.contentTypeDistribution.movie = totalItems ? (movieCount / totalItems) * 100 : 0;

  // Calculate genre distribution
  const genreCounts: { [genre: string]: number } = {};
  history.forEach(item => {
    const genres = contentGenres[item.contentId] || [];
    genres.forEach(genre => {
      genreCounts[genre] = (genreCounts[genre] || 0) + 1;
    });
  });

  // Convert genre counts to percentages
  const totalGenreCounts = Object.values(genreCounts).reduce((total, count) => total + count, 0);
  Object.keys(genreCounts).forEach(genre => {
    stats.genreDistribution[genre] = totalGenreCounts ? (genreCounts[genre] / totalGenreCounts) * 100 : 0;
  });

  // Calculate weekly watch time
  history.forEach(item => {
    const day = getDayName(item.watchedAt);
    if (day in stats.weeklyWatchTime) {
      stats.weeklyWatchTime[day] += item.watchTime;
    }
  });

  // Calculate watch time per time of day
  history.forEach(item => {
    const hour = new Date(item.watchedAt).getHours();
    if (hour >= 5 && hour < 12) {
      stats.watchTimePerTimeOfDay.morning += item.watchTime;
    } else if (hour >= 12 && hour < 17) {
      stats.watchTimePerTimeOfDay.afternoon += item.watchTime;
    } else if (hour >= 17 && hour < 22) {
      stats.watchTimePerTimeOfDay.evening += item.watchTime;
    } else {
      stats.watchTimePerTimeOfDay.night += item.watchTime;
    }
  });

  // Convert time of day to percentages
  const totalDayTime = Object.values(stats.watchTimePerTimeOfDay).reduce((total, time) => total + time, 0);
  if (totalDayTime > 0) {
    Object.keys(stats.watchTimePerTimeOfDay).forEach(key => {
      stats.watchTimePerTimeOfDay[key as keyof typeof stats.watchTimePerTimeOfDay] =
        (stats.watchTimePerTimeOfDay[key as keyof typeof stats.watchTimePerTimeOfDay] / totalDayTime) * 100;
    });
  }

  // Top favorite genres
  stats.favoriteGenres = Object.entries(genreCounts)
    .map(([genre, count]) => ({ genre, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);

  // Top watched content
  const contentWatchTimes: { [id: string]: number } = {};
  history.forEach(item => {
    const contentId = item.contentId;
    contentWatchTimes[contentId] = (contentWatchTimes[contentId] || 0) + item.watchTime;
  });

  // Get unique content entries with the highest watch time
  const uniqueContentIds = new Set<string>();
  const uniqueHistoryItems: WatchHistoryItem[] = [];

  // Sort history by content ID and then by watch time
  const sortedHistory = [...history].sort((a, b) => {
    if (a.contentId === b.contentId) {
      return b.watchTime - a.watchTime;
    }
    return contentWatchTimes[b.contentId] - contentWatchTimes[a.contentId];
  });

  // Get top items, ensuring uniqueness by content ID
  sortedHistory.forEach(item => {
    if (!uniqueContentIds.has(item.contentId) && uniqueHistoryItems.length < 5) {
      uniqueContentIds.add(item.contentId);
      uniqueHistoryItems.push(item);
    }
  });

  stats.topWatched = uniqueHistoryItems;

  // Recent activity (last 5 unique content pieces)
  const recentContentIds = new Set<string>();
  const recentItems: WatchHistoryItem[] = [];

  // Sort by most recent
  const sortedByDate = [...history].sort((a, b) =>
    new Date(b.watchedAt).getTime() - new Date(a.watchedAt).getTime()
  );

  // Get recent items, ensuring uniqueness
  sortedByDate.forEach(item => {
    if (!recentContentIds.has(item.contentId) && recentItems.length < 5) {
      recentContentIds.add(item.contentId);
      recentItems.push(item);
    }
  });

  stats.recentActivity = recentItems;

  // Calculate watch streak
  let currentStreak = 0;
  let lastDate: Date | null = null;
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // Sort history by date (most recent first)
  const sortedForStreak = [...history].sort((a, b) =>
    new Date(b.watchedAt).getTime() - new Date(a.watchedAt).getTime()
  );

  // Group watches by day
  const watchesByDay = new Map<string, boolean>();
  sortedForStreak.forEach(item => {
    const date = new Date(item.watchedAt);
    date.setHours(0, 0, 0, 0);
    watchesByDay.set(date.toISOString(), true);
  });

  // Calculate streak
  const dates = Array.from(watchesByDay.keys())
    .map(dateStr => new Date(dateStr))
    .sort((a, b) => b.getTime() - a.getTime());

  for (let i = 0; i < dates.length; i++) {
    const date = dates[i];
    if (!lastDate) {
      // First day in streak
      lastDate = date;
      currentStreak = 1;
      continue;
    }

    const dayDiff = Math.floor((lastDate.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    if (dayDiff === 1) {
      // Consecutive day
      currentStreak++;
      lastDate = date;
    } else {
      // Streak broken
      break;
    }
  }

  stats.watchStreak = currentStreak;

  // Calculate unfinished content
  stats.unfinishedContent = sortedByDate
    .filter(item => !item.completed && item.progress > 0 && item.progress < 90)
    .reduce((acc, item) => {
      if (!acc.some(i => i.contentId === item.contentId)) {
        acc.push(item);
      }
      return acc;
    }, [] as WatchHistoryItem[])
    .slice(0, 5);

  // Calculate completion rate
  const startedContent = new Set<string>();
  const completedContent = new Set<string>();

  history.forEach(item => {
    startedContent.add(item.contentId);
    if (item.completed) {
      completedContent.add(item.contentId);
    }
  });

  stats.completionRate = startedContent.size > 0
    ? (completedContent.size / startedContent.size) * 100
    : 0;

  return stats;
}

// Generate sample watch history for demonstration
export function generateSampleWatchHistory(continueWatching: WatchItem[], allContent: IContent[]): WatchHistoryItem[] {
  const result: WatchHistoryItem[] = [];

  // Helper to create dates in the past
  const daysAgo = (days: number, hourOffset = 0) => {
    const date = new Date();
    date.setDate(date.getDate() - days);
    date.setHours(date.getHours() - hourOffset);
    return date.toISOString();
  };

  // Helper to get random watch time between min and max minutes
  const randomWatchTime = (min: number, max: number) => {
    return Math.floor(Math.random() * (max - min + 1) + min) * 60; // Convert to seconds
  };

  // Add items from continue watching with realistic progress
  continueWatching.forEach((item, index) => {
    const content = allContent.find(c => c.id === item.id);
    if (!content) return;

    result.push({
      id: `wh_${Math.random().toString(36).substring(2, 11)}`,
      contentId: item.id,
      title: content.title,
      type: content.type,
      image: content.image,
      watchedAt: daysAgo(index, Math.floor(Math.random() * 24)), // Spread over last few days
      progress: Math.floor(Math.random() * 40) + 30, // 30-70% progress
      duration: content.type === 'movie' ? 7200 : 2700, // 2h for movies, 45m for shows
      watchTime: randomWatchTime(30, 90), // 30-90 minutes watched
      completed: false,
      season: content.type === 'show' ? 1 : undefined,
      episode: content.type === 'show' ? '1' : undefined,
      episodeTitle: content.type === 'show' ? 'Pilot' : undefined
    });
  });

  // Add some completed content for better stats
  const completedContent = allContent
    .filter(content => !continueWatching.some(item => item.id === content.id))
    .slice(0, 10);

  completedContent.forEach((content, index) => {
    // Create 2-3 watch sessions for each completed content
    const sessionsCount = Math.floor(Math.random() * 2) + 2;
    const totalProgress = 100;
    let remainingProgress = totalProgress;

    for (let session = 0; session < sessionsCount; session++) {
      const isLastSession = session === sessionsCount - 1;
      const sessionProgress = isLastSession
        ? remainingProgress
        : Math.floor((remainingProgress / (sessionsCount - session)) * (0.7 + Math.random() * 0.6));
      
      remainingProgress -= sessionProgress;

      result.push({
        id: `wh_${Math.random().toString(36).substring(2, 11)}`,
        contentId: content.id,
        title: content.title,
        type: content.type,
        image: content.image,
        watchedAt: daysAgo(index * 2 + session, Math.floor(Math.random() * 12)), // Spread over past month
        progress: totalProgress - remainingProgress,
        duration: content.type === 'movie' ? 7200 : 2700,
        watchTime: randomWatchTime(45, 120), // 45-120 minutes per session
        completed: isLastSession,
        season: content.type === 'show' ? 1 : undefined,
        episode: content.type === 'show' ? '1' : undefined,
        episodeTitle: content.type === 'show' ? 'Pilot' : undefined
      });
    }
  });

  // Add some binge-watching sessions (multiple episodes of same show in one day)
  const bingeShows = allContent
    .filter(content => content.type === 'show' && !continueWatching.some(item => item.id === content.id))
    .slice(0, 3);

  bingeShows.forEach((show, showIndex) => {
    // 3-5 episodes per binge session
    const episodeCount = Math.floor(Math.random() * 3) + 3;
    
    for (let episode = 1; episode <= episodeCount; episode++) {
      result.push({
        id: `wh_${Math.random().toString(36).substring(2, 11)}`,
        contentId: show.id,
        title: show.title,
        type: 'show',
        image: show.image,
        watchedAt: daysAgo(showIndex * 3, episode), // Same day, different hours
        progress: 100,
        duration: 2700, // 45 minutes
        watchTime: randomWatchTime(40, 45), // Almost full episode
        completed: true,
        season: 1,
        episode: episode.toString(),
        episodeTitle: `Episode ${episode}`
      });
    }
  });

  // Sort by watchedAt
  return result.sort((a, b) => new Date(b.watchedAt).getTime() - new Date(a.watchedAt).getTime());
}

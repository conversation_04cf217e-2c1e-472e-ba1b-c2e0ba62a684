"use client";

import { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Menu, Search, X, ChevronDown, Bell, Globe,
  LogOut, Settings, User, Download, Users,
  Home, Film, Tv, Heart, LayoutDashboard, Wifi
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import LanguageSelector from '@/components/LanguageSelector';
// Import language context for LanguageSelector component
import { useLanguage } from '@/lib/i18n/LanguageContext';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
// Removed unused import
import { useAuth } from '@/contexts/AuthContext';
import SearchBar from '@/components/SearchBar';
import { useProfiles } from '@/contexts/ProfileContext';
import { UserAvatar } from '@/components/UserAvatar';
import { useNotifications } from '@/contexts/NotificationContext';
import { cn } from '@/lib/utils';
import { ConnectionStatusIndicator } from '@/components/ConnectionStatusIndicator';

interface NavbarProps {
  className?: string;
}

// Using named export to ensure ESM compatibility
export const Navbar = ({
  className
}: NavbarProps) => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  // State for UI elements
  const [profileMenuVisible, setProfileMenuVisible] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  // We need to initialize the language context for the LanguageSelector component
  // even if we don't directly use the 't' function in this component
  useLanguage();
  const { user, isAuthenticated, signOut, isAdmin } = useAuth();
  const { activeProfile } = useProfiles();
  const { unreadCount: notificationCount } = useNotifications();

  const pathname = usePathname();
  const router = useRouter();

  const profileMenuRef = useRef<HTMLDivElement>(null);
  const profileButtonRef = useRef<HTMLButtonElement>(null);

  // Add state for profile image to track changes
  const [profileImage, setProfileImage] = useState('');
  const [userInitials, setUserInitials] = useState('SV');
  const [displayName, setDisplayName] = useState('User');
  const [isMainProfile, setIsMainProfile] = useState(false);

  // Memoize the profile values to prevent unnecessary re-renders
  const memoizedProfileValues = useMemo(() => {
    // Calculate image source - prefer active profile image if available
    const imgSrc = activeProfile?.avatar || user?.picture || '/favicon.svg';
    
    // Calculate initials as fallback
    const initials = activeProfile?.name
      ? activeProfile.name.substring(0, 2).toUpperCase()
      : user?.name
        ? user.name.substring(0, 2).toUpperCase()
        : 'SV';
    
    // Calculate display name
    const name = activeProfile?.name || user?.name || 'User';
    
    // Calculate main profile flag
    const isMain = activeProfile?.isPrimary || false;

    return { imgSrc, initials, name, isMain };
  }, [activeProfile, user]);

  // Update profile image and related state only when memoized values change
  useEffect(() => {
    setProfileImage(memoizedProfileValues.imgSrc);
    setUserInitials(memoizedProfileValues.initials);
    setDisplayName(memoizedProfileValues.name);
    setIsMainProfile(memoizedProfileValues.isMain);
  }, [memoizedProfileValues]);

  // Add a constant for the sign in text
  const signInText = "Sign In";

  // Handle client-side hydration
  useEffect(() => {
    // Set mounted state immediately
    setIsMounted(true);

    // Create a more robust sign-in button handler focused on edge cases
    const handleSignInButtonClick = (e: MouseEvent) => {
      // Only run this handler for elements that are NOT buttons
      // This handler is meant to catch cases where a child element is clicked but not the button itself
      const target = e.target as HTMLElement;
      
      // Skip if the target is a button or if a parent button was directly clicked
      if (target.tagName === 'BUTTON' || target.closest('button[onclick]')) {
        return;
      }
      
      const signInButton = target.closest('button[data-signin-button="true"]');

      if (signInButton) {
        e.preventDefault();
        e.stopPropagation();
        console.log('Sign-in button clicked via global handler (edge case)');

        // Use a more direct navigation approach to avoid Next.js router issues during hydration
        setTimeout(() => {
          window.location.href = '/auth';
        }, 10);
      }
    };

    // Add the event listener with capture phase to ensure it runs before React's handlers
    document.addEventListener('click', handleSignInButtonClick, true);

    // Clean up the event listener when component unmounts
    return () => {
      document.removeEventListener('click', handleSignInButtonClick, true);
    };
  }, []);

  // No need to track pathname for auth checks anymore

  // Update UI when auth state changes
  useEffect(() => {
    // Close mobile menu when auth state changes
    setIsMobileMenuOpen(false);
    setProfileMenuVisible(false);

    // Reset body overflow
    document.body.style.overflow = '';

    // This effect runs whenever isAuthenticated or user changes
    // ensuring the navbar UI updates accordingly
  }, [isAuthenticated, user]);

  // Reset body overflow when component unmounts
  useEffect(() => {
    return () => {
      document.body.style.overflow = '';
    };
  }, []);

  // Handle scroll event to change navbar appearance
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 20) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Handle toggle of profile menu
  const toggleProfileMenu = () => {
    setProfileMenuVisible(!profileMenuVisible);
  };

  // Handle click outside profile menu to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Close the menu if clicking outside both the menu and the button
      if (
        profileMenuVisible &&
        profileMenuRef.current &&
        profileButtonRef.current &&
        !profileMenuRef.current.contains(event.target as Node) &&
        !profileButtonRef.current.contains(event.target as Node)
      ) {
        setProfileMenuVisible(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [profileMenuVisible]);

  // Check if a path is active
  const isActive = (path: string) => {
    if (!pathname) return false;
    return pathname === path || pathname.startsWith(`${path}/`);
  };

  // Handle search toggle
  const handleSearchToggle = () => {
    setIsSearchOpen(!isSearchOpen);
  };

  // Handle sign in - use direct navigation to avoid router issues
  const handleSignIn = (e: React.MouseEvent | React.TouchEvent) => {
    // Prevent default actions immediately
    if (e && e.preventDefault) e.preventDefault();
    if (e && e.stopPropagation) e.stopPropagation();
    
    console.log('Sign-in button clicked via React handler');
    
    // Force a small delay before navigation on mobile to ensure event is fully processed
    setTimeout(() => {
      window.location.href = '/auth';
    }, 10);
  };

  // Handle sign out with improved error handling and feedback
  const handleSignOut = async () => {
    try {
      console.log('Navbar: Initiating sign out process');

      // Close UI elements first for better UX
      setProfileMenuVisible(false);
      setIsMobileMenuOpen(false);

      // Call the sign out function from AuthContext
      // Now it returns a Promise we can await
      const result = await signOut();

      if (!result.success) {
        console.error('Navbar: Sign out reported an error:', result.error);
      } else {
        console.log('Navbar: Sign out successful');
        if (result.error) {
          console.warn('Navbar: Sign out warning:', result.error);
        }
      }

      // Force a page reload to ensure all state is cleared
      // This is important for Netlify deployments where cookie clearing might have issues
      console.log('Navbar: Sign out completed, reloading page');
      window.location.href = '/';
    } catch (error) {
      console.error('Navbar: Error during sign out:', error);
      // Continue with navigation even if there's an error
      window.location.href = '/';
    }
  };

  // Profile menu content
  const renderProfileMenu = () => (
    <motion.div
      ref={profileMenuRef}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 10 }}
      transition={{ duration: 0.2 }}
      className="absolute right-0 mt-2 w-72 bg-vista-dark-lighter/95 backdrop-blur-md rounded-lg shadow-xl overflow-hidden border border-vista-light/20 z-50"
    >
      <div className="pb-2">
        <div className="p-5 mb-1">
          <h3 className="text-sm font-semibold text-vista-light/80 mb-3">Who's watching?</h3>
          <div className="flex items-center space-x-4">
            <UserAvatar
              src={profileImage}
              alt={displayName}
              fallback={userInitials}
              size="md"
              className={isMainProfile ? "ring-2 ring-vista-blue" : ""}
            />
            <div className="flex flex-col">
              <div className="flex items-center">
                <span className="font-medium text-vista-light mr-2 text-base">
                  {displayName}
                </span>
                {isMainProfile && (
                  <Badge variant="outline" className="text-[11px] h-5 border-vista-blue text-vista-blue font-medium px-1.5">Main</Badge>
                )}
              </div>
              <span className="text-sm text-vista-light/70 mt-0.5">
                {activeProfile?.isKids ? 'Kids Profile' : 'Profile'}
              </span>
            </div>
          </div>
        </div>

        <div className="px-5 pb-3">
          <Link href="/profiles">
            <Button
              variant="outline"
              size="sm"
              className="w-full text-sm bg-transparent border-vista-blue text-vista-blue hover:bg-vista-blue/10 py-2 h-auto font-medium"
            >
              <Users className="h-4 w-4 mr-2" /> Switch Profile
            </Button>
          </Link>
        </div>
      </div>

      <div className="border-t border-vista-light/15 px-5 py-3">
        <div className="flex items-center py-2">
          <User className="h-5 w-5 mr-3 text-vista-light/70" />
          <div className="flex flex-col">
            <span className="text-sm font-medium text-vista-light">{user?.name}</span>
            <span className="text-xs text-vista-light/70 mt-0.5">{user?.email}</span>
          </div>
        </div>
      </div>

      <div className="border-t border-vista-light/15">
        <Link href="/settings" className="flex items-center px-5 py-3 text-sm text-vista-light hover:bg-vista-light/10 transition-colors">
          <Settings className="h-5 w-5 mr-3 text-vista-light/70" />
          <span className="font-medium">Account & Settings</span>
        </Link>

        <Link href="/profiles" className="flex items-center px-5 py-3 text-sm text-vista-light hover:bg-vista-light/10 transition-colors">
          <Users className="h-5 w-5 mr-3 text-vista-light/70" />
          <span className="font-medium">Manage Profiles</span>
        </Link>

        <Link href="/my-list" className="flex items-center px-5 py-3 text-sm text-vista-light hover:bg-vista-light/10 transition-colors">
          <Heart className="h-5 w-5 mr-3 text-vista-light/70" />
          <span className="font-medium">My List</span>
        </Link>

        {isAdmin() && (
          <div
            className="flex items-center px-5 py-3 text-sm text-vista-blue hover:bg-vista-blue/10 transition-colors cursor-pointer"
            onClick={(e) => {
              e.preventDefault();
              setProfileMenuVisible(false);
              // Use direct navigation to avoid potential issues with Next.js router
              window.location.href = '/admin';
            }}
          >
            <LayoutDashboard className="h-5 w-5 mr-3 text-vista-blue" />
            <span className="font-medium">Admin Dashboard</span>
          </div>
        )}
      </div>

      <div className="border-t border-vista-light/15 p-4">
        <button
          onClick={handleSignOut}
          className="w-full flex items-center justify-center px-4 py-2.5 text-sm font-medium text-vista-light hover:bg-vista-light/10 rounded-md transition-colors"
        >
          <LogOut className="h-5 w-5 mr-2.5 text-vista-light/70" />
          <span>Sign Out</span>
        </button>
      </div>
    </motion.div>
  );

  // Prevent hydration errors by rendering minimal content until mounted
  if (!isMounted) {
    return (
      <header className="fixed top-0 left-0 right-0 z-50 bg-black bg-opacity-95">
        <div className="container mx-auto px-4 md:px-6">
          <div className="flex items-center justify-between h-16 md:h-16">
            {/* Logo */}
            <div className="flex items-center">
              <div className="relative w-12 h-12 md:w-14 md:h-14">
                {/* Image placeholder - will be properly rendered after mounting */}
                <div className="w-12 h-12 bg-vista-blue/10 flex items-center justify-center">
                  <div className="w-8 h-8 bg-vista-blue/20"></div>
                </div>
              </div>
              <span className="ml-2 text-xl md:text-2xl font-bold tracking-tight text-vista-light">StreamVista</span>
            </div>
          </div>
        </div>
      </header>
    );
  }

  return (
    <>
      <header
        className={cn(
          "fixed top-0 left-0 right-0 z-50 transition-all duration-300",
          isScrolled || isSearchOpen
            ? "bg-black bg-opacity-95 backdrop-blur-md"
            : "bg-gradient-to-b from-black to-transparent md:bg-gradient-to-b md:from-black md:to-transparent bg-black",
          "navbar-fixed",
          className
        )}
      >
        <div className="container mx-auto px-2 xs:px-3 sm:px-4 md:px-6">
          <div className="flex items-center justify-between h-16 md:h-16">
            {/* Logo */}
            <Link href="/" className="flex items-center group logo-container">
              <div className="relative w-12 h-12 xs:w-14 xs:h-14 md:w-16 md:h-16">
                <Image
                  src="/logo.webp"
                  alt="StreamVista"
                  width={64}
                  height={64}
                  priority
                  quality={100}
                  className="object-contain transition-transform duration-300 group-hover:scale-105 drop-shadow-[0_2px_4px_rgba(0,0,0,0.3)]"
                />
              </div>
              <span className="ml-1 xs:ml-2 text-lg xs:text-xl md:text-2xl font-bold tracking-tight text-vista-light group-hover:text-vista-blue transition-colors duration-300">StreamVista</span>
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-8">
              <Link
                href="/"
                className={`text-vista-light hover:text-vista-blue transition-colors text-sm ${isActive('/') ? 'font-medium text-vista-blue' : ''}`}
              >
                <div className="flex items-center">
                  <Home className="h-3.5 w-3.5 mr-1.5" />
                  Home
                </div>
              </Link>
              <Link
                href="/movies"
                className={`text-vista-light hover:text-vista-blue transition-colors text-sm ${isActive('/movies') ? 'font-medium text-vista-blue' : ''}`}
              >
                <div className="flex items-center">
                  <Film className="h-3.5 w-3.5 mr-1.5" />
                  Movies
                </div>
              </Link>
              <Link
                href="/shows"
                className={`text-vista-light hover:text-vista-blue transition-colors text-sm ${isActive('/shows') ? 'font-medium text-vista-blue' : ''}`}
              >
                <div className="flex items-center">
                  <Tv className="h-3.5 w-3.5 mr-1.5" />
                  TV Shows
                </div>
              </Link>
              <Link
                href="/watch-party"
                className={`text-vista-light hover:text-vista-blue transition-colors text-sm ${isActive('/watch-party') ? 'font-medium text-vista-blue' : ''}`}
              >
                <div className="flex items-center">
                  <Users className="h-3.5 w-3.5 mr-1.5" />
                  Watch Party
                </div>
              </Link>
              <Link
                href="/my-list"
                className={`text-vista-light hover:text-vista-blue transition-colors text-sm ${isActive('/my-list') ? 'font-medium text-vista-blue' : ''}`}
              >
                <div className="flex items-center">
                  <Heart className="h-3.5 w-3.5 mr-1.5" />
                  My List
                </div>
              </Link>

            </nav>

            {/* Tablet Navigation (md screens like iPad) */}
            <nav className="hidden md:flex lg:hidden items-center">
              <Link
                href="/"
                className={`px-3 py-2 text-vista-light hover:text-vista-blue transition-colors ${isActive('/') ? 'text-vista-blue' : ''}`}
              >
                <Home className="h-5 w-5" />
              </Link>
              <Link
                href="/movies"
                className={`px-3 py-2 text-vista-light hover:text-vista-blue transition-colors ${isActive('/movies') ? 'text-vista-blue' : ''}`}
              >
                <Film className="h-5 w-5" />
              </Link>
              <Link
                href="/shows"
                className={`px-3 py-2 text-vista-light hover:text-vista-blue transition-colors ${isActive('/shows') ? 'text-vista-blue' : ''}`}
              >
                <Tv className="h-5 w-5" />
              </Link>
              <Link
                href="/watch-party"
                className={`px-3 py-2 text-vista-light hover:text-vista-blue transition-colors ${isActive('/watch-party') ? 'text-vista-blue' : ''}`}
              >
                <Users className="h-5 w-5" />
              </Link>
              <Link
                href="/my-list"
                className={`px-3 py-2 text-vista-light hover:text-vista-blue transition-colors ${isActive('/my-list') ? 'text-vista-blue' : ''}`}
              >
                <Heart className="h-5 w-5" />
              </Link>
            </nav>

            {/* Action Buttons */}
            <div className="flex items-center space-x-3 xs:space-x-4 sm:space-x-5">
              {/* Search Button */}
              <Button
                variant="ghost"
                size="icon"
                className="text-white hover:text-white hover:bg-transparent w-9 h-9 flex items-center justify-center"
                onClick={handleSearchToggle}
              >
                <Search className="h-4 w-4 xs:h-4.5 xs:w-4.5" />
              </Button>

              {/* Sign In Button - Only show when NOT authenticated */}
              {!isAuthenticated && (
                <Button
                  variant="default"
                  size="sm"
                  className="hidden md:flex items-center bg-white text-black hover:bg-white/90"
                  onClick={handleSignIn}
                  onTouchEnd={handleSignIn}
                  data-signin-button="true"
                >
                  <User className="h-3.5 w-3.5 mr-1.5" />
                  <span>{signInText}</span>
                </Button>
              )}

              {/* Connection Status Indicator - Only show when authenticated */}
              {isAuthenticated && (
                <div className="hidden md:block">
                  <ConnectionStatusIndicator />
                </div>
              )}

              {/* Notifications - Only show when authenticated */}
              {isAuthenticated && (
                <div className="hidden md:block relative">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-vista-light hover:text-vista-accent w-9 h-9 flex items-center justify-center"
                    onClick={() => router.push('/notifications')}
                  >
                    <Bell className="h-4.5 w-4.5 xs:h-5 xs:w-5" />
                    {notificationCount > 0 && (
                      <span className="absolute top-0 right-0 h-4 w-4 bg-vista-blue text-white rounded-full text-xs flex items-center justify-center">
                        {notificationCount > 9 ? '9+' : notificationCount}
                      </span>
                    )}
                  </Button>
                </div>
              )}

              {/* User Profile Button - Only show when authenticated */}
              {isAuthenticated && (
                <div className="relative">
                  <button
                    ref={profileButtonRef}
                    onClick={toggleProfileMenu}
                    className="flex items-center focus:outline-none h-9"
                    aria-label="User menu"
                  >
                    <div className="relative">
                      <UserAvatar
                        src={profileImage}
                        alt={displayName}
                        fallback={userInitials}
                        size="sm"
                        className={`${isMainProfile ? "ring-2 ring-vista-blue" : "ring-1 ring-vista-light/30"}`}
                      />
                      {activeProfile?.isKids && (
                        <div className="absolute -bottom-1 -right-1 bg-amber-500 rounded-full w-3 h-3 border border-black"></div>
                      )}
                      {isMainProfile && (
                        <div className="absolute -top-1 -right-1 bg-vista-blue rounded-full w-3 h-3 border border-black"></div>
                      )}
                    </div>
                    <div className="hidden md:flex flex-col ml-2.5 mr-1.5 items-start">
                      <span className="text-sm font-medium text-vista-light">{displayName}</span>
                      <span className="text-xs text-vista-light/70">
                        {isMainProfile ? "Main Profile" : "Profile"}
                      </span>
                    </div>
                    <ChevronDown className={`h-4 w-4 text-vista-light/70 transition-transform duration-200 ${profileMenuVisible ? 'rotate-180' : ''}`} />
                  </button>

                  <AnimatePresence>
                    {profileMenuVisible && (
                      <div ref={profileMenuRef}>
                        {renderProfileMenu()}
                      </div>
                    )}
                  </AnimatePresence>
                </div>
              )}

              {/* Mobile Menu Button - CHANGED FROM md:hidden TO lg:hidden */}
              <Button
                variant="outline"
                size="icon"
                onClick={() => {
                  const newState = !isMobileMenuOpen;
                  setIsMobileMenuOpen(newState);
                  // Prevent body scrolling when menu is open
                  document.body.style.overflow = newState ? 'hidden' : '';
                }}
                className="lg:hidden w-9 h-9 flex items-center justify-center mobile-menu-button relative z-50"
                style={{ position: 'relative', zIndex: 50 }}
              >
                {isMobileMenuOpen ? (
                  <X className="h-5 w-5" />
                ) : (
                  <Menu className="h-5 w-5" />
                )}
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile Menu - CHANGED FROM md:hidden TO lg:hidden */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="fixed inset-0 z-40 pt-16 bg-black lg:hidden overflow-y-auto"
            onClick={(e) => {
              // Close menu when clicking on the backdrop
              if (e.target === e.currentTarget) {
                setIsMobileMenuOpen(false);
                document.body.style.overflow = '';
              }
            }}
          >
            <div className="px-4 xs:px-5 sm:px-6 pt-6 pb-24 flex flex-col h-full bg-vista-dark">
              {isAuthenticated ? (
                <div className="flex items-center mb-6 pb-6 border-b border-vista-light/10">
                  <Avatar className="h-10 w-10 xs:h-12 xs:w-12">
                    <AvatarImage className="rounded-full" src={profileImage} alt={user?.name || 'User'} />
                    <AvatarFallback>{userInitials}</AvatarFallback>
                  </Avatar>
                  <div className="ml-3">
                    <p className="text-vista-light font-medium text-sm xs:text-base">{user?.name}</p>
                    <p className="text-vista-light/60 text-xs xs:text-sm">{user?.email}</p>
                  </div>
                  <Badge variant="outline" className="ml-auto text-xs">{isAdmin() ? 'Admin' : 'User'}</Badge>
                </div>
              ) : (
                <div className="mb-6 pb-6 border-b border-vista-light/10">
                  <h2 className="text-vista-light font-medium text-lg xs:text-xl mb-1">Welcome to StreamVista</h2>
                  <p className="text-vista-light/70 text-sm xs:text-base">Sign in to access your personalized content and watchlist.</p>
                </div>
              )}

              <nav className="flex flex-col space-y-3 xs:space-y-4 py-3 xs:py-4">
                <Link
                  href="/"
                  className="text-lg xs:text-xl font-medium text-vista-light hover:text-vista-blue transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <div className="flex items-center">
                    <Home className="h-4 w-4 xs:h-5 xs:w-5 mr-2" />
                    Home
                  </div>
                </Link>
                <Link
                  href="/shows"
                  className="text-lg xs:text-xl font-medium text-vista-light hover:text-vista-blue transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <div className="flex items-center">
                    <Tv className="h-4 w-4 xs:h-5 xs:w-5 mr-2" />
                    Shows
                  </div>
                </Link>
                <Link
                  href="/movies"
                  className="text-lg xs:text-xl font-medium text-vista-light hover:text-vista-blue transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <div className="flex items-center">
                    <Film className="h-4 w-4 xs:h-5 xs:w-5 mr-2" />
                    Movies
                  </div>
                </Link>
                <Link
                  href="/watch-party"
                  className="text-lg xs:text-xl font-medium text-vista-light hover:text-vista-blue transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <div className="flex items-center">
                    <Users className="h-4 w-4 xs:h-5 xs:w-5 mr-2" />
                    Watch Party
                  </div>
                </Link>
                <div className="text-lg xs:text-xl font-medium text-vista-light hover:text-vista-blue transition-colors">
                  <div className="flex items-center">
                    <Globe className="h-4 w-4 xs:h-5 xs:w-5 mr-2" />
                    <LanguageSelector variant="text" align="start" className="p-0 m-0 h-auto" />
                  </div>
                </div>
                {isAuthenticated && (
                  <>
                    <Link
                      href="/my-list"
                      className="text-lg xs:text-xl font-medium text-vista-light hover:text-vista-blue transition-colors"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <div className="flex items-center">
                        <Heart className="h-4 w-4 xs:h-5 xs:w-5 mr-2" />
                        My List
                      </div>
                    </Link>
                    <Link
                      href="/profiles"
                      className="text-lg xs:text-xl font-medium text-vista-light hover:text-vista-blue transition-colors"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <div className="flex items-center">
                        <Users className="h-4 w-4 xs:h-5 xs:w-5 mr-2" />
                        Switch Profiles
                      </div>
                    </Link>
                    <Link
                      href="/settings"
                      className="text-lg xs:text-xl font-medium text-vista-light hover:text-vista-blue transition-colors"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <div className="flex items-center">
                        <Settings className="h-4 w-4 xs:h-5 xs:w-5 mr-2" />
                        Account & Settings
                      </div>
                    </Link>
                    <div className="text-lg xs:text-xl font-medium text-vista-light mt-2">
                      <div className="flex items-center">
                        <Wifi className="h-4 w-4 xs:h-5 xs:w-5 mr-2" />
                        Connection Status
                        <div className="ml-2">
                          <ConnectionStatusIndicator showTooltip={false} />
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </nav>

              {isAuthenticated && (
                <div className="mb-2 xs:mb-3 py-3 xs:py-4 border-t border-vista-light/10">
                  <h3 className="text-vista-light/50 text-xs xs:text-sm font-medium uppercase mb-2 xs:mb-3">Account</h3>
                  <div className="flex flex-col space-y-3 xs:space-y-4">
                    {isAdmin() && (
                      <div
                        className="text-base xs:text-lg font-medium text-vista-light hover:text-vista-blue transition-colors cursor-pointer"
                        onClick={() => {
                          setIsMobileMenuOpen(false);
                          setProfileMenuVisible(false);
                          // Use direct navigation to avoid potential issues with Next.js router
                          window.location.href = '/admin';
                        }}
                      >
                        <div className="flex items-center">
                          <LayoutDashboard className="h-4 w-4 xs:h-5 xs:w-5 mr-2" />
                          Admin Dashboard
                        </div>
                      </div>
                    )}
                    <Link
                      href="/notifications"
                      className="text-base xs:text-lg font-medium text-vista-light hover:text-vista-blue transition-colors"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <div className="flex items-center">
                        <Bell className="h-4 w-4 xs:h-5 xs:w-5 mr-2" />
                        Notifications
                        {notificationCount > 0 && (
                          <span className="ml-2 h-4 w-4 xs:h-5 xs:w-5 bg-vista-blue text-white rounded-full text-xs flex items-center justify-center">
                            {notificationCount > 9 ? '9+' : notificationCount}
                          </span>
                        )}
                      </div>
                    </Link>
                  </div>
                </div>
              )}

              <div className="mt-4 mb-8 xs:mb-10 pt-4 border-t border-vista-light/10">
                {isAuthenticated ? (
                  <div className="flex flex-col items-center">
                    <Button
                      variant="outline"
                      className="w-full max-w-xs border-none bg-gradient-to-br from-red-800/90 via-red-700/90 to-red-900/90 text-white hover:text-white/95 hover:from-red-700 hover:via-red-600 hover:to-red-800 py-4 xs:py-5 rounded-lg text-sm xs:text-base shadow-[0_6px_20px_rgba(185,28,28,0.25)] hover:shadow-[0_8px_25px_rgba(185,28,28,0.35)] backdrop-blur-sm transition-all duration-300 flex items-center justify-center mb-10 border border-red-500/20"
                      onClick={() => {
                        handleSignOut();
                        setIsMobileMenuOpen(false);
                      }}
                    >
                      <LogOut className="mr-2.5 h-4 w-4 xs:h-5 xs:w-5 text-white/90" />
                      <span className="font-medium tracking-wide">Sign Out</span>
                    </Button>
                  </div>
                ) : (
                  <div className="flex flex-col items-center">
                    <Button
                      variant="default"
                      className="w-full max-w-xs border-none bg-gradient-to-r from-vista-blue/80 to-blue-500/80 hover:from-vista-blue hover:to-blue-600 text-white py-3 xs:py-4 rounded-md text-sm xs:text-base shadow-[0_4px_20px_rgba(0,120,255,0.3)] backdrop-blur-sm transition-all duration-300 flex items-center justify-center"
                      onClick={(e) => {
                        handleSignIn(e);
                        setIsMobileMenuOpen(false);
                      }}
                      onTouchEnd={(e) => {
                        handleSignIn(e);
                        setIsMobileMenuOpen(false);
                      }}
                      data-signin-button="true"
                    >
                      <User className="mr-2 h-4 w-4 xs:h-5 xs:w-5" />
                      <span className="font-medium">{signInText}</span>
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Search Bar Component */}
      <SearchBar isOpen={isSearchOpen} onClose={() => setIsSearchOpen(false)} />
    </>
  );
};

// Also add a default export that re-exports the named export for backward compatibility
export default Navbar;

/**
 * Utility functions for handling API errors
 */

/**
 * Extract a readable error message from various error types
 * @param error The error object
 * @returns A user-friendly error message
 */
export function getApiErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  if (error && typeof error === 'object' && 'message' in error && typeof error.message === 'string') {
    return error.message;
  }
  
  return 'An unexpected error occurred';
}

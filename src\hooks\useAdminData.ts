'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import useSWR from 'swr';
import { useToastHelpers } from '@/lib/ToastContext';
import { useAuth } from '@/contexts/AuthContext';

interface FetchOptions {
  endpoint: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  body?: Record<string, unknown>;
  params?: Record<string, string | number | boolean | undefined>;
  cacheDuration?: number; // Optional cache duration in milliseconds
}

interface UseFetchResult<T> {
  data: T | null;
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
}

// Global cache for storing admin data responses
const responseCache = new Map<string, { data: unknown; timestamp: number }>();

/**
 * Custom hook for fetching admin data
 */
export function useAdminData<T = Record<string, unknown>>({
  endpoint,
  method = 'GET',
  body,
  params = {},
  cacheDuration = 60000, // Default: 1 minute cache
}: FetchOptions): UseFetchResult<T> {
  const [data, setData] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const lastFetchTime = useRef<number>(0);
  const cacheKey = useRef<string>('');
  const isMounted = useRef<boolean>(true);

  // Get the user from auth context
  const { user } = useAuth();
  const userId = user?.id;

  // Build URL with query parameters and generate cache key
  const buildUrl = useCallback(() => {
    if (!userId) return null; // Don't build URL if no userId

    const url = new URL(`/api/admin/${endpoint}`, window.location.origin);

    // Add userId as a query parameter
    url.searchParams.append('userId', userId);

    // Add other query parameters
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        url.searchParams.append(key, String(value));
      }
    });

    // Generate cache key
    cacheKey.current = `${method}:${url.toString()}:${JSON.stringify(body || {})}`;

    return url.toString();
  }, [endpoint, params, method, body, userId]);

  // Fetch data function
  const fetchData = useCallback(async (forceRefresh = false) => {
    const url = buildUrl();
    const now = Date.now();

    // If URL is null, return early
    if (!url) {
      setError(new Error('Cannot build URL: missing userId or endpoint'));
      setIsLoading(false);
      return;
    }

    // Check if we have a valid cached response and it's not a forced refresh
    if (!forceRefresh && responseCache.has(cacheKey.current)) {
      const cachedData = responseCache.get(cacheKey.current);
      if (cachedData && (now - cachedData.timestamp < cacheDuration)) {
        if (!data) { // Only update state if we don't have data yet
          setData(cachedData.data as T);
          setIsLoading(false);
        }
        return;
      }
    }

    // Throttle requests - at least 2 seconds between requests unless forced
    if (!forceRefresh && (now - lastFetchTime.current < 2000)) {
      return;
    }

    lastFetchTime.current = now;

    if (!isMounted.current) return;
    setIsLoading(true);
    setError(null);

    try {
      const options: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${userId}`,
          'Cache-Control': 'no-cache',
          'X-Internal-API': 'true'
        },
        credentials: 'include', // Include cookies for authentication
        cache: 'no-store' // Prevent caching
      };

      // Add body for POST, PUT requests
      if (body && (method === 'POST' || method === 'PUT')) {
        options.body = JSON.stringify(body);
      }

      const response = await fetch(url, options);

      // Handle non-2xx responses
      if (!response.ok) {
        let errorMessage = `Request failed with status ${response.status}`;

        // Try to parse the error message from the response
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorMessage;
        } catch (e) {
          // If we can't parse the response, just use the default error message
        }

        // Add more context for common error codes
        if (response.status === 401) {
          errorMessage = 'Unauthorized: You need admin privileges to access this resource. Please log in with an admin account.';

          // If on admin dashboard, attempt to redirect to login
          if (url.includes('/api/admin/') && typeof window !== 'undefined') {
            console.warn('Admin access denied, redirecting to login...');
            setTimeout(() => {
              window.location.href = '/auth?redirect=/admin';
            }, 2000);
          }
        } else if (response.status === 403) {
          errorMessage = 'Forbidden: You do not have permission to access this resource.';
        } else if (response.status === 404) {
          errorMessage = 'Not Found: The requested resource does not exist.';
        } else if (response.status === 500) {
          errorMessage = 'Server Error: Something went wrong on the server. Please try again later.';
        }

        throw new Error(errorMessage);
      }

      const result = await response.json();

      if (isMounted.current) {
        setData(result as T);
        // Update cache
        responseCache.set(cacheKey.current, { data: result, timestamp: now });
      }
    } catch (err) {
      if (isMounted.current) {
        const error = err instanceof Error ? err : new Error('An unknown error occurred');
        setError(error);
        console.error('Error fetching admin data:', error.message);
      }
    } finally {
      if (isMounted.current) {
        setIsLoading(false);
      }
    }
  }, [buildUrl, method, body, cacheDuration, data, userId]);

  // Fetch data on mount and when dependencies change
  useEffect(() => {
    isMounted.current = true;
    fetchData(false);

    return () => {
      isMounted.current = false;
    };
  }, [fetchData]);

  // Return data, loading state, error, and refetch function
  return {
    data,
    isLoading,
    error,
    refetch: () => fetchData(true), // Force refresh when manually clicking refresh
  };
}

/**
 * Hook for fetching users data with advanced filtering
 */
export interface UserFilters {
  page?: number;
  limit?: number;
  search?: string;
  role?: string;
  status?: string;
  dateFrom?: string | null;
  dateTo?: string | null;
  subscription?: string;
  verified?: boolean | null;
}

export function useUsers(filters: UserFilters = {}) {
  const {
    page = 1,
    limit = 10,
    search = '',
    role = '',
    status = '',
    dateFrom = null,
    dateTo = null,
    subscription = '',
    verified = null
  } = filters;

  const toast = useToastHelpers();

  // Build query string with all filters
  const queryString = new URLSearchParams();
  queryString.append('page', page.toString());
  queryString.append('limit', limit.toString());
  if (search) queryString.append('q', search);
  if (role) queryString.append('role', role);
  if (status) queryString.append('status', status);
  if (dateFrom) queryString.append('dateFrom', dateFrom);
  if (dateTo) queryString.append('dateTo', dateTo);
  if (subscription) queryString.append('subscription', subscription);
  if (verified !== null) queryString.append('verified', verified.toString());

  // Get the user from auth context
  const { user } = useAuth();
  const userId = user?.id;
  
  // Only append userId once
  if (userId) {
    queryString.append('userId', userId);
  }
  
  const url = `/api/admin/users?${queryString.toString()}`;

  // Keep track of retry attempts
  const retryCount = useRef(0);
  const MAX_RETRIES = 3;

  // Define a more specific type for the user object
  type UserWithStringProps = {
    id: string;
    name: string;
    email: string;
    role?: string;
    status?: string;
    verified?: boolean;
    profileImage?: string;
    createdAt?: string;
    lastLogin?: string | null;
    subscription?: string | { plan: string; status: string };
    [key: string]: unknown;
  };
  
  // Define the response type
  interface UserResponse {
    users: {
      id: string;
      name: string;
      email: string;
      role: 'user' | 'admin' | 'moderator' | 'superadmin';
      status: 'active' | 'inactive' | 'pending' | 'suspended';
      verified: boolean;
      profileImage?: string;
      createdAt: string;
      lastLogin?: string | null;
      subscription?: string | { plan: string; status: string };
      emailVerified?: Date | null;
      // Use more specific types for additional properties
      [key: string]: string | number | boolean | Date | null | undefined | { [key: string]: string };
    }[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
    error?: string;
  }

  const {
    data,
    error,
    isLoading,
    mutate
  } = useSWR(
    userId ? url : null, // Only fetch if we have a userId
    async (url: string) => {
      try {
        // Implement retry logic with exponential backoff
        const fetchWithRetry = async (attempt = 0): Promise<UserResponse> => {
          try {
            const response = await fetch(url, {
              headers: {
                'Authorization': `Bearer ${userId}`,
                'Cache-Control': 'no-cache'
              },
              credentials: 'include',
              cache: 'no-store'
            });

            if (!response.ok) {
              const errorData = await response.json().catch(() => ({ error: 'Failed to parse error response' }));
              throw new Error(errorData.error || errorData.message || `Failed to fetch users (${response.status})`);
            }

            const responseData = await response.json();
            
            // Validate the response structure
            if (!responseData || typeof responseData !== 'object') {
              throw new Error('Invalid response format');
            }
            
            // Ensure users property exists and is an array
            if (!Array.isArray(responseData.users)) {
              responseData.users = [];
            } else {
              // Validate and cast role and status to proper string literal types
              responseData.users = responseData.users.map((user: UserWithStringProps) => {
                // Ensure role is one of the accepted values
                if (user.role && typeof user.role === 'string') {
                  if (!['user', 'admin', 'moderator', 'superadmin'].includes(user.role)) {
                    user.role = 'user'; // Default to 'user' for unrecognized roles
                  }
                } else {
                  user.role = 'user'; // Default to 'user' if missing or invalid
                }
                
                // Ensure status is one of the accepted values
                if (user.status && typeof user.status === 'string') {
                  if (!['active', 'inactive', 'pending', 'suspended'].includes(user.status)) {
                    user.status = 'active'; // Default to 'active' for unrecognized status
                  }
                } else {
                  user.status = 'active'; // Default to 'active' if missing or invalid
                }
                
                return user;
              });
            }
            
            // Ensure pagination exists
            if (!responseData.pagination) {
              responseData.pagination = { 
                page: 1, 
                limit: 10, 
                total: responseData.users.length || 0, 
                pages: 1 
              };
            }
            
            return responseData;
          } catch (err) {
            // If we haven't reached max retries, try again with backoff
            if (attempt < MAX_RETRIES) {
              const delay = Math.min(1000 * Math.pow(2, attempt), 8000); // Exponential backoff with 8s max
              console.log(`Fetch attempt ${attempt + 1} failed, retrying in ${delay}ms...`);
              await new Promise(resolve => setTimeout(resolve, delay));
              return fetchWithRetry(attempt + 1);
            }
            throw err; // If we've reached max retries, rethrow the error
          }
        };

        // Start the retry chain
        return await fetchWithRetry();
      } catch (error) {
        console.error('Error fetching users:', error);
        // Only show toast once to avoid spamming the user
        if (retryCount.current === 0) {
          toast.error('Error', 'Failed to load users. Check your network connection and try again.');
          retryCount.current++;
        }
        
        // Return empty data structure instead of throwing to avoid SWR error state
        return {
          users: [],
          pagination: { page: 1, limit: 10, total: 0, pages: 1 },
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    },
    {
      revalidateOnFocus: false,
      dedupingInterval: 5000, // Add a small deduping interval to prevent rapid retries
      revalidateIfStale: true,
      revalidateOnMount: true,
      onSuccess: () => {
        // Reset retry count on success
        retryCount.current = 0;
      },
      // Add an error retry function
      onError: (err) => {
        console.error('SWR error handler:', err);
      }
    }
  );

  return {
    data,
    error,
    isLoading,
    refetch: () => {
      // Reset retry count on manual refetch
      retryCount.current = 0;
      return mutate();
    }
  };
}

/**
 * Hook for fetching system health
 */
export function useSystemHealth() {
  const toast = useToastHelpers();

  // Get the user from auth context
  const { user } = useAuth();
  const userId = user?.id;

  // Create query string and append userId
  const queryString = new URLSearchParams();
  if (userId) {
    queryString.append('userId', userId);
  }

  const url = `/api/admin/system/health?${queryString.toString()}`;

  interface SystemHealthData {
    timestamp: string;
    status: 'healthy' | 'degraded' | 'unhealthy';
    database: {
      status: string;
      details: Record<string, unknown>;
    };
    system: {
      platform: string;
      arch: string;
      cpus: number;
      memoryUsage: {
        total: number;
        free: number;
        used: number;
        usedPercentage: number;
      };
      uptime: number;
      uptimeFormatted: string;
      cpuUsage: number;
    };
    process: {
      pid: number;
      memoryUsage: {
        rss: number;
        heapTotal: number;
        heapUsed: number;
        external: number;
      };
      uptime: number;
      uptimeFormatted: string;
      version: string;
    };
  }

  const {
    data,
    error,
    isLoading,
    mutate
  } = useSWR<SystemHealthData>(
    userId ? url : null, // Only fetch if we have a userId
    async (url: string) => {
      try {
        const response = await fetch(url, {
          headers: {
            'Authorization': `Bearer ${userId}`,
            'Cache-Control': 'no-cache'
          },
          credentials: 'include',
          cache: 'no-store'
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || errorData.message || `Failed to fetch system health (${response.status})`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error fetching system health:', error);
        toast.error('Error', 'Failed to load system health data');
        throw error;
      }
    },
    {
      revalidateOnFocus: false,
      dedupingInterval: 10000
    }
  );

  return {
    data,
    error,
    isLoading,
    refetch: mutate
  };
}

// Types for dashboard statistics
export interface DashboardStats {
  timestamp: string;
  users: {
    total: number;
    active: number;
    new: number;
  };
  visitors: {
    total: number;
    new: number;
  };
  content: {
    movies: number;
    shows: number;
    episodes: number;
    newMovies?: number;
    newShows?: number;
    total: number;
  };
  views: {
    total: number;
    weekly: number;
  };
  system: {
    status: 'healthy' | 'warning' | 'critical' | 'degraded';
    uptime: string;
    load: string;
    memory?: {
      total: number;
      free: number;
      used: number;
      usedPercentage: number;
    };
    platform?: string;
    cpus?: number;
  };
  activity?: Array<{
    type: 'user_registration' | 'user_login' | 'content_added' | 'content_viewed' | 'system_alert';
    action: string;
    message: string;
    details: string;
    timestamp: string;
    userId?: string;
  }>;
  popular?: Array<{
    id: string;
    title: string;
    type: 'movie' | 'tv' | 'show';
    views: number;
    posterPath?: string;
    rating?: number;
  }>;
}

// Hook for fetching dashboard statistics
export function useDashboardStats(autoRefreshInterval?: number) {
  const toast = useToastHelpers();
  const [refreshInterval, setRefreshInterval] = useState<number | null>(autoRefreshInterval || null);

  // Get the user from auth context
  const { user } = useAuth();
  const userId = user?.id;

  // Create query string and append userId
  const queryString = new URLSearchParams();
  if (userId) {
    queryString.append('userId', userId);
  }
  
  const url = `/api/admin/dashboard/stats?${queryString.toString()}`;

  // SWR configuration for data fetching
  const {
    data,
    error,
    isLoading,
    mutate
  } = useSWR<DashboardStats>(
    userId ? url : null, // Only fetch if we have a userId
    async (url: string) => {
      try {
        const response = await fetch(url, {
          headers: {
            'Authorization': `Bearer ${userId}`,
            'Cache-Control': 'no-cache'
          },
          credentials: 'include',
          cache: 'no-store'
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || errorData.message || `Failed to fetch dashboard statistics (${response.status})`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
        toast.error('Error', 'Failed to load dashboard statistics');
        throw error;
      }
    },
    {
      revalidateOnFocus: false,
      refreshInterval: refreshInterval || undefined,
      dedupingInterval: 60000, // Increase dedupe time to 60 seconds to prevent excessive requests
      onError: (err: Error) => {
        console.error('SWR Error:', err);
        toast.error('Error', 'Error loading dashboard data');
      }
    }
  );

  // Toggle auto-refresh functionality
  const toggleAutoRefresh = (enabled: boolean, interval = 30000) => {
    setRefreshInterval(enabled ? interval : null);
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      setRefreshInterval(null);
    };
  }, []);

  return {
    data,
    error,
    isLoading,
    refetch: mutate,
    toggleAutoRefresh,
    isAutoRefreshEnabled: !!refreshInterval,
    refreshInterval
  };
}

/**
 * Hook for fetching user analytics
 */
export function useUserAnalytics() {
  return useAdminData<{
    dailySignups: number[];
    dates: string[];
    subscriptionDistribution: Record<string, number>;
    deviceDistribution: Record<string, number>;
    geographicDistribution: Record<string, number>;
    dailyActiveUsers: number[];
    timestamp: string;
  }>({
    endpoint: 'analytics/users',
  });
}

/**
 * Hook for fetching content analytics
 */
export function useContentAnalytics() {
  return useAdminData<{
    topMovies: Array<{
      title: string;
      views: number;
      avgWatchTime: number;
      completionRate: number;
    }>;
    topShows: Array<{
      title: string;
      views: number;
      avgWatchTime: number;
      completionRate: number;
    }>;
    genrePopularity: Record<string, number>;
    watchTimeByHour: number[];
    watchHistory?: Record<string, unknown>;
    timestamp: string;
  }>({
    endpoint: 'analytics/content',
  });
}

/**
 * Hook for fetching content list
 */
export function useContentList(page = 1, limit = 10, search = '', type = '', status = '', genre = '') {
  return useAdminData<{
    content: Array<{
      id: string;
      title: string;
      type: 'movie' | 'show';
      tmdbId: string;
      imdbId?: string;
      posterPath: string;
      year: string;
      genres: string[];
      rating?: number;
      status: 'published' | 'draft' | 'archived';
      featured?: boolean;
      trending?: boolean;
      views?: number;
      createdAt: string;
    }>;
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  }>({
    endpoint: 'content',
    params: { page, limit, search, type, status, genre },
  });
}

/**
 * Hook for fetching a single content item
 */
export function useContentItem(id: string) {
  return useAdminData<{
    id: string;
    title: string;
    type: 'movie' | 'show';
    tmdbId: string;
    imdbId?: string;
    posterPath: string;
    backdropPath?: string;
    overview?: string;
    year: string;
    releaseDate?: string;
    genres: string[];
    runtime?: number;
    rating?: number;
    seasons?: number;
    episodes?: number;
    status: 'published' | 'draft' | 'archived';
    featured?: boolean;
    trending?: boolean;
    views?: number;
    watchTime?: number;
    completionRate?: number;
    createdAt: string;
    updatedAt: string;
  }>({
    endpoint: `content/${id}`,
  });
}

/**
 * Hook for fetching user activity logs
 */
export function useUserActivityLogs(userId?: string, page = 1, limit = 50, type?: string) {
  const endpoint = userId
    ? `users/${userId}/activity`
    : 'activity';

  return useAdminData<{
    logs: Array<{
      id: string;
      userId: string;
      type: string;
      action: string;
      details: string;
      ipAddress: string;
      userAgent: string;
      timestamp: string;
      metadata?: Record<string, unknown>;
    }>;
    pagination: {
      total: number;
      limit: number;
      skip: number;
      page: number;
      pages: number;
    };
    user?: {
      id: string;
      name: string;
      profileImage?: string;
    };
  }>({endpoint, params: { page, limit, ...(type ? { type } : {}) }});
}

/**
 * Hook for fetching system logs
 */
export function useSystemLogs(page = 1, limit = 50, level?: 'info' | 'warning' | 'error' | 'debug', source?: string) {
  return useAdminData<{
    logs: Array<{
      _id: string;
      userId: string;
      category: string;
      action: string;
      description: string;
      ip?: string;
      userAgent?: string;
      metadata?: Record<string, unknown>;
      timestamp: string;
    }>;
    pagination: {
      total: number;
      limit: number;
      page: number;
      pages: number;
    };
  }>({endpoint: 'system/logs', params: { page, limit, ...(level ? { level } : {}), ...(source ? { source } : {}) }});
}

// Hook for fetching content data
export function useContentData(contentType?: 'movie' | 'tv', page = 1, limit = 10) {
  const toast = useToastHelpers();

  // Get the user from auth context
  const { user } = useAuth();
  const userId = user?.id;

  // Create query string with all parameters
  const queryString = new URLSearchParams();
  if (contentType) queryString.append('type', contentType);
  queryString.append('page', page.toString());
  queryString.append('limit', limit.toString());
  if (userId) queryString.append('userId', userId);
  
  const url = `/api/admin/content?${queryString.toString()}`;

  const {
    data,
    error,
    isLoading,
    mutate
  } = useSWR(
    userId ? url : null, // Only fetch if we have a userId
    async (url: string) => {
      try {
        const response = await fetch(url, {
          headers: {
            'Authorization': `Bearer ${userId}`,
            'Cache-Control': 'no-cache'
          },
          credentials: 'include',
          cache: 'no-store'
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || errorData.message || `Failed to fetch content (${response.status})`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error fetching content:', error);
        toast.error('Error', 'Failed to load content data');
        throw error;
      }
    },
    {
      revalidateOnFocus: false,
      dedupingInterval: 5000
    }
  );

  return {
    data,
    error,
    isLoading,
    refetch: mutate
  };
}


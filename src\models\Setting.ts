import mongoose, { Schema, Document } from 'mongoose';

export interface ISetting extends Document {
  key: string;
  value: any;
  group: string;
  description?: string;
  updatedAt: Date;
  createdAt: Date;
}

// Define the Setting schema
const SettingSchema = new Schema<ISetting>({
  key: { 
    type: String, 
    required: true, 
    unique: true 
  },
  value: { 
    type: Schema.Types.Mixed, 
    required: true 
  },
  group: { 
    type: String, 
    required: true
  },
  description: { 
    type: String 
  }
}, {
  timestamps: true
});

// Add only necessary indexes that aren't already defined by field options
// Note: We've removed the duplicate indexes that were causing warnings
// The 'unique: true' on the 'key' field already creates an index
SettingSchema.index({ group: 1 });

// Use mongoose.models.Setting if it exists, otherwise create a new model
const Setting = mongoose.models.Setting as mongoose.Model<ISetting> || 
                mongoose.model<ISetting>('Setting', SettingSchema);

export default Setting;

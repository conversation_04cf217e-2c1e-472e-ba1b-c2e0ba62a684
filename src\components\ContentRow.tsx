"use client";

import { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { ChevronRight, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import ContentCard from './ContentCard';
import { ContentCardType } from '@/lib/content-utils';

interface ContentRowProps {
  title: string;
  subtitle?: string;
  seeAllLink?: string;
  contents: ContentCardType[];
}

export default function ContentRow({
  title,
  subtitle,
  seeAllLink,
  contents
}: ContentRowProps) {
  const rowRef = useRef<HTMLDivElement>(null);
  const [showControls, setShowControls] = useState(false);
  const [showLeftButton, setShowLeftButton] = useState(false);
  const [showRightButton, setShowRightButton] = useState(true);
  const [isMounted, setIsMounted] = useState(false);

  // Mark component as mounted on client-side
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Detect if scrolling is possible
  useEffect(() => {
    if (rowRef.current) {
      const { scrollWidth, clientWidth } = rowRef.current;
      setShowControls(scrollWidth > clientWidth);
    }

    // Add resize listener to update controls
    const handleResize = () => {
      if (rowRef.current) {
        const { scrollWidth, clientWidth } = rowRef.current;
        setShowControls(scrollWidth > clientWidth);
        handleScroll(); // Re-check button visibility
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isMounted]); // Only run on client-side

  // Scroll row to the left
  const scrollLeft = () => {
    if (rowRef.current) {
      rowRef.current.scrollBy({ left: -rowRef.current.clientWidth * 0.8, behavior: 'smooth' });
    }
  };

  // Scroll row to the right
  const scrollRight = () => {
    if (rowRef.current) {
      rowRef.current.scrollBy({ left: rowRef.current.clientWidth * 0.8, behavior: 'smooth' });
    }
  };

  // Update scroll buttons visibility
  const handleScroll = () => {
    if (rowRef.current) {
      // Check if we can scroll to the left
      setShowLeftButton(rowRef.current.scrollLeft > 20);

      // Check if we can scroll to the right
      const maxScrollLeft = rowRef.current.scrollWidth - rowRef.current.clientWidth;
      setShowRightButton(Math.ceil(rowRef.current.scrollLeft) < maxScrollLeft - 20);
    }
  };

  // If contents are empty, don't render
  if (!contents || contents.length === 0) {
    return null;
  }

  return (
    <section className="py-6 md:py-8">
      <div className="container px-4 md:px-6 mx-auto">
        {/* Row Header */}
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl md:text-2xl font-semibold text-vista-light tracking-tight">{title}</h2>
            {subtitle && (
              <p className="text-sm text-vista-light/70 mt-0.5">{subtitle}</p>
            )}
          </div>

          {seeAllLink && (
            <Link href={seeAllLink}>
              <Button variant="outline" className="bg-white text-black hover:bg-white/90 gap-1">
                See All
                <ChevronRight className="h-4 w-4" />
              </Button>
            </Link>
          )}
        </div>

        {/* Content Row with Scroll */}
        <div className="relative">
          {/* Scrollable Content */}
          <div
            ref={rowRef}
            className="flex space-x-3 md:space-x-4 overflow-x-auto scrollbar-hide pb-1 -mx-1 px-1 pt-1"
            onScroll={handleScroll}
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {contents.map((content, index) => (
              <div
                key={content.id}
                className="flex-none w-32 sm:w-[170px] md:w-[170px] lg:w-[170px]"
              >
                <ContentCard
                  id={content.id}
                  title={content.title}
                  imagePath={content.imagePath}
                  type={content.type}
                  year={content.year}
                  ageRating={content.ageRating}
                  index={index}
                  link={`/watch/${content.id}?forcePlay=true&contentType=${content.type === 'shows' ? 'show' : 'movie'}`}
                  isAwardWinning={content.isAwardWinning}
                  dataSource={content.dataSource}
                  showDataSource={true}
                />
              </div>
            ))}
          </div>

          {/* Scroll buttons - only shown if scrolling is possible and component is mounted */}
          {isMounted && showControls && (
            <>
              {/* Left button */}
              <button
                className={`absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-black/80 text-vista-light rounded-full w-8 h-8 flex items-center justify-center transition-opacity
                ${showLeftButton ? 'opacity-0 hover:opacity-100' : 'opacity-0 cursor-default'}`}
                onClick={scrollLeft}
                disabled={!showLeftButton}
                aria-label="Scroll left"
              >
                <ChevronRight className="h-5 w-5 transform rotate-180" />
              </button>

              {/* Right button */}
              <button
                className={`absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-black/80 text-vista-light rounded-full w-8 h-8 flex items-center justify-center transition-opacity
                ${showRightButton ? 'opacity-0 hover:opacity-100' : 'opacity-0 cursor-default'}`}
                onClick={scrollRight}
                disabled={!showRightButton}
                aria-label="Scroll right"
              >
                <ChevronRight className="h-5 w-5" />
              </button>
            </>
          )}
        </div>
      </div>
    </section>
  );
}

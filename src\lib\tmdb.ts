const TMDB_API_KEY = process.env.NEXT_PUBLIC_TMDB_API_KEY;
const TMDB_ACCESS_TOKEN = process.env.NEXT_PUBLIC_TMDB_ACCESS_TOKEN;
const TMDB_BASE_URL = process.env.NEXT_PUBLIC_TMDB_BASE_URL || 'https://api.themoviedb.org/3';
const TMDB_AUTH_METHOD = process.env.NEXT_PUBLIC_TMDB_AUTH_METHOD || 'api_key';

/**
 * Fetches data from the TMDb API
 * @param endpoint The API endpoint to fetch from (e.g., "movie/popular")
 * @param params Additional query parameters
 * @returns The JSON response from the API
 */
export async function fetchFromTMDB(endpoint: string, params: Record<string, string> = {}) {
  // Check if we have at least one auth method available
  if (!TMDB_API_KEY && !TMDB_ACCESS_TOKEN) {
    const error = 'Neither TMDB_API_KEY nor TMDB_ACCESS_TOKEN is defined in environment variables';
    console.error(error);
    throw new Error(error);
  }

  const url = new URL(`${TMDB_BASE_URL}/${endpoint}`);

  // Add auth parameters based on the configured method
  if (TMDB_AUTH_METHOD === 'api_key' && TMDB_API_KEY) {
    url.searchParams.append('api_key', TMDB_API_KEY);
  }

  // Add language parameter
  url.searchParams.append('language', 'en-US');

  // Add additional parameters
  Object.entries(params).forEach(([key, value]) => {
    url.searchParams.append(key, value);
  });

  const options: RequestInit = {
    method: 'GET',
    headers: {}
  };

  // If using bearer token auth, add the Authorization header
  if (TMDB_AUTH_METHOD === 'bearer_token' && TMDB_ACCESS_TOKEN) {
    options.headers = {
      Authorization: `Bearer ${TMDB_ACCESS_TOKEN}`,
      accept: 'application/json'
    };
  }

  try {
    const response = await fetch(url.toString(), options);

    if (!response.ok) {
      const error = `TMDb API error for endpoint "${endpoint}": ${response.status} ${response.statusText}`;
      console.error(error);
      throw new Error(error);
    }

    return response.json();
  } catch (error) {
    console.error('Error fetching from TMDb:', error);
    throw error;
  }
}
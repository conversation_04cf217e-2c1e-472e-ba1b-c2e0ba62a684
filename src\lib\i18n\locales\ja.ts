// Japanese translations (placeholder - this would include full translations)
import en from './en';

// Start with English translations as a fallback
const translations = { ...en };

// Add a few sample Japanese translations to demonstrate functionality
const japaneseTranslations = {
  'common.loading': '読み込み中...',
  'common.search': '検索',
  'common.cancel': 'キャンセル',
  'common.save': '保存',
  'common.delete': '削除',
  'common.edit': '編集',

  'nav.home': 'ホーム',
  'nav.shows': 'シリーズ',
  'nav.movies': '映画',
  'nav.categories': 'カテゴリー',
  'nav.myList': 'マイリスト',
  'nav.downloads': 'ダウンロード',
  'nav.settings': '設定',

  'home.popularShows': '人気シリーズ',
  'home.popularMovies': '人気映画',
  'home.continueWatching': '続きを見る',

  'downloads.title': 'ダウンロード',
  'downloads.subtitle': 'お気に入りのシリーズや映画をオフラインで視聴',

  'insights.title': '視聴統計',
  'insights.watchHistory': '視聴履歴',
  'insights.overview': '概要',

  'settings.language': '言語',
  'settings.displayLanguage': '表示言語',
};

// Merge the Japanese translations with the English fallbacks
Object.assign(translations, japaneseTranslations);

export default translations;

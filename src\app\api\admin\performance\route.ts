import { NextRequest, NextResponse } from 'next/server';
import { performanceMonitor } from '@/middleware/performance';
import { apiCache } from '@/lib/api-cache';

export async function GET(request: NextRequest) {
  try {
    // Check if user is admin (you can add your admin check logic here)
    const isAdmin = true; // Replace with actual admin check
    
    if (!isAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get performance metrics
    const metrics = performanceMonitor.getMetrics();
    const slowestEndpoints = performanceMonitor.getSlowestEndpoints(10);
    const cacheStats = apiCache.getStats();

    // Calculate response time percentiles
    const responseTimes = metrics.map(m => m.duration).sort((a, b) => a - b);
    const p50 = responseTimes[Math.floor(responseTimes.length * 0.5)] || 0;
    const p95 = responseTimes[Math.floor(responseTimes.length * 0.95)] || 0;
    const p99 = responseTimes[Math.floor(responseTimes.length * 0.99)] || 0;

    // Get recent slow requests
    const recentSlowRequests = metrics
      .filter(m => m.duration > 1000)
      .slice(-10)
      .reverse();

    // Get endpoint performance summary
    const endpointStats = new Map<string, { count: number; totalTime: number; avgTime: number }>();
    
    for (const metric of metrics) {
      const existing = endpointStats.get(metric.path) || { count: 0, totalTime: 0, avgTime: 0 };
      existing.count += 1;
      existing.totalTime += metric.duration;
      existing.avgTime = existing.totalTime / existing.count;
      endpointStats.set(metric.path, existing);
    }

    const performanceData = {
      summary: {
        totalRequests: metrics.length,
        averageResponseTime: metrics.length > 0 
          ? metrics.reduce((sum, m) => sum + m.duration, 0) / metrics.length 
          : 0,
        p50,
        p95,
        p99,
        slowestEndpoint: slowestEndpoints[0] || null
      },
      cache: {
        hits: cacheStats.hits,
        misses: cacheStats.misses,
        hitRate: cacheStats.hits + cacheStats.misses > 0 
          ? (cacheStats.hits / (cacheStats.hits + cacheStats.misses) * 100).toFixed(2) + '%'
          : '0%',
        size: cacheStats.size,
        lastCleanup: new Date(cacheStats.lastCleanup).toISOString()
      },
      slowestEndpoints,
      recentSlowRequests: recentSlowRequests.map(m => ({
        path: m.path,
        method: m.method,
        duration: m.duration,
        timestamp: new Date(m.timestamp).toISOString()
      })),
      endpointStats: Array.from(endpointStats.entries()).map(([path, stats]) => ({
        path,
        count: stats.count,
        averageTime: Math.round(stats.avgTime)
      })).sort((a, b) => b.averageTime - a.averageTime).slice(0, 20)
    };

    return NextResponse.json(performanceData);
  } catch (error) {
    console.error('Error fetching performance data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch performance data' },
      { status: 500 }
    );
  }
} 
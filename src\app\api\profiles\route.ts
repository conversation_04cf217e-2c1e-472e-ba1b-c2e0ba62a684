import { NextRequest, NextResponse } from 'next/server';
import { ProfileSession } from '@/lib/types';

/**
 * GET /api/profiles
 * Get all profiles for the current user
 */
import { apiCache, generateUserCacheKey } from '@/lib/api-cache';

export async function GET(request: NextRequest) {
  try {
    // Get user ID from the URL or authorization
    const userId = request.nextUrl.searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Check cache first
    const cacheKey = generateUserCacheKey(userId, 'profiles');
    const cached = apiCache.get<{ profiles: ProfileSession[] }>(cacheKey);
    if (cached) {
      return NextResponse.json(cached);
    }

    // Use optimized MongoDB connection
    const { ensureMongooseConnection } = await import('@/lib/mongodb');
    const mongoose = await ensureMongooseConnection();

    // Define the Profile schema directly
    const ProfileSchema = new mongoose.Schema({
      userId: mongoose.Schema.Types.ObjectId,
      name: String,
      avatar: String,
      isKids: Boolean,
      isPrimary: Boolean
    }, {
      timestamps: true
    });

    // Get the Profile model
    const Profile = mongoose.models.Profile ||
                   mongoose.model('Profile', ProfileSchema);

    // Find all profiles for this user with optimized query
    const profiles = await Profile.find({ userId })
      .select('name avatar isKids isPrimary createdAt')
      .sort({ isPrimary: -1, createdAt: 1 })
      .lean()
      .exec();

    // Transform to client-friendly format
    const profilesResponse: ProfileSession[] = profiles.map((profile: Record<string, unknown>) => ({
      id: (profile._id as { toString: () => string }).toString(),
      name: profile.name as string,
      avatar: profile.avatar as string,
      isKids: profile.isKids as boolean,
      isPrimary: profile.isPrimary as boolean
    }));

    const response = { profiles: profilesResponse };
    
    // Cache the result for 2 minutes
    apiCache.set(cacheKey, response, 2 * 60 * 1000);

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching profiles:', error);
    return NextResponse.json(
      { error: 'Failed to fetch profiles', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/profiles
 * Create a new profile for the current user
 */
export async function POST(request: NextRequest) {
  try {
    // Use optimized MongoDB connection
    const { ensureMongooseConnection } = await import('@/lib/mongodb');
    const mongoose = await ensureMongooseConnection();

    // Define the Profile schema directly
    const ProfileSchema = new mongoose.Schema({
      userId: mongoose.Schema.Types.ObjectId,
      name: String,
      avatar: String,
      isKids: Boolean,
      isPrimary: Boolean
    }, {
      timestamps: true
    });

    // Get the Profile model
    const Profile = mongoose.models.Profile ||
                   mongoose.model('Profile', ProfileSchema);

    // Parse the request body
    const data = await request.json();
    const { userId, name, avatar, isKids } = data;

    // Validate required fields
    if (!userId || !name) {
      return NextResponse.json(
        { error: 'User ID and name are required' },
        { status: 400 }
      );
    }

    // Check if this is the first profile for this user (should be primary)
    const existingProfiles = await Profile.countDocuments({ userId });
    const isPrimary = existingProfiles === 0;

    // Create the profile
    const newProfile = new Profile({
      userId, // MongoDB will convert this to ObjectId
      name,
      avatar: avatar || `/avatars/avatar-${Math.floor(Math.random() * 6) + 1}.png`,
      isKids: isKids || false,
      isPrimary,
      // Other fields will use default values from the schema
    });

    await newProfile.save();

    // Return the created profile
    return NextResponse.json({
      profile: {
        id: newProfile._id.toString(),
        name: newProfile.name,
        avatar: newProfile.avatar,
        isKids: newProfile.isKids,
        isPrimary: newProfile.isPrimary
      }
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating profile:', error);
    return NextResponse.json(
      { error: 'Failed to create profile', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
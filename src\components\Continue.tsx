'use client';

import { useState, useRef } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Play, Info, ChevronRight, ChevronLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useLanguage } from '@/lib/i18n/LanguageContext';
import { Card, CardContent } from '@/components/ui/card';
import { useContinueWatching } from '@/hooks/useContinueWatching';
import { useAuth } from '@/contexts/AuthContext';

export default function Continue() {
  const { t } = useLanguage();
  const { isAuthenticated } = useAuth();
  const { items: continueWatchingData, isLoading } = useContinueWatching();
  const scrollRef = useRef<HTMLDivElement>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);

  const handleScroll = () => {
    if (scrollRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;

      // Show left arrow if we've scrolled to the right
      setShowLeftArrow(scrollLeft > 0);

      // Show right arrow if there's more content to scroll to
      setShowRightArrow(scrollLeft + clientWidth < scrollWidth - 10);
    }
  };

  const scroll = (direction: 'left' | 'right') => {
    if (scrollRef.current) {
      const scrollAmount = scrollRef.current.clientWidth * 0.8;
      const newPosition = direction === 'left'
        ? scrollRef.current.scrollLeft - scrollAmount
        : scrollRef.current.scrollLeft + scrollAmount;

      scrollRef.current.scrollTo({
        left: newPosition,
        behavior: 'smooth'
      });
    }
  };

  return (
    <div className="py-8">
      <div className="container px-4 mx-auto">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-vista-light">
              Continue Watching
            </h2>
            {isAuthenticated && (
              <p className="text-xs text-vista-light/60 mt-1">Sign in to track your watch progress</p>
            )}
          </div>
          <Link href="/my-list">
            <Button variant="outline" className="bg-white text-black hover:bg-white/90 gap-1">
              See All
              <ChevronRight className="h-4 w-4" />
            </Button>
          </Link>
        </div>

        {/* Scroll Container with Navigation */}
        <div className="relative group">
          {/* Left Scroll Button */}
          {showLeftArrow && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-vista-dark/80 text-vista-light hover:bg-vista-dark/90 rounded-full h-10 w-10 shadow-md"
              onClick={() => scroll('left')}
            >
              <ChevronLeft className="h-6 w-6" />
            </Button>
          )}

          {/* Content Scroll Area */}
          <div
            ref={scrollRef}
            className="flex gap-4 overflow-x-auto hide-scrollbar pb-4"
            onScroll={handleScroll}
          >
            {isLoading ? (
              // Loading skeleton
              Array(4).fill(0).map((_, index) => (
                <Card
                  key={`skeleton-${index}`}
                  className="shrink-0 bg-vista-dark-card border-vista-dark-card w-[280px] overflow-hidden animate-pulse"
                >
                  <CardContent className="p-0">
                    <div className="relative">
                      <div className="h-[157px] w-[280px] bg-vista-dark-hover"></div>
                      <div className="h-1 bg-vista-dark/70"></div>
                    </div>
                    <div className="p-3">
                      <div className="h-5 bg-vista-dark-hover rounded w-3/4 mb-2"></div>
                      <div className="h-4 bg-vista-dark-hover rounded w-1/2"></div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : continueWatchingData.map((item) => (
              <Card
                key={item.id}
                className="shrink-0 bg-vista-dark-card border-vista-dark-card w-[280px] overflow-hidden"
              >
                <CardContent className="p-0">
                  <div className="relative">
                    <Link href={`/watch/${item.id}`}>
                      <div className="relative h-[157px] w-[280px] group">
                        <Image
                          src={item.image}
                          alt={item.title}
                          className="object-cover transition-transform group-hover:scale-105"
                          fill
                        />
                        <div className="absolute inset-0 bg-black/20 group-hover:bg-black/30 transition-colors flex justify-center items-center opacity-0 group-hover:opacity-100">
                          <Button size="icon" className="rounded-full bg-vista-blue/90 hover:bg-vista-blue">
                            <Play className="h-5 w-5 text-white" fill="white" />
                          </Button>
                        </div>
                      </div>
                    </Link>
                    <Progress value={item.progress} className="absolute bottom-0 left-0 right-0 h-1 rounded-none bg-vista-dark/70" />
                  </div>
                  <div className="p-3">
                    <div className="flex justify-between items-start mb-1">
                      <h3 className="font-medium text-vista-light line-clamp-1">{item.title}</h3>
                      <Button variant="ghost" size="sm" className="h-7 w-7 p-0 text-vista-light/70 hover:text-vista-light hover:bg-vista-dark-hover">
                        <Info className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="flex items-center justify-between text-sm text-vista-light/70">
                      <span>{item.episode || 'Movie'}</span>
                      <span>
                        {item.duration && item.progress ?
                          `${Math.ceil((item.duration * (100 - item.progress) / 100) / 60)} min left` :
                          item.timestamp || 'Continue'}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Right Scroll Button */}
          {showRightArrow && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-vista-dark/80 text-vista-light hover:bg-vista-dark/90 rounded-full h-10 w-10 shadow-md"
              onClick={() => scroll('right')}
            >
              <ChevronRight className="h-6 w-6" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';

import { useAuth } from '@/contexts/AuthContext';
import { ProfileSession } from '@/lib/types';
import axios from 'axios';
import { debounce } from 'lodash';
import { toast } from 'sonner';

// Define the context shape
interface ProfileContextType {
  profiles: ProfileSession[];
  activeProfile: ProfileSession | null;
  isLoadingProfiles: boolean;
  fetchProfiles: () => Promise<{ profiles: ProfileSession[] } | undefined>;
  createProfile: (name: string, avatar: string, isKids: boolean, isPrimary?: boolean) => Promise<{ success: boolean; error?: string; profile?: ProfileSession }>;
  updateProfile: (profileId: string, data: Partial<ProfileSession>) => Promise<{ success: boolean; error?: string }>;
  deleteProfile: (profileId: string) => Promise<{ success: boolean; error?: string }>;
  setActiveProfile: (profile: ProfileSession) => void;
  clearActiveProfile: () => void;
  remainingProfiles: number;
}

// Create the context with a default value
const ProfileContext = createContext<ProfileContextType>({
  profiles: [],
  activeProfile: null,
  isLoadingProfiles: false,
  fetchProfiles: async () => ({ profiles: [] }),
  createProfile: async () => ({ success: false }),
  updateProfile: async () => ({ success: false }),
  deleteProfile: async () => ({ success: false }),
  setActiveProfile: () => {},
  clearActiveProfile: () => {},
  remainingProfiles: 0
});

// Hook to use the profile context
export const useProfiles = () => useContext(ProfileContext);

// Provider component
export const ProfileProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const [profiles, setProfiles] = useState<ProfileSession[]>([]);
  const [activeProfile, setActiveProfile] = useState<ProfileSession | null>(null);
  const [isLoadingProfiles, setIsLoadingProfiles] = useState(false);
  const [hasLoadedProfiles, setHasLoadedProfiles] = useState(false);
  const [lastFetchTime, setLastFetchTime] = useState(0);

  // Maximum number of profiles allowed per user
  const MAX_PROFILES = 5;

  // Calculate remaining profile slots
  const remainingProfiles = MAX_PROFILES - profiles.length;

  // Create a memoized, debounced fetch function (PUT)
  const debouncedPut = useCallback(
    debounce(async (url: string, data: any) => {
      console.log("Making debounced PUT request to:", url, data);

      try {
        const response = await fetch(url, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(data)
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Failed: ${response.status}`);
        }

        return { data: await response.json() };
      } catch (error) {
        console.error('Error in debouncedPut:', error);
        throw error;
      }
    }, 500),
    [] // Empty dependency array ensures the debounced function is created only once
  );

  // Function to fetch profiles from the API, with debounce protection
  const fetchProfiles = async () => {
    if (!user?.id) return;

    // If we already have profiles loaded and no significant time has passed, return the cached profiles
    const now = Date.now();
    if (hasLoadedProfiles && profiles.length > 0 && now - lastFetchTime < 2000) {
      return { profiles };
    }

    // Update the last fetch time
    setLastFetchTime(now);

    if (isLoadingProfiles) return { profiles };
    setIsLoadingProfiles(true);

    try {
      const response = await axios.get(`/api/profiles?userId=${user.id}`);
      if (response.data.profiles) {
        setProfiles(response.data.profiles);
        setHasLoadedProfiles(true);

        // If no active profile is set and profiles exist, set the first one as active
        if (!activeProfile && response.data.profiles.length > 0) {
          // Prefer the primary profile if it exists
          const primaryProfile = response.data.profiles.find((p: ProfileSession) => p.isPrimary);
          setActiveProfile(primaryProfile || response.data.profiles[0]);

          // Store in localStorage
          localStorage.setItem('activeProfile', JSON.stringify(primaryProfile || response.data.profiles[0]));
        }

        return { profiles: response.data.profiles };
      }
      return { profiles };
    } catch (error) {
      console.error('Error fetching profiles:', error);
      return { profiles };
    } finally {
      setIsLoadingProfiles(false);
    }
  };

  // Function to create a new profile
  const createProfile = async (name: string, avatar: string, isKids: boolean, isPrimary: boolean = false) => {
    if (!user?.id) return { success: false, error: 'User not authenticated' };

    // Check if the user has reached the profile limit
    if (profiles.length >= MAX_PROFILES) {
      return { success: false, error: `You've reached the maximum limit of ${MAX_PROFILES} profiles` };
    }

    // Validate that there's only one primary profile
    if (isPrimary && profiles.some(p => p.isPrimary)) {
      return { success: false, error: 'You can only have one main profile' };
    }

    try {
      const response = await axios.post('/api/profiles', {
        userId: user.id,
        name,
        avatar,
        isKids,
        isPrimary
      });

      if (response.data.profile) {
        // Add the new profile to the list
        setProfiles(prev => [...prev, response.data.profile]);

        // If this is the first profile or it's the primary profile, set it as active
        if (profiles.length === 0 || isPrimary) {
          setActiveProfile(response.data.profile);
          localStorage.setItem('activeProfile', JSON.stringify(response.data.profile));
        }

        return { success: true, profile: response.data.profile };
      }

      return { success: false, error: 'Failed to create profile' };
    } catch (error) {
      console.error('Error creating profile:', error);
      if (axios.isAxiosError(error) && error.response) {
        return { success: false, error: error.response.data.error || 'Error creating profile' };
      }
      return { success: false, error: 'Error creating profile' };
    }
  };

  // Function to update a profile
  const updateProfile = async (profileId: string, data: Partial<ProfileSession>) => {
    try {
      // Handle name validation differently
      // Only validate name if it's not undefined and not an empty string (empty string means user is in the process of typing)
      if (data.name !== undefined && data.name !== '' && data.name.trim() === '') {
        toast.error('Profile name cannot be empty');
        return { success: false, error: 'Profile name cannot be empty' };
      }

      // Prepare the data to send - don't send empty names to the API
      const dataToSend = { ...data };

      // Only include name in the update if it's not empty and not just whitespace
      if (dataToSend.name !== undefined) {
        if (dataToSend.name !== '') {
          dataToSend.name = dataToSend.name.trim();

          // If name becomes empty after trimming, don't proceed
          if (dataToSend.name === '') {
            return { success: false, error: 'Profile name cannot be empty' };
          }
        } else {
          // If name is empty string, don't include it in the update
          // This prevents the API from getting empty name updates during deletion
          delete dataToSend.name;
        }
      }

      // Don't proceed with the update if there are no fields to update
      if (Object.keys(dataToSend).length === 0) {
        return { success: true };
      }

      // Make a direct fetch call instead of using the debounced version for profile image updates
      // This ensures immediate updates for avatar changes
      const isAvatarUpdate = data.hasOwnProperty('avatar');

      let response;
      try {
        if (isAvatarUpdate) {
          // For avatar updates, use direct fetch for immediate response
          const fetchResponse = await fetch(`/api/profiles/${profileId}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(dataToSend)
          });

          if (!fetchResponse.ok) {
            const errorData = await fetchResponse.json();
            throw new Error(errorData.error || `Failed to update profile: ${fetchResponse.status}`);
          }

          response = { data: await fetchResponse.json() };
        } else {
          // For other updates, use the direct fetch instead of debounced to avoid confusing state
          // This gives immediate feedback to the user
          const fetchResponse = await fetch(`/api/profiles/${profileId}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(dataToSend)
          });

          if (!fetchResponse.ok) {
            const errorData = await fetchResponse.json();
            throw new Error(errorData.error || `Failed to update profile: ${fetchResponse.status}`);
          }

          response = { data: await fetchResponse.json() };
        }
      } catch (error) {
        console.error('Error during fetch:', error);
        throw error;
      }

      if (response?.data?.profile) {
        // Immediately update the profile in the list for UI responsiveness
        setProfiles(prev =>
          prev.map(profile =>
            profile.id === profileId ? { ...profile, ...response.data.profile } : profile
          )
        );

        // If the active profile was updated, update it as well
        if (activeProfile?.id === profileId) {
          const updatedProfile = { ...activeProfile, ...response.data.profile };
          setActiveProfile(updatedProfile);

          // Store in localStorage to ensure consistency
          localStorage.setItem('activeProfile', JSON.stringify(updatedProfile));
        }

        return { success: true };
      }

      return { success: false, error: 'Failed to update profile' };
    } catch (error) {
      console.error('Error updating profile:', error);
      if (axios.isAxiosError(error) && error.response) {
        return { success: false, error: error.response.data.error || 'Error updating profile' };
      }
      return { success: false, error: error instanceof Error ? error.message : 'Error updating profile' };
    }
  };

  // Function to delete a profile
  const deleteProfile = async (profileId: string) => {
    try {
      const response = await axios.delete(`/api/profiles/${profileId}`);

      if (response.status === 200) {
        // Remove the profile from the list
        setProfiles(prev => prev.filter(profile => profile.id !== profileId));

        // If the active profile was deleted, set the first remaining profile as active
        if (activeProfile?.id === profileId && profiles.length > 1) {
          const newActiveProfile = profiles.find(p => p.id !== profileId);
          if (newActiveProfile) {
            setActiveProfile(newActiveProfile);
            localStorage.setItem('activeProfile', JSON.stringify(newActiveProfile));
          } else {
            setActiveProfile(null);
            localStorage.removeItem('activeProfile');
          }
        }

        return { success: true };
      }

      return { success: false, error: 'Failed to delete profile' };
    } catch (error) {
      console.error('Error deleting profile:', error);
      if (axios.isAxiosError(error) && error.response) {
        return { success: false, error: error.response.data.error || 'Error deleting profile' };
      }
      return { success: false, error: 'Error deleting profile' };
    }
  };

  // Function to set the active profile
  const handleSetActiveProfile = (profile: ProfileSession) => {
    setActiveProfile(profile);
    localStorage.setItem('activeProfile', JSON.stringify(profile));
  };

  // Function to clear the active profile
  const clearActiveProfile = () => {
    setActiveProfile(null);
    localStorage.removeItem('activeProfile');
  };

  // Load profiles when the user changes and we don't already have them
  useEffect(() => {
    if (user?.id && !hasLoadedProfiles) {
      fetchProfiles();
    } else if (!user?.id) {
      // Clear profiles when user logs out
      setProfiles([]);
      setActiveProfile(null);
      setHasLoadedProfiles(false);
      localStorage.removeItem('activeProfile');
    }
  }, [user?.id, hasLoadedProfiles]);

  // Listen for user name changes and update profiles accordingly
  useEffect(() => {
    // Handler for the user name change event
    const handleUserNameChange = async (event: CustomEvent) => {
      const { userId, name } = event.detail;

      // Only process if this is the current user
      if (user?.id === userId) {
        console.log('User name changed, refreshing profiles');

        // Refresh profiles from the server
        await fetchProfiles();

        // Also update the active profile if it's the primary profile
        if (activeProfile?.isPrimary) {
          const updatedActiveProfile = { ...activeProfile, name };
          setActiveProfile(updatedActiveProfile);
          localStorage.setItem('activeProfile', JSON.stringify(updatedActiveProfile));
        }
      }
    };

    // Add event listener for user name changes
    window.addEventListener('user-name-changed', handleUserNameChange as EventListener);

    // Clean up the event listener when the component unmounts
    return () => {
      window.removeEventListener('user-name-changed', handleUserNameChange as EventListener);
    };
  }, [user?.id, activeProfile, fetchProfiles]);

  // Load active profile from localStorage on initial load
  useEffect(() => {
    const storedProfile = localStorage.getItem('activeProfile');
    if (storedProfile && !activeProfile) {
      try {
        const parsedProfile = JSON.parse(storedProfile);
        setActiveProfile(parsedProfile);
      } catch (error) {
        console.error('Error parsing stored profile:', error);
        localStorage.removeItem('activeProfile');
      }
    }
  }, [activeProfile]);

  // Memoize the context value to prevent unnecessary renders
  const contextValue = React.useMemo(() => ({
    profiles,
    activeProfile,
    isLoadingProfiles,
    fetchProfiles,
    createProfile,
    updateProfile,
    deleteProfile,
    setActiveProfile: handleSetActiveProfile,
    clearActiveProfile,
    remainingProfiles
  }), [
    profiles,
    activeProfile,
    isLoadingProfiles,
    remainingProfiles
  ]);

  return (
    <ProfileContext.Provider value={contextValue}>
      {children}
    </ProfileContext.Provider>
  );
};
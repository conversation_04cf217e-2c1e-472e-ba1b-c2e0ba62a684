import { NextRequest, NextResponse } from 'next/server';

// Define default permissions
const DEFAULT_PERMISSIONS = {
  user: {
    canViewContent: true,
    canRateContent: true,
    canComment: true,
    canCreateLists: true,
    canShareContent: true
  },
  moderator: {
    canViewContent: true,
    canRateContent: true,
    canComment: true,
    canCreateLists: true,
    canShareContent: true,
    canModerateComments: true,
    canEditMetadata: true,
    canApproveContent: true
  },
  admin: {
    canViewContent: true,
    canRateContent: true,
    canComment: true,
    canCreateLists: true,
    canShareContent: true,
    canModerateComments: true,
    canEditMetadata: true,
    canApproveContent: true,
    canManageUsers: true,
    canManageContent: true,
    canManageSettings: true,
    canViewAnalytics: true,
    canManageRoles: true
  }
};

/**
 * GET /api/admin/users/[id]/permissions
 * Get permissions for a specific user
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the user ID from the cookie directly
    const userId = request.cookies.get('userId')?.value;
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const UserSchema = new mongoose.default.Schema({
      name: String,
      email: String,
      role: String,
      permissions: mongoose.default.Schema.Types.Mixed
    });

    const User = mongoose.default.models.User ||
                mongoose.default.model('User', UserSchema);

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Validate user ID
    if (!mongoose.default.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
    }

    // Check if target user exists
    const targetUser = await User.findById(params.id);
    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get user permissions directly
    const role = targetUser.role || 'user';
    let permissions = targetUser.permissions || {};
    let isDefault = !targetUser.permissions || Object.keys(targetUser.permissions).length === 0;

    // If no custom permissions, use defaults
    if (isDefault) {
      permissions = DEFAULT_PERMISSIONS[role as keyof typeof DEFAULT_PERMISSIONS] || DEFAULT_PERMISSIONS.user;
    }

    // Return user permissions
    return NextResponse.json({
      userId: params.id,
      permissions,
      isDefault,
      defaultPermissions: DEFAULT_PERMISSIONS,
      user: {
        id: targetUser._id.toString(),
        name: targetUser.name,
        email: targetUser.email,
        role: targetUser.role || 'user'
      }
    });
  } catch (error) {
    console.error('Error fetching user permissions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user permissions', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/users/[id]/permissions
 * Update permissions for a specific user
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the user ID from the cookie directly
    const userId = request.cookies.get('userId')?.value;
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const UserSchema = new mongoose.default.Schema({
      name: String,
      email: String,
      role: String,
      permissions: mongoose.default.Schema.Types.Mixed
    });

    const User = mongoose.default.models.User ||
                mongoose.default.model('User', UserSchema);

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the UserActivity schema directly
    const UserActivitySchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      action: String,
      details: String,
      ipAddress: String,
      userAgent: String,
      timestamp: { type: Date, default: Date.now },
      metadata: mongoose.default.Schema.Types.Mixed
    }, {
      timestamps: true
    });

    // Get the UserActivity model
    const UserActivity = mongoose.default.models.UserActivity ||
                        mongoose.default.model('UserActivity', UserActivitySchema);

    // Validate user ID
    if (!mongoose.default.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
    }

    // Check if target user exists
    const targetUser = await User.findById(params.id);
    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get request data
    const data = await request.json();

    let updatedPermissions;

    // Check if we're resetting to defaults
    if (data.reset) {
      const role = targetUser.role || 'user';
      // Reset permissions to defaults directly
      const defaultPerms = DEFAULT_PERMISSIONS[role as keyof typeof DEFAULT_PERMISSIONS] || DEFAULT_PERMISSIONS.user;

      // Update user with default permissions
      await User.findByIdAndUpdate(params.id, { permissions: null });
      updatedPermissions = defaultPerms;

      // Log admin activity directly
      await UserActivity.create({
        userId: new mongoose.default.Types.ObjectId(userId),
        type: 'admin',
        action: 'reset_user_permissions',
        details: `Admin reset permissions for user: ${targetUser.name} (${targetUser.email}) to ${role} defaults`,
        ipAddress: request.ip || request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        timestamp: new Date(),
        metadata: {
          userId: params.id,
          role
        }
      });
    } else {
      // Validate permissions
      if (!data.permissions || typeof data.permissions !== 'object') {
        return NextResponse.json({ error: 'Invalid permissions data' }, { status: 400 });
      }

      // Update user permissions directly
      await User.findByIdAndUpdate(params.id, { permissions: data.permissions });
      updatedPermissions = data.permissions;

      // Log admin activity directly
      await UserActivity.create({
        userId: new mongoose.default.Types.ObjectId(userId),
        type: 'admin',
        action: 'update_user_permissions',
        details: `Admin updated permissions for user: ${targetUser.name} (${targetUser.email})`,
        ipAddress: request.ip || request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        timestamp: new Date(),
        metadata: {
          userId: params.id,
          permissions: data.permissions
        }
      });
    }

    // Return updated permissions
    return NextResponse.json({
      userId: params.id,
      permissions: updatedPermissions,
      user: {
        id: targetUser._id.toString(),
        name: targetUser.name,
        email: targetUser.email,
        role: targetUser.role || 'user'
      }
    });
  } catch (error) {
    console.error('Error updating user permissions:', error);
    return NextResponse.json(
      { error: 'Failed to update user permissions', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

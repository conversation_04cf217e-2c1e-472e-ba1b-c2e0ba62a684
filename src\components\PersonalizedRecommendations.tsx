'use client';

import { useState, useEffect, useMemo, useCallback } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { ChevronRight, Play, Plus, Info, Star, Clock, Loader2, Film, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';
import { usePathname } from 'next/navigation';
import { useWatchlist } from '@/contexts/WatchlistContext';
import { ContentCardType } from '@/lib/content-utils';
import { getPopularMovies, getTopRatedTVShows, MappedContent, TMDB_GENRE_MAP } from '@/lib/tmdb-api';
import { useLanguage } from '@/lib/i18n/LanguageContext';
import { toast } from 'sonner';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  type CarouselApi,
} from "@/components/ui/carousel";

interface PersonalizedRecommendationsProps {
  title?: string;
  maxItems?: number;
  showReason?: boolean;
  className?: string;
}

// Component for individual card with its own hover state
const RecommendationCard = ({
  content,
  handleToggleWatchlist,
  showReason,
  getRecommendationReason,
  isInWatchlist,
  processingItems
}: {
  content: MappedContent & { isInWatchlist?: boolean, watchTimeMinutes?: number },
  handleToggleWatchlist: (content: MappedContent) => void,
  showReason: boolean,
  getRecommendationReason: (content: MappedContent & { isInWatchlist?: boolean, watchTimeMinutes?: number }) => string,
  isInWatchlist: boolean,
  processingItems: { [id: string]: boolean }
}) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      className="group relative"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="relative aspect-[2/3] rounded-lg overflow-hidden bg-vista-dark-lighter">
        {content.posterUrl ? (
          <Image
            src={content.posterUrl}
            alt={content.title}
            fill
            sizes="(max-width: 640px) 33vw, (max-width: 1024px) 25vw, 16vw"
            className={`object-cover transition-transform duration-300 ${isHovered ? 'scale-105' : ''}`}
            priority={false}
          />
        ) : (
          <div className="w-full h-full flex flex-col items-center justify-center bg-vista-dark-lighter text-vista-light/50">
            <Film className="h-8 w-8 mb-2" />
            <span className="text-xs text-center px-2">No poster available</span>
          </div>
        )}

        {/* Special status badges */}
        <div className="absolute top-2 left-2 flex flex-col gap-1.5">
          {content.releaseDate && new Date(content.releaseDate) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) && (
            <Badge className="bg-vista-blue text-white text-[10px] px-1.5 py-0.5">
              NEW
            </Badge>
          )}
          {content.voteAverage >= 8.5 && (
            <Badge className="bg-yellow-600 text-white text-[10px] px-1.5 py-0.5">
              TOP RATED
            </Badge>
          )}
          {isInWatchlist && (
            <Badge className="bg-vista-blue/80 text-white text-[10px] px-1.5 py-0.5">
              IN MY LIST
            </Badge>
          )}
        </div>

        {/* Hover overlay with actions */}
        <div
          className={`absolute inset-0 bg-black/80 flex flex-col justify-center items-center gap-2 p-3 transition-opacity duration-300 ${
            isHovered ? 'opacity-100' : 'opacity-0 pointer-events-none'
          }`}
        >
          <Link href={`/watch/${content.id}?forcePlay=true&contentType=${content.mediaType === 'tv' ? 'show' : 'movie'}`}>
            <Button size="sm" className="w-full bg-white text-vista-dark hover:bg-white/90 gap-1">
              <Play className="h-3 w-3" />
              Watch
            </Button>
          </Link>
          <Button
            size="sm"
            variant="outline"
            className={`w-full gap-1 ${
              isInWatchlist
                ? "bg-vista-blue text-white hover:bg-vista-blue/90 border-0"
                : processingItems[content.id]
                  ? "border-vista-blue/50 text-vista-blue/90 animate-pulse"
                  : "border-vista-light/30"
            }`}
            onClick={() => handleToggleWatchlist(content)}
            disabled={processingItems[content.id]}
          >
            {isInWatchlist ? (
              <Check className="h-3 w-3" />
            ) : processingItems[content.id] ? (
              <Loader2 className="h-3 w-3 animate-spin" />
            ) : (
              <Plus className="h-3 w-3" />
            )}
            {isInWatchlist ? "In My List" : processingItems[content.id] ? "Adding..." : "Add to List"}
          </Button>
          <Link href={`/details/${content.mediaType === 'tv' ? 'shows' : 'movies'}/${content.id}`}>
            <Button size="sm" variant="ghost" className="w-full gap-1 border-vista-light/30 text-vista-light">
              <Info className="h-3 w-3" />
              Details
            </Button>
          </Link>
        </div>
      </div>

      <div className="mt-2">
        <Link href={`/details/${content.mediaType === 'tv' ? 'shows' : 'movies'}/${content.id}`}>
          <h3 className={`font-medium text-vista-light line-clamp-1 transition-colors`}>
            {content.title}
          </h3>
        </Link>

        {showReason && (
          <p className="text-xs text-vista-light/70 mt-0.5">
            {getRecommendationReason(content)}
          </p>
        )}
      </div>
    </div>
  );
};

export default function PersonalizedRecommendations({
  title = "Recommended for You",
  maxItems = 10,
  showReason = true,
  className = ""
}: PersonalizedRecommendationsProps) {
  const { t } = useLanguage();
  const [isLoading, setIsLoading] = useState(true);
  const [recommendedContent, setRecommendedContent] = useState<MappedContent[]>([]);
  const { addToWatchlist, removeFromWatchlist, isInWatchlist } = useWatchlist();
  const [localWatchlistItems, setLocalWatchlistItems] = useState<Set<string>>(new Set());
  const pathname = usePathname();
  const [processingItems, setProcessingItems] = useState<{ [id: string]: boolean }>({});
  const [carouselApi, setCarouselApi] = useState<CarouselApi | null>(null);
  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(true);
  const [isMobile, setIsMobile] = useState(false);

  // Check for mobile on mount
  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 768);
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Fetch recommended content
  useEffect(() => {
    const fetchRecommendedContent = async () => {
      try {
        setIsLoading(true);
        const [movies, shows] = await Promise.all([
          getPopularMovies(),
          getTopRatedTVShows()
        ]);
        const mixedContent = [
          ...movies.slice(0, Math.ceil(maxItems / 2)),
          ...shows.slice(0, Math.floor(maxItems / 2))
        ].sort(() => Math.random() - 0.5);

        const initialWatchlistItems = new Set<string>();
        const enhancedContent = mixedContent.map(item => {
          const inWatchlist = isInWatchlist(item.id);
          if (inWatchlist) {
            initialWatchlistItems.add(item.id);
          }
          return {
            ...item,
            isInWatchlist: inWatchlist,
            watchTimeMinutes: Math.random() > 0.8 ? Math.floor(Math.random() * 40) + 10 : 0
          };
        });

        setLocalWatchlistItems(initialWatchlistItems);
        setRecommendedContent(enhancedContent);
      } catch (error) {
        console.error('Error fetching recommended content:', error);
        setRecommendedContent([]);
      } finally {
        setIsLoading(false);
      }
    };
    fetchRecommendedContent();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [maxItems]); // Removed isInWatchlist dependency

  // Handle Carousel API initialization and scroll events
  useEffect(() => {
    if (!carouselApi) {
      return;
    }
    const onSelect = () => {
      setCanScrollPrev(carouselApi.canScrollPrev());
      setCanScrollNext(carouselApi.canScrollNext());
    };
    carouselApi.on('select', onSelect);
    carouselApi.on('reInit', onSelect);
    onSelect(); // Initial check
    return () => {
      carouselApi.off('select', onSelect);
      carouselApi.off('reInit', onSelect);
    };
  }, [carouselApi]);

  // Local check if item is in watchlist
  const isItemInWatchlist = useCallback((id: string) => {
    return localWatchlistItems.has(id);
  }, [localWatchlistItems]);

  const handleToggleWatchlist = useCallback((content: MappedContent) => {
    if (processingItems[content.id]) return;
    setProcessingItems(prev => ({ ...prev, [content.id]: true }));

    const currentlyInList = isItemInWatchlist(content.id);
    try {
      if (currentlyInList) {
        removeFromWatchlist(content.id);
        setLocalWatchlistItems(prev => {
          const newSet = new Set(prev);
          newSet.delete(content.id);
          return newSet;
        });
        toast(`Removed "${content.title}" from My List`);
      } else {
        const contentItem: ContentCardType = {
          id: content.id,
          title: content.title,
          imagePath: content.posterUrl || content.backdropUrl || '/favicon.svg',
          type: content.mediaType === 'tv' ? 'shows' : 'movies',
          year: content.year ? content.year.toString() : '',
          ageRating: '',
          dataSource: 'tmdb'
        };
        addToWatchlist(contentItem);
        setLocalWatchlistItems(prev => new Set(prev).add(content.id));
        toast(`Added "${content.title}" to My List`);
      }
    } catch (error) {
      console.error('Error toggling watchlist:', error);
      toast.error("Failed to update watchlist");
    } finally {
      setTimeout(() => {
        setProcessingItems(prev => ({ ...prev, [content.id]: false }));
      }, 800);
    }
  }, [processingItems, isItemInWatchlist, removeFromWatchlist, addToWatchlist]);

  const getRecommendationReason = useCallback((content: MappedContent & { isInWatchlist?: boolean, watchTimeMinutes?: number }): string => {
    if (content.watchTimeMinutes && content.watchTimeMinutes > 0) {
      return `You watched ${content.watchTimeMinutes} min`;
    }
    if (content.isInWatchlist) {
      return "In your watchlist";
    }
    if (content.voteAverage >= 8.5) {
      return "Critically acclaimed";
    }
    // Use genreIds and the Genre Map
    if (content.genreIds && content.genreIds.length > 0) {
      const genreName = TMDB_GENRE_MAP[content.genreIds[0]];
      if (genreName) {
        return `Because you like ${genreName}`;
      }
    }
    return "Popular this week";
  }, []); // Removed TMDB_GENRE_MAP dependency, as it's a constant import

  const renderSkeletons = () => {
    return Array(maxItems).fill(0).map((_, index) => (
       <CarouselItem key={`skeleton-${index}`} className="pl-3 md:pl-4 basis-1/2 sm:basis-1/3 md:basis-1/4 lg:basis-1/5 xl:basis-1/6">
         <div className="animate-pulse">
           <div className="aspect-[2/3] rounded-lg bg-vista-dark-lighter mb-2"></div>
           <div className="h-4 bg-vista-dark-lighter rounded w-3/4 mb-1"></div>
           <div className="h-3 bg-vista-dark-lighter rounded w-1/2"></div>
         </div>
       </CarouselItem>
    ));
  };

  return (
    <section className={`py-6 md:py-8 ${className}`}>
      <div className="container px-4 md:px-6 mx-auto">
        {/* Row Header */}
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl md:text-2xl font-semibold text-vista-light tracking-tight">
              {title}
            </h2>
          </div>
          <Link href="/recommendations"> {/* Assuming a dedicated page */} 
            <Button variant="ghost" size="sm" className="text-vista-light hover:text-vista-light/80 hover:bg-vista-dark-lighter">
              See All <ChevronRight className="ml-1 h-4 w-4" />
            </Button>
          </Link>
        </div>

        <Carousel
          setApi={setCarouselApi}
          opts={{
            align: "start",
            containScroll: "trimSnaps",
            dragFree: true,
            loop: true, // Enable looping for an infinite feel
          }}
          className="relative w-full -ml-3 md:-ml-4" // Adjust margin to align items
        >
          <CarouselContent className="">
            {isLoading
              ? renderSkeletons()
              : recommendedContent.map((content, index) => (
                <CarouselItem key={`${content.id}-${index}`} className="pl-3 md:pl-4 basis-1/2 sm:basis-1/3 md:basis-1/4 lg:basis-1/5 xl:basis-1/6">
                  <RecommendationCard
                    content={content}
                    handleToggleWatchlist={handleToggleWatchlist}
                    showReason={showReason}
                    getRecommendationReason={getRecommendationReason}
                    isInWatchlist={isItemInWatchlist(content.id)}
                    processingItems={processingItems}
                  />
                </CarouselItem>
              ))}
          </CarouselContent>

          {/* Use Carousel controls, hidden on mobile */}
          {!isMobile && (
            <>
              <CarouselPrevious
                className={`absolute left-[-5px] top-[calc(50%-20px)] -translate-y-1/2 z-20 bg-black/60 hover:bg-black/80 border-none text-white w-8 h-8 rounded-full transition-opacity ${canScrollPrev ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}
                disabled={!canScrollPrev}
              />
              <CarouselNext
                 className={`absolute right-[-5px] top-[calc(50%-20px)] -translate-y-1/2 z-20 bg-black/60 hover:bg-black/80 border-none text-white w-8 h-8 rounded-full transition-opacity ${canScrollNext ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}
                 disabled={!canScrollNext}
              />
            </>
          )}
        </Carousel>
      </div>
    </section>
  );
}

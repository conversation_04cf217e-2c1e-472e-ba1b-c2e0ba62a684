"use client"

import * as React from "react"
import * as TabsPrimitive from "@radix-ui/react-tabs"

import { cn } from "@/lib/utils"

// Create a wrapper for Tabs that includes a unique key to force remounting
const Tabs = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Root>
>(({ ...props }, ref) => {
  // Generate a unique ID on component mount - this forces remounting when navigating
  const [uniqueId] = React.useState(() => `tabs-${Math.random().toString(36).substring(2, 9)}`)
  
  return (
    <TabsPrimitive.Root
      ref={ref}
      key={uniqueId}
      {...props}
    />
  )
})
Tabs.displayName = TabsPrimitive.Root.displayName

const TabsList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.List
    ref={ref}
    className={cn(
      "inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",
      className
    )}
    {...props}
  />
))
TabsList.displayName = TabsPrimitive.List.displayName

const TabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>
>(({ className, value, onClick, ...props }, ref) => {
  // Add a key to ensure proper remounting when tab value changes
  const triggerKey = `trigger-${value || ''}`;
  
  // Add click handling to ensure activations always work
  const handleClick = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    // Call the original onClick if it exists
    if (onClick) {
      onClick(e);
    }
    
    // Force a DOM focus on the element to ensure activation works
    if (ref && typeof ref !== 'function' && ref.current) {
      ref.current.focus();
    }
  };
  
  return (
    <TabsPrimitive.Trigger
      ref={ref}
      key={triggerKey}
      value={value}
      className={cn(
        "inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",
        className
      )}
      onClick={handleClick}
      {...props}
    />
  )
})
TabsTrigger.displayName = TabsPrimitive.Trigger.displayName

const TabsContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
>(({ className, value, ...props }, ref) => {
  // Add a key to ensure proper remounting when tab value changes
  const valueKey = `content-${value || ''}`;
  
  return (
    <TabsPrimitive.Content
      ref={ref}
      key={valueKey}
      value={value}
      className={cn(
        "mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
        className
      )}
      {...props}
    />
  )
})
TabsContent.displayName = TabsPrimitive.Content.displayName

export { Tabs, TabsList, TabsTrigger, TabsContent }


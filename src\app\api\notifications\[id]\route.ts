import { NextRequest, NextResponse } from 'next/server';
import dbConnect, { ensureMongooseConnection } from '@/lib/mongodb';
import Notification from '@/models/Notification';
import User from '@/models/User';

// GET handler to fetch a specific notification
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get user ID from query parameters
    const searchParams = request.nextUrl.searchParams;
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Connect to the database
    await ensureMongooseConnection();

    // Find the user by ID
    const user = await User.findById(userId);

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Find the notification
    const notification = await Notification.findOne({
      _id: params.id,
      userId: user._id
    });

    if (!notification) {
      return NextResponse.json({ error: 'Notification not found' }, { status: 404 });
    }

    return NextResponse.json({ notification });
  } catch (error) {
    console.error('Error fetching notification:', error);
    return NextResponse.json(
      { error: 'Failed to fetch notification' },
      { status: 500 }
    );
  }
}

// PATCH handler to update a notification (mark as read)
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Connect to the database
    await ensureMongooseConnection();

    // Get the request body
    const body = await request.json();

    // Get user ID from query parameters or body
    const searchParams = request.nextUrl.searchParams;
    const userId = searchParams.get('userId') || body.userId;

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Find the user by ID
    const user = await User.findById(userId);

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Find and update the notification
    const notification = await Notification.findOneAndUpdate(
      { _id: params.id, userId: user._id },
      { $set: body },
      { new: true }
    );

    if (!notification) {
      return NextResponse.json({ error: 'Notification not found' }, { status: 404 });
    }

    return NextResponse.json({ notification });
  } catch (error) {
    console.error('Error updating notification:', error);
    return NextResponse.json(
      { error: 'Failed to update notification' },
      { status: 500 }
    );
  }
}

// DELETE handler to delete a notification
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const notificationId = params.id; // Extract id early

  try {
    // Get user ID from query parameters
    const searchParams = request.nextUrl.searchParams;
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Check if notificationId is valid (optional but good practice)
    if (!notificationId) {
        return NextResponse.json({ error: 'Notification ID is missing in URL path' }, { status: 400 });
    }

    // Connect to the database
    await ensureMongooseConnection();

    // Find the user by ID
    const user = await User.findById(userId);

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Instead of deleting the notification, add the user's ID to the deletedBy array
    const notification = await Notification.findOneAndUpdate(
      {
        _id: notificationId,
        userId: user._id, // Ensure the notification belongs to this user
        deletedBy: { $ne: user._id } // Make sure user hasn't already deleted it
      },
      {
        $addToSet: { deletedBy: user._id } // Add user to deletedBy array if not already there
      },
      { new: true }
    );

    if (!notification) {
      // Check if notification exists but user has already deleted it
      const exists = await Notification.findOne({
        _id: notificationId,
        userId: user._id
      });

      if (exists) {
        // Notification exists but user already deleted it
        return NextResponse.json({
          success: true,
          message: 'Notification was already deleted by this user'
        });
      }

      return NextResponse.json({ error: 'Notification not found' }, { status: 404 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting notification:', error);
    return NextResponse.json(
      { error: 'Failed to delete notification' },
      { status: 500 }
    );
  }
}

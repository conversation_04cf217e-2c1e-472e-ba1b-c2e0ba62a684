import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import { apiCache, generateUserCacheKey } from '@/lib/api-cache';
import Notification from '@/models/Notification';
import User from '@/models/User';

// GET handler to fetch notifications for the current user
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const userId = searchParams.get('userId');
    const limit = parseInt(searchParams.get('limit') || '10');
    const page = parseInt(searchParams.get('page') || '1');
    const readFilter = searchParams.get('read');

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Check cache first (only for first page, unread notifications)
    const cacheKey = generateUserCacheKey(userId, 'notifications', { 
      limit, 
      page, 
      readFilter 
    });
    const cached = apiCache.get<{
      notifications: unknown[];
      pagination: { total: number; page: number; limit: number; pages: number };
      unreadCount: number;
    }>(cacheKey);
    
    if (cached && page === 1) {
      return NextResponse.json(cached);
    }

    // Connect to the database
    await ensureMongooseConnection();

    // Find the user by ID with optimized query
    const user = await User.findById(userId).select('_id').lean();

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Build the query
    const query: Record<string, unknown> = {
      userId: user._id,
      // Exclude notifications that this user has deleted
      deletedBy: { $ne: user._id }
    };

    // Add read filter if specified
    if (readFilter === 'true') {
      query.read = true;
    } else if (readFilter === 'false') {
      query.read = false;
    }

    // Check for expired notifications and exclude them
    const now = new Date();
    query.$or = [
      { expiresAt: { $exists: false } },  // No expiration date
      { expiresAt: null },                // Null expiration date
      { expiresAt: { $gt: now } }         // Not yet expired
    ];

    // Execute queries in parallel for better performance
    const [total, notifications, unreadCount] = await Promise.all([
      Notification.countDocuments(query),
      Notification.find(query)
        .select('title message type read createdAt expiresAt')
        .sort({ createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .lean()
        .exec(),
      Notification.countDocuments({
        userId: user._id,
        read: false,
        deletedBy: { $ne: user._id },
        $or: [
          { expiresAt: { $exists: false } },
          { expiresAt: null },
          { expiresAt: { $gt: now } }
        ]
      })
    ]);

    const response = {
      notifications,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      },
      unreadCount
    };

    // Cache only first page results for 1 minute
    if (page === 1) {
      apiCache.set(cacheKey, response, 60 * 1000);
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching notifications:', error);
    return NextResponse.json(
      { error: 'Failed to fetch notifications' },
      { status: 500 }
    );
  }
}

// POST handler to create a new notification
export async function POST(request: NextRequest) {
  try {
    // Get the request body
    const body = await request.json();

    // Simple API key validation for admin/system operations
    const apiKey = request.headers.get('x-api-key');
    const isSystemRequest = apiKey === process.env.SYSTEM_API_KEY;

    // Only allow system requests or requests with valid userId
    if (!isSystemRequest && (!body.createdBy || body.createdBy !== body.userId)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to the database
    await ensureMongooseConnection();

    // Validate required fields
    if (!body.userId || !body.type || !body.title || !body.message) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Import pusher server
    const { pusherServer } = await import('@/lib/pusher-server');

    // Create the notification
    const notification = await Notification.create(body);

    // Trigger a Pusher event to notify the user in real-time
    try {
      // Send to the specific user's channel
      await pusherServer.trigger(
        `user-${body.userId}`,
        'new-notification',
        {
          message: body.message,
          title: body.title,
          type: body.type,
          notificationId: (notification as unknown as Record<string, unknown>)._id?.toString() || '',
          timestamp: new Date().toISOString()
        }
      );

      // Also send to the global channel for broader awareness
      await pusherServer.trigger(
        'global-notifications',
        'new-notification',
        {
          message: 'You have a new notification',
          timestamp: new Date().toISOString(),
          count: 1
        }
      );
    } catch (error) {
      console.error('Error sending Pusher notification:', error);
      // Continue even if Pusher fails - the notification is still created
    }

    return NextResponse.json({ notification }, { status: 201 });
  } catch (error) {
    console.error('Error creating notification:', error);
    return NextResponse.json(
      { error: 'Failed to create notification' },
      { status: 500 }
    );
  }
}

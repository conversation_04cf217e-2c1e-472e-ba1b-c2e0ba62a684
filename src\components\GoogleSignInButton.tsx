'use client';

import { useEffect, useRef, useState } from 'react';
import { loadGoogleScript, isFedCMSupported } from '@/lib/auth';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';

interface GoogleSignInButtonProps {
  onSuccess?: (userData: any) => void;
  onError?: (error: Error) => void;
  text?: 'signin_with' | 'signup_with' | 'continue_with';
}

export default function GoogleSignInButton({
  onSuccess,
  onError,
  text = 'continue_with'
}: GoogleSignInButtonProps) {
  const { googleSignIn } = useAuth();
  const [isGoogleLoaded, setIsGoogleLoaded] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loadAttempts, setLoadAttempts] = useState(0);
  const maxAttempts = 2;
  // Add a ref to track if we've already loaded the script
  const scriptLoadedRef = useRef(false);

  useEffect(() => {
    // Skip if we've already loaded the script successfully
    if (scriptLoadedRef.current) {
      return;
    }

    // Load the Google script and initialize auth
    const loadAuth = async () => {
      // Don't attempt to reload if we're already loaded
      if (isGoogleLoaded) {
        return;
      }

      try {
        setError(null);
        setIsProcessing(true);
        console.log(`Loading Google auth script (attempt ${loadAttempts + 1}/${maxAttempts})...`);

        // Allow up to 15 seconds for script loading and API initialization
        await loadGoogleScript(15000);

        // Get client ID from environment variable
        const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
        if (!clientId) {
          throw new Error('Google Client ID is not defined in environment variables');
        }

        // Log the client ID (partially masked for security)
        console.log('Using Google client ID:', clientId.substring(0, 8) + '...' + clientId.substring(clientId.length - 4));

        // Initialize Google auth
        if (window.google && window.google.accounts) {
          const usesFedCM = isFedCMSupported();
          console.log('Google auth script loaded successfully, initializing with FedCM support:', usesFedCM);

          try {
            // Clear any existing Google Sign-In state if cancel is available
            if (window.google.accounts.id.cancel) {
              window.google.accounts.id.cancel();
            }

            // Initialize with fresh configuration
            window.google.accounts.id.initialize({
              client_id: clientId,
              callback: handleGoogleResponse,
              auto_select: false,
              cancel_on_tap_outside: true,
              use_fedcm_for_prompt: usesFedCM, // Enable FedCM based on browser support
            });

            setIsGoogleLoaded(true);
            scriptLoadedRef.current = true;
            console.log('Google auth initialized successfully');
          } catch (initError) {
            console.error('Error initializing Google auth:', initError);
            throw new Error(`Failed to initialize Google auth: ${(initError as Error).message}`);
          }
        } else {
          throw new Error('Google auth API not available after loading script');
        }
      } catch (error) {
        console.error('Failed to load Google authentication:', error);

        // Check if we should retry loading
        if (loadAttempts < maxAttempts - 1) {
          console.log(`Retrying Google auth script load (${loadAttempts + 1}/${maxAttempts})`);
          setLoadAttempts(prev => prev + 1);
        } else {
          // Max attempts reached, show error to user
          setError('Unable to load Google authentication. Please check your internet connection, browser privacy settings, or try a different browser.');
          onError?.(error as Error);
        }
      } finally {
        setIsProcessing(false);
      }
    };

    loadAuth();

    // Cleanup function to cancel Google Sign-In when component unmounts
    return () => {
      if (window.google && window.google.accounts && window.google.accounts.id.cancel) {
        try {
          window.google.accounts.id.cancel();
          console.log('Google Sign-In canceled on component unmount');
        } catch (error) {
          console.error('Error canceling Google Sign-In:', error);
        }
      }
    };
  }, [onError, loadAttempts, isGoogleLoaded]); // Add isGoogleLoaded to dependencies

  const handleGoogleResponse = async (response: any) => {
    try {
      console.log('Received Google sign-in response', response);
      setIsProcessing(true);
      setError(null);

      if (!response || !response.credential) {
        throw new Error('No credential received from Google');
      }

      console.log('Sending Google credential to auth context');
      const result = await googleSignIn(response);

      if (result.success && onSuccess) {
        console.log('Google sign-in successful');
        onSuccess(response);
      } else if (!result.success && onError) {
        const errorMessage = result.error || 'Google sign-in failed';
        console.error('Google sign-in failed:', errorMessage);
        setError(errorMessage);
        onError(new Error(errorMessage));
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error handling Google sign-in';
      console.error('Error handling Google sign-in:', error);
      setError(errorMessage);
      onError?.(error as Error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleGoogleClick = () => {
    setError(null);
    if (window.google && window.google.accounts) {
      console.log('Prompting Google sign-in...');
      try {
        // First cancel any existing prompt
        if (window.google.accounts.id.cancel) {
          try {
            window.google.accounts.id.cancel();
          } catch (cancelError) {
            console.log('No active Google prompt to cancel');
          }
        }

        // Then show the prompt with notification callback
        window.google.accounts.id.prompt((notification: any) => {
          if (notification) {
            if (notification.isNotDisplayed()) {
              const reason = notification.getNotDisplayedReason();
              console.warn('Google One Tap not displayed:', reason);

              // If browser policy prevents display, we should show a more helpful message
              if (reason === 'browser_not_supported' || reason === 'third_party_cookies_blocked') {
                setError('Your browser settings may be blocking Google sign-in. Please check your cookie settings or try a different browser.');
              } else if (reason === 'missing_client_id') {
                setError('Google Sign-In configuration error. Please contact support.');
                console.error('Missing client ID in Google Sign-In configuration');
              } else {
                // For other reasons, try rendering a button instead
                console.log('Attempting to render Google Sign-In button as fallback');
                try {
                  const buttonContainer = document.createElement('div');
                  buttonContainer.id = 'google-signin-fallback';
                  buttonContainer.style.display = 'none';
                  document.body.appendChild(buttonContainer);

                  // Check for Google API again to avoid TypeScript error
                  if (window.google && window.google.accounts) {
                    window.google.accounts.id.renderButton(buttonContainer, {
                      type: 'standard',
                      theme: 'outline',
                      size: 'large',
                      text: 'signin_with',
                    });

                    // Programmatically click the button
                    const button = buttonContainer.querySelector('div[role="button"]');
                    if (button instanceof HTMLElement) {
                      button.click();
                      console.log('Clicked fallback Google Sign-In button');
                    }
                  }
                } catch (renderError) {
                  console.error('Failed to render fallback button:', renderError);
                }
              }
            } else if (notification.isSkippedMoment()) {
              console.log('Google One Tap skipped:', notification.getSkippedReason());
            } else if (notification.isDismissedMoment()) {
              console.log('Google One Tap dismissed:', notification.getDismissedReason());
            }
          }
        });
        console.log('Google prompt called');
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Error showing Google prompt';
        console.error('Error showing Google prompt:', error);
        setError(errorMessage);
        onError?.(error as Error);

        // Try to reinitialize Google auth
        setLoadAttempts(prev => prev + 1);
      }
    } else {
      // Attempt to reload the Google auth if it's not available
      setLoadAttempts(prev => prev + 1);
      console.error('Google authentication is not initialized, attempting to reload');
      setError('Google authentication is not initialized, attempting to reload...');
    }
  };

  const buttonLabel = text === 'signin_with'
    ? 'Sign in with Google'
    : text === 'signup_with'
      ? 'Sign up with Google'
      : 'Continue with Google';

  return (
    <div className="w-full flex flex-col space-y-2">
      <Button
        type="button"
        variant="outline"
        onClick={handleGoogleClick}
        disabled={isProcessing}
        className="w-full py-2 md:py-2.5 rounded-xl border-gray-600/40 bg-gray-800/60 hover:bg-gray-700/60 text-white transition-all duration-300 backdrop-blur-sm hover:border-gray-500/60 hover:shadow-[0_0_20px_rgba(255,255,255,0.1)] font-medium text-sm md:text-base"
      >
        {isProcessing ? (
          <div className="flex items-center justify-center">
            <div className="w-4 h-4 md:w-5 md:h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
            <span>Connecting...</span>
          </div>
        ) : (
          <>
            <svg className="h-4 w-4 md:h-5 md:w-5 mr-2 md:mr-3" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path
                fill="#EA4335"
                d="M5.26620003,9.76452941 C6.19878754,6.93863203 8.85444915,4.90909091 12,4.90909091 C13.6909091,4.90909091 15.2181818,5.50909091 16.4181818,6.49090909 L19.9090909,3 C17.7818182,1.14545455 15.0545455,0 12,0 C7.27006974,0 3.1977497,2.69829785 1.23999023,6.65002441 L5.26620003,9.76452941 Z"
              />
              <path
                fill="#34A853"
                d="M16.0407269,18.0125889 C14.9509167,18.7163016 13.5660892,19.0909091 12,19.0909091 C8.86648613,19.0909091 6.21911939,17.076871 5.27698177,14.2678769 L1.23746264,17.3349879 C3.19279051,21.2936293 7.26500293,24 12,24 C14.9328362,24 17.7353462,22.9573905 19.834192,20.9995801 L16.0407269,18.0125889 Z"
              />
              <path
                fill="#4A90E2"
                d="M19.834192,20.9995801 C22.0291676,18.9520994 23.4545455,15.903663 23.4545455,12 C23.4545455,11.2909091 23.3454545,10.5272727 23.1818182,9.81818182 L12,9.81818182 L12,14.4545455 L18.4363636,14.4545455 C18.1187732,16.013626 17.2662994,17.2212117 16.0407269,18.0125889 L19.834192,20.9995801 Z"
              />
              <path
                fill="#FBBC05"
                d="M5.27698177,14.2678769 C5.03832634,13.556323 4.90909091,12.7937589 4.90909091,12 C4.90909091,11.2182781 5.03443647,10.4668121 5.26620003,9.76452941 L1.23999023,6.65002441 C0.43658717,8.26043162 0,10.0753848 0,12 C0,13.9195484 0.444780743,15.7301709 1.23746264,17.3349879 L5.27698177,14.2678769 Z"
              />
            </svg>
            <span className="font-medium">{buttonLabel}</span>
          </>
        )}
      </Button>

      {error && (
        <div className="text-red-300 text-xs md:text-sm p-2 md:p-3 bg-red-500/10 rounded-md border border-red-500/20 mt-3 md:mt-4">
          <p>{error}</p>
          <p className="text-xs mt-1 text-red-300/80 text-[10px] md:text-xs">
            Try checking your internet connection, enabling third-party cookies, or using a different browser.
          </p>
        </div>
      )}
    </div>
  );
}
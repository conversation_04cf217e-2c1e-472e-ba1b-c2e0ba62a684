'use client';

import { useState, useEffect, useRef } from 'react';
import { useReviews } from '@/contexts/ReviewContext';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import {
  Star,
  ThumbsUp,
  ThumbsDown,
  MessageCircle,
  Trash2,
  ShieldAlert,
  Pencil,
  Save,
  X,
  MoreHorizontal
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
  DialogFooter
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { toast } from 'sonner';
import { formatDistanceToNow } from 'date-fns';

interface ReviewTabProps {
  contentId: string;
  contentType: 'movie' | 'show';
  userId?: string;
  username?: string;
}

export default function ReviewTab({ contentId, contentType, userId, username }: ReviewTabProps) {
  const { reviews, stats, loading, error, page, totalPages, fetchReviews, addReview, likeReview, dislikeReview, deleteReview, setReviews, setStats } = useReviews();
  const { user } = useAuth();
  const [newReview, setNewReview] = useState({ rating: 5, comment: '' });
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [tempUsername, setTempUsername] = useState(username || '');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // State for editing reviews
  const [editingReviewId, setEditingReviewId] = useState<string | null>(null);
  const [editedReview, setEditedReview] = useState({ rating: 5, comment: '' });

  // State for delete confirmation dialog
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [reviewToDelete, setReviewToDelete] = useState<{ id: string, isAdmin: boolean } | null>(null);

  // Check if the current user is an admin
  const isAdmin = user?.role === 'admin' || user?.role === 'superadmin';

  // Use a ref to track if the initial fetch has been done
  interface FetchState {
    contentId: string;
    contentType: string;
  }
  const initialFetchDone = useRef<boolean | FetchState>(false);

  useEffect(() => {
    // Only fetch if we haven't already or if the content changes
    if (!initialFetchDone.current ||
        (typeof initialFetchDone.current !== 'boolean' && initialFetchDone.current.contentId !== contentId)) {
      console.log(`Initial fetch for reviews: ${contentId}, ${contentType}`);
      fetchReviews(contentId, contentType);
      initialFetchDone.current = { contentId, contentType };
    }
  }, [contentId, contentType, fetchReviews]);

  const handleRatingChange = (rating: number) => {
    setNewReview(prev => ({ ...prev, rating }));
  };

  const handleCommentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setNewReview(prev => ({ ...prev, comment: e.target.value }));
  };

  const handleUsernameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTempUsername(e.target.value);
  };

  const handleSubmitReview = async () => {
    // Prevent submission if already loading or submitting
    if (loading || isSubmitting) {
      return;
    }

    // Validate comment
    if (!newReview.comment.trim()) {
      toast.error('Please enter a comment');
      return;
    }

    // Validate username for anonymous users
    if (!userId && !tempUsername.trim()) {
      toast.error('Please enter a username');
      return;
    }

    // Set local loading state to provide immediate feedback
    setIsSubmitting(true);

    // Prepare review data
    const reviewData = {
      contentId,
      contentType,
      userId: userId || 'anonymous-' + Date.now(), // Add timestamp to make anonymous IDs unique
      username: username || tempUsername,
      rating: newReview.rating,
      comment: newReview.comment
    };

    try {
      const success = await addReview(reviewData);
      if (success) {
        // Reset form
        setNewReview({ rating: 5, comment: '' });
        setTempUsername(''); // Reset username field

        // Close the dialog
        setIsDialogOpen(false);
      }
    } catch (error) {
      console.error('Error submitting review:', error);
      // Error is already handled in the context
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLoadMore = () => {
    if (page < totalPages) {
      fetchReviews(contentId, contentType, page + 1);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch (error) {
      return 'Unknown date';
    }
  };

  // Import Review type from ReviewContext
  type Review = {
    _id: string;
    contentId: string;
    contentType: 'movie' | 'show';
    userId: string;
    username: string;
    rating: number;
    comment: string;
    likes: number;
    dislikes: number;
    createdAt: string;
    updatedAt: string;
  };

  // Function to start editing a review
  const startEditingReview = (review: Review) => {
    setEditingReviewId(review._id);
    setEditedReview({
      rating: review.rating,
      comment: review.comment
    });
  };

  // Function to cancel editing
  const cancelEditing = () => {
    setEditingReviewId(null);
    setEditedReview({ rating: 5, comment: '' });
  };

  // Function to save edited review
  const saveEditedReview = async (reviewId: string) => {
    if (!editedReview.comment.trim()) {
      toast.error('Review comment cannot be empty');
      return;
    }

    setIsSubmitting(true);

    try {
      // Find the review in the current list
      const review = reviews.find(r => r._id === reviewId);
      if (!review) {
        throw new Error('Review not found');
      }

      // Call the API to update the review
      const response = await fetch(`/api/reviews/${reviewId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: userId,
          rating: editedReview.rating,
          comment: editedReview.comment
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update review');
      }

      // Update the review in the local state
      const updatedReviews = reviews.map(r =>
        r._id === reviewId
          ? { ...r, rating: editedReview.rating, comment: editedReview.comment }
          : r
      );

      // Update the stats
      const oldRating = review.rating;
      const newRating = editedReview.rating;

      if (oldRating !== newRating) {
        const newDistribution = [...stats.ratingDistribution];
        newDistribution[oldRating - 1] -= 1;
        newDistribution[newRating - 1] += 1;

        const newAverage = (
          (stats.averageRating * stats.reviewCount) - oldRating + newRating
        ) / stats.reviewCount;

        // Update stats in the context
        // This would ideally be handled by the context, but for now we'll just refresh
        await fetchReviews(contentId, contentType);
      }

      toast.success('Review updated successfully');
      setEditingReviewId(null);
    } catch (error) {
      console.error('Error updating review:', error);
      toast.error('Failed to update review');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Function to handle delete confirmation
  const confirmDeleteReview = (reviewId: string, isAdminDelete: boolean) => {
    setReviewToDelete({ id: reviewId, isAdmin: isAdminDelete });
    setIsDeleteDialogOpen(true);
  };

  // Function to handle actual deletion
  const handleDeleteReview = async () => {
    if (!reviewToDelete) return;

    try {
      // Show loading toast to provide immediate feedback
      const loadingToast = toast.loading('Deleting review...');

      // Get the review before it's deleted to update UI optimistically
      const reviewBeingDeleted = reviews.find(r => r._id === reviewToDelete.id);

      if (!reviewBeingDeleted) {
        toast.dismiss(loadingToast);
        toast.error('Review not found');
        return;
      }

      // Optimistically update UI by removing the review from the local state
      setReviews(prevReviews => prevReviews.filter(r => r._id !== reviewToDelete.id));

      // Update stats optimistically
      if (reviewBeingDeleted) {
        const deletedRating = reviewBeingDeleted.rating;
        setStats(prevStats => {
          const newDistribution = [...prevStats.ratingDistribution];
          newDistribution[deletedRating - 1] = Math.max(0, newDistribution[deletedRating - 1] - 1);

          const newCount = Math.max(0, prevStats.reviewCount - 1);
          let newAverage = 0;
          if (newCount > 0) {
            newAverage = ((prevStats.averageRating * prevStats.reviewCount) - deletedRating) / newCount;
          }

          return {
            reviewCount: newCount,
            averageRating: newAverage,
            ratingDistribution: newDistribution
          };
        });
      }

      // Close the dialog immediately for better UX
      setIsDeleteDialogOpen(false);
      setReviewToDelete(null);

      // Actually perform the deletion in the background
      const success = await deleteReview(reviewToDelete.id, userId!, reviewToDelete.isAdmin);

      if (success) {
        // Dismiss the loading toast and show success
        toast.dismiss(loadingToast);
        toast.success('Review deleted successfully');

        // No need to refresh as we've already updated the UI optimistically
      } else {
        // If deletion failed, revert our optimistic updates by refreshing
        toast.dismiss(loadingToast);
        toast.error('Failed to delete review');
        await fetchReviews(contentId, contentType);
      }
    } catch (error) {
      console.error('Error deleting review:', error);
      toast.error('An error occurred while deleting the review');
      // Refresh to ensure UI is in sync with server
      await fetchReviews(contentId, contentType);
    }
  };

  return (
    <div className="space-y-6">
      {/* Review Summary */}
      <div className="bg-gradient-to-br from-vista-dark/60 to-vista-dark-lighter/40 backdrop-blur-sm rounded-lg sm:rounded-xl p-4 sm:p-6 border border-vista-light/10 shadow-inner mb-4 transform transition-all duration-300 hover:shadow-lg hover:border-vista-light/20 hover:from-vista-dark/70 hover:to-vista-dark-lighter/50">
        <div className="flex flex-col md:flex-row items-center gap-4 sm:gap-6">
          <div className="flex flex-col items-center justify-center bg-vista-dark/40 p-3 sm:p-4 rounded-lg sm:rounded-xl border border-vista-light/5 min-w-[90px] sm:min-w-[100px] transform transition-all duration-300 hover:bg-vista-dark/50 hover:border-vista-light/10 hover:scale-105 shadow-sm hover:shadow-md">
            <div className="text-3xl font-bold text-vista-blue mb-1 transition-colors duration-300 hover:text-vista-blue/90">
              {stats.averageRating ? stats.averageRating.toFixed(1) : '0.0'}
            </div>
            <div className="flex">
              {[1, 2, 3, 4, 5].map(i => (
                <Star
                  key={i}
                  className={`h-4 w-4 ${i <= Math.round(stats.averageRating)
                    ? 'fill-vista-blue text-vista-blue'
                    : 'text-vista-blue/40'}`}
                />
              ))}
            </div>
            <div className="text-xs text-vista-light/60 mt-1">{stats.reviewCount} reviews</div>
          </div>

          <div className="flex-1">
            <h3 className="text-lg font-semibold mb-2">Rating Distribution</h3>
            <div className="space-y-2">
              {[5, 4, 3, 2, 1].map(rating => {
                const count = stats.ratingDistribution[rating - 1];
                const percentage = stats.reviewCount > 0
                  ? (count / stats.reviewCount) * 100
                  : 0;

                return (
                  <div key={rating} className="flex items-center">
                    <div className="w-24 text-sm text-vista-light/80">{rating} Stars</div>
                    <div className="flex-1 h-2 bg-vista-dark-lighter rounded-full overflow-hidden">
                      <div
                        className="h-full bg-vista-blue rounded-full transition-all duration-500 hover:bg-vista-blue/80"
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                    <div className="w-10 text-right text-sm text-vista-light/80">{count}</div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Write a Review Button */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger asChild>
          <Button className="w-full bg-vista-dark-lighter hover:bg-vista-dark border border-vista-light/10 hover:border-vista-light/20 text-vista-light gap-2 py-6 rounded-xl transition-all duration-300">
            <MessageCircle className="h-4 w-4" />
            Write Your Review
          </Button>
        </DialogTrigger>
        <DialogContent className="bg-vista-dark-lighter border-vista-light/10 text-vista-light">
          <DialogHeader>
            <DialogTitle className="text-vista-light text-xl">Write a Review</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 mt-4">
            {!userId && (
              <div>
                <label className="text-sm text-vista-light/70 mb-1 block">Your Name</label>
                <Input
                  value={tempUsername}
                  onChange={handleUsernameChange}
                  placeholder="Enter your name"
                  className="bg-vista-dark border-vista-light/10 text-vista-light"
                />
              </div>
            )}
            <div>
              <label className="text-sm text-vista-light/70 mb-1 block">Rating</label>
              <div className="flex space-x-2">
                {[1, 2, 3, 4, 5].map(rating => (
                  <button
                    key={rating}
                    type="button"
                    onClick={() => handleRatingChange(rating)}
                    className="focus:outline-none"
                  >
                    <Star
                      className={`h-6 w-6 ${rating <= newReview.rating
                        ? 'fill-vista-blue text-vista-blue'
                        : 'text-vista-light/40'}`}
                    />
                  </button>
                ))}
              </div>
            </div>
            <div>
              <label className="text-sm text-vista-light/70 mb-1 block">Your Review</label>
              <Textarea
                value={newReview.comment}
                onChange={handleCommentChange}
                placeholder="Share your thoughts about this content..."
                className="bg-vista-dark border-vista-light/10 text-vista-light min-h-[100px]"
              />
            </div>
            <div className="flex justify-end space-x-2 pt-2">
              <DialogClose asChild>
                <Button variant="outline" className="bg-vista-dark border-vista-light/10 text-vista-light hover:bg-vista-dark/80">
                  Cancel
                </Button>
              </DialogClose>
              <Button
                onClick={handleSubmitReview}
                className="bg-vista-blue hover:bg-vista-blue/90 text-white"
                disabled={loading || isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin mr-2"></div>
                    Submitting...
                  </>
                ) : (
                  'Submit Review'
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Loading State */}
      {loading && reviews.length === 0 && (
        <div className="flex flex-col justify-center items-center py-8 space-y-4">
          <div className="w-10 h-10 border-4 border-vista-blue/20 border-t-vista-blue rounded-full animate-spin"></div>
          <p className="text-vista-light/70 text-sm">Loading reviews...</p>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4 text-center">
          <p className="text-red-400">{error}</p>
        </div>
      )}

      {/* No Reviews State */}
      {!loading && reviews.length === 0 && !error && (
        <div className="bg-vista-dark/40 backdrop-blur-sm rounded-lg p-6 text-center border border-vista-light/10">
          <MessageCircle className="h-12 w-12 text-vista-light/30 mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">No Reviews Yet</h3>
          <p className="text-vista-light/70 mb-4">
            Be the first to share your thoughts on this {contentType === 'movie' ? 'movie' : 'show'}.
          </p>
          <Button
            onClick={() => setIsDialogOpen(true)}
            className="bg-vista-blue hover:bg-vista-blue/90 text-white gap-2 py-2 px-4 rounded-lg transition-all duration-300"
          >
            <MessageCircle className="h-4 w-4" />
            Write a Review
          </Button>
        </div>
      )}

      {/* Reviews List */}
      {reviews.length > 0 && (
        <div className="space-y-4">
          {reviews.map(review => (
            <div
              key={review._id}
              className="bg-vista-dark/40 backdrop-blur-sm rounded-lg sm:rounded-xl p-4 sm:p-5 border border-vista-light/10 transform transition-all duration-300 hover:bg-vista-dark/50 hover:border-vista-light/20 hover:shadow-lg"
            >
              <div className="flex justify-between items-start mb-3">
                <div className="flex items-center">
                  <div className="w-10 h-10 rounded-full bg-vista-blue/20 flex items-center justify-center mr-3 transform transition-all duration-300 hover:scale-110 hover:bg-vista-blue/30 shadow-sm">
                    <span className="text-vista-light font-medium">{review.username.charAt(0).toUpperCase()}</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-vista-light hover:text-vista-blue/90 transition-colors duration-300">{review.username}</h4>
                    {editingReviewId === review._id ? (
                      <div className="flex items-center mt-1">
                        {[1, 2, 3, 4, 5].map(i => (
                          <button
                            key={i}
                            type="button"
                            onClick={() => setEditedReview(prev => ({ ...prev, rating: i }))}
                            className="focus:outline-none"
                          >
                            <Star
                              className={`h-3 w-3 ${i <= editedReview.rating
                                ? 'fill-vista-blue text-vista-blue'
                                : 'text-vista-blue/40'}`}
                            />
                          </button>
                        ))}
                        <span className="text-xs text-vista-light/60 ml-2">{editedReview.rating}.0</span>
                      </div>
                    ) : (
                      <div className="flex items-center">
                        {[1, 2, 3, 4, 5].map(i => (
                          <Star
                            key={i}
                            className={`h-3 w-3 ${i <= review.rating
                              ? 'fill-vista-blue text-vista-blue'
                              : 'text-vista-blue/40'}`}
                          />
                        ))}
                        <span className="text-xs text-vista-light/60 ml-2">{review.rating}.0</span>
                      </div>
                    )}
                  </div>
                </div>

                <span className="text-xs text-vista-light/60">{formatDate(review.createdAt)}</span>
              </div>

              {/* Review content - editable or static */}
              {editingReviewId === review._id ? (
                <div className="mt-2 mb-3">
                  <Textarea
                    value={editedReview.comment}
                    onChange={(e) => setEditedReview(prev => ({ ...prev, comment: e.target.value }))}
                    placeholder="Edit your review..."
                    className="bg-vista-dark border-vista-light/10 text-vista-light min-h-[80px] text-sm"
                  />
                  <div className="flex justify-end mt-2 space-x-2">
                    <Button
                      onClick={cancelEditing}
                      variant="outline"
                      size="sm"
                      className="h-8 px-3 bg-vista-dark border-vista-light/10 text-vista-light hover:bg-vista-dark/80"
                    >
                      <X className="h-3.5 w-3.5 mr-1" />
                      Cancel
                    </Button>
                    <Button
                      onClick={() => saveEditedReview(review._id)}
                      size="sm"
                      className="h-8 px-3 bg-vista-blue hover:bg-vista-blue/90 text-white"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <div className="w-3.5 h-3.5 border-2 border-white/20 border-t-white rounded-full animate-spin mr-1"></div>
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="h-3.5 w-3.5 mr-1" />
                          Save
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              ) : (
                <p className="text-vista-light/80 text-sm">{review.comment}</p>
              )}

              {/* Like/Dislike buttons and action buttons - only show when not editing */}
              {editingReviewId !== review._id && (
                <>
                  {/* Like/Dislike section */}
                  <div className="flex items-center mt-3">
                    <button
                      onClick={() => likeReview(review._id)}
                      className="flex items-center text-xs text-vista-light/60 hover:text-vista-light transition-colors duration-300 mr-4"
                    >
                      <ThumbsUp className="h-3 w-3 mr-1" />
                      <span>{review.likes}</span>
                    </button>
                    <button
                      onClick={() => dislikeReview(review._id)}
                      className="flex items-center text-xs text-vista-light/60 hover:text-vista-light transition-colors duration-300"
                    >
                      <ThumbsDown className="h-3 w-3 mr-1" />
                      <span>{review.dislikes}</span>
                    </button>
                  </div>

                  {/* Action buttons - with clear separator */}
                  {((userId && userId === review.userId) || (userId && isAdmin)) && (
                    <div className="mt-4 pt-3 border-t border-vista-light/10 flex justify-end space-x-3">
                      {/* Edit button - only for review owner */}
                      {userId === review.userId && (
                        <Button
                          onClick={() => startEditingReview(review)}
                          size="default"
                          variant="outline"
                          className="px-4 py-2 bg-vista-blue/20 border-vista-blue/40 text-vista-blue hover:bg-vista-blue/30 hover:text-white"
                        >
                          <Pencil className="h-4 w-4 mr-2" />
                          Edit Review
                        </Button>
                      )}

                      {/* Delete button - for review owner or admin */}
                      <Button
                        onClick={() => confirmDeleteReview(review._id, isAdmin && userId !== review.userId)}
                        size="default"
                        variant="destructive"
                        className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white border-none"
                      >
                        {isAdmin && userId !== review.userId ? (
                          <>
                            <ShieldAlert className="h-4 w-4 mr-2" />
                            Admin Delete
                          </>
                        ) : (
                          <>
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete Review
                          </>
                        )}
                      </Button>
                    </div>
                  )}
                </>
              )}
            </div>
          ))}

          {/* Load More Button */}
          {page < totalPages && (
            <Button
              onClick={handleLoadMore}
              variant="outline"
              className="w-full mt-4 bg-vista-dark/40 border-vista-light/10 hover:bg-vista-dark/60 hover:border-vista-light/20 text-vista-light/80 hover:text-vista-light"
              disabled={loading}
            >
              {loading ? 'Loading...' : 'Load More Reviews'}
            </Button>
          )}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="bg-vista-dark-lighter border-vista-light/10 text-vista-light">
          <DialogHeader>
            <DialogTitle className="text-vista-light text-xl">Confirm Deletion</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-vista-light/80">
              Are you sure you want to delete this review? This action cannot be undone.
            </p>
          </div>
          <DialogFooter className="flex justify-end space-x-2">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              className="bg-vista-dark border-vista-light/10 text-vista-light hover:bg-vista-dark/80"
            >
              Cancel
            </Button>
            <Button
              onClick={handleDeleteReview}
              className="bg-red-500 hover:bg-red-600 text-white"
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

# Search Service

## Overview

StreamVista's Search Service provides a robust and flexible search functionality that allows users to find content across movies and TV shows. The service integrates with TMDb API for comprehensive content search and includes features like real-time search, search history, and advanced filtering options.

## Core Components

### Search Types

```typescript
interface SearchResult {
  id: string;
  title: string;
  year?: number;
  type: 'movie' | 'show';
  posterPath?: string;
  tmdbId?: string;
  imdbId?: string;
}

interface TMDbSearchResponse {
  page: number;
  results: TMDbItem[];
  total_pages: number;
  total_results: number;
}
```

### Search API Routes

The search service provides multiple search endpoints:

1. **Text Search**: `/api/search?query=searchterm`
2. **IMDb ID Search**: `/api/search?imdbId=tt1234567&type=movie|show`
3. **Genre Search**: `/api/search?genre=action&type=movie|show`

## Implementation Details

### Search Components

1. **SearchOverlay Component**
   - Real-time search functionality
   - Recent searches management
   - Search suggestions
   - Keyboard navigation support

2. **SearchResults Component**
   - Grid layout for search results
   - Filtering by content type (movies/shows)
   - Loading states
   - Error handling
   - Direct play functionality

### Search Functions

```typescript
// Main search function
async function search(
  query: string, 
  page = 1, 
  type: 'movie' | 'tv' | 'multi' = 'multi'
): Promise<MappedContent[]>

// Movie-specific search
async function searchMovies(
  query: string, 
  page = 1
): Promise<MappedContent[]>

// IMDb ID search functions
async function getMovieByImdbId(imdbId: string): Promise<IContent | null>
async function getTVByImdbId(imdbId: string): Promise<IContent | null>
```

### Search Page Implementation

The search page (`/search`) includes:
- Debounced search input
- Error handling with fallback to local content
- Retry mechanism with exponential backoff
- Results caching
- Responsive grid layout

## Features

1. **Real-time Search**
   - Debounced input handling
   - Instant results display
   - Search suggestions

2. **Advanced Filtering**
   - Content type filtering (movies/shows)
   - Genre-based filtering
   - Year filtering
   - Rating filtering

3. **Search History**
   - Recent searches storage
   - Search suggestions based on history
   - Clear search history option

4. **Error Handling**
   - Graceful degradation
   - Fallback to local content
   - Retry mechanism
   - User-friendly error messages

5. **Performance Optimizations**
   - Request debouncing
   - Results caching
   - Lazy loading of images
   - Pagination support

## Integration

### TMDb Integration

```typescript
const TMDB_CONFIG = {
  API_KEY: process.env.TMDB_API_KEY,
  ACCESS_TOKEN: process.env.TMDB_ACCESS_TOKEN,
  BASE_URL: 'https://api.themoviedb.org/3',
  IMAGE_BASE_URL: 'https://image.tmdb.org/t/p',
  POSTER_SIZES: ['w92', 'w154', 'w185', 'w342', 'w500', 'w780', 'original'],
  BACKDROP_SIZES: ['w300', 'w780', 'w1280', 'original'],
  LANGUAGE: 'en-US'
};
```

### Content Service Integration

The search service integrates with the Content Service for:
- Content details retrieval
- Image path resolution
- Content type mapping
- Rating conversion

## Best Practices

1. **Performance**
   - Implement debouncing for search input
   - Cache search results
   - Use appropriate image sizes
   - Implement pagination
   - Lazy load results

2. **User Experience**
   - Show loading states
   - Provide clear error messages
   - Maintain search history
   - Enable keyboard navigation
   - Support filters and sorting

3. **Error Handling**
   - Implement retry mechanism
   - Provide fallback content
   - Show user-friendly error messages
   - Log errors for debugging
   - Handle network issues gracefully

4. **Security**
   - Validate search inputs
   - Sanitize search terms
   - Rate limit requests
   - Handle API keys securely
   - Implement request timeouts

5. **Maintenance**
   - Monitor search performance
   - Track failed searches
   - Update search indexes
   - Maintain API compatibility
   - Document search patterns 
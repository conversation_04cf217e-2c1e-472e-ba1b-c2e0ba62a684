import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import { isValidObjectId } from 'mongoose';

/**
 * GET /api/admin/content/search
 * Search for content by title
 */
export async function GET(request: NextRequest) {
  try {
    // Get the query parameter
    const url = new URL(request.url);
    const query = url.searchParams.get('query') || '';

    // Get user ID for authorization
    let userId = url.searchParams.get('userId') || '';

    // If not in query, check headers
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7);
      }
    }

    // Check if user ID is provided
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to the database
    await ensureMongooseConnection();

    // Import models dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Get User model
    const User = mongoose.default.models.User ||
              mongoose.default.model('User', new mongoose.default.Schema({
                role: String,
                email: String,
                name: String
              }));

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();

    // Define a simple interface for the user role
    interface UserWithRole {
      role?: string;
    }

    if (!user || (user as UserWithRole).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define Content schema if it doesn't exist
    const ContentSchema = new mongoose.default.Schema({
      title: { type: String, required: true },
      type: { type: String, enum: ['movie', 'show'], required: true },
      tmdbId: { type: String, required: true },
      imdbId: { type: String },
      posterPath: { type: String, required: true },
      backdropPath: { type: String },
      overview: { type: String },
      tagline: { type: String },
      year: { type: String },
      genres: { type: [String], default: [] },
      status: {
        type: String,
        enum: ['published', 'draft'],
        default: 'draft'
      },
    }, {
      timestamps: true
    });

    // Get Content model
    const Content = mongoose.default.models.Content ||
                  mongoose.default.model('Content', ContentSchema);

    // Perform search on the unified Content model
    console.log(`Searching for content with query: "${query}"`);

    // First check if we have any content at all
    const contentCount = await Content.countDocuments();
    console.log(`Total content in database: ${contentCount}`);

    // Create search query - make status optional in case some content doesn't have it
    // Define the type for our search query to allow dynamic properties
    interface SearchQuery {
      title: { $regex: string; $options: string };
      status?: string;
    }

    const searchQuery: SearchQuery = {
      title: { $regex: query, $options: 'i' }
    };

    // Add status filter only if we have content with status field
    const hasStatusField = await Content.findOne({ status: { $exists: true } });
    if (hasStatusField) {
      searchQuery.status = 'published';
    }

    console.log('Search query:', JSON.stringify(searchQuery));

    const contentResults = await Content.find(searchQuery)
      .select('_id title type posterPath backdropPath')
      .limit(10)
      .lean();

    console.log(`Content search for "${query}" found ${contentResults.length} results`);

    // Log the first result for debugging
    if (contentResults.length > 0) {
      console.log('First result:', JSON.stringify(contentResults[0]));
    }

    // Format results with safe type handling
    const formattedResults = contentResults.map(content => {
      const result = content as unknown as {
        _id: { toString(): string } | string;
        title?: string;
        type: 'movie' | 'show';
        posterPath?: string;
        backdropPath?: string
      };

      const id = typeof result._id === 'string'
        ? result._id
        : result._id?.toString() || '';

      return {
        id,
        title: result.title || 'Unknown Title',
        type: result.type || 'movie',
        image: result.posterPath || result.backdropPath || undefined
      };
    });

    // Sort results
    const sortedResults = formattedResults.sort((a, b) => {
      // First, check for exact title match
      const aExactMatch = a.title.toLowerCase() === query.toLowerCase();
      const bExactMatch = b.title.toLowerCase() === query.toLowerCase();

      if (aExactMatch && !bExactMatch) return -1;
      if (!aExactMatch && bExactMatch) return 1;

      // Then, check for starts with
      const aStartsWith = a.title.toLowerCase().startsWith(query.toLowerCase());
      const bStartsWith = b.title.toLowerCase().startsWith(query.toLowerCase());

      if (aStartsWith && !bStartsWith) return -1;
      if (!aStartsWith && bStartsWith) return 1;

      // Finally, alphabetical
      return a.title.localeCompare(b.title);
    });

    return NextResponse.json({
      success: true,
      results: sortedResults.slice(0, 10) // Limit to 10 results total
    });
  } catch (error) {
    console.error('Error searching content:', error);
    return NextResponse.json(
      { error: 'Failed to search content', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
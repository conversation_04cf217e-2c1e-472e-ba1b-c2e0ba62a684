import { useQuery } from '@tanstack/react-query';

interface CategoryImage {
  id: number;
  title: string;
  posterPath: string;
  backdropPath: string;
}

interface CategoryImagesResponse {
  category: string;
  images: CategoryImage[];
}

/**
 * Custom hook to fetch images for a specific category
 */
export function useCategoryImages(category: string, count: number = 5) {
  return useQuery<CategoryImagesResponse>({
    queryKey: ['categoryImages', category, count],
    queryFn: async () => {
      const response = await fetch(`/api/category-images?category=${encodeURIComponent(category)}&count=${count}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch category images');
      }
      
      return response.json();
    },
    // Using defaults from QueryProvider
  });
}

/**
 * Hook for fetching multiple categories at once
 */
export function useMultipleCategoryImages(categories: string[], count: number = 5) {
  return useQuery({
    queryKey: ['multipleCategories', categories.join(','), count],
    queryFn: async () => {
      // Batch fetch all categories in parallel
      const results = await Promise.all(
        categories.map(async (category) => {
          try {
            const response = await fetch(`/api/category-images?category=${encodeURIComponent(category)}&count=${count}`);
            if (!response.ok) {
              console.error(`Failed to fetch category: ${category}`);
              return { category, images: [] };
            }
            return response.json();
          } catch (error) {
            console.error(`Error fetching category ${category}:`, error);
            return { category, images: [] };
          }
        })
      );
      
      // Return as an object with categories as keys
      return results.reduce((acc, result) => {
        acc[result.category] = result;
        return acc;
      }, {} as Record<string, CategoryImagesResponse>);
    },
    // Using defaults from QueryProvider
  });
} 
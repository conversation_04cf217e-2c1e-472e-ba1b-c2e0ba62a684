import { NextRequest, NextResponse } from 'next/server';

/**
 * GET /api/admin/tags
 * Get all tags with pagination and search
 */
export async function GET(request: NextRequest) {
  try {
    // Get the user ID from the cookie directly
    const userId = request.cookies.get('userId')?.value;
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const UserSchema = new mongoose.default.Schema({
      role: String
    });

    const User = mongoose.default.models.User ||
                mongoose.default.model('User', UserSchema);

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the Tag schema directly
    const TagSchema = new mongoose.default.Schema({
      name: { type: String, required: true },
      slug: { type: String, required: true, unique: true },
      count: { type: Number, default: 0 }
    }, {
      timestamps: true
    });

    // Get the Tag model
    const Tag = mongoose.default.models.Tag ||
               mongoose.default.model('Tag', TagSchema);

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('q') || '';
    const limit = parseInt(searchParams.get('limit') || '50');
    const page = parseInt(searchParams.get('page') || '1');
    const skip = (page - 1) * limit;

    // Build query
    const query: any = {};
    if (search) {
      query.$text = { $search: search };
    }

    // Fetch tags
    const tags = await Tag.find(query)
      .sort({ name: 1 })
      .skip(skip)
      .limit(limit);

    // Get total count
    const total = await Tag.countDocuments(query);

    // Return tags
    return NextResponse.json({
      tags: tags.map((tag: any) => ({
        id: tag._id.toString(),
        name: tag.name,
        slug: tag.slug,
        count: tag.count,
        createdAt: tag.createdAt
      })),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching tags:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tags', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/tags
 * Create a new tag
 */
export async function POST(request: NextRequest) {
  try {
    // Get the user ID from the cookie directly
    const userId = request.cookies.get('userId')?.value;
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const UserSchema = new mongoose.default.Schema({
      role: String
    });

    const User = mongoose.default.models.User ||
                mongoose.default.model('User', UserSchema);

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the Tag schema directly
    const TagSchema = new mongoose.default.Schema({
      name: { type: String, required: true },
      slug: { type: String, required: true, unique: true },
      count: { type: Number, default: 0 }
    }, {
      timestamps: true
    });

    // Get the Tag model
    const Tag = mongoose.default.models.Tag ||
               mongoose.default.model('Tag', TagSchema);

    // Define the UserActivity schema directly
    const UserActivitySchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      action: String,
      details: String,
      ipAddress: String,
      userAgent: String,
      timestamp: { type: Date, default: Date.now },
      metadata: mongoose.default.Schema.Types.Mixed
    }, {
      timestamps: true
    });

    // Get the UserActivity model
    const UserActivity = mongoose.default.models.UserActivity ||
                        mongoose.default.model('UserActivity', UserActivitySchema);

    // Get request data
    const data = await request.json();

    // Validate required fields
    if (!data.name) {
      return NextResponse.json({ error: 'Tag name is required' }, { status: 400 });
    }

    // Generate slug if not provided
    if (!data.slug) {
      data.slug = data.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
    }

    // Check if slug already exists
    const existingTag = await Tag.findOne({ slug: data.slug });
    if (existingTag) {
      return NextResponse.json({ error: 'Tag with this slug already exists' }, { status: 400 });
    }

    // Create new tag
    const tag = new Tag({
      name: data.name,
      slug: data.slug,
      count: 0
    });

    // Save tag
    await tag.save();

    // Log admin activity directly
    await UserActivity.create({
      userId: new mongoose.default.Types.ObjectId(userId),
      type: 'admin',
      action: 'create_tag',
      details: `Admin created tag: ${data.name}`,
      ipAddress: request.ip || request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      timestamp: new Date(),
      metadata: { tagId: tag._id.toString() }
    });

    // Return new tag
    return NextResponse.json({
      id: tag._id.toString(),
      name: tag.name,
      slug: tag.slug,
      count: tag.count,
      createdAt: tag.createdAt
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating tag:', error);
    return NextResponse.json(
      { error: 'Failed to create tag', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

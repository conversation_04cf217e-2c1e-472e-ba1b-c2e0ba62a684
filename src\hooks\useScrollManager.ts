import { useEffect, useRef, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { usePathname, useSearchParams } from 'next/navigation';

interface ScrollPosition {
  x: number;
  y: number;
  timestamp: number;
}

interface ScrollStorage {
  [key: string]: ScrollPosition;
}

const STORAGE_KEY = 'scroll_positions';
const MAX_STORAGE_AGE = 10 * 60 * 1000; // 10 minutes

function getStoredScrollPositions(): ScrollStorage {
  if (typeof window === 'undefined') return {};
  
  try {
    const stored = sessionStorage.getItem(STORAGE_KEY);
    if (!stored) return {};
    
    const positions: ScrollStorage = JSON.parse(stored);
    const now = Date.now();
    
    // Clean up old positions
    const cleaned: ScrollStorage = {};
    Object.entries(positions).forEach(([key, position]) => {
      if (now - position.timestamp < MAX_STORAGE_AGE) {
        cleaned[key] = position;
      }
    });
    
    return cleaned;
  } catch {
    return {};
  }
}

function storeScrollPosition(key: string, position: ScrollPosition): void {
  if (typeof window === 'undefined') return;
  
  try {
    const positions = getStoredScrollPositions();
    positions[key] = position;
    sessionStorage.setItem(STORAGE_KEY, JSON.stringify(positions));
  } catch {
    // Ignore storage errors
  }
}

function removeScrollPosition(key: string): void {
  if (typeof window === 'undefined') return;
  
  try {
    const positions = getStoredScrollPositions();
    delete positions[key];
    sessionStorage.setItem(STORAGE_KEY, JSON.stringify(positions));
  } catch {
    // Ignore storage errors
  }
}

export function useScrollManager() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const lastScrollSave = useRef<string | null>(null);
  const restoreTimeoutRef = useRef<NodeJS.Timeout>();
  
  // Create a unique key for the current page
  const getPageKey = useCallback(() => {
    const params = searchParams ? new URLSearchParams(searchParams).toString() : '';
    return `${pathname}${params ? '?' + params : ''}`;
  }, [pathname, searchParams]);

  // Save current scroll position
  const saveScrollPosition = useCallback(() => {
    if (typeof window === 'undefined') return;
    
    const key = getPageKey();
    const position: ScrollPosition = {
      x: window.scrollX,
      y: window.scrollY,
      timestamp: Date.now()
    };
    
    storeScrollPosition(key, position);
    lastScrollSave.current = key;
    
    console.log('[ScrollManager] Saved scroll position:', { key, position });
  }, [getPageKey]);

  // Restore scroll position for current page
  const restoreScrollPosition = useCallback((force = false) => {
    if (typeof window === 'undefined') return false;
    
    const key = getPageKey();
    const positions = getStoredScrollPositions();
    const position = positions[key];
    
    if (position && (force || key !== lastScrollSave.current)) {
      console.log('[ScrollManager] Restoring scroll position:', { key, position });
      
      // Clear any pending restore timeout
      if (restoreTimeoutRef.current) {
        clearTimeout(restoreTimeoutRef.current);
      }
      
      // Use requestAnimationFrame to ensure DOM is ready
      requestAnimationFrame(() => {
        window.scrollTo({
          left: position.x,
          top: position.y,
          behavior: 'auto' // Use 'auto' for instant restoration
        });
      });
      
      return true;
    }
    
    return false;
  }, [getPageKey]);

  // Scroll to top with smooth behavior
  const scrollToTop = useCallback((behavior: ScrollBehavior = 'smooth') => {
    if (typeof window === 'undefined') return;
    
    // Clear any stored position for this page since we're explicitly going to top
    const key = getPageKey();
    removeScrollPosition(key);
    
    console.log('[ScrollManager] Scrolling to top:', { key, behavior });
    
    window.scrollTo({
      top: 0,
      left: 0,
      behavior
    });
  }, [getPageKey]);

  // Handle navigation events
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Function to save scroll position before navigation
    const handleBeforeUnload = () => {
      saveScrollPosition();
    };

    // Function to handle page show (back/forward navigation)
    const handlePageShow = (event: PageTransitionEvent) => {
      if (event.persisted) {
        // Page was restored from cache (back/forward navigation)
        console.log('[ScrollManager] Page restored from cache');
        
        // Try to restore scroll position after a short delay
        restoreTimeoutRef.current = setTimeout(() => {
          if (!restoreScrollPosition()) {
            // If no stored position, check if this is a detail page return
            const referrer = document.referrer;
            const isFromDetailPage = referrer && (referrer.includes('/details/') || referrer.includes('/watch/'));
            
            if (isFromDetailPage) {
              console.log('[ScrollManager] Returning from detail page, scrolling to top');
              scrollToTop('auto');
            }
          }
        }, 100);
      }
    };

    // Save scroll position before navigation
    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('pagehide', handleBeforeUnload);
    window.addEventListener('pageshow', handlePageShow);

    // Cleanup
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('pagehide', handleBeforeUnload);
      window.removeEventListener('pageshow', handlePageShow);
      
      if (restoreTimeoutRef.current) {
        clearTimeout(restoreTimeoutRef.current);
      }
    };
  }, [saveScrollPosition, restoreScrollPosition, scrollToTop]);

  // Handle route changes
  useEffect(() => {
    // Try to restore scroll position when route changes
    const timeoutId = setTimeout(() => {
      if (!restoreScrollPosition()) {
        // If no stored position and this looks like a return from detail page
        const currentKey = getPageKey();
        const isListPage = pathname && (pathname.includes('/movies') || pathname.includes('/shows') || pathname.includes('/discover'));
        
        if (isListPage && searchParams?.has('page')) {
          console.log('[ScrollManager] List page with pagination, scrolling to top');
          scrollToTop('smooth');
        }
      }
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [pathname, searchParams, restoreScrollPosition, scrollToTop, getPageKey]);

  return {
    saveScrollPosition,
    restoreScrollPosition,
    scrollToTop,
    getPageKey
  };
} 
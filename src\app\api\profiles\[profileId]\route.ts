import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongoose';
import Profile from '@/models/Profile';
import { getCloudinaryPublicId, deleteFromCloudinary } from '@/utils/cloudinary-utils';
import User from '@/models/User';

type ProfileParams = Promise<{ profileId: string }>;

/**
 * GET /api/profiles/:profileId
 * Get a profile by ID
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: ProfileParams }
) {
  try {
    // Properly await the params object
    const { profileId } = await params;

    await ensureMongooseConnection();

    const profile = await Profile.findById(profileId);

    if (!profile) {
      return NextResponse.json(
        { error: 'Profile not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      profile: {
        id: profile._id.toString(),
        name: profile.name,
        avatar: profile.avatar,
        isKids: profile.isKids,
        isPrimary: profile.isPrimary,
        preferences: profile.preferences
      }
    });
  } catch (error) {
    console.error('Error fetching profile:', error);
    return NextResponse.json(
      { error: 'Failed to fetch profile' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/profiles/:profileId
 * Update a profile
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: ProfileParams }
) {
  try {
    // Properly await the params object
    const { profileId } = await params;

    const data = await request.json();
    const { name, avatar, isKids } = data;

    await ensureMongooseConnection();

    // Validate name if it's being updated
    if (name !== undefined) {
      if (!name || name.trim() === '') {
        return NextResponse.json(
          { error: 'Profile name cannot be empty' },
          { status: 400 }
        );
      }

      if (name.length > 30) {
        return NextResponse.json(
          { error: 'Profile name cannot exceed 30 characters' },
          { status: 400 }
        );
      }
    }

    // Prepare update object based on what's provided
    const updateFields: any = {};

    // Only add name to update if it's provided and valid
    if (name !== undefined) {
      updateFields.name = name.trim();
    }

    // Add other fields if provided
    if (avatar !== undefined) {
      updateFields.avatar = avatar;
    }

    if (isKids !== undefined) {
      updateFields.isKids = isKids;
    }

    if (data.preferences) {
      updateFields.preferences = data.preferences;
    }

    // Get the current profile to check for existing avatar
    const currentProfile = await Profile.findById(profileId);

    if (!currentProfile) {
      return NextResponse.json(
        { error: 'Profile not found' },
        { status: 404 }
      );
    }

    // Check if we need to delete the old avatar
    if (avatar !== undefined && currentProfile.avatar && avatar !== currentProfile.avatar) {
      // Extract the public ID from the old avatar URL
      const publicId = getCloudinaryPublicId(currentProfile.avatar);
      if (publicId) {
        // Delete the old avatar from Cloudinary
        const deleted = await deleteFromCloudinary(publicId);
        if (!deleted) {
          console.warn(`Failed to delete previous avatar: ${publicId}`);
        } else {
          console.log(`Successfully deleted previous avatar: ${publicId}`);
        }
      }
    }

    // Update the profile
    const updatedProfile = await Profile.findByIdAndUpdate(
      profileId,
      { $set: updateFields },
      { new: true } // Return the updated document
    );

    // If this is the primary profile and avatar was updated, also update the user's profile image
    if (updatedProfile && updatedProfile.isPrimary && avatar !== undefined) {
      // Find the user associated with this profile and update their profile image
      await User.findByIdAndUpdate(
        updatedProfile.userId,
        {
          $set: {
            profileImage: avatar,
            picture: avatar // Update both fields for consistency
          }
        }
      );
      console.log(`Updated user ${updatedProfile.userId} profile image to match primary profile avatar`);
    }

    if (!updatedProfile) {
      return NextResponse.json(
        { error: 'Profile not found' },
        { status: 404 }
      );
    }

    // Return the updated profile
    return NextResponse.json({
      profile: {
        id: updatedProfile._id.toString(),
        name: updatedProfile.name,
        avatar: updatedProfile.avatar,
        isKids: updatedProfile.isKids,
        isPrimary: updatedProfile.isPrimary
      }
    });
  } catch (error) {
    console.error('Error updating profile:', error);
    return NextResponse.json(
      { error: 'Failed to update profile' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/profiles/:profileId
 * Delete a profile
 */
export async function DELETE(
  _request: NextRequest,
  { params }: { params: ProfileParams }
) {
  try {
    // Properly await the params object
    const { profileId } = await params;

    await ensureMongooseConnection();

    // Check if this is a primary profile
    const profile = await Profile.findById(profileId);

    if (!profile) {
      return NextResponse.json(
        { error: 'Profile not found' },
        { status: 404 }
      );
    }

    // Can't delete the primary profile
    if (profile.isPrimary) {
      return NextResponse.json(
        { error: 'Cannot delete the primary profile' },
        { status: 400 }
      );
    }

    // Delete the profile
    await Profile.findByIdAndDelete(profileId);

    return NextResponse.json(
      { message: 'Profile deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting profile:', error);
    return NextResponse.json(
      { error: 'Failed to delete profile' },
      { status: 500 }
    );
  }
}
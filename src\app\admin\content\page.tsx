'use client';

import React, { useState, useEffect } from 'react';
import {
  Search,
  Filter,
  Plus,
  Edit,
  Trash2,
  MoreHorizontal,
  Eye,
  Film,
  Tv,
  Calendar,
  Star,
  Clock,
  RefreshCw,
  PlusCircle,
  RotateCw,
  Folder
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useContentList } from '@/hooks/useAdminData';
import { toast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import Image from 'next/image';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, CheckCircle } from 'lucide-react';
import ContentFormModal from '@/components/admin/ContentFormModal';

type ContentType = 'movie' | 'show';
type ContentStatus = 'published' | 'draft' | 'archived';

interface ContentApiResponse {
  content: ContentApiItem[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

interface ContentApiItem {
  id: string;
  title: string;
  type: "movie" | "show" | "tv";  // Include "tv" for compatibility with ContentFormModal
  tmdbId: string;
  imdbId?: string;
  posterPath: string;
  backdropPath?: string;
  overview: string;  // Required by ContentFormModal
  year?: string;
  genres: string[];
  releaseDate?: string;
  runtime?: number;
  rating?: number;
  status: "published" | "draft" | "archived";
  featured?: boolean;
  trending?: boolean;
  views?: number;
  createdAt: string;
  seasons?: number;
  episodes?: number;
}

// Define a type for the content form modal
interface ContentFormItem {
  id: string;
  title: string;
  type: "movie" | "tv";
  overview: string;
  releaseDate?: string;
  posterPath?: string;
  backdropPath?: string;
  genres?: string[];
  status?: string;
  featured?: boolean;
}

interface SyncResult {
  inserted: number;
  updated: number;
  total: number;
  message?: string;
}

export default function ContentManagementPage() {
  const { user, isAdmin } = useAuth();
  const router = useRouter();

  // Admin access is now protected by middleware and layout

  // State for tabs, search, and filters
  const [activeTab, setActiveTab] = useState('movies');
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [genreFilter, setGenreFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncResult, setSyncResult] = useState<SyncResult | null>(null);
  const [isContentModalOpen, setIsContentModalOpen] = useState(false);
  const [editingContent, setEditingContent] = useState<ContentFormItem | undefined>(undefined);

  // Determine content type based on active tab
  const contentType = activeTab === 'movies' ? 'movie' : 'show';

  // Fetch content data
  const {
    data: contentData,
    isLoading,
    error,
    refetch
  } = useContentList(
    currentPage,
    10,
    searchQuery,
    contentType,
    statusFilter !== 'all' ? statusFilter : '',
    genreFilter !== 'all' ? genreFilter : ''
  );

  // Map the API response to typed content items with correct types
  const content = React.useMemo(() => {
    if (!contentData?.content) return [];

    // Use a type assertion to treat the API response with the correct shape
    return contentData.content.map(item => {
      // Create a properly typed object with all expected properties
      // Use type assertion to handle the API response
      const apiItem = item as unknown as {
        id: string;
        title: string;
        type: string;
        tmdbId: string;
        imdbId?: string;
        posterPath: string;
        backdropPath?: string;
        overview?: string;
        year?: string;
        genres?: string[];
        releaseDate?: string;
        runtime?: number;
        rating?: number;
        status: string;
        featured?: boolean;
        trending?: boolean;
        views?: number;
        createdAt: string;
        seasons?: number;
        episodes?: number;
      };

      const contentItem: ContentApiItem = {
        id: apiItem.id,
        title: apiItem.title,
        type: (apiItem.type === 'show' ? 'tv' : apiItem.type) as "movie" | "show" | "tv",
        tmdbId: apiItem.tmdbId,
        imdbId: apiItem.imdbId,
        posterPath: apiItem.posterPath,
        backdropPath: apiItem.backdropPath,
        overview: apiItem.overview || '', // Provide a default empty string
        year: apiItem.year,
        genres: apiItem.genres || [],
        releaseDate: apiItem.releaseDate,
        runtime: apiItem.runtime,
        rating: apiItem.rating,
        status: apiItem.status as "published" | "draft" | "archived",
        featured: apiItem.featured,
        trending: apiItem.trending,
        views: apiItem.views,
        createdAt: apiItem.createdAt,
        seasons: apiItem.seasons,
        episodes: apiItem.episodes
      };
      return contentItem;
    });
  }, [contentData]);

  // Extract pagination info
  const pagination = contentData?.pagination || { page: 1, limit: 10, total: 0, pages: 1 };

  // Get all unique genres from content
  const allGenres = [...new Set(
    content.flatMap(item => item.genres || [])
  )].sort();

  // Handle search
  const handleSearch = () => {
    refetch();
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setCurrentPage(1); // Reset to first page when changing tabs
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle filter change
  const handleFilterChange = (type: 'status' | 'genre', value: string) => {
    if (type === 'status') {
      setStatusFilter(value);
    } else {
      setGenreFilter(value);
    }
    setCurrentPage(1); // Reset to first page when changing filters
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Format duration for display
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  // Handle content deletion
  const handleDeleteContent = async (id: string) => {
    if (confirm('Are you sure you want to delete this content? This action cannot be undone.')) {
      try {
        const response = await fetch(`/api/admin/content/${id}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include'
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Failed to delete content (${response.status})`);
        }

        toast({
          title: 'Content deleted',
          description: 'The content has been successfully deleted.',
          variant: 'default'
        });

        // Refresh the content list
        refetch();
      } catch (error) {
        console.error('Error deleting content:', error);
        toast({
          title: 'Error',
          description: error instanceof Error ? error.message : 'Failed to delete content',
          variant: 'destructive'
        });
      }
    }
  };

  // Handle status change
  const handleStatusChange = async (id: string, newStatus: string) => {
    try {
      const response = await fetch(`/api/admin/content/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus }),
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to update content status (${response.status})`);
      }

      toast({
        title: 'Status updated',
        description: `Content status has been updated to ${newStatus}.`,
        variant: 'default'
      });

      // Refresh the content list
      refetch();
    } catch (error) {
      console.error('Error updating content status:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update content status',
        variant: 'destructive'
      });
    }
  };

  // Handle sync with TMDB
  const handleSync = async () => {
    if (isSyncing) return;

    setIsSyncing(true);
    setSyncResult(null);

    try {
      const response = await fetch('/api/admin/content?action=sync', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to sync content');
      }

      setSyncResult({
        inserted: data.inserted || 0,
        updated: data.updated || 0,
        total: data.total || 0,
        message: data.message
      });

      // Refresh content list
      refetch();
    } catch (error) {
      console.error('Error syncing content:', error);
      toast({
        title: 'Sync Failed',
        description: error instanceof Error ? error.message : 'Failed to sync content',
        variant: 'destructive'
      });
    } finally {
      setIsSyncing(false);
    }
  };

  // Handle adding new content
  const handleAddContent = () => {
    setEditingContent(undefined);
    setIsContentModalOpen(true);
  };

  // Convert ContentApiItem to the format expected by ContentFormModal
  const convertToFormContent = (content: ContentApiItem): ContentFormItem => {
    return {
      id: content.id,
      title: content.title,
      type: content.type === 'show' ? 'tv' : content.type as 'movie' | 'tv',
      overview: content.overview,
      releaseDate: content.releaseDate,
      posterPath: content.posterPath,
      backdropPath: content.backdropPath,
      genres: content.genres,
      status: content.status,
      featured: content.featured
    };
  };

  // Handle editing content
  const handleEditContent = (content: ContentApiItem) => {
    setEditingContent(convertToFormContent(content));
    setIsContentModalOpen(true);
  };

  // Handle modal close
  const handleModalClose = () => {
    setIsContentModalOpen(false);
    setEditingContent(undefined);
  };

  // Calculate pagination info
  const totalPages = pagination.pages;

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-vista-light">Content Management</h1>
          <p className="text-vista-light/70">
            Manage movies, TV shows, and other content
          </p>
        </div>
        <div className="flex gap-3">
          <Button
            onClick={() => router.push('/admin/content/categories')}
            variant="outline"
            className="gap-2"
          >
            <Folder className="h-4 w-4" />
            Categories & Tags
          </Button>
          <Button
            onClick={() => router.push('/admin/content/featured')}
            variant="outline"
            className="gap-2"
          >
            <Star className="h-4 w-4" />
            Featured Content
          </Button>
          <Button
            onClick={handleSync}
            disabled={isSyncing}
            className="gap-2"
            variant="outline"
          >
            <RotateCw className={`h-4 w-4 ${isSyncing ? 'animate-spin' : ''}`} />
            {isSyncing ? 'Syncing...' : 'Sync with TMDB'}
          </Button>
          <Button
            onClick={handleAddContent}
            className="gap-2"
          >
            <PlusCircle className="h-4 w-4" />
            Add Content
          </Button>
        </div>
      </div>

      {/* Sync result alert */}
      {syncResult && (
        <Alert variant={syncResult.inserted > 0 ? "default" : "destructive"}>
          {syncResult.inserted > 0 ? (
            <CheckCircle className="h-4 w-4" />
          ) : (
            <AlertCircle className="h-4 w-4" />
          )}
          <AlertTitle>
            {syncResult.inserted > 0 ? "Sync Completed" : "Sync Failed"}
          </AlertTitle>
          <AlertDescription>
            {syncResult.inserted > 0 ? (
              `Successfully synced ${syncResult.total} content items. Added ${syncResult.inserted} new items and updated ${syncResult.updated} existing items.`
            ) : syncResult.message}
          </AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle className="text-vista-light">Content Library</CardTitle>
          <CardDescription>
            Manage your streaming content
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-vista-light/50" />
              <Input
                placeholder="Search content..."
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={(value) => handleFilterChange('status', value)}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="published">Published</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="archived">Archived</SelectItem>
                </SelectContent>
              </Select>

              <Select value={genreFilter} onValueChange={(value) => handleFilterChange('genre', value)}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Genre" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Genres</SelectItem>
                  {allGenres.map(genre => (
                    <SelectItem key={genre} value={genre}>{genre}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Button variant="outline" onClick={handleSearch} disabled={isLoading}>
                <Search className="mr-2 h-4 w-4" />
                Search
              </Button>
              <Button variant="outline" onClick={() => refetch()} disabled={isLoading}>
                <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                {isLoading ? 'Loading...' : 'Refresh'}
              </Button>
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="movies" className="flex items-center">
                <Film className="mr-2 h-4 w-4" />
                Movies
              </TabsTrigger>
              <TabsTrigger value="shows" className="flex items-center">
                <Tv className="mr-2 h-4 w-4" />
                TV Shows
              </TabsTrigger>
            </TabsList>

            <TabsContent value="movies">
              <div className="rounded-md border border-vista-light/10 overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Title</TableHead>
                      <TableHead>Release Date</TableHead>
                      <TableHead>Genres</TableHead>
                      <TableHead>Rating</TableHead>
                      <TableHead>Duration</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoading ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-6 text-vista-light/70">
                          <RefreshCw className="h-5 w-5 animate-spin mx-auto mb-2" />
                          Loading content...
                        </TableCell>
                      </TableRow>
                    ) : content.filter(item => item.type === 'movie').length > 0 ? (
                      content.filter(item => item.type === 'movie').map((movie) => (
                        <TableRow key={movie.id} isSelectable>
                          <TableCell>
                            <div className="flex items-center gap-3">
                              <div className="h-12 w-8 rounded overflow-hidden bg-vista-dark-lighter relative">
                                {movie.posterPath && (
                                  <Image
                                    src={`https://image.tmdb.org/t/p/w92${movie.posterPath}`}
                                    alt={movie.title}
                                    className="object-cover"
                                    fill
                                    sizes="(max-width: 768px) 100vw, 33vw"
                                  />
                                )}
                              </div>
                              <div>
                                <p className="font-medium text-vista-light">{movie.title}</p>
                                <p className="text-vista-light/70 text-xs">ID: {movie.id}</p>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Calendar className="h-3 w-3 mr-1 text-vista-light/70" />
                              {movie.releaseDate ? formatDate(movie.releaseDate) : movie.year}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-wrap gap-1">
                              {(movie.genres || []).map((genre, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {genre}
                                </Badge>
                              ))}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Star className="h-3 w-3 mr-1 text-yellow-500" />
                              {movie.rating || 'N/A'}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Clock className="h-3 w-3 mr-1 text-vista-light/70" />
                              {movie.runtime ? formatDuration(movie.runtime) : 'N/A'}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                movie.status === 'published'
                                  ? 'default'
                                  : movie.status === 'draft'
                                    ? 'default'
                                    : 'secondary'
                              }
                              className="capitalize"
                            >
                              {movie.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                  <span className="sr-only">Open menu</span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem>
                                  <Eye className="mr-2 h-4 w-4" />
                                  View Details
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleEditContent(movie)}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit Movie
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuLabel>Status</DropdownMenuLabel>
                                <DropdownMenuItem onClick={() => handleStatusChange(movie.id, 'published')}>
                                  Set as Published
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleStatusChange(movie.id, 'draft')}>
                                  Set as Draft
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleStatusChange(movie.id, 'archived')}>
                                  Archive
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  className="text-red-500 focus:text-red-500"
                                  onClick={() => handleDeleteContent(movie.id)}
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete Movie
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-6 text-vista-light/70">
                          No movies found matching your search criteria.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>

            <TabsContent value="shows">
              <div className="rounded-md border border-vista-light/10 overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Title</TableHead>
                      <TableHead>Release Date</TableHead>
                      <TableHead>Genres</TableHead>
                      <TableHead>Rating</TableHead>
                      <TableHead>Seasons</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoading ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-6 text-vista-light/70">
                          <RefreshCw className="h-5 w-5 animate-spin mx-auto mb-2" />
                          Loading content...
                        </TableCell>
                      </TableRow>
                    ) : content.filter(item => item.type === 'show').length > 0 ? (
                      content.filter(item => item.type === 'show').map((show) => (
                        <TableRow key={show.id} isSelectable>
                          <TableCell>
                            <div className="flex items-center gap-3">
                              <div className="h-12 w-8 rounded overflow-hidden bg-vista-dark-lighter relative">
                                {show.posterPath && (
                                  <Image
                                    src={`https://image.tmdb.org/t/p/w92${show.posterPath}`}
                                    alt={show.title}
                                    className="object-cover"
                                    fill
                                    sizes="(max-width: 768px) 100vw, 33vw"
                                  />
                                )}
                              </div>
                              <div>
                                <p className="font-medium text-vista-light">{show.title}</p>
                                <p className="text-vista-light/70 text-xs">ID: {show.id}</p>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Calendar className="h-3 w-3 mr-1 text-vista-light/70" />
                              {show.releaseDate ? formatDate(show.releaseDate) : show.year}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-wrap gap-1">
                              {(show.genres || []).map((genre, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {genre}
                                </Badge>
                              ))}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Star className="h-3 w-3 mr-1 text-yellow-500" />
                              {show.rating || 'N/A'}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <span className="font-medium">{show.seasons || 'N/A'}</span>
                              {show.episodes && <span className="text-vista-light/70 ml-1">({show.episodes} eps)</span>}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                show.status === 'published'
                                  ? 'default'
                                  : show.status === 'draft'
                                    ? 'default'
                                    : 'secondary'
                              }
                              className="capitalize"
                            >
                              {show.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                  <span className="sr-only">Open menu</span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem>
                                  <Eye className="mr-2 h-4 w-4" />
                                  View Details
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleEditContent(show)}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit Show
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuLabel>Status</DropdownMenuLabel>
                                <DropdownMenuItem onClick={() => handleStatusChange(show.id, 'published')}>
                                  Set as Published
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleStatusChange(show.id, 'draft')}>
                                  Set as Draft
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleStatusChange(show.id, 'archived')}>
                                  Archive
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  className="text-red-500 focus:text-red-500"
                                  onClick={() => handleDeleteContent(show.id)}
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete Show
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-6 text-vista-light/70">
                          No TV shows found matching your search criteria.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
          </Tabs>

          {/* Pagination */}
          {pagination.pages > 1 && (
            <div className="flex justify-between items-center mt-4">
              <div className="text-sm text-vista-light/70">
                Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} items
              </div>
              <div className="flex gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page === 1 || isLoading}
                >
                  Previous
                </Button>
                {Array.from({ length: pagination.pages }, (_, i) => i + 1)
                  .filter(page => {
                    // Show first page, last page, current page, and pages around current page
                    return page === 1 ||
                           page === pagination.pages ||
                           Math.abs(page - pagination.page) <= 1;
                  })
                  .map((page, index, array) => {
                    // Add ellipsis between non-consecutive pages
                    const showEllipsisBefore = index > 0 && array[index - 1] !== page - 1;
                    return (
                      <div key={page} className="flex items-center">
                        {showEllipsisBefore && (
                          <span className="px-2 text-vista-light/50">...</span>
                        )}
                        <Button
                          variant={pagination.page === page ? "default" : "outline"}
                          size="sm"
                          onClick={() => handlePageChange(page)}
                          disabled={isLoading}
                        >
                          {page}
                        </Button>
                      </div>
                    );
                  })}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page === pagination.pages || isLoading}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Content Form Modal */}
      <ContentFormModal
        isOpen={isContentModalOpen}
        onClose={handleModalClose}
        onSuccess={refetch}
        editContent={editingContent}
      />
    </div>
  );
}

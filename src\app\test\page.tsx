'use client';

import { useState } from 'react';
import { extractTrailerFromTMDB } from '@/lib/trailer-utils';
import CustomVideoPlayer from '@/components/CustomVideoPlayer';
import { Button } from '@/components/ui/button';

export default function TestPage() {
  const [trailerVideoId, setTrailerVideoId] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);
  const [rawVideoData, setRawVideoData] = useState<{results: unknown[]} | null>(null);
  const [playerStatus, setPlayerStatus] = useState<string>('Not loaded');

  const addLog = (message: string) => {
    console.log(message); // Also log to browser console
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const updatePlayerStatus = (status: string) => {
    setPlayerStatus(status);
    addLog(`Player Status: ${status}`);
  };

  const testSimpleIframe = () => {
    const testVideoId = 'dQw4w9WgXcQ';
    addLog(`Testing simple iframe with video ID: ${testVideoId}`);
    updatePlayerStatus('Loading simple iframe...');
    setTrailerVideoId(`iframe-test-${testVideoId}`);
  };

  const testDirectVideoId = () => {
    // Test with a known working YouTube video ID
    const knownWorkingId = 'dQw4w9WgXcQ'; // Rick Roll - almost always available
    addLog(`Testing with known working video ID: ${knownWorkingId}`);
    updatePlayerStatus('Loading...');
    setTrailerVideoId(knownWorkingId);
  };

  const testVideoIdValidation = () => {
    addLog('Testing video ID validation...');
    
    const testIds = [
      'dQw4w9WgXcQ', // Valid ID
      'invalid_id', // Invalid ID
      '123456789012345', // Too long
      'short', // Too short
      '', // Empty
    ];
    
    testIds.forEach(id => {
      const isValid = /^[a-zA-Z0-9_-]{11}$/.test(id);
      addLog(`Video ID "${id}": ${isValid ? 'VALID' : 'INVALID'}`);
    });
  };

  const testMovieTrailer = async () => {
    setLoading(true);
    addLog('Testing movie trailer extraction...');
    
    try {
      // Test with a popular movie (Spider-Man: No Way Home)
      const movieId = '634649';
      addLog(`Fetching movie details for ID: ${movieId}`);
      
      const response = await fetch(`/api/content?id=${movieId}&type=movie`);
      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }
      
      const contentData = await response.json();
      addLog(`Received content data: ${contentData.title}`);
      addLog(`Videos available: ${contentData.videos?.results?.length || 0}`);
      
      if (contentData.videos) {
        setRawVideoData(contentData.videos);
        addLog('Raw videos data structure:');
        addLog(JSON.stringify(contentData.videos, null, 2));
        
        // Check each video in detail
        if (contentData.videos.results) {
          contentData.videos.results.forEach((video: {name: string, type: string, site: string, official: boolean, key: string}, index: number) => {
            addLog(`Video ${index + 1}: ${video.name} (${video.type}, ${video.site}, Official: ${video.official}, Key: ${video.key})`);
          });
        }
        
        const extractedTrailerId = extractTrailerFromTMDB(contentData.videos);
        addLog(`Extracted trailer ID: ${extractedTrailerId}`);
        
        if (extractedTrailerId) {
          // Validate the extracted ID
          const isValidId = /^[a-zA-Z0-9_-]{11}$/.test(extractedTrailerId);
          addLog(`Trailer ID validation: ${isValidId ? 'VALID' : 'INVALID'}`);
          
          if (isValidId) {
            updatePlayerStatus('Loading extracted movie trailer...');
            setTrailerVideoId(extractedTrailerId);
            addLog('SUCCESS: Trailer found and ready to play!');
          } else {
            updatePlayerStatus('Error: Invalid trailer ID');
            addLog('ERROR: Extracted trailer ID is invalid format');
          }
        } else {
          updatePlayerStatus('Error: No trailer found');
          addLog('WARNING: No trailer found in video data');
        }
      } else {
        updatePlayerStatus('Error: No videos data');
        addLog('ERROR: No videos data in response');
      }
    } catch (error) {
      updatePlayerStatus('Error: API failed');
      addLog(`ERROR: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setLoading(false);
    }
  };

  const testTVTrailer = async () => {
    setLoading(true);
    addLog('Testing TV show trailer extraction...');
    
    try {
      // Test with a popular TV show (Stranger Things)
      const showId = '66732';
      addLog(`Fetching TV show details for ID: ${showId}`);
      
      const response = await fetch(`/api/content?id=${showId}&type=show`);
      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }
      
      const contentData = await response.json();
      addLog(`Received content data: ${contentData.title}`);
      addLog(`Videos available: ${contentData.videos?.results?.length || 0}`);
      
      if (contentData.videos) {
        setRawVideoData(contentData.videos);
        addLog('Raw videos data structure:');
        addLog(JSON.stringify(contentData.videos, null, 2));
        
        // Check each video in detail
        if (contentData.videos.results) {
          contentData.videos.results.forEach((video: {name: string, type: string, site: string, official: boolean, key: string}, index: number) => {
            addLog(`Video ${index + 1}: ${video.name} (${video.type}, ${video.site}, Official: ${video.official}, Key: ${video.key})`);
          });
        }
        
        const extractedTrailerId = extractTrailerFromTMDB(contentData.videos);
        addLog(`Extracted trailer ID: ${extractedTrailerId}`);
        
        if (extractedTrailerId) {
          // Validate the extracted ID
          const isValidId = /^[a-zA-Z0-9_-]{11}$/.test(extractedTrailerId);
          addLog(`Trailer ID validation: ${isValidId ? 'VALID' : 'INVALID'}`);
          
          if (isValidId) {
            updatePlayerStatus('Loading extracted TV trailer...');
            setTrailerVideoId(extractedTrailerId);
            addLog('SUCCESS: Trailer found and ready to play!');
          } else {
            updatePlayerStatus('Error: Invalid trailer ID');
            addLog('ERROR: Extracted trailer ID is invalid format');
          }
        } else {
          updatePlayerStatus('Error: No trailer found');
          addLog('WARNING: No trailer found in video data');
        }
      } else {
        updatePlayerStatus('Error: No videos data');
        addLog('ERROR: No videos data in response');
      }
    } catch (error) {
      updatePlayerStatus('Error: API failed');
      addLog(`ERROR: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setLoading(false);
    }
  };

  const testYouTubeAccess = async () => {
    addLog('Testing direct YouTube access...');
    
    try {
      // Test if we can access YouTube's oEmbed API
      const testVideoId = 'dQw4w9WgXcQ';
      const response = await fetch(`https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v=${testVideoId}&format=json`);
      
      if (response.ok) {
        const data = await response.json();
        addLog('YouTube oEmbed API accessible');
        addLog(`Test video title: ${data.title}`);
      } else {
        addLog(`YouTube oEmbed API error: ${response.status}`);
      }
    } catch (error) {
      addLog(`YouTube access test failed: ${error}`);
    }
  };

  const testBasicIframe = () => {
    addLog('Testing basic iframe loading...');
    updatePlayerStatus('Testing basic iframe...');
    
    // Create a minimal iframe test
    const testDiv = document.createElement('div');
    testDiv.style.position = 'fixed';
    testDiv.style.top = '10px';
    testDiv.style.right = '10px';
    testDiv.style.width = '300px';
    testDiv.style.height = '200px';
    testDiv.style.background = 'black';
    testDiv.style.border = '2px solid red';
    testDiv.style.zIndex = '9999';
    
    const iframe = document.createElement('iframe');
    iframe.src = 'https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=0&controls=1';
    iframe.style.width = '100%';
    iframe.style.height = '100%';
    iframe.style.border = 'none';
    
    iframe.onload = () => {
      addLog('Basic iframe loaded successfully!');
      updatePlayerStatus('Basic iframe: SUCCESS');
      setTimeout(() => document.body.removeChild(testDiv), 5000);
    };
    
    iframe.onerror = () => {
      addLog('Basic iframe failed to load!');
      updatePlayerStatus('Basic iframe: FAILED');
      setTimeout(() => document.body.removeChild(testDiv), 5000);
    };
    
    testDiv.appendChild(iframe);
    document.body.appendChild(testDiv);
    
    // Remove after 10 seconds regardless
    setTimeout(() => {
      if (document.body.contains(testDiv)) {
        document.body.removeChild(testDiv);
        addLog('Basic iframe test timed out');
        updatePlayerStatus('Basic iframe: TIMEOUT');
      }
    }, 10000);
  };

  const testNetworkAccess = async () => {
    addLog('Testing network access to YouTube...');
    
    try {
      // Test 1: Fetch YouTube main page
      const response = await fetch('https://www.youtube.com', { 
        method: 'HEAD', 
        mode: 'no-cors' 
      });
      addLog('YouTube main page accessible');
      
      // Test 2: Try to access a YouTube embed URL directly
      const embedResponse = await fetch('https://www.youtube.com/embed/dQw4w9WgXcQ', { 
        method: 'HEAD', 
        mode: 'no-cors' 
      });
      addLog('YouTube embed URL accessible');
      
      // Test 3: Check if we can load an image from YouTube
      const img = new Image();
      img.onload = () => addLog('YouTube images loading correctly');
      img.onerror = () => addLog('YouTube images blocked');
      img.src = 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg';
      
    } catch (error) {
      addLog(`Network test error: ${error}`);
    }
  };

  const testMinimalIframe = () => {
    addLog('Creating minimal iframe test...');
    updatePlayerStatus('Testing minimal iframe...');
    
    const container = document.createElement('div');
    container.innerHTML = `
      <div style="position: fixed; top: 50px; right: 10px; width: 400px; height: 300px; background: white; border: 3px solid blue; z-index: 10000;">
        <div style="padding: 10px; background: blue; color: white; font-size: 12px;">Minimal Iframe Test</div>
        <iframe 
          src="https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=0&controls=1&rel=0" 
          width="100%" 
          height="250"
          frameborder="0"
          allowfullscreen
        ></iframe>
      </div>
    `;
    
    const iframe = container.querySelector('iframe')!;
    
    iframe.addEventListener('load', () => {
      addLog('✅ Minimal iframe loaded successfully!');
      updatePlayerStatus('Minimal iframe: SUCCESS');
      setTimeout(() => document.body.removeChild(container), 5000);
    });
    
    iframe.addEventListener('error', () => {
      addLog('❌ Minimal iframe failed to load!');
      updatePlayerStatus('Minimal iframe: FAILED');
      setTimeout(() => document.body.removeChild(container), 5000);
    });
    
    document.body.appendChild(container);
    
    // Timeout after 10 seconds
    setTimeout(() => {
      if (document.body.contains(container)) {
        addLog('⏱️ Minimal iframe test timed out');
        updatePlayerStatus('Minimal iframe: TIMEOUT');
        document.body.removeChild(container);
      }
    }, 10000);
  };

  const testEnvironment = () => {
    addLog('Testing environment...');
    
    // Check if we're in development vs production
    addLog(`Environment: ${process.env.NODE_ENV}`);
    addLog(`User Agent: ${navigator.userAgent}`);
    addLog(`Location: ${window.location.href}`);
    addLog(`Protocol: ${window.location.protocol}`);
    
    // Check for potential blockers
    if (typeof window.fetch === 'undefined') {
      addLog('❌ Fetch API not available');
    } else {
      addLog('✅ Fetch API available');
    }
    
    // Check if iframe creation works at all
    try {
      const testIframe = document.createElement('iframe');
      testIframe.src = 'about:blank';
      addLog('✅ Iframe creation works');
    } catch (e) {
      addLog(`❌ Iframe creation failed: ${e}`);
    }
    
    // Check Content Security Policy
    const metaTags = document.querySelectorAll('meta[http-equiv="Content-Security-Policy"]');
    if (metaTags.length > 0) {
      addLog(`CSP found: ${metaTags[0].getAttribute('content')}`);
    } else {
      addLog('No CSP meta tag found');
    }
  };

  const clearLogs = () => {
    setLogs([]);
    setTrailerVideoId(null);
    setRawVideoData(null);
    setPlayerStatus('Not loaded');
  };

  // Create a completely simple iframe test
  const testUltraSimpleIframe = () => {
    addLog('Creating ultra-simple iframe test...');
    updatePlayerStatus('Testing ultra-simple iframe...');
    
    // Remove any existing test players
    const existingTestPlayers = document.querySelectorAll('.test-iframe-container');
    existingTestPlayers.forEach(el => el.remove());
    
    const container = document.createElement('div');
    container.className = 'test-iframe-container';
    container.style.cssText = `
      position: fixed; 
      top: 100px; 
      right: 10px; 
      width: 480px; 
      height: 320px; 
      background: black; 
      border: 3px solid lime; 
      z-index: 10001;
      padding: 10px;
    `;
    
    container.innerHTML = `
      <div style="color: white; font-size: 14px; margin-bottom: 10px;">Ultra Simple Test</div>
      <iframe 
        src="https://www.youtube.com/embed/1mTjfMFyPi8?autoplay=0&controls=1&disablekb=0" 
        width="460" 
        height="280"
        frameborder="0"
        allowfullscreen
        title="Test Video"
      ></iframe>
    `;
    
    document.body.appendChild(container);
    
    // Auto-remove after 15 seconds
    setTimeout(() => {
      if (document.body.contains(container)) {
        document.body.removeChild(container);
        addLog('Ultra-simple iframe test auto-removed');
      }
    }, 15000);
    
    addLog('Ultra-simple iframe created with Spider-Man trailer ID: 1mTjfMFyPi8');
    updatePlayerStatus('Ultra-simple iframe: CREATED');
  };

  const testYouTubeEmbedding = () => {
    addLog('Testing different YouTube embedding approaches...');
    updatePlayerStatus('Testing YouTube embedding...');
    
    // Remove any existing test containers
    document.querySelectorAll('.embed-test-container').forEach(el => el.remove());
    
    const container = document.createElement('div');
    container.className = 'embed-test-container';
    container.style.cssText = `
      position: fixed; 
      top: 50px; 
      right: 10px; 
      width: 600px; 
      height: 800px; 
      background: white; 
      border: 2px solid red; 
      z-index: 10002;
      padding: 20px;
      overflow-y: auto;
      font-family: Arial, sans-serif;
    `;
    
    const videoId = '1mTjfMFyPi8';
    
    container.innerHTML = `
      <h3 style="margin: 0 0 20px 0; color: black;">YouTube Embedding Test - Video ID: ${videoId}</h3>
      
      <div style="margin-bottom: 20px;">
        <h4 style="color: black; margin: 0 0 10px 0;">1. Basic Embed (Standard)</h4>
        <iframe 
          src="https://www.youtube.com/embed/${videoId}" 
          width="560" 
          height="315"
          frameborder="0"
          allowfullscreen
          title="Basic Embed"
        ></iframe>
      </div>
      
      <div style="margin-bottom: 20px;">
        <h4 style="color: black; margin: 0 0 10px 0;">2. Embed with Basic Parameters</h4>
        <iframe 
          src="https://www.youtube.com/embed/${videoId}?autoplay=0&controls=1" 
          width="560" 
          height="315"
          frameborder="0"
          allowfullscreen
          title="Embed with Parameters"
        ></iframe>
      </div>
      
      <div style="margin-bottom: 20px;">
        <h4 style="color: black; margin: 0 0 10px 0;">3. Embed with All Our Parameters</h4>
        <iframe 
          src="https://www.youtube.com/embed/${videoId}?autoplay=0&mute=0&controls=1&showinfo=0&rel=0&modestbranding=1" 
          width="560" 
          height="315"
          frameborder="0"
          allowfullscreen
          allow="autoplay; fullscreen; picture-in-picture"
          title="Full Parameters"
        ></iframe>
      </div>
      
      <div style="margin-bottom: 20px;">
        <h4 style="color: black; margin: 0 0 10px 0;">4. Nocookie Embed</h4>
        <iframe 
          src="https://www.youtube-nocookie.com/embed/${videoId}?autoplay=0&controls=1" 
          width="560" 
          height="315"
          frameborder="0"
          allowfullscreen
          title="Nocookie Embed"
        ></iframe>
      </div>
      
      <button onclick="document.body.removeChild(document.querySelector('.embed-test-container'))" 
              style="background: red; color: white; padding: 10px 20px; border: none; cursor: pointer; margin-top: 20px;">
        Close Test
      </button>
    `;
    
    document.body.appendChild(container);
    
    addLog('Created embedding test with 4 different iframe approaches');
    addLog('Check which ones load and which ones fail');
    updatePlayerStatus('Embedding test: CREATED');
  };

  const testGeneratedURL = () => {
    const videoId = '1mTjfMFyPi8';
    addLog('Testing the exact URL our CustomVideoPlayer generates...');
    
    // Replicate the exact URL building logic from CustomVideoPlayer
    const params = new URLSearchParams({
      autoplay: '0',
      mute: '0',
      rel: '0',
      modestbranding: '1',
      controls: '1',
    });
    
    const generatedUrl = `https://www.youtube.com/embed/${videoId}?${params.toString()}`;
    
    addLog(`Generated URL: ${generatedUrl}`);
    addLog('Opening generated URL in new tab...');
    
    // Open in new tab to test if URL works
    window.open(generatedUrl, '_blank');
    
    updatePlayerStatus('URL test: OPENED');
  };

  const checkCSPAndErrors = () => {
    addLog('Checking for Content Security Policy and console errors...');
    
    // Check if there are CSP meta tags
    const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
    if (cspMeta) {
      const cspContent = cspMeta.getAttribute('content');
      addLog(`CSP found: ${cspContent}`);
      
      // Check if CSP allows YouTube
      if (cspContent && cspContent.includes('youtube.com')) {
        addLog('✅ CSP allows YouTube domains');
      } else {
        addLog('❌ CSP may be blocking YouTube domains');
      }
    } else {
      addLog('ℹ️ No CSP meta tag found');
    }
    
    // Check current protocol
    addLog(`Current protocol: ${window.location.protocol}`);
    addLog(`Current origin: ${window.location.origin}`);
    
    // Check if running on localhost
    if (window.location.hostname === 'localhost') {
      addLog('⚠️ Running on localhost - some iframe restrictions may apply');
    }
    
    // Override console.error temporarily to catch iframe errors
    const originalError = console.error;
    const errors: string[] = [];
    
    console.error = (...args) => {
      errors.push(args.join(' '));
      originalError.apply(console, args);
    };
    
    // Create a test iframe and monitor for errors
    const testContainer = document.createElement('div');
    testContainer.style.cssText = 'position: fixed; top: -100px; left: -100px; width: 1px; height: 1px;';
    
    const testIframe = document.createElement('iframe');
    testIframe.src = 'https://www.youtube.com/embed/1mTjfMFyPi8?autoplay=0&controls=1';
    testIframe.style.width = '1px';
    testIframe.style.height = '1px';
    
    testIframe.onload = () => {
      addLog('✅ Hidden test iframe loaded successfully');
      if (errors.length === 0) {
        addLog('✅ No console errors detected during iframe load');
      } else {
        addLog(`❌ Console errors detected: ${errors.join(', ')}`);
      }
    };
    
    testIframe.onerror = () => {
      addLog('❌ Hidden test iframe failed to load');
    };
    
    testContainer.appendChild(testIframe);
    document.body.appendChild(testContainer);
    
    // Clean up after 5 seconds
    setTimeout(() => {
      console.error = originalError;
      if (document.body.contains(testContainer)) {
        document.body.removeChild(testContainer);
      }
      addLog('CSP and error check completed');
    }, 5000);
    
    updatePlayerStatus('CSP check: RUNNING');
  };

  return (
    <div className="min-h-screen bg-vista-dark text-vista-light p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Enhanced Trailer Functionality Test</h1>
        
        {/* Quick Diagnostic */}
        <div className="mb-8 p-4 bg-vista-dark-lighter rounded-lg border border-vista-light/20">
          <h2 className="text-lg font-semibold mb-2">🔍 Quick Diagnostic</h2>
          <p className="text-sm text-vista-light/80 mb-4">
            If you're experiencing trailer timeouts, try these tests in order:
          </p>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
            <Button size="sm" onClick={testEnvironment} className="bg-gray-600 hover:bg-gray-700 text-xs">
              📋 Environment Info
            </Button>
            <Button size="sm" onClick={testMinimalIframe} className="bg-red-600 hover:bg-red-700 text-xs">
              🔴 Minimal Iframe
            </Button>
            <Button size="sm" onClick={testBasicIframe} className="bg-orange-600 hover:bg-orange-700 text-xs">
              🟠 Basic Iframe  
            </Button>
            <Button size="sm" onClick={testNetworkAccess} className="bg-yellow-600 hover:bg-yellow-700 text-xs">
              🟡 Network Test
            </Button>
            <Button size="sm" onClick={testDirectVideoId} className="bg-green-600 hover:bg-green-700 text-xs">
              🟢 Known Video
            </Button>
            <Button size="sm" onClick={testSimpleIframe} className="bg-blue-600 hover:bg-blue-700 text-xs">
              🔵 Simple Iframe
            </Button>
            <Button size="sm" onClick={testYouTubeAccess} className="bg-purple-600 hover:bg-purple-700 text-xs">
              🟣 YouTube API
            </Button>
            <Button size="sm" onClick={testMovieTrailer} className="bg-vista-blue hover:bg-vista-blue/90 text-xs">
              🎬 Movie API
            </Button>
            <Button size="sm" onClick={testUltraSimpleIframe} className="bg-lime-600 hover:bg-lime-700 text-xs">
              🟢 Ultra Simple Iframe
            </Button>
            <Button size="sm" onClick={testYouTubeEmbedding} className="bg-cyan-600 hover:bg-cyan-700 text-xs">
              🔵 YouTube Embedding
            </Button>
            <Button size="sm" onClick={testGeneratedURL} className="bg-indigo-600 hover:bg-indigo-700 text-xs">
              🟣 URL Tester
            </Button>
            <Button size="sm" onClick={checkCSPAndErrors} className="bg-red-600 hover:bg-red-700 text-xs">
              🔴 CSP & Console Errors
            </Button>
          </div>
          
          <div className="mt-4 p-3 bg-vista-dark rounded border border-yellow-500/30">
            <h3 className="text-sm font-semibold text-yellow-400 mb-2">🚨 Since Direct YouTube Works But Iframe Doesn't:</h3>
            <ol className="text-xs text-vista-light/80 space-y-1">
              <li>1. <strong>Open browser console</strong> (F12) to see detailed logs</li>
              <li>2. Click <strong>"🔵 YouTube Embedding"</strong> - Test 4 different iframe approaches</li>
              <li>3. Click <strong>"🟣 URL Tester"</strong> - Test our exact generated URL in new tab</li>
              <li>4. Click <strong>"🔴 CSP & Console Errors"</strong> - Check for security policy blocks</li>
              <li>5. Try <strong>"🟢 Known Video"</strong> with our simplified player (2-second timeout)</li>
              <li>6. Watch the <strong>Player Status</strong> below and console logs</li>
              <li>7. Compare which iframe approaches work vs fail</li>
            </ol>
          </div>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Test Controls */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold mb-4">Test Controls</h2>
            
            <Button
              onClick={testDirectVideoId}
              className="w-full bg-green-600 hover:bg-green-700"
            >
              Test Known Working Video ID
            </Button>
            
            <Button
              onClick={testVideoIdValidation}
              className="w-full bg-purple-600 hover:bg-purple-700"
            >
              Test Video ID Validation
            </Button>
            
            <Button
              onClick={testYouTubeAccess}
              className="w-full bg-orange-600 hover:bg-orange-700"
            >
              Test YouTube Access
            </Button>
            
            <Button
              onClick={testMovieTrailer}
              disabled={loading}
              className="w-full bg-vista-blue hover:bg-vista-blue/90"
            >
              Test Movie Trailer (Spider-Man: No Way Home)
            </Button>
            
            <Button
              onClick={testTVTrailer}
              disabled={loading}
              className="w-full bg-vista-accent hover:bg-vista-accent/90"
            >
              Test TV Show Trailer (Stranger Things)
            </Button>
            
            <Button
              onClick={clearLogs}
              variant="outline"
              className="w-full"
            >
              Clear Logs
            </Button>
            
            {loading && (
              <div className="flex items-center justify-center p-4">
                <div className="w-6 h-6 border-2 border-vista-blue/30 border-t-vista-blue rounded-full animate-spin"></div>
                <span className="ml-2">Testing...</span>
              </div>
            )}
          </div>
          
          {/* Logs */}
          <div>
            <h2 className="text-xl font-semibold mb-4">Logs</h2>
            <div className="bg-vista-dark-lighter rounded-lg p-4 h-96 overflow-y-auto">
              {logs.length === 0 ? (
                <p className="text-vista-light/60">Run a test to see logs...</p>
              ) : (
                <div className="space-y-1">
                  {logs.map((log, index) => (
                    <div 
                      key={index} 
                      className={`text-xs font-mono ${
                        log.includes('ERROR') ? 'text-red-400' :
                        log.includes('WARNING') ? 'text-yellow-400' :
                        log.includes('SUCCESS') ? 'text-green-400' :
                        log.includes('VALID') ? 'text-blue-400' :
                        log.includes('INVALID') ? 'text-red-300' :
                        'text-vista-light/80'
                      }`}
                    >
                      {log}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
          
          {/* Raw Data */}
          <div>
            <h2 className="text-xl font-semibold mb-4">Raw Video Data</h2>
            <div className="bg-vista-dark-lighter rounded-lg p-4 h-96 overflow-y-auto">
              {rawVideoData ? (
                <pre className="text-xs text-vista-light/80 whitespace-pre-wrap">
                  {JSON.stringify(rawVideoData, null, 2)}
                </pre>
              ) : (
                <p className="text-vista-light/60">No video data available...</p>
              )}
            </div>
          </div>
        </div>
        
        {/* Player Status */}
        {trailerVideoId && (
          <div className="mt-6 p-4 bg-vista-dark-lighter rounded-lg border border-vista-light/20">
            <h3 className="text-lg font-semibold mb-2">🎯 Player Status</h3>
            <div className="flex items-center gap-3">
              <div className={`w-3 h-3 rounded-full ${
                playerStatus.includes('Error') ? 'bg-red-500' :
                playerStatus.includes('Loading') ? 'bg-yellow-500 animate-pulse' :
                playerStatus.includes('Loaded') ? 'bg-green-500' :
                'bg-gray-500'
              }`}></div>
              <span className="text-sm font-mono">{playerStatus}</span>
            </div>
          </div>
        )}

        {/* Video Player */}
        {trailerVideoId && (
          <div className="mt-8">
            <h2 className="text-xl font-semibold mb-4">
              Trailer Player (Video ID: {trailerVideoId.replace('iframe-test-', '')})
            </h2>
            
            {/* Show either custom player or simple iframe test */}
            {trailerVideoId.startsWith('iframe-test-') ? (
              <div>
                <h3 className="text-lg mb-2">Simple Iframe Test</h3>
                <div className="aspect-video bg-black rounded-lg overflow-hidden">
                  <iframe
                    src={`https://www.youtube.com/embed/${trailerVideoId.replace('iframe-test-', '')}?autoplay=0&mute=0&rel=0&modestbranding=1&controls=1`}
                    className="w-full h-full border-0"
                    allowFullScreen
                    allow="autoplay; fullscreen; picture-in-picture"
                    title="Simple Iframe Test"
                    onLoad={() => {
                      updatePlayerStatus('Simple iframe loaded successfully');
                      addLog('Simple iframe loaded successfully!');
                    }}
                    onError={() => {
                      updatePlayerStatus('Error: Simple iframe failed');
                      addLog('Simple iframe failed to load!');
                    }}
                  />
                </div>
              </div>
            ) : (
              <div>
                <h3 className="text-lg mb-2">Custom Video Player</h3>
                <div className="aspect-video">
                  <CustomVideoPlayer
                    videoId={trailerVideoId}
                    title="Test Trailer"
                    autoPlay={false}
                    className="w-full h-full rounded-lg overflow-hidden"
                    onError={(error) => {
                      updatePlayerStatus(`Error: ${error}`);
                      addLog(`Player Error: ${error}`);
                    }}
                    onLoad={() => {
                      updatePlayerStatus('Loaded successfully');
                      addLog('Player loaded successfully!');
                    }}
                  />
                </div>
              </div>
            )}
            
            {/* Direct YouTube link for comparison */}
            <div className="mt-4 p-4 bg-vista-dark-lighter rounded-lg">
              <p className="text-sm text-vista-light/80 mb-2">
                Direct YouTube link for comparison:
              </p>
              <a 
                href={`https://www.youtube.com/watch?v=${trailerVideoId.replace('iframe-test-', '')}`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-vista-blue hover:text-vista-blue/80 underline"
              >
                https://www.youtube.com/watch?v={trailerVideoId.replace('iframe-test-', '')}
              </a>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 
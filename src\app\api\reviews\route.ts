import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongoose';
import Review from '@/models/Review';

/**
 * GET /api/reviews
 * Get reviews for a specific content
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const contentId = searchParams.get('contentId');
    const contentType = searchParams.get('contentType') as 'movie' | 'show';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    if (!contentId || !contentType) {
      return NextResponse.json(
        { error: 'Content ID and type are required' },
        { status: 400 }
      );
    }

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Get reviews for the content
    const reviews = await Review.find({ contentId, contentType })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // Get total count for pagination
    const total = await Review.countDocuments({ contentId, contentType });

    // Calculate average rating
    const ratingData = await Review.aggregate([
      { $match: { contentId, contentType } },
      {
        $group: {
          _id: null,
          averageRating: { $avg: '$rating' },
          count: { $sum: 1 },
          ratings: {
            $push: '$rating'
          }
        }
      }
    ]);

    // Calculate rating distribution
    let ratingDistribution = [0, 0, 0, 0, 0]; // 1-5 stars
    let averageRating = 0;
    let reviewCount = 0;

    if (ratingData.length > 0) {
      averageRating = ratingData[0].averageRating;
      reviewCount = ratingData[0].count;

      // Count occurrences of each rating
      ratingData[0].ratings.forEach((rating: number) => {
        ratingDistribution[rating - 1]++;
      });
    }

    return NextResponse.json({
      success: true,
      reviews,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      },
      stats: {
        averageRating,
        reviewCount,
        ratingDistribution
      }
    });
  } catch (error) {
    console.error('Error fetching reviews:', error);
    return NextResponse.json(
      { error: 'Failed to fetch reviews' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/reviews
 * Create a new review
 */
export async function POST(request: NextRequest) {
  try {
    // Get data from request
    const data = await request.json();
    const { contentId, contentType, userId, username, rating, comment } = data;

    // Validate required fields
    if (!contentId || !contentType || !userId || !username || !rating || !comment) {
      return NextResponse.json(
        { error: 'All fields are required' },
        { status: 400 }
      );
    }

    // Validate rating
    if (rating < 1 || rating > 5) {
      return NextResponse.json(
        { error: 'Rating must be between 1 and 5' },
        { status: 400 }
      );
    }

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Only check for duplicate reviews if the user is authenticated
    // Anonymous users with generated IDs can submit multiple reviews
    if (!userId.startsWith('anonymous-')) {
      const existingReview = await Review.findOne({ contentId, userId });
      if (existingReview) {
        // Return a more specific status code for duplicate reviews
        return NextResponse.json(
          {
            error: 'You have already reviewed this content',
            existingReview // Include the existing review in the response
          },
          { status: 409 } // 409 Conflict is more appropriate for this case
        );
      }
    }

    // Create the review
    const review = new Review({
      contentId,
      contentType,
      userId,
      username,
      rating,
      comment,
      likes: 0,
      dislikes: 0
    });

    await review.save();

    return NextResponse.json({
      success: true,
      review
    });
  } catch (error) {
    console.error('Error creating review:', error);
    return NextResponse.json(
      { error: 'Failed to create review' },
      { status: 500 }
    );
  }
}

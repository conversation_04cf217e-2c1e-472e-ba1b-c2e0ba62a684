// English translations
const translations = {
  // Common
  'common.appName': 'StreamVista',
  'common.loading': 'Loading...',
  'common.error': 'An error occurred',
  'common.search': 'Search',
  'common.cancel': 'Cancel',
  'common.save': 'Save',
  'common.delete': 'Delete',
  'common.edit': 'Edit',
  'common.view': 'View',
  'common.back': 'Back',
  'common.next': 'Next',
  'common.previous': 'Previous',
  'common.submit': 'Submit',
  'common.done': 'Done',
  'common.more': 'More',
  'common.less': 'Less',
  'common.yes': 'Yes',
  'common.no': 'No',
  'common.all': 'All',
  'common.add': 'Add',
  'common.remove': 'Remove',
  'common.create': 'Create',
  'common.actions': 'Actions',
  'common.close': 'Close',
  'common.showMore': 'Show more',
  'common.showLess': 'Show less',
  'common.seeAll': 'See all',
  'common.readMore': 'Read more',
  'common.continue': 'Continue',
  'common.filter': 'Filter',
  'common.sort': 'Sort',
  'common.apply': 'Apply',
  'common.reset': 'Reset',
  'common.success': 'Success',
  'common.warning': 'Warning',
  'common.info': 'Information',

  // Navigation
  'nav.home': 'Home',
  'nav.shows': 'Shows',
  'nav.movies': 'Movies',
  'nav.categories': 'Categories',
  'nav.myList': 'My List',
  'nav.downloads': 'Downloads',
  'nav.insights': 'Insights',
  'nav.account': 'Account',
  'nav.settings': 'Settings',
  'nav.profiles': 'Profiles',
  'nav.help': 'Help',
  'nav.signIn': 'Sign In',
  'nav.signOut': 'Sign Out',

  // Home page
  'home.popularShows': 'Popular Shows',
  'home.popularShowsSubtitle': 'Trending series everyone\'s watching',
  'home.popularMovies': 'Popular Movies',
  'home.popularMoviesSubtitle': 'From blockbusters to indies',
  'home.continueWatching': 'Continue Watching',
  'home.featured': 'Featured',
  'home.browseCategoryTitle': 'Browse by Category',

  // Authentication
  'auth.welcomeBack': 'Welcome back!',
  'auth.signInToContinue': 'Sign in to continue to StreamVista',
  'auth.email': 'Email',
  'auth.password': 'Password',
  'auth.forgotPassword': 'Forgot password?',
  'auth.signIn': 'Sign In',
  'auth.signUp': 'Sign Up',
  'auth.noAccount': 'Don\'t have an account?',
  'auth.haveAccount': 'Already have an account?',
  'auth.createAccount': 'Create Account',
  'auth.termsAgree': 'By signing up, you agree to our Terms and Privacy Policy',
  'auth.rememberMe': 'Remember me',

  // Downloads
  'downloads.title': 'Downloads',
  'downloads.subtitle': 'Watch your favorite shows and movies offline',
  'downloads.noDownloads': 'No downloads available',
  'downloads.noDownloadsMessage': 'Download your favorite shows and movies to watch them offline anytime, anywhere.',
  'downloads.browseContent': 'Browse Content',
  'downloads.storage': 'Storage',
  'downloads.storageUsed': 'used',
  'downloads.storageAvailable': 'available',
  'downloads.totalDownloads': 'Total Downloads',
  'downloads.completed': 'completed',
  'downloads.downloading': 'Downloading',
  'downloads.paused': 'paused',
  'downloads.allDownloads': 'All Downloads',
  'downloads.completedFilter': 'Completed',
  'downloads.downloadingFilter': 'Downloading',
  'downloads.pausedFilter': 'Paused',
  'downloads.deleteConfirmTitle': 'Delete Download',
  'downloads.deleteConfirmMessage': 'Are you sure you want to delete this download? This action cannot be undone.',
  'downloads.remaining': 'remaining',
  'downloads.downloadSettings': 'Download Settings',
  'downloads.wifiOnly': 'Wi-Fi Only',
  'downloads.standardQuality': 'Standard Quality',
  'downloads.changeSettings': 'Change settings',

  // Insights
  'insights.title': 'Viewing Insights',
  'insights.subtitle': 'Discover your viewing habits and recommendations',
  'insights.watchTime': 'Watch Time',
  'insights.watchStreak': 'days',
  'insights.completion': 'Completion',
  'insights.overview': 'Overview',
  'insights.watchHistory': 'Watch History',
  'insights.forYou': 'For You',
  'insights.total': 'Total',
  'insights.weeklyActivity': 'Weekly Activity',
  'insights.peakViewingTime': 'Peak Viewing Time',
  'insights.morning': 'Morning',
  'insights.afternoon': 'Afternoon',
  'insights.evening': 'Evening',
  'insights.night': 'Night',
  'insights.contentPreferences': 'Content Preferences',
  'insights.contentType': 'Content Type',
  'insights.favoriteGenres': 'Favorite Genres',
  'insights.completionRate': 'Completion Rate',
  'insights.completed': 'Completed',
  'insights.topWatched': 'Top Watched',
  'insights.continueWatching': 'Continue Watching',
  'insights.caughtUp': 'All caught up! No unfinished content.',
  'insights.watched': 'Watched',
  'insights.noHistory': 'No watch history',
  'insights.noHistoryMessage': 'You haven\'t watched any content yet. Start watching to build your history.',
  'insights.shows': 'Shows',
  'insights.movies': 'Movies',
  'insights.items': 'items',
  'insights.item': 'item',
  'insights.personalizedTitle': 'Personalized For You',
  'insights.personalizedMessage': 'Based on your viewing history, we think you\'ll enjoy these titles.',
  'insights.topGenres': 'Your top genres:',
  'insights.notEnoughData': 'Not enough data',
  'insights.notEnoughDataMessage': 'Watch more content to get personalized recommendations based on your taste.',
  'insights.rewatch': 'Rewatch',
  'insights.resume': 'Resume',
  'insights.watchedTime': 'watched',

  // Content details
  'content.episodes': 'Episodes',
  'content.season': 'Season',
  'content.seasons': 'Seasons',
  'content.details': 'Details',
  'content.starring': 'Starring',
  'content.creators': 'Creators',
  'content.creator': 'Creator',
  'content.director': 'Director',
  'content.genres': 'Genres:',
  'content.relatedContent': 'More Like This',
  'content.watchNow': 'Watch Now',
  'content.addToList': 'Add to My List',
  'content.watchlist': 'Add to Watchlist',
  'content.share': 'Share',
  'content.moreInfo': 'More Info',
  'content.continue': 'Continue Watching',
  'content.new': 'New',
  'content.awardWinning': 'Award Winning',

  // Video player
  'player.pause': 'Pause',
  'player.play': 'Play',
  'player.mute': 'Mute',
  'player.unmute': 'Unmute',
  'player.fullscreen': 'Fullscreen',
  'player.exitFullscreen': 'Exit Fullscreen',
  'player.settings': 'Settings',
  'player.subtitles': 'Subtitles',
  'player.quality': 'Quality',
  'player.playbackSpeed': 'Playback Speed',
  'player.normal': 'Normal',
  'player.pictureInPicture': 'Picture-in-Picture',
  'player.exitPictureInPicture': 'Exit Picture-in-Picture',
  'player.theaterMode': 'Theater Mode',
  'player.autoNext': 'Play Next Episode',
  'player.downloading': 'Downloading',
  'player.downloaded': 'Downloaded',
  'player.watchOffline': 'Watch Offline',

  // Settings
  'settings.title': 'Settings',
  'settings.account': 'Account',
  'settings.preferences': 'Preferences',
  'settings.playback': 'Playback & Quality',
  'settings.notifications': 'Notifications',
  'settings.privacy': 'Privacy & Security',
  'settings.billing': 'Billing & Subscription',
  'settings.language': 'Language',
  'settings.appearance': 'Appearance',
  'settings.theme': 'Theme',
  'settings.darkMode': 'Dark Mode',
  'settings.lightMode': 'Light Mode',
  'settings.displayLanguage': 'Display Language',
  'settings.autoplayNext': 'Autoplay Next Episode',
  'settings.autoplayMessage': 'Automatically play the next episode in a series',
  'settings.videoQuality': 'Video Quality',
  'settings.downloadQuality': 'Download Quality',
  'settings.subtitlesDefault': 'Enable Subtitles by Default',
  'settings.saveChanges': 'Save Changes',
  'settings.saving': 'Saving...',

  // Toast notifications
  'toast.downloadComplete': 'Download Complete',
  'toast.downloadCompleteMessage': '{title} is ready to watch offline',
  'toast.downloadPaused': 'Download Paused',
  'toast.downloadPausedMessage': '{title} has been paused',
  'toast.downloadResumed': 'Download Resumed',
  'toast.downloadResumedMessage': '{title} is downloading again',
  'toast.downloadDeleted': 'Download Deleted',
  'toast.downloadDeletedMessage': '{title} has been removed',
  'toast.addedToList': 'Added to List',
  'toast.addedToListMessage': '{title} has been added to your list',
  'toast.addedToListDesc': 'has been added to your list',
  'toast.removedFromList': 'Removed from List',
  'toast.removedFromListMessage': '{title} has been removed from your list',
  'toast.settingsSaved': 'Settings Saved',
  'toast.settingsSavedMessage': 'Your settings have been saved successfully',

  // Recommendations
  'recommendations.title': 'Recommended for You',
  'recommendations.becauseYouWatched': 'Because you watched {title}',
  'recommendations.becauseYouLike': 'Because you like {genre}',
  'recommendations.popular': 'Popular on StreamVista',
  'recommendations.trending': 'Trending Now',
  'recommendations.newRelease': 'New Release',
  'recommendations.criticallyAcclaimed': 'Critically Acclaimed',
  'recommendations.movie': 'Movie',
  'recommendations.show': 'Show',
  'recommendations.similarTo': 'Similar to {title}',
  'recommendations.continueWatching': 'Continue Watching',
  'recommendations.topPicks': 'Top Picks for You',
  'recommendations.because': 'Because you watched',
  'recommendations.inYourWatchlist': 'In your watchlist',
  'recommendations.awardWinning': 'Award winning',
  'recommendations.recommended': 'Recommended for you',
};

export default translations;

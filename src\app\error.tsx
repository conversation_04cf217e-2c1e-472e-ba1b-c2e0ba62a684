'use client';

import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const [isMobile, setIsMobile] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  useEffect(() => {
    // Detect mobile device
    setIsMobile(window.innerWidth < 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent));

    // Log the error to an error reporting service
    console.error('Error boundary caught error:', error);
    
    // For session-related errors, attempt to clean up local storage
    if (error.message.includes('session') || 
        error.message.includes('verification') || 
        error.message.includes('Critical error')) {
      console.log('Session error detected, cleaning up local storage...');
      try {
        localStorage.removeItem('user');
        localStorage.removeItem('userId');
        localStorage.removeItem('watchPartyUserId');
      } catch (cleanupError) {
        console.error('Failed to clean up local storage:', cleanupError);
      }
    }
  }, [error]);

  const handleReset = () => {
    try {
      setRetryCount(prev => prev + 1);
      reset();
    } catch (resetError) {
      console.error('Reset failed, reloading page:', resetError);
      window.location.reload();
    }
  };

  const handleSignOut = () => {
    try {
      // Clear all auth data
      localStorage.removeItem('user');
      localStorage.removeItem('userId');
      localStorage.removeItem('watchPartyUserId');
      
      // Redirect to auth page
      window.location.href = '/auth';
    } catch (signOutError) {
      console.error('Sign out failed:', signOutError);
      // Force page reload as a fallback
      window.location.reload();
    }
  };

  // Check if this is a network-related error (common on mobile)
  const isNetworkError = error.message.includes('network') || 
                         error.message.includes('fetch') ||
                         error.message.includes('connection') ||
                         error.message.includes('timeout');

  // Check if this is a session-related error
  const isSessionError = error.message.includes('session') || 
                         error.message.includes('verification') || 
                         error.message.includes('Critical error');

  return (
    <div className="min-h-screen bg-vista-dark flex items-center justify-center p-4">
      <div className="max-w-md w-full text-center space-y-6">
        <div className="space-y-2">
          <div className="text-6xl">⚠️</div>
          <h2 className="text-2xl font-bold text-vista-light">
            {isNetworkError ? 'Connection Problem' : 'Something went wrong'}
          </h2>
          <p className="text-vista-light/70 text-sm">
            {isNetworkError && isMobile 
              ? 'Poor network connection detected. This is common on mobile networks.'
              : isSessionError 
                ? 'Session verification failed. You may need to sign in again.'
                : 'An unexpected error occurred.'
            }
          </p>
          {retryCount > 0 && (
            <p className="text-vista-light/50 text-xs">
              Retry attempt: {retryCount}
            </p>
          )}
        </div>

        <div className="space-y-3">
          <Button 
            onClick={handleReset} 
            className="w-full bg-vista-blue hover:bg-vista-blue-dark"
          >
            {isNetworkError ? 'Retry Connection' : 'Try Again'}
          </Button>
          
          {isMobile && isNetworkError && (
            <div className="text-xs text-vista-light/60 space-y-1">
              <p>Mobile troubleshooting tips:</p>
              <ul className="text-left space-y-1">
                <li>• Switch between WiFi and mobile data</li>
                <li>• Check your internet connection</li>
                <li>• Try refreshing the page</li>
              </ul>
            </div>
          )}
          
          {isSessionError && (
            <Button 
              onClick={handleSignOut} 
              variant="outline"
              className="w-full border-vista-blue/30 text-vista-blue hover:bg-vista-blue/10"
            >
              Sign Out & Restart
            </Button>
          )}
          
          <Button 
            onClick={() => window.location.href = '/'} 
            variant="ghost"
            className="w-full text-vista-light/70 hover:text-vista-light"
          >
            Go Home
          </Button>
        </div>

        {process.env.NODE_ENV === 'development' && (
          <details className="text-left">
            <summary className="text-xs text-vista-light/50 cursor-pointer">
              Error Details (Development)
            </summary>
            <pre className="text-xs text-vista-light/40 mt-2 p-2 bg-vista-dark-lighter rounded">
              {error.message}
            </pre>
          </details>
        )}
      </div>
    </div>
  );
}

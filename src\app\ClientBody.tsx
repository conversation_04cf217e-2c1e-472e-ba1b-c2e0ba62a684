"use client";

import { useState, useEffect } from 'react';
import ErrorBoundary from '@/components/ErrorBoundary';

interface ClientBodyProps {
  children: React.ReactNode;
  className?: string;
}

export default function ClientBody({ children, className }: ClientBodyProps) {
  const [mounted, setMounted] = useState(false);

  // After mounting, we have access to the window
  useEffect(() => {
    // Set mounted state once on client side
    setMounted(true);

    // Add handlers for unhandled errors
    const handleError = (event: ErrorEvent) => {
      console.error('Global error caught:', event.error);
    };

    window.addEventListener('error', handleError);

    return () => {
      window.removeEventListener('error', handleError);
    };
  }, []);

  // We always return the children - the mounted state is only used to determine
  // when to render the error boundary, which avoids hydration issues
  return (
    <div className={className}>
      {mounted ? (
        <ErrorBoundary>{children}</ErrorBoundary>
      ) : (
        // During server rendering and initial hydration, just render children without the error boundary
        children
      )}
    </div>
  );
}

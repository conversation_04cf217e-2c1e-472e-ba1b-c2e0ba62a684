import { NextRequest, NextResponse } from 'next/server';
import { getPopularMovies } from '@/lib/tmdb-api';
import { formatTMDbContentForCards } from '@/lib/content-utils';

export async function GET(request: NextRequest) {
  try {
    // Get page from query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    
    // Fetch popular movies from TMDb
    const popularMovies = await getPopularMovies(page);
    
    // Format the data for content cards
    const formattedMovies = formatTMDbContentForCards(popularMovies);
    
    // Return the formatted data
    return NextResponse.json({
      success: true,
      data: formattedMovies,
      page,
      hasMore: page < 5 // Limit to 5 pages for now
    });
  } catch (error) {
    console.error('Error fetching popular movies:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch popular movies' },
      { status: 500 }
    );
  }
}

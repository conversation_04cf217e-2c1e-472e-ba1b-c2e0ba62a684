/**
 * OMDB API Service
 *
 * Provides utilities for interacting with the OMDB API for movie and TV show data.
 */

import { OMDB_CONFIG } from '@/config/api';
import { IContent } from '@/data/content';

// OMDB API response types
export interface OMDBResponse {
  Title: string;
  Year: string;
  Rated: string;
  Released: string;
  Runtime: string;
  Genre: string;
  Director: string;
  Writer: string;
  Actors: string;
  Plot: string;
  Language: string;
  Country: string;
  Awards: string;
  Poster: string;
  Ratings: {
    Source: string;
    Value: string;
  }[];
  Metascore: string;
  imdbRating: string;
  imdbVotes: string;
  imdbID: string;
  Type: string;
  totalSeasons?: string;
  Response: string;
  Error?: string;
}

export interface OMDBSearchResponse {
  Search: {
    Title: string;
    Year: string;
    imdbID: string;
    Type: string;
    Poster: string;
  }[];
  totalResults: string;
  Response: string;
  Error?: string;
}

/**
 * Fetches data from the OMDB API
 * @param params Query parameters for the OMDB API
 * @returns The JSON response from the API
 */
export async function fetchFromOMDB<T>(params: Record<string, string> = {}): Promise<T> {
  const apiKey = OMDB_CONFIG.API_KEY;
  const baseUrl = OMDB_CONFIG.BASE_URL;

  if (!apiKey) {
    console.error('OMDB API Key not found');
    throw new Error('OMDB API Key not found');
  }

  const url = new URL(baseUrl);

  // Add API key to params
  url.searchParams.append('apikey', apiKey);

  // Add other params
  Object.entries(params).forEach(([key, value]) => {
    url.searchParams.append(key, value);
  });

  // Log the URL without exposing the API key
  const safeUrl = new URL(url.toString());
  safeUrl.searchParams.delete('apikey');
  console.log(`Fetching from OMDB: ${safeUrl.toString()}`);

  try {
    const response = await fetch(url.toString());

    if (!response.ok) {
      throw new Error(`OMDB API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    // Check if the API returned an error
    if (data.Response === 'False') {
      console.error('OMDB API returned an error:', data.Error);
      // Don't throw an error, just return the data with the error
      return data;
    }

    // Create a safe copy of params without sensitive information
    const safeParams = { ...params };
    // Remove any potentially sensitive parameters
    delete safeParams.apikey;
    console.log(`OMDB API success for ${JSON.stringify(safeParams)}`);
    return data;
  } catch (error) {
    console.error('Error fetching from OMDB:', error);
    throw error;
  }
}

/**
 * Maps an OMDB response to our content interface
 */
export function mapOMDBToContent(omdbData: OMDBResponse): IContent {
  // Extract year from Year field (which might be a range like "2019–2023")
  const year = omdbData.Year.split('–')[0].trim();

  // Convert runtime from "123 min" to number
  const runtimeMatch = omdbData.Runtime.match(/^(\d+)/);
  const runtime = runtimeMatch ? parseInt(runtimeMatch[1]) : undefined;

  // Convert IMDB rating to number
  const rating = omdbData.imdbRating && omdbData.imdbRating !== 'N/A'
    ? parseFloat(omdbData.imdbRating)
    : undefined;

  // Determine content type - normalize OMDB types to our application types
  let type: 'movie' | 'show' = 'movie';
  if (omdbData.Type === 'series' || omdbData.Type === 'episode') {
    type = 'show';
  }

  // Extract seasons if available
  const seasons = omdbData.totalSeasons && omdbData.totalSeasons !== 'N/A'
    ? parseInt(omdbData.totalSeasons)
    : undefined;

  // Process actors string into array
  const actors = omdbData.Actors && omdbData.Actors !== 'N/A'
    ? omdbData.Actors.split(', ').map(actor => actor.trim())
    : undefined;

  // Process genres string into array
  const genres = omdbData.Genre && omdbData.Genre !== 'N/A'
    ? omdbData.Genre.split(', ').map(genre => genre.trim())
    : undefined;

  return {
    id: omdbData.imdbID,
    title: omdbData.Title,
    type,
    year,
    posterPath: omdbData.Poster && omdbData.Poster !== 'N/A' ? omdbData.Poster : '',
    backdropPath: '', // OMDB doesn't provide backdrop images
    overview: omdbData.Plot && omdbData.Plot !== 'N/A' ? omdbData.Plot : undefined,
    genres,
    runtime,
    rating,
    seasons,
    imdbId: omdbData.imdbID,
    // Additional OMDB-specific fields
    director: omdbData.Director && omdbData.Director !== 'N/A' ? omdbData.Director : undefined,
    actors,
    awards: omdbData.Awards && omdbData.Awards !== 'N/A' ? omdbData.Awards : undefined,
    rated: omdbData.Rated && omdbData.Rated !== 'N/A' ? omdbData.Rated : undefined,
    released: omdbData.Released && omdbData.Released !== 'N/A' ? omdbData.Released : undefined,
    metascore: omdbData.Metascore && omdbData.Metascore !== 'N/A' ? parseInt(omdbData.Metascore) : undefined,
    dataSource: 'omdb'
  };
}

/**
 * Gets content details by IMDb ID
 */
export async function getContentByImdbId(imdbId: string): Promise<IContent> {
  const data = await fetchFromOMDB<OMDBResponse>({
    i: imdbId,
    plot: 'full'
  });

  return mapOMDBToContent(data);
}

/**
 * Searches for content by title
 */
export async function searchOMDB(query: string, page: number = 1, type?: 'movie' | 'series'): Promise<IContent[]> {
  const params: Record<string, string> = {
    s: query,
    page: page.toString()
  };

  // Add type if specified
  if (type) {
    params.type = type;
  }

  try {
    const data = await fetchFromOMDB<OMDBSearchResponse>(params);

    // If no results or error, return empty array
    if (!data.Search || data.Response === 'False') {
      console.log(`No OMDB results found for query: ${query}`);
      return [];
    }

    // For search results, we'll just return basic info without making additional API calls
    // to avoid hitting rate limits. Full details can be fetched when needed.
    return data.Search.map(item => {
      // Normalize content type: OMDB uses 'movie', 'series', 'episode', etc.
      let contentType: 'movie' | 'show' = 'movie';
      if (item.Type === 'series' || item.Type === 'episode') {
        contentType = 'show';
      }

      // Process year field which might include ranges like "2010–2015"
      const year = item.Year.split('–')[0].trim();

      return {
        id: item.imdbID,
        title: item.Title,
        type: contentType,
        year,
        posterPath: item.Poster && item.Poster !== 'N/A' ? item.Poster : '',
        imdbId: item.imdbID,
        dataSource: 'omdb'
      } as IContent;
    });
  } catch (error) {
    console.error(`Error searching OMDB for "${query}":`, error);
    return [];
  }
}

/**
 * Enhances existing content with OMDB data
 */
export async function enhanceContentWithOMDB(content: IContent): Promise<IContent> {
  // Only proceed if we have an IMDb ID
  if (!content.imdbId) {
    console.log(`No IMDb ID available for ${content.title}, skipping OMDB enhancement`);
    return content;
  }

  try {
    const omdbData = await getContentByImdbId(content.imdbId);

    // Create an array of genres from both sources, removing duplicates
    const combinedGenres = Array.from(new Set([
      ...(content.genres || []),
      ...(omdbData.genres || [])
    ]));

    // Merge the data, preferring TMDB for core content data and images
    // but using OMDB for additional details and metadata
    return {
      ...content,
      // Only use OMDB poster if TMDB doesn't have one
      posterPath: content.posterPath || omdbData.posterPath,
      // Only use OMDB overview if TMDB doesn't have one
      overview: content.overview || omdbData.overview,
      // Enhance with OMDB-specific fields
      director: omdbData.director,
      actors: omdbData.actors,
      awards: omdbData.awards,
      rated: omdbData.rated,
      released: omdbData.released,
      metascore: omdbData.metascore,
      // Use OMDB rating if TMDB doesn't have one
      rating: content.rating || omdbData.rating,
      // Use OMDB runtime if TMDB doesn't have one
      runtime: content.runtime || omdbData.runtime,
      // Use merged genres
      genres: combinedGenres.length > 0 ? combinedGenres : undefined,
      // Mark as having data from both sources
      dataSource: 'both'
    };
  } catch (error) {
    console.error(`Error enhancing content with OMDB data for ${content.imdbId}:`, error);
    // Return the original content if OMDB enhancement fails
    return content;
  }
}

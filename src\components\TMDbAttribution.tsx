import React from 'react';
import Image from 'next/image';
import Link from 'next/link';

interface TMDbAttributionProps {
  className?: string;
  variant?: 'default' | 'small' | 'dark';
}

export default function TMDbAttribution({
  className = '',
  variant = 'default'
}: TMDbAttributionProps) {
  // Logo variants
  const logoSrc = 
    variant === 'dark' 
      ? '/tmdb-logos/tmdb-logo-alt.svg' 
      : '/tmdb-logos/tmdb-logo.svg';

  // Size variants
  const sizes = {
    default: { width: 120, height: 17 },
    small: { width: 80, height: 12 }
  };
  
  const size = variant === 'small' ? sizes.small : sizes.default;

  return (
    <div className={`flex flex-col items-center ${className}`}>
      <div className="flex items-center">
        <Link href="https://www.themoviedb.org/" target="_blank" rel="noopener noreferrer">
          <div className="relative" style={{ width: size.width, height: size.height }}>
            <Image 
              src={logoSrc} 
              alt="TMDb Attribution" 
              width={size.width}
              height={size.height}
              className="object-contain"
            />
          </div>
        </Link>
      </div>
      <p className={`text-xs ${variant === 'dark' ? 'text-vista-dark' : 'text-vista-light/70'} mt-1 text-center`}>
        This product uses the TMDB API but is not endorsed or certified by TMDB.
      </p>
    </div>
  );
} 
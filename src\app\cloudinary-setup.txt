# Setting Up Cloudinary for StreamVista

To enable profile image uploads, you need to create a Cloudinary account and set up the proper upload presets. Follow these steps:

## 1. Create a Cloudinary Account

1. Go to [Cloudinary](https://cloudinary.com/) and sign up for a free account
2. After signing up, you'll be taken to your dashboard

## 2. Get Your Cloud Name

1. On your Cloudinary dashboard, look for your "Cloud name" - you'll need this for the .env.local file
2. Update your .env.local file with:
   ```
   NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloud_name
   ```

## 3. Create an Upload Preset

1. In the Cloudinary dashboard, go to Settings → Upload
2. Scroll down to "Upload presets" and click "Add upload preset"
3. Set the following:
   - Preset name: `streamvista_profiles`
   - Signing Mode: Unsigned
   - Folder: `streamvista/profiles`
   - Allowed formats: Leave as is or restrict to image formats only
   - Enable "Use filename or externally defined public ID" option
4. Click "Save"
5. Update your .env.local file with:
   ```
   NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET=streamvista_profiles
   ```

## 4. Configure CORS Settings

1. In the Cloudinary dashboard, go to Settings → Security
2. Under "Allowed CORS origins", add your website URL (for development: http://localhost:3000)
3. Click "Save"

## 5. Restart Your Application

1. Stop the development server
2. Restart it with `npm run dev`

You should now be able to upload profile images in the settings page of your application! 
import { WatchHistoryItem } from './insights-utils';
import { IContent } from '@/data/content';

interface ContentScore {
  content: IContent;
  score: number;
}

// User insights interface
interface UserInsights {
  genrePreferences: Record<string, number>;
  preferMoviesOverShows: number; // Positive for movies, negative for shows
}

// Using a stable seed for randomization to avoid hydration mismatches
function stableRandomGenerator(seed = 42) {
  // Simple, deterministic pseudo-random number generator
  let currentSeed = seed;
  return function() {
    // Simple PRNG algorithm
    currentSeed = (currentSeed * 9301 + 49297) % 233280;
    return currentSeed / 233280;
  };
}

// Deterministic Fisher-Yates shuffle
function stableShuffle<T>(array: T[], seededRandom: () => number): T[] {
  const result = [...array];
  for (let i = result.length - 1; i > 0; i--) {
    const j = Math.floor(seededRandom() * (i + 1));
    [result[i], result[j]] = [result[j], result[i]];
  }
  return result;
}

// Generate user insights from watch history
function generateUserInsights(watchHistory: WatchHistoryItem[], allContent: IContent[]): UserInsights {
  // Create content map for genre lookup
  const contentMap = new Map<string, IContent>();
  allContent.forEach(item => {
    contentMap.set(item.id, item);
  });

  // Initialize
  const genreCounts: Record<string, number> = {};
  let movieCount = 0;
  let showCount = 0;

  // Process watch history
  watchHistory.forEach(item => {
    const content = contentMap.get(item.contentId);
    if (!content) return;

    // Count content types
    if (item.type === 'movie') {
      movieCount++;
    } else {
      showCount++;
    }

    // Count genres
    content.genres.forEach(genre => {
      // Weight by progress and completed status
      const weight = (item.progress / 100) * (item.completed ? 1.5 : 1);
      genreCounts[genre] = (genreCounts[genre] || 0) + weight;
    });
  });

  // Calculate genre preferences (normalized)
  const genrePreferences: Record<string, number> = {};
  const totalGenreWeight = Object.values(genreCounts).reduce((sum, count) => sum + count, 0);

  if (totalGenreWeight > 0) {
    Object.entries(genreCounts).forEach(([genre, count]) => {
      genrePreferences[genre] = count / totalGenreWeight;
    });
  }

  // Calculate content type preference (-1 to 1 scale)
  const totalItems = movieCount + showCount;
  let preferMoviesOverShows = 0;

  if (totalItems > 0) {
    // Scale from -1 (all shows) to 1 (all movies)
    preferMoviesOverShows = (movieCount - showCount) / totalItems;
  }

  return {
    genrePreferences,
    preferMoviesOverShows
  };
}

export function generateRecommendations(
  watchHistory: WatchHistoryItem[],
  allContent: IContent[],
  count: number = 10
): IContent[] {
  // Create a stable random function
  const getRandom = stableRandomGenerator(42);

  // If no watch history, return random popular content
  if (watchHistory.length === 0) {
    return stableShuffle(allContent, getRandom).slice(0, count);
  }

  // Get insights from user's watching patterns
  const userInsights = generateUserInsights(watchHistory, allContent);

  // Create a map of watched content IDs to filter them out later
  const watchedContentIds = new Set(watchHistory.map((item) => item.contentId));

  // Create a map of content scores
  const contentScores: ContentScore[] = [];

  // Score each piece of content based on user preferences
  allContent.forEach((content) => {
    // Skip content that the user has already watched
    if (watchedContentIds.has(content.id)) {
      return;
    }

    let score = 0;

    // Base score for newer content
    const currentYear = new Date().getFullYear();
    score += Math.max(0, 10 - (currentYear - content.year));

    // Score based on genre preferences
    content.genres.forEach((genre) => {
      const genrePreference = userInsights.genrePreferences[genre] || 0;
      score += genrePreference * 5;
    });

    // Score based on type preferences (movie/show)
    if (content.type === 'movie' && userInsights.preferMoviesOverShows > 0) {
      score += userInsights.preferMoviesOverShows * 3;
    } else if (content.type === 'show' && userInsights.preferMoviesOverShows < 0) {
      score += Math.abs(userInsights.preferMoviesOverShows) * 3;
    }

    // Popularity boost
    if (content.popularity) {
      score += content.popularity;
    }

    // Add a small deterministic variation to break ties
    // Using contentId hash instead of Math.random()
    const contentIdHash = content.id
      .split('')
      .reduce((acc, char) => acc + char.charCodeAt(0), 0);
    score += (contentIdHash % 10) / 10;

    contentScores.push({ content, score });
  });

  // Sort by score (descending)
  contentScores.sort((a, b) => b.score - a.score);

  // Return top N recommendations
  const topRecommendations = contentScores.slice(0, count * 2).map((item) => item.content);

  // Add some stable randomization to the top recommendations for variety
  return stableShuffle(topRecommendations, getRandom).slice(0, count);
}

import { NextRequest, NextResponse } from 'next/server';
import { getTVShowDetails } from '@/lib/tmdb-api';

/**
 * API route for fetching TV show details from TMDb
 */
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const id = searchParams.get('id');
  
  if (!id) {
    return NextResponse.json({ error: 'TV show ID is required' }, { status: 400 });
  }
  
  try {
    // Fetch TV show details from TMDb
    const tvShowDetails = await getTVShowDetails(id);
    
    console.log(`API: Fetched TV show details for ID ${id}`);
    
    return NextResponse.json(tvShowDetails);
  } catch (error) {
    console.error(`Error fetching TV show details:`, error);
    return NextResponse.json({ error: 'Failed to fetch TV show details' }, { status: 500 });
  }
} 
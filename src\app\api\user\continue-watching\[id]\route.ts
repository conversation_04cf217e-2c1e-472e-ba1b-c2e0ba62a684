import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const url = new URL(request.url);
    const userId = url.searchParams.get('userId');

    // If no userId is provided, try to get it from headers or session
    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'User ID is required'
      }, { status: 400 });
    }

    // Connect to MongoDB
    await ensureMongooseConnection();

    try {
      // Import the UserContentInteraction model
      const { default: UserContentInteraction } = await import('@/models/UserContentInteraction');
      
      // Delete the specific watch history item
      const result = await UserContentInteraction.deleteMany({
        userId,
        contentId: id,
        interactionType: 'view'
      });

      return NextResponse.json({
        success: true,
        message: `Removed ${result.deletedCount} watch history entries`,
        deletedCount: result.deletedCount
      });

    } catch (dbError) {
      console.error('Database error removing watch history item:', dbError);
      
      return NextResponse.json({
        success: false,
        error: 'Failed to remove item from watch history'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error in continue watching delete API:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

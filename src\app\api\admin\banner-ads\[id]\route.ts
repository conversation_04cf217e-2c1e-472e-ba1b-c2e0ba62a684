import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import BannerAd from '@/models/BannerAd';
import User from '@/models/User';
import mongoose from 'mongoose';

/**
 * GET /api/admin/banner-ads/[id]
 * Get a specific banner ad by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Try to get the user ID from the cookie directly
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    if (!userId) {
      console.error('Admin banner-ads API: No user ID found in request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await ensureMongooseConnection();

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as { role?: string }).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    const id = (await params).id;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid banner ad ID' },
        { status: 400 }
      );
    }

    // Find the banner ad
    const bannerAd = await BannerAd.findById(id)
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email')
      .lean();

    if (!bannerAd) {
      return NextResponse.json(
        { error: 'Banner ad not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(bannerAd);

  } catch (error) {
    console.error('Error fetching banner ad:', error);
    return NextResponse.json(
      { error: 'Failed to fetch banner ad' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/banner-ads/[id]
 * Update a specific banner ad
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Try to get the user ID from the cookie directly
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    if (!userId) {
      console.error('Admin banner-ads API: No user ID found in request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await ensureMongooseConnection();

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as { role?: string }).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    const id = (await params).id;
    const body = await request.json();

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid banner ad ID' },
        { status: 400 }
      );
    }

    // Check if banner ad exists
    const existingBannerAd = await BannerAd.findById(id);
    if (!existingBannerAd) {
      return NextResponse.json(
        { error: 'Banner ad not found' },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: Record<string, unknown> = {
      updatedBy: new mongoose.Types.ObjectId(userId)
    };

    // Update fields if provided
    if (body.title !== undefined) updateData.title = body.title;
    if (body.description !== undefined) updateData.description = body.description;
    if (body.imageUrl !== undefined) updateData.imageUrl = body.imageUrl;
    if (body.linkUrl !== undefined) updateData.linkUrl = body.linkUrl;
    if (body.isActive !== undefined) updateData.isActive = body.isActive;
    if (body.startDate !== undefined) updateData.startDate = new Date(body.startDate);
    if (body.endDate !== undefined) updateData.endDate = body.endDate ? new Date(body.endDate) : null;
    if (body.duration !== undefined) updateData.duration = body.duration;
    if (body.priority !== undefined) updateData.priority = body.priority;

    // Update styling if provided
    if (body.styling) {
      updateData.styling = {
        ...existingBannerAd.styling,
        ...body.styling
      };
    }

    // Update the banner ad
    const updatedBannerAd = await BannerAd.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true, runValidators: true }
    )
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email')
      .lean();

    return NextResponse.json(updatedBannerAd);

  } catch (error) {
    console.error('Error updating banner ad:', error);
    return NextResponse.json(
      { error: 'Failed to update banner ad' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/banner-ads/[id]
 * Delete a specific banner ad
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Try to get the user ID from the cookie directly
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    if (!userId) {
      console.error('Admin banner-ads API: No user ID found in request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await ensureMongooseConnection();

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as { role?: string }).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    const id = (await params).id;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid banner ad ID' },
        { status: 400 }
      );
    }

    // Check if banner ad exists
    const bannerAd = await BannerAd.findById(id);
    if (!bannerAd) {
      return NextResponse.json(
        { error: 'Banner ad not found' },
        { status: 404 }
      );
    }

    // Delete the banner ad
    await BannerAd.findByIdAndDelete(id);

    return NextResponse.json(
      { message: 'Banner ad deleted successfully' },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error deleting banner ad:', error);
    return NextResponse.json(
      { error: 'Failed to delete banner ad' },
      { status: 500 }
    );
  }
}

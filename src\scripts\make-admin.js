// <PERSON>ript to make a user an admin
// Usage: node -r esm scripts/make-admin.js <EMAIL>

import { ensureMongooseConnection } from '../lib/mongodb';
import User from '../models/User';

async function makeAdmin(email) {
  if (!email) {
    console.error('Error: Email is required');
    console.log('Usage: node -r esm scripts/make-admin.js <EMAIL>');
    process.exit(1);
  }

  console.log(`Attempting to make user with email ${email} an admin...`);
  
  try {
    // Connect to MongoDB
    await ensureMongooseConnection();
    console.log('Connected to MongoDB');
    
    // Find the user first to confirm they exist
    const user = await User.findOne({ email });
    
    if (!user) {
      console.error(`Error: No user found with email ${email}`);
      process.exit(1);
    }
    
    console.log(`Found user: ${user.name} (Current role: ${user.role || 'user'})`);
    
    // Update the user's role to admin
    const result = await User.updateOne(
      { email }, 
      { $set: { role: 'admin' } }
    );
    
    if (result.modifiedCount === 0) {
      console.log('No changes made - user might already be an admin');
    } else {
      console.log(`Success! Updated ${result.modifiedCount} user(s) to admin role`);
    }
    
    // Verify the change
    const updatedUser = await User.findOne({ email });
    console.log(`User ${updatedUser.name} now has role: ${updatedUser.role}`);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    process.exit();
  }
}

// Get email from command line arguments
const email = process.argv[2];
makeAdmin(email); 
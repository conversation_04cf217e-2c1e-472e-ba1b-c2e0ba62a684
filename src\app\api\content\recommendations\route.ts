import { NextRequest, NextResponse } from 'next/server';
import { getPopularMovies, getTopRatedTVShows, getTrendingDaily } from '@/lib/tmdb-api';
import { formatTMDbContentForCards } from '@/lib/content-utils';

// Cache the results to improve performance
const cache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds

export async function GET(request: NextRequest) {
  try {
    // Get page from query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');

    // Check if we have a cached response for this page
    const cacheKey = `recommendations-page-${page}`;
    const cachedData = cache.get(cacheKey);

    if (cachedData && Date.now() - cachedData.timestamp < CACHE_TTL) {
      return NextResponse.json(cachedData.data);
    }

    // Use trending content for faster loading
    const trending = await getTrendingDaily('all', page);

    // Format the data for content cards
    const formattedContent = formatTMDbContentForCards(trending.slice(0, 12));

    // Add some recommendation metadata for display
    const enhancedContent = formattedContent.map(item => {
      // Add random watch time for some items (for demo purposes)
      const hasWatchTime = Math.random() > 0.8;

      return {
        ...item,
        watchTimeMinutes: hasWatchTime ? Math.floor(Math.random() * 40) + 10 : undefined
      };
    });

    // Prepare the response
    const response = {
      success: true,
      data: enhancedContent,
      page,
      hasMore: page < 5 // Limit to 5 pages for now
    };

    // Cache the response
    cache.set(cacheKey, {
      data: response,
      timestamp: Date.now()
    });

    // Return the formatted data
    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching recommendations:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch recommendations' },
      { status: 500 }
    );
  }
}

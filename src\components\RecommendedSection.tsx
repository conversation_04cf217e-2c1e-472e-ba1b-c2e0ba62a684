'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { ChevronLeft, ChevronRight, Play, Plus, Clock, Loader2, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/lib/i18n/LanguageContext';
import { getTrendingDaily, MappedContent } from '@/lib/tmdb-api';
import { useWatchlist } from '@/contexts/WatchlistContext';
import { ContentCardType } from '@/lib/content-utils';
import { toast } from 'sonner';

interface RecommendedSectionProps {
  title?: string;
  className?: string;
}

// Create a new component for individual trending card with its own hover state
const TrendingCard = ({
  item,
  handleToggleWatchlist,
  index,
  isInWatchlist,
  isProcessing
}: {
  item: MappedContent;
  handleToggleWatchlist: (item: MappedContent) => void;
  index: number;
  isInWatchlist: boolean;
  isProcessing: boolean;
}) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
      className="min-w-[260px] w-[260px] relative flex-shrink-0 snap-start"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="relative h-[390px] rounded-lg overflow-hidden bg-vista-dark-lighter">
        <Image
          src={item.posterUrl || '/images/placeholder-poster.jpg'}
          alt={item.title}
          fill
          sizes="260px"
          className={`object-cover transition-transform duration-300 ${isHovered ? 'scale-105' : ''}`}
          priority={index < 4}
        />

        {/* Overlay gradient for text visibility */}
        <div className={`absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent transition-opacity ${isHovered ? 'opacity-100' : 'opacity-0'}`}></div>

        {/* Content info */}
        <div className={`absolute bottom-0 left-0 right-0 p-4 transition-transform duration-300 ${isHovered ? 'translate-y-0' : 'translate-y-8'}`}>
          <div className="flex space-x-2 mb-3">
            <Link href={`/watch/${item.id}?forcePlay=true&contentType=${item.mediaType === 'tv' ? 'show' : 'movie'}`}>
              <Button size="sm" className="bg-white text-vista-dark hover:bg-white/90 gap-1 h-8">
                <Play className="h-3 w-3" />
                Watch
              </Button>
            </Link>
            <Button
              size="icon"
              variant="ghost"
              className={`h-8 w-8 rounded-full ${
                isInWatchlist
                  ? 'bg-vista-blue text-white hover:bg-vista-blue/90'
                  : isProcessing
                    ? 'bg-vista-blue/60 text-white animate-pulse'
                    : 'bg-black/40 text-vista-light hover:bg-black/60'
              }`}
              onClick={() => handleToggleWatchlist(item)}
              disabled={isProcessing}
              aria-label={isInWatchlist ? "Remove from your list" : "Add to your list"}
            >
              {isInWatchlist ? (
                <Check className="h-3.5 w-3.5" />
              ) : (
                <Plus className="h-3.5 w-3.5" />
              )}
            </Button>
          </div>
        </div>

        {/* Already in watchlist indicator */}
        {isInWatchlist && (
          <div className="absolute top-2 right-2 bg-vista-blue text-white text-xs font-medium py-1 px-2 rounded shadow-sm">
            In My List
          </div>
        )}
      </div>
      <div className="mt-3">
        <Link href={`/details/${item.mediaType === 'tv' ? 'shows' : 'movies'}/${item.id}`}>
          <h3 className={`font-medium text-vista-light line-clamp-1 transition-colors ${isHovered ? 'text-vista-light' : ''}`}>
            {item.title}
          </h3>
        </Link>
        <div className="flex items-center justify-between mt-1">
          <span className="text-sm text-vista-light/70">
            {item.year || (item.releaseDate ? new Date(item.releaseDate).getFullYear() : '')}
          </span>
          {item.voteAverage > 0 && (
            <span className="text-sm text-vista-light/70">
              ★ {item.voteAverage.toFixed(1)}
            </span>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default function RecommendedSection({
  title = "Trending Now",
  className = ""
}: RecommendedSectionProps) {
  const { t } = useLanguage();
  const { addToWatchlist, removeFromWatchlist, isInWatchlist } = useWatchlist();
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showLeftScroll, setShowLeftScroll] = useState(false);
  const [showRightScroll, setShowRightScroll] = useState(true);
  const [trendingData, setTrendingData] = useState<MappedContent[]>([]);
  const [processingItems, setProcessingItems] = useState<Record<string, boolean>>({});

  // Fetch trending data
  useEffect(() => {
    const fetchTrendingData = async () => {
      try {
        setIsLoading(true);
        const data = await getTrendingDaily('all');
        // Filter out items without poster images
        const filteredData = data.filter(item => item.posterUrl);
        setTrendingData(filteredData);
      } catch (error) {
        console.error('Error fetching trending data:', error);
        setTrendingData([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTrendingData();
  }, []);

  // Handle scroll buttons visibility
  useEffect(() => {
    const handleScroll = () => {
      if (!scrollContainerRef.current) return;

      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setShowLeftScroll(scrollLeft > 0);
      setShowRightScroll(scrollLeft < scrollWidth - clientWidth - 10);
    };

    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll);
      // Initial check
      handleScroll();
    }

    return () => {
      if (scrollContainer) {
        scrollContainer.removeEventListener('scroll', handleScroll);
      }
    };
  }, [isLoading]);

  // Scroll handlers
  const handleScrollLeft = () => {
    if (!scrollContainerRef.current) return;
    scrollContainerRef.current.scrollBy({
      left: -300,
      behavior: 'smooth'
    });
  };

  const handleScrollRight = () => {
    if (!scrollContainerRef.current) return;
    scrollContainerRef.current.scrollBy({
      left: 300,
      behavior: 'smooth'
    });
  };

  // Add to or remove from watchlist handler
  const handleToggleWatchlist = (item: MappedContent) => {
    // Prevent multiple clicks
    if (processingItems[item.id]) return;

    // Set this item as processing
    setProcessingItems(prev => ({ ...prev, [item.id]: true }));

    try {
      // Check if already in watchlist
      if (isInWatchlist(item.id)) {
        // Remove from watchlist
        removeFromWatchlist(item.id);

        // Show toast
        toast(`Removed "${item.title}" from My List`);

        // Reset state after delay
        setTimeout(() => {
          setProcessingItems(prev => ({ ...prev, [item.id]: false }));
        }, 800);
        return;
      }

      // Add to watchlist
      const contentType = item.mediaType === 'tv' ? 'shows' : 'movies';

      const contentItem: ContentCardType = {
        id: item.id,
        title: item.title,
        imagePath: item.posterUrl || item.backdropUrl || '/images/placeholder.jpg',
        type: contentType,
        year: item.year?.toString() || (item.releaseDate ? new Date(item.releaseDate).getFullYear().toString() : ''),
        userRating: item.voteAverage
      };

      addToWatchlist(contentItem);

      // Show toast
      toast(`Added "${item.title}" to My List`);

      // Reset state after delay
      setTimeout(() => {
        setProcessingItems(prev => ({ ...prev, [item.id]: false }));
      }, 800);
    } catch (error) {
      console.error('Error updating watchlist:', error);
      toast.error(`Failed to update "${item.title}" in your list`);
      setProcessingItems(prev => ({ ...prev, [item.id]: false }));
    }
  };

  // Check if item is in watchlist (using local state for UI)
  const isItemInWatchlist = (id: string) => {
    return isInWatchlist(id);
  };

  if (isLoading) {
    return (
      <section className={`py-8 ${className}`}>
        <div className="container px-4 mx-auto">
          <h2 className="text-2xl font-bold text-vista-light mb-6">{title}</h2>
          <div className="flex justify-center items-center h-64">
            <Loader2 className="w-10 h-10 text-vista-blue animate-spin" />
          </div>
        </div>
      </section>
    );
  }

  if (trendingData.length === 0) {
    return (
      <section className={`py-8 ${className}`}>
        <div className="container px-4 mx-auto">
          <h2 className="text-2xl font-bold text-vista-light mb-6">{title}</h2>
          <div className="flex justify-center items-center p-8 h-48 bg-vista-dark-lighter rounded-lg">
            <p className="text-vista-light text-center">
              Unable to load trending content at this time. Please check back later.
            </p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className={`py-8 ${className}`}>
      <div className="container px-4 mx-auto">
        <h2 className="text-2xl font-bold text-vista-light mb-6">{title}</h2>

        <div className="relative">
          {/* Left scroll button */}
          {showLeftScroll && (
            <button
              className="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 rounded-full bg-black/60 p-1 text-vista-light hover:bg-black/80 transition-all"
              onClick={handleScrollLeft}
              aria-label="Scroll left"
            >
              <ChevronLeft className="h-6 w-6" />
            </button>
          )}

          {/* Scrollable content */}
          <div
            ref={scrollContainerRef}
            className="flex overflow-x-auto pb-4 space-x-4 hide-scrollbar snap-x"
          >
            {trendingData.map((item, index) => (
              <TrendingCard
                key={item.id}
                item={item}
                handleToggleWatchlist={handleToggleWatchlist}
                index={index}
                isInWatchlist={isItemInWatchlist(item.id)}
                isProcessing={!!processingItems[item.id]}
              />
            ))}
          </div>

          {/* Right scroll button */}
          {showRightScroll && (
            <button
              className="absolute right-0 top-1/2 transform -translate-y-1/2 z-10 rounded-full bg-black/60 p-1 text-vista-light hover:bg-black/80 transition-all"
              onClick={handleScrollRight}
              aria-label="Scroll right"
            >
              <ChevronRight className="h-6 w-6" />
            </button>
          )}
        </div>
      </div>
    </section>
  );
}
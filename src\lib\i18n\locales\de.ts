// German translations (placeholder - this would include full translations)
import en from './en';

// Start with English translations as a fallback
const translations = { ...en };

// Add a few sample German translations to demonstrate functionality
const germanTranslations = {
  'common.loading': 'Wird geladen...',
  'common.search': 'Suchen',
  'common.cancel': 'Abbrechen',
  'common.save': 'Speichern',
  'common.delete': 'Löschen',
  'common.edit': 'Bearbeiten',

  'nav.home': 'Startseite',
  'nav.shows': 'Serien',
  'nav.movies': 'Filme',
  'nav.categories': 'Kategorien',
  'nav.myList': 'Meine Liste',
  'nav.downloads': 'Downloads',
  'nav.settings': 'Einstellungen',

  'home.popularShows': 'Beliebte Serien',
  'home.popularMovies': 'Beliebte Filme',
  'home.continueWatching': 'Weiterschauen',

  'downloads.title': 'Downloads',
  'downloads.subtitle': 'Schauen Sie Ihre Lieblingsserien und -filme offline an',

  'insights.title': 'Nutzungsstatistiken',
  'insights.watchHistory': 'Wiedergabeverlauf',
  'insights.overview': 'Übersicht',

  'settings.language': 'Sprache',
  'settings.displayLanguage': 'Anzeigesprache',
};

// Merge the German translations with the English fallbacks
Object.assign(translations, germanTranslations);

export default translations;
